"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 数据库备份脚本
 * 导出数据库结构和数据
 */
const child_process_1 = require("child_process");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
// 备份目录
const BACKUP_DIR = path_1.default.join(__dirname, '../../backups');
/**
 * 创建备份目录（如果不存在）
 */
function createBackupDir() {
    if (!fs_1.default.existsSync(BACKUP_DIR)) {
        fs_1.default.mkdirSync(BACKUP_DIR, { recursive: true });
        logger_1.default.info(`创建备份目录: ${BACKUP_DIR}`);
    }
}
/**
 * 生成备份文件名
 * @returns 备份文件名
 */
function generateBackupFileName() {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-');
    return `backup_${config_1.default.database.name}_${timestamp}.sql`;
}
/**
 * 备份数据库
 * @returns 备份文件路径
 */
function backupDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始备份数据库...');
        // 创建备份目录
        createBackupDir();
        // 生成备份文件名
        const backupFileName = generateBackupFileName();
        const backupFilePath = path_1.default.join(BACKUP_DIR, backupFileName);
        // 构建 mysqldump 命令
        const command = `mysqldump -h ${config_1.default.database.host} -P ${config_1.default.database.port} -u ${config_1.default.database.user} -p${config_1.default.database.password} ${config_1.default.database.name} > "${backupFilePath}"`;
        return new Promise((resolve, reject) => {
            (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                if (error) {
                    logger_1.default.error(`备份数据库失败: ${error.message}`);
                    reject(error);
                    return;
                }
                if (stderr) {
                    logger_1.default.warn(`备份过程中的警告: ${stderr}`);
                }
                logger_1.default.info(`数据库备份成功: ${backupFilePath}`);
                resolve(backupFilePath);
            });
        });
    });
}
/**
 * 清理旧备份文件
 * @param maxBackups 保留的最大备份数量
 */
function cleanupOldBackups(maxBackups = 5) {
    logger_1.default.info(`清理旧备份文件，保留最新的 ${maxBackups} 个备份...`);
    // 获取所有备份文件
    const backupFiles = fs_1.default.readdirSync(BACKUP_DIR)
        .filter(file => file.startsWith('backup_') && file.endsWith('.sql'))
        .map(file => ({
        name: file,
        path: path_1.default.join(BACKUP_DIR, file),
        time: fs_1.default.statSync(path_1.default.join(BACKUP_DIR, file)).mtime.getTime(),
    }))
        .sort((a, b) => b.time - a.time); // 按时间降序排序
    // 删除多余的备份文件
    if (backupFiles.length > maxBackups) {
        const filesToDelete = backupFiles.slice(maxBackups);
        for (const file of filesToDelete) {
            fs_1.default.unlinkSync(file.path);
            logger_1.default.info(`删除旧备份文件: ${file.name}`);
        }
        logger_1.default.info(`共删除 ${filesToDelete.length} 个旧备份文件`);
    }
    else {
        logger_1.default.info('没有需要删除的旧备份文件');
    }
}
/**
 * 主函数
 */
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // 备份数据库
            yield backupDatabase();
            // 清理旧备份文件
            cleanupOldBackups();
            logger_1.default.info('数据库备份完成');
        }
        catch (error) {
            logger_1.default.error('数据库备份失败:', error);
            process.exit(1);
        }
    });
}
// 如果直接运行此脚本，则执行备份
if (require.main === module) {
    main();
}
exports.default = backupDatabase;
//# sourceMappingURL=backupDatabase.js.map