/**
 * 数据库模型索引文件
 * 统一导出所有模型，并建立模型之间的关联关系
 */
import sequelize from '../config/database';
import logger from '../utils/logger';

// 导入模型
// 用户相关模型
import User from './User';
import Membership from './Membership';
import Transaction from './Transaction';
import SignIn from './SignIn';
import BackgroundSetting from './BackgroundSetting';
import SearchHistory from './SearchHistory';
import AdView from './AdView';

// 厨房相关模型
import Kitchen from './Kitchen';
import KitchenMember from './KitchenMember';
import Table from './Table';

// 菜品相关模型
import Category from './Category';
import Dish from './Dish';
import DishImage from './DishImage';
import Ingredient from './Ingredient';
import Nutrition from './Nutrition';
import CookingStep from './CookingStep';
import Like from './Like';
import Report from './Report';
import Comment from './Comment';
import HotKeyword from './HotKeyword';

// 订单相关模型
import CartItem from './CartItem';
import Order from './Order';
import OrderItem from './OrderItem';

// 消息相关模型
import Message from './Message';
import DiscoverItem from './DiscoverItem';

// 反馈相关模型
import Feedback from './Feedback';

// 系统相关模型
import SystemSetting from './SystemSetting';

// 建立模型之间的关联关系
export const initializeAssociations = (): void => {
  logger.info('初始化模型关联关系...');

  // 用户与会员关系
  User.hasOne(Membership, { foreignKey: 'user_id', as: 'membership' });
  Membership.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与交易记录关系
  User.hasMany(Transaction, { foreignKey: 'user_id', as: 'transactions' });
  Transaction.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与签到记录关系
  User.hasMany(SignIn, { foreignKey: 'user_id', as: 'signIns' });
  SignIn.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与背景设置关系
  User.hasOne(BackgroundSetting, { foreignKey: 'user_id', as: 'backgroundSetting' });
  BackgroundSetting.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与搜索历史关系
  User.hasMany(SearchHistory, { foreignKey: 'user_id', as: 'searchHistories' });
  SearchHistory.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与广告观看记录关系
  User.hasMany(AdView, { foreignKey: 'user_id', as: 'adViews' });
  AdView.belongsTo(User, { foreignKey: 'user_id' });

  // 用户与厨房关系
  User.hasMany(Kitchen, { foreignKey: 'owner_id', as: 'ownedKitchens' });
  Kitchen.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

  // 用户与厨房成员关系
  User.hasMany(KitchenMember, { foreignKey: 'user_id', as: 'kitchenMemberships' });
  KitchenMember.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 厨房与厨房成员关系
  Kitchen.hasMany(KitchenMember, { foreignKey: 'kitchen_id', as: 'members' });
  KitchenMember.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 厨房与桌号关系
  Kitchen.hasMany(Table, { foreignKey: 'kitchen_id', as: 'tables' });
  Table.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 厨房与分类关系
  Kitchen.hasMany(Category, { foreignKey: 'kitchen_id', as: 'categories' });
  Category.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 分类与菜品关系
  Category.hasMany(Dish, { foreignKey: 'category_id', as: 'dishes' });
  Dish.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

  // 厨房与菜品关系
  Kitchen.hasMany(Dish, { foreignKey: 'kitchen_id', as: 'dishes' });
  Dish.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 用户与创建的菜品关系
  User.hasMany(Dish, { foreignKey: 'created_by', as: 'createdDishes' });
  Dish.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

  // 菜品与菜品图片关系
  Dish.hasMany(DishImage, { foreignKey: 'dish_id', as: 'images' });
  DishImage.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });

  // 菜品与配料关系
  Dish.hasMany(Ingredient, { foreignKey: 'dish_id', as: 'ingredients' });
  Ingredient.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });

  // 菜品与营养成分关系
  Dish.hasOne(Nutrition, { foreignKey: 'dish_id', as: 'nutrition' });
  Nutrition.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });

  // 菜品与烹饪步骤关系
  Dish.hasMany(CookingStep, { foreignKey: 'dish_id', as: 'cookingSteps' });
  CookingStep.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });

  // 菜品与点赞关系
  Dish.hasMany(Like, { foreignKey: 'dish_id', as: 'likes' });
  Like.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });
  User.hasMany(Like, { foreignKey: 'user_id', as: 'userLikes' }); // 修改关联名称，避免命名冲突
  Like.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 菜品与举报关系
  Dish.hasMany(Report, { foreignKey: 'dish_id', as: 'reports' });
  Report.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });
  User.hasMany(Report, { foreignKey: 'user_id', as: 'reports' });
  Report.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 菜品与评论关系
  Dish.hasMany(Comment, { foreignKey: 'dish_id', as: 'comments' });
  Comment.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });
  User.hasMany(Comment, { foreignKey: 'user_id', as: 'comments' });
  Comment.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 用户与购物车关系
  User.hasMany(CartItem, { foreignKey: 'user_id', as: 'cartItems' });
  CartItem.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Dish.hasMany(CartItem, { foreignKey: 'dish_id', as: 'cartItems' });
  CartItem.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });
  Kitchen.hasMany(CartItem, { foreignKey: 'kitchen_id', as: 'cartItems' });
  CartItem.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 用户与订单关系
  User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
  Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Kitchen.hasMany(Order, { foreignKey: 'kitchen_id', as: 'orders' });
  Order.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 订单与订单项关系
  Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
  OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Dish.hasMany(OrderItem, { foreignKey: 'dish_id', as: 'orderItems' });
  OrderItem.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });

  // 用户与消息关系
  User.hasMany(Message, { foreignKey: 'user_id', as: 'messages' });
  Message.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 用户与发现项关系
  User.hasMany(DiscoverItem, { foreignKey: 'user_id', as: 'discoverItems' });
  DiscoverItem.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Dish.hasMany(DiscoverItem, { foreignKey: 'dish_id', as: 'discoverItems' });
  DiscoverItem.belongsTo(Dish, { foreignKey: 'dish_id', as: 'dish' });
  Kitchen.hasMany(DiscoverItem, { foreignKey: 'kitchen_id', as: 'discoverItems' });
  DiscoverItem.belongsTo(Kitchen, { foreignKey: 'kitchen_id', as: 'kitchen' });

  // 用户与反馈关系
  User.hasMany(Feedback, { foreignKey: 'user_id', as: 'feedbacks' });
  Feedback.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  logger.info('模型关联关系初始化完成');
};

// 导出所有模型
export {
  User,
  Membership,
  Transaction,
  SignIn,
  BackgroundSetting,
  SearchHistory,
  AdView,
  Kitchen,
  KitchenMember,
  Table,
  Category,
  Dish,
  DishImage,
  Ingredient,
  Nutrition,
  CookingStep,
  Like,
  Report,
  Comment,
  HotKeyword,
  CartItem,
  Order,
  OrderItem,
  Message,
  DiscoverItem,
  Feedback,
  SystemSetting,
};

// 导出数据库实例
export default sequelize;
