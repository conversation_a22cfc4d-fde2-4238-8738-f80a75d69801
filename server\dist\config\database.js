"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncModels = exports.testConnection = void 0;
/**
 * 数据库连接配置
 * 使用Sequelize ORM连接MySQL数据库
 */
const sequelize_1 = require("sequelize");
const config_1 = __importDefault(require("./config"));
const logger_1 = __importDefault(require("../utils/logger"));
// 创建Sequelize实例
const sequelize = new sequelize_1.Sequelize(config_1.default.database.name, config_1.default.database.user, config_1.default.database.password, {
    host: config_1.default.database.host,
    port: config_1.default.database.port,
    dialect: 'mysql',
    timezone: '+08:00', // 设置时区为东八区
    define: {
        timestamps: true, // 默认为模型添加createdAt和updatedAt字段
        underscored: true, // 使用下划线命名法
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
    },
    logging: (msg) => logger_1.default.debug(msg), // 使用自定义日志记录SQL查询
    pool: {
        max: 10, // 连接池最大连接数
        min: 0, // 连接池最小连接数
        acquire: 30000, // 获取连接的最大等待时间（毫秒）
        idle: 10000, // 连接在释放之前可以空闲的最长时间（毫秒）
    },
});
// 测试数据库连接
const testConnection = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield sequelize.authenticate();
        logger_1.default.info('数据库连接成功: ' + config_1.default.database.name);
    }
    catch (error) {
        logger_1.default.error('数据库连接失败:', error);
        throw error;
    }
});
exports.testConnection = testConnection;
// 同步数据库模型
const syncModels = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (force = false) {
    try {
        yield sequelize.sync({ force });
        logger_1.default.info(`数据库模型同步${force ? '(强制)' : ''} 完成`);
    }
    catch (error) {
        logger_1.default.error('数据库模型同步失败:', error);
        throw error;
    }
});
exports.syncModels = syncModels;
exports.default = sequelize;
//# sourceMappingURL=database.js.map