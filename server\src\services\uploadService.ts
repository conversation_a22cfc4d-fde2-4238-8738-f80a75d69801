/**
 * 上传服务
 * 处理文件上传相关的业务逻辑
 */
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import config from '../config/config';

/**
 * 保存上传文件
 * @param file 文件对象
 * @param type 文件类型
 * @returns 相对路径
 */
const saveUploadFile = async (file: Express.Multer.File, type: string): Promise<string> => {
  // 检查文件是否存在
  if (!file) {
    throw new BusinessError('文件不存在');
  }
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.mimetype)) {
    throw new BusinessError('不支持的文件类型');
  }
  
  // 检查文件大小
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    throw new BusinessError('文件大小超过限制');
  }
  
  // 生成文件名
  const fileName = `${uuidv4()}${path.extname(file.originalname)}`;
  
  // 确定存储目录
  let uploadDir = '';
  switch (type) {
    case 'dish':
      uploadDir = 'dish';
      break;
    case 'category':
      uploadDir = 'category';
      break;
    case 'kitchen':
      uploadDir = 'kitchen';
      break;
    case 'avatar':
      uploadDir = 'avatar';
      break;
    case 'background':
      uploadDir = 'background';
      break;
    default:
      uploadDir = 'dish';
  }
  
  // 创建目录（如果不存在）
  const dirPath = path.join(config.upload.path, uploadDir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  
  // 保存文件
  const filePath = path.join(dirPath, fileName);
  fs.copyFileSync(file.path, filePath);
  
  // 删除临时文件
  fs.unlinkSync(file.path);
  
  // 返回相对路径
  return `/uploads/${uploadDir}/${fileName}`;
};

/**
 * 上传菜品图片
 * @param file 文件对象
 * @returns 图片URL
 */
const uploadDishImage = async (file: Express.Multer.File): Promise<string> => {
  return await saveUploadFile(file, 'dish');
};

/**
 * 上传背景图片
 * @param file 文件对象
 * @returns 图片URL
 */
const uploadBackgroundImage = async (file: Express.Multer.File): Promise<string> => {
  return await saveUploadFile(file, 'background');
};

/**
 * 上传分类图标
 * @param file 文件对象
 * @returns 图标URL
 */
const uploadCategoryIcon = async (file: Express.Multer.File): Promise<string> => {
  return await saveUploadFile(file, 'category');
};

/**
 * 上传厨房头像
 * @param file 文件对象
 * @returns 头像URL
 */
const uploadKitchenAvatar = async (file: Express.Multer.File): Promise<string> => {
  return await saveUploadFile(file, 'kitchen');
};

/**
 * 上传用户头像
 * @param file 文件对象
 * @returns 头像URL
 */
const uploadUserAvatar = async (file: Express.Multer.File): Promise<string> => {
  return await saveUploadFile(file, 'avatar');
};

export default {
  uploadDishImage,
  uploadBackgroundImage,
  uploadCategoryIcon,
  uploadKitchenAvatar,
  uploadUserAvatar,
};
