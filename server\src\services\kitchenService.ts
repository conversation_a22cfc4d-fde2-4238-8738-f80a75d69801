/**
 * 厨房服务
 * 处理厨房相关的业务逻辑
 */
import { Op } from 'sequelize';
import sequelize from '../config/database';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { Kitchen, KitchenMember, User, Category, Dish, DishImage, Ingredient, Nutrition, CookingStep } from '../models';
import { generateKitchenId, getRandomKitchenAvatar, getRandomKitchenBackground } from '../utils/helpers';
import userService from './userService';
import config from '../config/config';
import { processImageUrl } from '../utils/helpers';
import { compressImage } from '../utils/imageCompressor';
import QRCode from 'qrcode';
import path from 'path';
import fs from 'fs';
import axios from 'axios';
import sharp from 'sharp';
import FormData from 'form-data';
import { convertToRelativePath, convertToFullUrl } from '../utils/urlManager';

/**
 * 获取厨房列表
 * @param userId 用户ID
 * @returns 厨房列表
 */
const getKitchenList = async (userId: number): Promise<any[]> => {
  try {
    logger.info(`获取用户(ID:${userId})的厨房列表`);
    
    // 获取用户创建的厨房
    let ownedKitchens: any[] = [];
    try {
      ownedKitchens = await Kitchen.findAll({
        where: { owner_id: userId },
        order: [['created_at', 'DESC']],
      });
      logger.info(`用户(ID:${userId})创建的厨房数量: ${ownedKitchens.length}`);
    } catch (err) {
      logger.error(`获取用户创建的厨房失败:`, err);
      ownedKitchens = [];
    }

    // 获取用户加入的厨房
    let joinedKitchens: any[] = [];
    try {
      const joinedKitchenMembers = await KitchenMember.findAll({
        where: {
          user_id: userId,
          role: { [Op.ne]: 'owner' }, // 排除自己创建的厨房
        },
        include: [
          {
            model: Kitchen,
            as: 'kitchen',
          },
        ],
        order: [['join_time', 'DESC']],
      });
      
      logger.info(`用户(ID:${userId})加入的厨房成员记录数量: ${joinedKitchenMembers.length}`);
      
      joinedKitchens = joinedKitchenMembers
        .filter(member => member.kitchen)
        .map(member => member.kitchen!);
      
      logger.info(`用户(ID:${userId})有效加入的厨房数量: ${joinedKitchens.length}`);
    } catch (err) {
      logger.error(`获取用户加入的厨房失败:`, err);
      joinedKitchens = [];
    }

    // 合并并去重
    const allKitchens = [...ownedKitchens, ...joinedKitchens];
    const uniqueKitchenMap = new Map();
    
    // 使用Map去重，避免直接使用复杂对象作为键
    allKitchens.forEach(kitchen => {
      if (kitchen && kitchen.id) {
        uniqueKitchenMap.set(kitchen.id, kitchen);
      }
    });
    
    const uniqueKitchens = Array.from(uniqueKitchenMap.values());
    
    logger.info(`用户(ID:${userId})去重后的厨房数量: ${uniqueKitchens.length}`);

    // 转换为前端需要的格式
    return uniqueKitchens.map(kitchen => ({
      id: kitchen.id,
      name: kitchen.name || '未命名厨房',
      level: kitchen.level || 1,
      avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
      notice: kitchen.notice || '',
      isOwner: kitchen.owner_id === userId,
    }));
  } catch (error) {
    logger.error(`获取厨房列表失败:`, error);
    // 即使出错也返回空数组而不是抛出异常，确保前端能收到有效响应
    return [];
  }
};

/**
 * 获取用户创建的厨房列表
 * @param userId 用户ID
 * @returns 厨房列表
 */
const getOwnedKitchenList = async (userId: number): Promise<any[]> => {
  const kitchens = await Kitchen.findAll({
    where: { owner_id: userId },
    order: [['created_at', 'DESC']],
  });

  return kitchens.map(kitchen => ({
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    notice: kitchen.notice,
    isOwner: true,
  }));
};

/**
 * 获取用户加入的厨房列表
 * @param userId 用户ID
 * @returns 厨房列表
 */
const getJoinedKitchenList = async (userId: number): Promise<any[]> => {
  const members = await KitchenMember.findAll({
    where: {
      user_id: userId,
      role: { [Op.ne]: 'owner' }, // 排除自己创建的厨房
    },
    include: [
      {
        model: Kitchen,
        as: 'kitchen',
      },
    ],
    order: [['join_time', 'DESC']],
  });

  return members
    .filter(member => member.kitchen)
    .map(member => ({
      id: member.kitchen!.id,
      name: member.kitchen!.name,
      level: member.kitchen!.level,
      avatarUrl: processImageUrl(member.kitchen!.avatar_url, 'kitchen-avatar'),
      notice: member.kitchen!.notice,
      isOwner: false,
      role: member.role,
    }));
};

/**
 * 获取厨房详细信息
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 厨房详细信息
 */
const getKitchenInfo = async (userId: number, kitchenId: string): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId, {
    include: [
      {
        model: User,
        as: 'owner',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
  });

  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房成员
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member) {
    throw new BusinessError('您不是该厨房的成员');
  }

  // 获取厨房分类数量
  const categoryCount = await Category.count({
    where: { kitchen_id: kitchenId },
  });

  // 获取厨房菜品数量
  const dishCount = await Dish.count({
    where: { kitchen_id: kitchenId },
  });

  // 获取厨房成员数量
  const memberCount = await KitchenMember.count({
    where: { kitchen_id: kitchenId },
  });

  // 获取用户的厨房数量统计
  const kitchenCount = await Kitchen.count({
    where: { owner_id: userId },
  });

  return {
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    backgroundUrl: processImageUrl(kitchen.background_url, 'kitchen-background'),
    notice: kitchen.notice,
    owner: {
      id: kitchen.owner?.id,
      nickName: kitchen.owner?.nick_name,
      avatarUrl: processImageUrl(kitchen.owner?.avatar_url, 'user-avatar'),
    },
    kitchenCount,
    kitchenLimit: kitchen.kitchen_limit,
    categoryCount,
    dishCount,
    memberCount,
    categoryLimit: kitchen.category_limit,
    dishLimit: kitchen.dish_limit,
    memberLimit: kitchen.member_limit,
    userRole: member.role,
    isOwner: member.isOwner(),
    canEdit: member.canEdit(),
    createdAt: kitchen.created_at,
  };
};

/**
 * 获取厨房基本信息
 * @param kitchenId 厨房ID
 * @param userId 用户ID（可选）
 * @returns 厨房基本信息
 */
const getKitchenBaseInfo = async (kitchenId: string, userId?: number): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId, {
    include: [
      {
        model: User,
        as: 'owner',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
  });

  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房成员
  let userRole = null;
  let isOwner = false;
  let canEdit = false;

  if (userId) {
    const member = await KitchenMember.findOne({
      where: {
        user_id: userId,
        kitchen_id: kitchenId,
      },
    });

    if (member) {
      userRole = member.role;
      isOwner = member.isOwner();
      canEdit = member.canEdit();
    }
  }

  return {
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    backgroundUrl: processImageUrl(kitchen.background_url, 'kitchen-background'),
    notice: kitchen.notice,
    owner: {
      id: kitchen.owner?.id || kitchen.owner_id, // 如果关联查询失败，使用owner_id
      nickName: kitchen.owner?.nick_name || '',
      avatarUrl: processImageUrl(kitchen.owner?.avatar_url, 'user-avatar'),
    },
    userRole,
    isOwner,
    canEdit,
  };
};

/**
 * 创建厨房
 * @param userId 用户ID
 * @param name 厨房名称
 * @param notice 厨房公告
 * @param avatarUrl 厨房头像URL
 * @returns 创建结果
 */
const createKitchen = async (userId: number, name: string, notice?: string, avatarUrl?: string): Promise<any> => {
  // 检查用户是否存在
  const user = await User.findByPk(userId);
  if (!user) {
    throw new BusinessError('用户不存在');
  }

  // 转换头像URL为相对路径
  if (avatarUrl) {
    avatarUrl = convertToRelativePath(avatarUrl);
  }

  // 创建厨房需要288大米
  const createCost = 288;
  
  // 检查用户大米是否足够
  if (user.coins < createCost) {
    throw new BusinessError(`大米不足，创建厨房需要${createCost}大米，您当前有${user.coins}大米`);
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 扣除用户大米并记录交易
    await userService.createTransaction(
      userId,
      'out',
      createCost,
      '创建厨房',
      transaction
    );

    // 更新用户大米数量
    await user.update({
      coins: user.coins - createCost,
    }, { transaction });

  // 生成厨房ID
  const kitchenId = generateKitchenId();

  // 创建厨房
  const kitchen = await Kitchen.create({
    id: kitchenId,
    name,
    notice: notice || '',
    owner_id: userId,
    avatar_url: avatarUrl || getRandomKitchenAvatar(), // 如果没有提供头像，使用随机头像
    level: 1, // 默认等级为1
    kitchen_count: 1, // 当前厨房数量
    kitchen_limit: 999, // 厨房数量上限（设置为很大的值，实际无限制）
    category_count: 0, // 分类数量
    category_limit: 5, // 分类数量上限
    dish_count: 0, // 菜品数量
    dish_limit: 30, // 菜品数量上限
    member_count: 1, // 成员数量（包含拥有者）
    member_limit: 100, // 成员数量上限
    background_url: getRandomKitchenBackground(), // 使用随机背景图
    }, { transaction });

  // 创建厨房成员记录（拥有者）
  await KitchenMember.create({
    kitchen_id: kitchenId,
    user_id: userId,
    role: 'owner',
    join_time: new Date(),
    }, { transaction });

  // 更新用户厨房数量
  await user.update({
    kitchens: user.kitchens + 1,
    }, { transaction });

    await transaction.commit();

  return {
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    notice: kitchen.notice,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    backgroundUrl: processImageUrl(kitchen.background_url, 'kitchen-background'),
      costPaid: createCost,
      remainingCoins: user.coins - createCost,
  };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 更新厨房信息
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param kitchenData 厨房数据
 */
const updateKitchen = async (userId: number, kitchenId: string, kitchenData: any): Promise<void> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否有权限更新厨房
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限更新厨房信息');
  }

  // 转换头像URL为相对路径
  if (kitchenData.avatarUrl) {
    kitchenData.avatarUrl = convertToRelativePath(kitchenData.avatarUrl);
  }

  // 转换背景图片URL为相对路径
  if (kitchenData.backgroundUrl) {
    kitchenData.backgroundUrl = convertToRelativePath(kitchenData.backgroundUrl);
  }

  // 如果更新头像，需要删除旧的头像文件
  if (kitchenData.avatarUrl !== undefined && kitchen.avatar_url && kitchen.avatar_url !== kitchenData.avatarUrl) {
    try {
      // 提取旧头像的文件名
      const oldAvatarUrl = kitchen.avatar_url;
      const oldFilename = oldAvatarUrl.split('/').pop();
      
      if (oldFilename && !oldAvatarUrl.includes('default')) {
        // 导入deleteFile函数
        const { deleteFile } = require('../middlewares/upload');
        deleteFile(oldFilename, 'kitchen');
        logger.info(`已删除旧厨房头像: ${oldFilename}`);
      }
    } catch (error) {
      logger.error('删除旧厨房头像失败:', error);
      // 不抛出错误，继续更新操作
    }
  }

  // 如果更新背景图，需要删除旧的背景图文件
  if (kitchenData.backgroundUrl !== undefined && kitchen.background_url && kitchen.background_url !== kitchenData.backgroundUrl) {
    try {
      // 提取旧背景图的文件名
      const oldBackgroundUrl = kitchen.background_url;
      const oldFilename = oldBackgroundUrl.split('/').pop();
      
      if (oldFilename && !oldBackgroundUrl.includes('default')) {
        // 导入deleteFile函数
        const { deleteFile } = require('../middlewares/upload');
        deleteFile(oldFilename, 'background');
        logger.info(`已删除旧背景图: ${oldFilename}`);
      }
    } catch (error) {
      logger.error('删除旧背景图失败:', error);
      // 不抛出错误，继续更新操作
    }
  }

  // 更新厨房信息
  const updateData: any = {};

  if (kitchenData.name !== undefined) {
    updateData.name = kitchenData.name;
  }

  if (kitchenData.notice !== undefined) {
    updateData.notice = kitchenData.notice;
  }

  if (kitchenData.avatarUrl !== undefined) {
    updateData.avatar_url = kitchenData.avatarUrl;
  }

  if (kitchenData.backgroundUrl !== undefined) {
    updateData.background_url = kitchenData.backgroundUrl;
  }

  await kitchen.update(updateData);
};

/**
 * 加入厨房
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 加入结果
 */
const joinKitchen = async (userId: number, kitchenId: string): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否已经是厨房成员
  const existingMember = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (existingMember) {
    throw new BusinessError('您已经是该厨房的成员');
  }

  // 检查厨房成员数量是否超过限制
  const memberCount = await KitchenMember.count({
    where: { kitchen_id: kitchenId },
  });

  if (memberCount >= kitchen.member_limit) {
    throw new BusinessError(`厨房成员数量已达上限(${kitchen.member_limit})`);
  }

  // 创建厨房成员记录
  await KitchenMember.create({
    kitchen_id: kitchenId,
    user_id: userId,
    role: 'member', // 默认角色为普通成员
    join_time: new Date(),
  });

  // 更新厨房成员数量
  await kitchen.update({
    member_count: kitchen.member_count + 1,
  });

  return {
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    role: 'member',
  };
};

/**
 * 退出厨房
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 */
const leaveKitchen = async (userId: number, kitchenId: string): Promise<void> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房成员
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member) {
    throw new BusinessError('您不是该厨房的成员');
  }

  // 拥有者不能退出厨房
  if (member.role === 'owner') {
    throw new BusinessError('厨房拥有者不能退出厨房，请先转让厨房或解散厨房');
  }

  // 删除厨房成员记录
  await member.destroy();

  // 更新厨房成员数量
  if (kitchen.member_count > 0) {
    await kitchen.update({
      member_count: kitchen.member_count - 1,
    });
  }
};

/**
 * 解散厨房
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 */
const dismissKitchen = async (userId: number, kitchenId: string): Promise<void> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房拥有者
  if (kitchen.owner_id !== userId) {
    throw new BusinessError('只有厨房拥有者才能解散厨房');
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 删除厨房相关数据
    // 1. 删除厨房成员
    await KitchenMember.destroy({
      where: { kitchen_id: kitchenId },
      transaction,
    });

    // 2. 删除厨房分类和菜品
    const categories = await Category.findAll({
      where: { kitchen_id: kitchenId },
      transaction,
    });

    for (const category of categories) {
      // 删除分类下的菜品
      const dishes = await Dish.findAll({
        where: {
          category_id: category.id,
          kitchen_id: kitchenId,
        },
        transaction,
      });

      for (const dish of dishes) {
        // 删除菜品相关数据
        await DishImage.destroy({
          where: { dish_id: dish.id },
          transaction,
        });

        await Ingredient.destroy({
          where: { dish_id: dish.id },
          transaction,
        });

        await Nutrition.destroy({
          where: { dish_id: dish.id },
          transaction,
        });

        await CookingStep.destroy({
          where: { dish_id: dish.id },
          transaction,
        });

        // 删除菜品
        await dish.destroy({ transaction });
      }

      // 删除分类
      await category.destroy({ transaction });
    }

    // 3. 删除厨房
    await kitchen.destroy({ transaction });

    // 4. 更新用户厨房数量
    const user = await User.findByPk(userId);
    if (user && user.kitchens > 0) {
      await user.update({
        kitchens: user.kitchens - 1,
      }, { transaction });
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 升级厨房
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 升级结果
 */
const upgradeKitchen = async (userId: number, kitchenId: string): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房拥有者
  if (kitchen.owner_id !== userId) {
    throw new BusinessError('只有厨房拥有者才能升级厨房');
  }

  // 计算升级所需大米（固定38大米）
  const nextLevel = kitchen.level + 1;
  const upgradeCost = 38;

  // 检查用户大米是否足够
  const user = await User.findByPk(userId);
  if (!user) {
    throw new BusinessError('用户不存在');
  }

  if (user.coins < upgradeCost) {
    throw new BusinessError(`大米不足，升级到${nextLevel}级需要${upgradeCost}大米`);
  }

  // 计算新的限制（固定增量）
  const newCategoryLimit = kitchen.category_limit + 2;  // 分类额度+2
  const newDishLimit = kitchen.dish_limit + 5;          // 菜品额度+5
  const newMemberLimit = kitchen.member_limit + 1;      // 成员限制+1
  // 不再增加厨房限制
  const newKitchenLimit = kitchen.kitchen_limit;

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 扣除用户大米并记录交易（使用userService统一处理）
    await userService.createTransaction(
      userId,
      'out',
      upgradeCost,
      `升级厨房到${nextLevel}级`,
      transaction
    );

    // 更新用户大米数量
    await user.update({
      coins: user.coins - upgradeCost,
    }, { transaction });

    // 升级厨房
    await kitchen.update({
      level: nextLevel,
      category_limit: newCategoryLimit,
      dish_limit: newDishLimit,
      member_limit: newMemberLimit,
      kitchen_limit: newKitchenLimit,
    }, { transaction });

    await transaction.commit();

    return {
      success: true,
      level: nextLevel,
      categoryLimit: newCategoryLimit,
      dishLimit: newDishLimit,
      memberLimit: newMemberLimit,
      kitchenLimit: newKitchenLimit,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 获取厨房成员列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 成员列表
 */
const getKitchenMembers = async (userId: number, kitchenId: string): Promise<any[]> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房成员
  const userMember = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!userMember) {
    throw new BusinessError('您不是该厨房的成员');
  }

  // 获取厨房成员列表
  const members = await KitchenMember.findAll({
    where: { kitchen_id: kitchenId },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
    order: [
      ['role', 'ASC'], // 按角色排序，owner在前
      ['join_time', 'ASC'], // 按加入时间排序
    ],
  });

  return members.map(member => ({
    id: member.id,
    userId: member.user_id,
    userName: member.user?.nick_name,
    userAvatar: processImageUrl(member.user?.avatar_url, 'user-avatar'),
    role: member.role,
    joinTime: member.join_time,
    isOwner: member.role === 'owner',
    canEdit: member.role === 'owner' || member.role === 'admin' || member.role === 'editor',
  }));
};

/**
 * 更新成员权限
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param memberId 成员ID
 * @param role 角色
 */
const updateMemberPermission = async (userId: number, kitchenId: string, memberId: number, role: string): Promise<void> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房拥有者或管理员
  const userMember = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!userMember || !userMember.isAdmin()) {
    throw new BusinessError('没有权限更新成员权限');
  }

  // 检查要更新的成员是否存在
  const member = await KitchenMember.findOne({
    where: {
      id: memberId,
      kitchen_id: kitchenId,
    },
  });

  if (!member) {
    throw new BusinessError('成员不存在');
  }

  // 不能更改拥有者的角色
  if (member.role === 'owner') {
    throw new BusinessError('不能更改拥有者的角色');
  }

  // 检查角色是否有效
  if (!['admin', 'editor', 'member'].includes(role)) {
    throw new BusinessError('无效的角色');
  }

  // 如果用户是管理员，不能设置其他人为管理员
  if (userMember.role === 'admin' && role === 'admin') {
    throw new BusinessError('管理员不能设置其他人为管理员');
  }

  // 更新成员角色
  await member.update({ role });
};

/**
 * 移除成员
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param memberId 成员ID
 */
const removeMember = async (userId: number, kitchenId: string, memberId: number): Promise<void> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房拥有者或管理员
  const userMember = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!userMember || !userMember.isAdmin()) {
    throw new BusinessError('没有权限移除成员');
  }

  // 检查要移除的成员是否存在
  const member = await KitchenMember.findOne({
    where: {
      id: memberId,
      kitchen_id: kitchenId,
    },
  });

  if (!member) {
    throw new BusinessError('成员不存在');
  }

  // 不能移除拥有者
  if (member.role === 'owner') {
    throw new BusinessError('不能移除拥有者');
  }

  // 管理员不能移除其他管理员
  if (userMember.role === 'admin' && member.role === 'admin') {
    throw new BusinessError('管理员不能移除其他管理员');
  }

  // 移除成员
  await member.destroy();

  // 更新厨房成员数量
  if (kitchen.member_count > 0) {
    await kitchen.update({
      member_count: kitchen.member_count - 1,
    });
  }
};

/**
 * 获取厨房二维码
 * @param userId 用户ID（可选，支持匿名访问）
 * @param kitchenId 厨房ID
 * @param forceRefresh 是否强制刷新
 * @param enableCompress 是否启用压缩
 * @returns 二维码信息
 */
const getKitchenQrcode = async (userId: number | undefined, kitchenId: string, forceRefresh?: boolean, enableCompress?: boolean): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 如果提供了用户ID，检查用户是否是厨房成员
  // 如果没有提供用户ID（匿名访问），则允许获取二维码（分享厨房模式）
  if (userId) {
    const member = await KitchenMember.findOne({
      where: {
        user_id: userId,
        kitchen_id: kitchenId,
      },
    });

    if (!member) {
      // 用户不是厨房成员，但仍然允许获取二维码（分享厨房模式）
      console.log(`用户 ${userId} 不是厨房 ${kitchenId} 的成员，但允许获取二维码（分享厨房模式）`);
    }
  } else {
    console.log(`匿名用户访问厨房 ${kitchenId} 的二维码（分享厨房模式）`);
  }

  // 如果不是强制刷新且已有二维码，直接返回现有二维码
  if (!forceRefresh && kitchen.qr_code_url) {
    return {
      qrCodeUrl: convertToFullUrl(kitchen.qr_code_url), // 将数据库中的相对路径转换为完整URL
      kitchenId,
      isRefreshed: false,
    };
  }

  try {
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'uploads', 'qrcode');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 获取微信access_token
    const accessToken = await getWeixinAccessToken();
    if (!accessToken) {
      throw new Error('获取微信access_token失败');
    }

    // 生成小程序码，如果失败则生成普通二维码
    let qrCodeBuffer: Buffer;
    let qrCodeType = 'miniprogram';

    try {
      qrCodeBuffer = await generateMiniProgramCode(accessToken, kitchenId);
      logger.info('成功生成微信小程序码');
    } catch (error) {
      logger.warn('微信小程序码生成失败，使用普通二维码作为备选方案:', error);
      qrCodeBuffer = await generateFallbackQRCode(kitchenId);
      qrCodeType = 'fallback';
    }

    // 生成文件名
    const fileName = `kitchen_${kitchenId}_${Date.now()}_${qrCodeType}.png`;
    const filePath = path.join(uploadDir, fileName);

    // 保存二维码文件
    fs.writeFileSync(filePath, qrCodeBuffer);

    // 如果启用压缩，则压缩二维码
    logger.info(`二维码压缩参数: enableCompress=${enableCompress}`);
    if (enableCompress) {
      logger.info('开始压缩二维码...');
      try {
        const compressedFileName = `kitchen_${kitchenId}_${Date.now()}_compressed.png`;
        const compressedFilePath = path.join(uploadDir, compressedFileName);

        // 压缩二维码
        await compressImage(filePath, compressedFilePath, 'qrcode');

        // 删除原始文件
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }

        // 使用压缩后的文件
        const qrCodeRelativePath = `/uploads/qrcode/${compressedFileName}`;

        logger.info(`二维码压缩成功: ${fileName} -> ${compressedFileName}`);

        // 删除旧的二维码文件（如果存在）
        if (kitchen.qr_code_url) {
          try {
            const oldFileName = kitchen.qr_code_url.split('/').pop();
            if (oldFileName) {
              const oldFilePath = path.join(uploadDir, oldFileName);
              if (fs.existsSync(oldFilePath)) {
                fs.unlinkSync(oldFilePath);
                logger.info(`已删除旧二维码文件: ${oldFileName}`);
              }
            }
          } catch (error) {
            logger.error('删除旧二维码文件失败:', error);
            // 不抛出错误，继续执行
          }
        }

        // 更新数据库中的二维码信息（存储相对路径）
        await kitchen.update({
          qr_code_url: qrCodeRelativePath,
          qr_code_expire_time: undefined, // 二维码长期有效，不设置过期时间
        });

        logger.info(`为厨房 ${kitchenId} 生成新小程序码(已压缩): ${compressedFileName}`);

        return {
          qrCodeUrl: convertToFullUrl(qrCodeRelativePath), // 返回给前端时转换为完整URL
          kitchenId,
          isRefreshed: true,
          compressed: true,
        };
      } catch (compressError) {
        logger.error('二维码压缩失败，使用原始文件:', compressError);
        // 压缩失败时使用原始文件
      }
    }

    // 生成访问URL（未压缩或压缩失败时）
    const qrCodeRelativePath = `/uploads/qrcode/${fileName}`;

    // 删除旧的二维码文件（如果存在）
    if (kitchen.qr_code_url) {
      try {
        const oldFileName = kitchen.qr_code_url.split('/').pop();
        if (oldFileName) {
          const oldFilePath = path.join(uploadDir, oldFileName);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
            logger.info(`已删除旧二维码文件: ${oldFileName}`);
          }
        }
      } catch (error) {
        logger.error('删除旧二维码文件失败:', error);
        // 不抛出错误，继续执行
      }
    }

    // 更新数据库中的二维码信息（存储相对路径）
    await kitchen.update({
      qr_code_url: qrCodeRelativePath,
      qr_code_expire_time: undefined, // 二维码长期有效，不设置过期时间
    });

    logger.info(`为厨房 ${kitchenId} 生成新小程序码: ${fileName}`);

    return {
      qrCodeUrl: convertToFullUrl(qrCodeRelativePath), // 返回给前端时转换为完整URL
      kitchenId,
      isRefreshed: true,
      compressed: false,
    };
  } catch (error) {
    logger.error('生成厨房小程序码失败:', error);
    throw new BusinessError('生成小程序码失败，请稍后重试');
  }
};

/**
 * 获取微信access_token
 */
const getWeixinAccessToken = async (): Promise<string | null> => {
  const appId = config.wechat?.appId;
  const appSecret = config.wechat?.appSecret;
  
  if (!appId || !appSecret) {
    logger.error('微信配置信息缺失，请检查appId和appSecret');
    return null;
  }

  try {
    const response = await axios.get(`https://api.weixin.qq.com/cgi-bin/token`, {
      params: {
        grant_type: 'client_credential',
        appid: appId,
        secret: appSecret
      }
    });
    
    if (response.data?.access_token) {
      logger.info('获取微信access_token成功');
      return response.data.access_token;
    } else {
      logger.error('获取微信access_token失败:', response.data);
      return null;
    }
  } catch (error) {
    logger.error('获取微信access_token异常:', error);
    return null;
  }
};

/**
 * 生成小程序码
 */
const generateMiniProgramCode = async (accessToken: string, kitchenId: string): Promise<Buffer> => {
  try {
    logger.info(`开始生成小程序码，厨房ID: ${kitchenId}, access_token: ${accessToken.substring(0, 10)}...`);
    
    const response = await axios.post(`https://api.weixin.qq.com/wxa/getwxacodeunlimit`, {
      scene: `k=${kitchenId}`, // 简化参数，减少长度
      // page: 'pages/restaurant/restaurant', // 暂时注释掉页面路径，让其跳转到首页
      width: 430,
      auto_color: false,
      line_color: { r: 0, g: 0, b: 0 },
      is_hyaline: false,
      env_version: 'release' // 改为正式版，避免页面路径验证问题
    }, {
      params: {
        access_token: accessToken
      },
      responseType: 'arraybuffer'
    });

    logger.info(`微信API响应状态: ${response.status}, 内容类型: ${response.headers['content-type']}, 数据大小: ${response.data.byteLength} 字节`);

    // 检查响应是否是图片数据
    const contentType = response.headers['content-type'];
    if (contentType && contentType.includes('application/json')) {
      // 如果返回的是JSON，说明有错误
      const errorData = JSON.parse(Buffer.from(response.data).toString());
      logger.error('微信API返回JSON错误:', JSON.stringify(errorData, null, 2));
      
      // 如果是页面路径错误，抛出异常让上层处理
      if (errorData.errcode === 41030) {
        throw new Error(`小程序码生成失败(页面路径错误): ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
      }
      
      throw new Error(`生成小程序码失败: ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
    }

    // 检查返回的数据大小，如果太小可能是错误信息
    if (response.data.byteLength < 1000) {
      const errorText = Buffer.from(response.data).toString();
      logger.error('微信API返回异常数据:', errorText);
      
      // 尝试解析为JSON错误信息
      try {
        const errorData = JSON.parse(errorText);
        logger.error('解析出的错误信息:', JSON.stringify(errorData, null, 2));
        
        // 如果是页面路径错误，抛出异常让上层处理
        if (errorData.errcode === 41030) {
          throw new Error(`小程序码生成失败(页面路径错误): ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
        }
        
        throw new Error(`生成小程序码失败: ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
      } catch (parseError) {
        logger.error('无法解析错误信息:', parseError);
        // 如果解析失败，抛出异常让上层处理
        logger.error('解析错误信息失败，抛出异常让上层处理');
        throw new Error('小程序码生成失败，无法解析错误信息');
      }
    }

    logger.info(`成功生成小程序码，数据大小: ${response.data.byteLength} 字节`);
    return Buffer.from(response.data);
  } catch (error: any) {
    logger.error('生成小程序码异常详情:', {
      message: error.message,
      stack: error.stack,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data ? Buffer.from(error.response.data).toString().substring(0, 500) : null
      } : null
    });

    // 检查是否是axios错误且有响应数据
    if (error.response) {
      const contentType = error.response.headers['content-type'];
      if (contentType?.includes('application/json')) {
        try {
          const errorData = JSON.parse(error.response.data.toString());
          logger.error('生成小程序码失败，微信API错误:', JSON.stringify(errorData, null, 2));
          
          // 如果是页面路径错误，抛出异常让上层处理
          if (errorData.errcode === 41030) {
            throw new Error(`小程序码生成失败(页面路径错误): ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
          }
          
          throw new Error(`生成小程序码失败: ${errorData.errmsg || '未知错误'} (错误码: ${errorData.errcode})`);
        } catch (parseError) {
          logger.error('解析错误响应失败:', parseError);
        }
      }
    }
    
    // 如果所有小程序码生成方式都失败，抛出异常让上层处理
    logger.error('小程序码生成完全失败，抛出异常让上层处理');
    throw new Error('小程序码生成失败，请稍后重试');
  }
};

/**
 * 生成备选普通二维码
 */
const generateFallbackQRCode = async (kitchenId: string): Promise<Buffer> => {
  const QRCode = require('qrcode');
  
  // 生成包含厨房ID的URL，用户可以复制到浏览器或分享
  const qrData = `https://miniprogram.com/kitchen/${kitchenId}?from=qrcode`;
  
  try {
    const qrCodeBuffer = await QRCode.toBuffer(qrData, {
      type: 'png',
      width: 430,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    logger.info(`成功生成备选普通二维码: ${qrData}`);
    return qrCodeBuffer;
  } catch (error) {
    logger.error('生成备选普通二维码失败:', error);
    throw new Error('生成二维码失败');
  }
};

/**
 * 根据ID查找厨房
 * @param kitchenId 厨房ID
 * @param userId 用户ID（可选）
 * @returns 厨房信息
 */
const findKitchenById = async (kitchenId: string, userId?: number): Promise<any> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId, {
    include: [
      {
        model: User,
        as: 'owner',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
  });

  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查用户是否是厨房成员
  let userRole = null;
  let isOwner = false;
  let canEdit = false;

  if (userId) {
    const member = await KitchenMember.findOne({
      where: {
        user_id: userId,
        kitchen_id: kitchenId,
      },
    });

    if (member) {
      userRole = member.role;
      isOwner = member.isOwner();
      canEdit = member.canEdit();
    }
  }

  return {
    id: kitchen.id,
    name: kitchen.name,
    level: kitchen.level,
    avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
    notice: kitchen.notice,
    owner: {
      id: kitchen.owner?.id,
      nickName: kitchen.owner?.nick_name,
      avatarUrl: processImageUrl(kitchen.owner?.avatar_url, 'user-avatar'),
    },
    userRole,
    isOwner,
    canEdit,
    isMember: userRole !== null,
  };
};

/**
 * 搜索厨房信息
 * @param userId 用户ID
 * @param keyword 关键词
 * @returns 厨房列表
 */
const searchKitchen = async (userId: number, keyword: string): Promise<any[]> => {
  // 搜索厨房
  const kitchens = await Kitchen.findAll({
    where: {
      [Op.or]: [
        { id: keyword },
        { name: { [Op.like]: `%${keyword}%` } },
      ],
    },
    include: [
      {
        model: User,
        as: 'owner',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
    limit: 10,
  });

  // 检查用户是否是各厨房的成员
  const members = await KitchenMember.findAll({
    where: {
      user_id: userId,
      kitchen_id: {
        [Op.in]: kitchens.map(k => k.id),
      },
    },
  });

  return kitchens.map(kitchen => {
    const member = members.find(m => m.kitchen_id === kitchen.id);

    return {
      id: kitchen.id,
      name: kitchen.name,
      level: kitchen.level,
      avatarUrl: processImageUrl(kitchen.avatar_url, 'kitchen-avatar'),
      owner: {
        id: kitchen.owner?.id,
        nickName: kitchen.owner?.nick_name,
        avatarUrl: processImageUrl(kitchen.owner?.avatar_url, 'user-avatar'),
      },
      isMember: !!member,
      isOwner: member?.role === 'owner',
    };
  });
};

/**
 * 获取源厨房分类列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 分类列表
 */
const getKitchenCategories = async (userId: number, kitchenId: string): Promise<any[]> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 获取厨房分类列表
  const categories = await Category.findAll({
    where: { kitchen_id: kitchenId },
    order: [['sort', 'ASC']],
  });

  // 获取每个分类下的菜品数量
  const categoryDishCounts = await Promise.all(
    categories.map(async (category) => {
      const count = await Dish.count({
        where: {
          category_id: category.id,
          kitchen_id: kitchenId,
          status: 'on', // 只统计上架的菜品
        },
      });
      return { categoryId: category.id, count };
    })
  );

  return categories.map(category => {
    // 查找对应分类的菜品数量
    const dishCountObj = categoryDishCounts.find(item => item.categoryId === category.id);
    const dishCount = dishCountObj ? dishCountObj.count : 0;
    
    return {
    id: category.id,
    name: category.name,
    icon: category.icon,
    sort: category.sort,
      dishCount: dishCount, // 添加菜品数量字段
    };
  });
};

/**
 * 获取厨房分类下的菜品列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param categoryId 分类ID
 * @returns 分类下的菜品列表
 */
const getCategoryDishes = async (userId: number, kitchenId: string, categoryId: number): Promise<any[]> => {
  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查分类是否存在
  const category = await Category.findOne({
    where: {
      id: categoryId,
      kitchen_id: kitchenId,
    },
  });

  if (!category) {
    throw new BusinessError('分类不存在');
  }

  // 获取分类下的菜品
  const dishes = await Dish.findAll({
    where: {
      category_id: categoryId,
      kitchen_id: kitchenId,
      status: 'on', // 只获取上架的菜品
    },
    include: [
      {
        model: Ingredient,
        as: 'ingredients',
        attributes: ['id', 'name', 'amount'],
        order: [['sort', 'ASC']],
      },
    ],
    order: [['sort', 'ASC']],
  });

  // 返回格式化后的菜品列表
  return dishes.map(dish => {
    // 从配料表生成标签，取前5个配料的名称
    const tags = dish.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];

    return {
      id: dish.id,
      name: dish.name,
      image: dish.image,
      price: dish.price,
      originalPrice: dish.original_price,
      description: dish.description,
      tags: tags, // 使用配料表生成的标签而不是原有的tags字段
      sales: dish.sales,
      rating: dish.rating,
    };
  });
};

/**
 * 克隆菜品到目标厨房
 * @param userId 用户ID
 * @param sourceKitchenId 源厨房ID
 * @param targetKitchenId 目标厨房ID
 * @param targetCategoryId 目标分类ID
 * @param dishIds 菜品ID列表
 * @returns 克隆结果
 */
const cloneDishes = async (userId: number, sourceKitchenId: string, targetKitchenId: string, targetCategoryId: number, dishIds: number[]): Promise<any> => {
  // 检查源厨房是否存在
  const sourceKitchen = await Kitchen.findByPk(sourceKitchenId);
  if (!sourceKitchen) {
    throw new BusinessError('源厨房不存在');
  }

  // 检查目标厨房是否存在
  const targetKitchen = await Kitchen.findByPk(targetKitchenId);
  if (!targetKitchen) {
    throw new BusinessError('目标厨房不存在');
  }

  // 检查用户是否有权限添加菜品到目标厨房
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: targetKitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限添加菜品到目标厨房');
  }

  // 检查目标分类是否存在
  const targetCategory = await Category.findOne({
    where: {
      id: targetCategoryId,
      kitchen_id: targetKitchenId,
    },
  });

  if (!targetCategory) {
    throw new BusinessError('目标分类不存在');
  }

  // 获取源菜品
  const sourceDishes = await Dish.findAll({
    where: {
      id: { [Op.in]: dishIds },
      kitchen_id: sourceKitchenId,
    },
    include: [
      {
        model: DishImage,
        as: 'images',
      },
      {
        model: Ingredient,
        as: 'ingredients',
      },
      {
        model: Nutrition,
        as: 'nutrition',
      },
      {
        model: CookingStep,
        as: 'cookingSteps',
      },
    ],
  });

  if (sourceDishes.length === 0) {
    throw new BusinessError('未找到要克隆的菜品');
  }

  // 检查目标厨房菜品数量限制
  const willExceedLimit = targetKitchen.dish_count + sourceDishes.length > targetKitchen.dish_limit;
  if (willExceedLimit) {
    throw new BusinessError(`克隆后菜品数量将超过限制（${targetKitchen.dish_limit}个），请先升级厨房或减少克隆数量`);
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    const clonedDishes = [];

    // 克隆菜品
    for (const sourceDish of sourceDishes) {
      // 创建新菜品
      const newDish = await Dish.create({
        kitchen_id: targetKitchenId,
        category_id: targetCategoryId,
        name: sourceDish.name,
        image: sourceDish.image,
        price: sourceDish.price,
        original_price: sourceDish.original_price,
        description: sourceDish.description,
        tags: sourceDish.tags,
        status: 'on',
        created_by: userId,
      }, { transaction });

      // 复制其他相关数据
      if (sourceDish.images && sourceDish.images.length > 0) {
        for (const image of sourceDish.images) {
          await DishImage.create({
            dish_id: newDish.id,
            url: image.url,
            sort: image.sort,
          }, { transaction });
        }
      }

      if (sourceDish.ingredients && sourceDish.ingredients.length > 0) {
        for (const ingredient of sourceDish.ingredients) {
          await Ingredient.create({
            dish_id: newDish.id,
            name: ingredient.name,
            amount: ingredient.amount,
            sort: ingredient.sort,
          }, { transaction });
        }
      }

      if (sourceDish.nutrition) {
        await Nutrition.create({
          dish_id: newDish.id,
          calories: sourceDish.nutrition.calories,
          protein: sourceDish.nutrition.protein,
          fat: sourceDish.nutrition.fat,
          carbs: sourceDish.nutrition.carbs,
          fiber: sourceDish.nutrition.fiber,
          sugar: sourceDish.nutrition.sugar,
          sodium: sourceDish.nutrition.sodium,
        }, { transaction });
      }

      if (sourceDish.cookingSteps && sourceDish.cookingSteps.length > 0) {
        for (const step of sourceDish.cookingSteps) {
          await CookingStep.create({
            dish_id: newDish.id,
            step_number: step.step_number,
            title: step.title || '',
            description: step.description,
            image: step.image,
          }, { transaction });
        }
      }

      clonedDishes.push({
        id: newDish.id,
        name: newDish.name,
      });
    }

    // 更新目标厨房菜品数量
    await targetKitchen.update({
      dish_count: targetKitchen.dish_count + clonedDishes.length,
    }, { transaction });

    // 更新用户菜品数量
    const user = await User.findByPk(userId);
    if (user) {
      await user.update({
        dishes: user.dishes + clonedDishes.length,
      }, { transaction });
    }

    await transaction.commit();

    return {
      clonedCount: clonedDishes.length,
      dishes: clonedDishes,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

export default {
  getKitchenList,
  getOwnedKitchenList,
  getJoinedKitchenList,
  getKitchenInfo,
  getKitchenBaseInfo,
  createKitchen,
  updateKitchen,
  joinKitchen,
  leaveKitchen,
  dismissKitchen,
  upgradeKitchen,
  getKitchenMembers,
  updateMemberPermission,
  removeMember,
  getKitchenQrcode,
  findKitchenById,
  searchKitchen,
  getKitchenCategories,
  getCategoryDishes,
  cloneDishes,
};