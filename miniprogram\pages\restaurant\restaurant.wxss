/* 餐厅页面样式 */
.restaurant-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

/* 顶部背景区域 */
.top-bg-area {
  position: relative;
  height: 400rpx;
  width: 100%;
  background-color: transparent;
  color: var(--text-color, #FFFFFF);
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
  z-index: 0;
}

/* 顶部标题区域 - 与餐厅信息区域完全独立 */
.top-title {
  position: absolute;
  z-index: 1;
  left: 30rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 32px;
  box-sizing: border-box;
  color: inherit;
  /* 由内联样式控制top */
}

.page-title {
  font-size: 34rpx;
  font-weight: 600;
  line-height: 32px;
}

/* 餐厅信息区域 - 与顶部标题区域完全独立 */
.restaurant-info {
  position: absolute;
  z-index: 1;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  top: 240rpx;
  left: 0;
  right: 0;
}

.info-left {
  display: flex;
  align-items: center;
}

.restaurant-avatar-area {
  margin-right: 20rpx;
  flex-shrink: 0;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

.restaurant-avatar {
  width: 100%;
  height: 100%;
}

.restaurant-detail {
  display: flex;
  flex: 1;
}

/* 已登录状态 */
.restaurant-detail block:first-child {
  display: flex;
  flex-direction: column;
}

.restaurant-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: inherit;
}

.restaurant-notice {
  font-size: 24rpx;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  max-width: 400rpx;
  text-overflow: ellipsis;
  color: inherit;
}

.guest-info {
  display: flex;
  align-items: center;
}

.guest-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-right: 20rpx;
  color: inherit;
}

.login-btn {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  color: inherit;
  font-size: 28rpx;
  padding: 8rpx 0;
  border-radius: 40rpx;
  width: 160rpx !important;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  height: 50rpx;
  min-height: auto;
  transition: all 0.2s ease-out;
}

.login-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 功能按钮区域 */
.function-btns {
  position: absolute;
  bottom: 46rpx;
  right: 30rpx;
  z-index: 1;
  display: flex;
}

.function-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  margin-left: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-out;
}

.function-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.btn-icon {
  font-size: 70rpx;
  color: inherit;
  width: 70rpx;
  height: 70rpx;
  object-fit: contain;
}

.btn-text {
  font-size: 22rpx;
  color: inherit;
}

/* 内容区域 */
.content-area {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -30rpx;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部操作栏 */
.top-action-bar {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.tab-area {
  display: flex;
}

.tab-item {
  font-size: 32rpx;
  color: #333333;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  position: relative;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #FF6B35;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  width: 40%;
  height: 4rpx;
  background-color: #FF6B35;
  transform-origin: center;
  animation: tabLineIn 0.3s ease forwards;
  transform: translateX(-50%) scaleX(0);
}

@keyframes tabLineIn {
  from {
    transform: translateX(-50%) scaleX(0);
  }
  to {
    transform: translateX(-50%) scaleX(1);
  }
}

.action-btns {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  background-color: #F5F5F5;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  transition: all 0.2s ease-out;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.action-btn .btn-icon {
  font-size: 32rpx;
  margin-right: 6rpx;
  margin-bottom: 0;
  width: 32rpx;
  height: 32rpx;
  object-fit: contain;
}

.action-btn .btn-text {
  font-size: 24rpx;
}

/* 全局滚动条隐藏样式 - 更强的选择器 */
::-webkit-scrollbar,
::scrollbar,
::-webkit-scrollbar-button,
::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece,
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-corner,
::-webkit-resizer {
  width: 0 !important;
  height: 0 !important;
  background-color: transparent !important;
  display: none !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 菜单区域 */
.menu-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧分类列表 */
.category-list {
  width: 160rpx;
  background-color: #F5F5F5;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none !important; /* 隐藏Firefox的滚动条 */
  -ms-overflow-style: none !important; /* 隐藏IE的滚动条 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  hide-scrollbar: true;
  pointer-events: auto !important;
}

.category-list::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  appearance: none !important;
  -webkit-appearance: none !important;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #666666;
  border-left: 6rpx solid transparent;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #FFFFFF;
  color: #FF6B35;
  border-left: 6rpx solid #FF6B35;
}

.category-item:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.category-icon {
  font-size: 50rpx;
  margin-bottom: 6rpx;
  width: 50rpx;
  height: 50rpx;
  object-fit: contain;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
}

/* 右侧菜品列表 */
.dish-list {
  flex: 1;
  height: 100%;
  padding: 20rpx;
  padding-bottom: 0; /* 移除底部内边距 */
  box-sizing: border-box;
  overflow-y: auto;
  scrollbar-width: none !important; /* 隐藏Firefox的滚动条 */
  -ms-overflow-style: none !important; /* 隐藏IE的滚动条 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  hide-scrollbar: true;
  pointer-events: auto !important;
}

.dish-list::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  appearance: none !important;
  -webkit-appearance: none !important;
}

/* 分类标题 */
.category-title {
  font-size: 28rpx;
  font-weight: 600;
  padding: 20rpx 0;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx; /* 固定高度 */
  box-sizing: border-box;
  position: relative; /* 确保相对定位 */
  overflow: visible; /* 允许内容溢出 */
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none; /* 不阻挡交互 */
}

/* 排序按钮 */
.sort-btn {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  padding: 6rpx 12rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
  font-weight: normal;
  transition: all 0.2s ease;
  position: absolute; /* 绝对定位确保不会推动标题 */
  right: 0;
  top: 50%;
  transform: translateY(-50%); /* 垂直居中 */
}

.sort-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.sort-icon {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.sort-text {
  font-size: 24rpx;
}

/* 排序模式下菜品卡片样式 */
.dish-card.sort-mode {
  position: relative;
}

/* 拖动手柄 */
.dish-card .drag-handle {
  position: absolute;
  right: 10rpx; /* 放到右侧 */
  top: 10rpx; /* 放到顶部 */
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: none;
  z-index: 3; /* 确保在最上层 */
  background-color: rgba(255, 255, 255, 0.8); /* 半透明背景 */
  border-radius: 50%; /* 圆形 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1); /* 轻微阴影 */
}

.dish-card .drag-icon {
  font-size: 36rpx;
  color: #999999;
}

/* 拖动时的样式 */
.dish-card.dragging {
  background-color: #F9F9F9;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
  z-index: 2;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dish-card.move-to {
  border-top: 3rpx dashed #FF6B35;
}

.dish-card {
  display: flex;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: auto; /* 保持自动高度 */
  box-sizing: border-box;
}

.dish-card:active {
  transform: scale(0.98);
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);
}

.dish-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  overflow: hidden;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: auto; /* 保持自动高度 */
  min-height: 160rpx; /* 与图片高度一致，保持布局 */
  overflow: hidden; /* 防止内容溢出影响布局 */
}

.dish-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-name {
  font-size: 30rpx;
  font-weight: 500;
  max-width: 280rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dish-tags {
  display: flex;
  flex-wrap: nowrap;
  margin: 10rpx 0;
  overflow-x: auto;
  scrollbar-width: none; /* 隐藏Firefox的滚动条 */
  -ms-overflow-style: none; /* 隐藏IE的滚动条 */
  white-space: nowrap;
  max-width: calc(100% - 20rpx); /* 限制最大宽度，避免影响其他元素 */
  flex-shrink: 0; /* 防止被其他元素压缩 */
}

.dish-tags::-webkit-scrollbar {
  display: none; /* 隐藏WebKit的滚动条 */
}

.dish-tag {
  font-size: 20rpx;
  color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  flex-shrink: 0; /* 防止标签被压缩 */
  white-space: nowrap; /* 保持标签文字在一行 */
}

.dish-rating-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  justify-content: space-between;
  height: 30rpx; /* 固定高度，避免编辑模式和点餐模式的差异 */
  width: 100%; /* 确保占满宽度 */
}

.rating-stars {
  display: flex;
}

.star {
  font-size: 30rpx;
  color: #DDDDDD;
  margin-right: 2rpx;
  width: 30rpx;
  height: 30rpx;
  object-fit: contain;
}

.star.filled {
  color: #FFD700;
}

.dish-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%; /* 确保占满宽度 */
  margin-top: auto; /* 推到底部 */
}

.dish-price {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 32rpx;
  color: #E57373;
  font-weight: 600;
}

.price-unit {
  font-size: 22rpx;
  color: #E57373;
  margin-left: 4rpx;
}

.dish-sales {
  font-size: 22rpx;
  color: #999999;
  flex-shrink: 0; /* 防止被其他元素压缩 */
  margin-left: auto; /* 推到右边 */
}

.dish-action {
  display: flex;
  justify-content: flex-end;
  margin-top: 0;
  flex-shrink: 0; /* 防止被其他元素压缩 */
  align-items: center;
}

.add-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-out;
  transform: scale(1);
}

.add-btn:active {
  transform: scale(0.9);
  opacity: 0.8;
}

.add-btn image {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
}

.quantity-control {
  display: flex;
  align-items: center;
  height: 50rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: transparent;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.2s ease-out;
  transform: scale(1);
}

.quantity-btn image {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
}

.quantity-btn:active {
  transform: scale(0.9);
  opacity: 0.8;
}

.quantity-number {
  width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
}

/* 购物车栏 */
.cart-bar {
  position: fixed;
  bottom: 10rpx;
  left: 30rpx;
  right: 30rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  transform: translateY(100%);
  opacity: 0;
  animation: slideUp 0.4s ease-out forwards;
}

/* 退出购物栏时的动画 */
.cart-bar.exit {
  animation: slideDown 0.3s ease-out forwards;
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  60% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
  80% {
    transform: translateY(5rpx);
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

.cart-bar.cart-expanded {
  /* 移除边框变化样式 */
}

.cart-icon-container {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-out;
  overflow: visible;
  background: none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0;
}

.cart-icon-container::after {
  border: none !important;
  content: none !important;
  outline: none !important;
  background: none !important;
}

.cart-icon {
  font-size: 60rpx;
  color: #FF6B35;
  line-height: 1;
  outline: none;
  border: none;
  -webkit-tap-highlight-color: transparent;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-icon image {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #E53935;
  color: white;
  font-size: 20rpx;
  width: 34rpx;
  height: 34rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-out;
}

.badge-animated {
  animation: badgePulse 0.4s ease-out;
}

@keyframes badgePulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.cart-expanded .cart-icon {
  transform: rotate(180deg);
}

.cart-icon:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.cart-info {
  flex: 1;
  padding: 0 20rpx;
}

.cart-total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B35;
}

.cart-total-price .price-unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
  color: #999;
}

.cart-empty-tip {
  font-size: 28rpx;
  color: #999;
  font-style: italic;
}

.cart-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.invite-btn {
  background-color: #FFFFFF;
  color: #FF6B35;
  border: 1rpx solid #FF6B35;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 0;
  line-height: 1.5;
  min-height: 0;
  width: auto !important;
  transition: all 0.2s ease-out;
}

.invite-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.submit-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 0;
  line-height: 1.5;
  min-height: 0;
  width: auto !important;
  transition: all 0.2s ease-out;
}

.submit-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #999;
  opacity: 0.6;
}

.submit-btn.disabled:active {
  opacity: 0.6;
  transform: none;
}

/* 购物车清单 */
.cart-list-container {
  position: fixed;
  bottom: 125rpx;
  left: 30rpx;
  right: 30rpx;
  background-color: #FFFFFF;
  border-radius: 30rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s ease-out;
  opacity: 0;
  transform: translateY(30rpx);
}

.cart-list-container.show {
  max-height: 600rpx;
  opacity: 1;
  transform: translateY(0);
  animation: bounceUp 0.4s ease-out;
}

@keyframes bounceUp {
  0% {
    transform: translateY(30rpx);
    opacity: 0;
  }
  60% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.cart-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.cart-list-title {
  font-size: 30rpx;
  font-weight: 500;
}

.cart-list-clear {
  font-size: 26rpx;
  color: #999999;
  padding: 10rpx;
  transition: color 0.3s ease;
}

.cart-list-clear:active {
  color: #FF6B35;
}

.cart-list {
  max-height: 500rpx;
}

.cart-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.cart-item-name {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300rpx;
}

.cart-item-right {
  display: flex;
  align-items: center;
}

.cart-item-price {
  font-size: 28rpx;
  color: #E57373;
  margin-right: 20rpx;
  font-weight: 500;
}

/* 顶部搜索蒙层 - 仅覆盖顶部背景区域 */
.top-search-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  z-index: 9;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-out;
}

.top-search-mask.active {
  visibility: visible;
  opacity: 0.5;
}

/* 搜索区域 */
.search-container {
  position: absolute;
  right: 30rpx;
  top: 15rpx;
  display: flex;
  align-items: center;
  width: 70rpx;
  height: 60rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0;
  visibility: hidden;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transform: translateX(0) scale(0.8);
  transform-origin: right center;
}

.search-container.active {
  width: 400rpx;
  opacity: 1;
  visibility: visible;
  transform: translateX(0) scale(1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-container.exit {
  width: 70rpx;
  opacity: 0;
  visibility: hidden;
  transform: translateX(0) scale(0.8);
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.search-input {
  flex: 1;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: transparent;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
  margin-right: 10rpx;
  opacity: 0;
  transition: opacity 0.3s ease-out 0.2s;
  outline: none;
  border: none;
}

.search-container.active .search-input {
  opacity: 1;
  transition: opacity 0.3s ease-out 0.2s;
}

.search-cancel {
  font-size: 24rpx;
  color: #999999;
  transition: all 0.3s ease-out 0.2s;
  padding: 0 15rpx;
  height: 60rpx;
  line-height: 60rpx;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(10rpx);
  flex-shrink: 0;
}

.search-container.active .search-cancel {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-out 0.2s;
}

.search-cancel:active {
  opacity: 0.8;
  color: #FF6B35;
}

/* 搜索结果样式 */
.search-results {
  padding: 10rpx 0;
}

.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #EEEEEE;
  margin-bottom: 20rpx;
}

.search-result-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.search-result-keyword {
  font-size: 24rpx;
  color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.search-result-item {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20rpx);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 设置每个搜索结果项的延迟出现时间 */
.search-result-item:nth-child(1) { animation-delay: 0.05s; }
.search-result-item:nth-child(2) { animation-delay: 0.1s; }
.search-result-item:nth-child(3) { animation-delay: 0.15s; }
.search-result-item:nth-child(4) { animation-delay: 0.2s; }
.search-result-item:nth-child(5) { animation-delay: 0.25s; }
.search-result-item:nth-child(6) { animation-delay: 0.3s; }
.search-result-item:nth-child(7) { animation-delay: 0.35s; }
.search-result-item:nth-child(8) { animation-delay: 0.4s; }
.search-result-item:nth-child(9) { animation-delay: 0.45s; }
.search-result-item:nth-child(10) { animation-delay: 0.5s; }

/* 空搜索结果样式 */
.empty-search-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #CCCCCC;
  width: 80rpx;
  height: 80rpx;
  object-fit: contain;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999999;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  flex-direction: column;
}

/* 分享厨房提示样式 */
.shared-kitchen-tip {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.shared-kitchen-tip .tip-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  margin-bottom: 4rpx;
}

.shared-kitchen-tip .login-tip {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

/* 底部空白区域 */
.dish-list::after {
  content: '';
  display: block;
  height: 0;
  width: 100%;
}

.category-manage {
  margin-top: 20rpx;
  border-top: 1rpx solid #EEEEEE;
  padding-top: 20rpx;
  padding-bottom: 10rpx;
  position: sticky;
  bottom: 0;
  background-color: #F5F5F5;
  z-index: 2;
}

/* 编辑模式样式 */
.dish-edit-row {
  display: flex;
  flex-direction: column;
  margin-top: 0;
  height: auto; /* 避免固定高度导致的拉伸问题 */
  min-height: 50rpx; /* 设置最小高度而不是固定高度 */
  box-sizing: border-box;
}

.dish-action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.dish-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.dish-status:not(.dish-disabled) {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.dish-disabled {
  color: #999999;
  background-color: rgba(153, 153, 153, 0.1);
}

/* 下架菜品的整体显示样式 */
.dish-card.disabled-dish {
  opacity: 0.7;
  background-color: rgba(245, 245, 245, 0.5);
}

.edit-actions {
  display: flex;
  gap: 10rpx;
  justify-content: flex-end;
  margin-top: 5rpx;
}

.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  transition: all 0.2s ease-out;
  background-color: #F5F5F5;
}

.edit-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.edit-icon {
  font-size: 22rpx;
  margin-right: 4rpx;
}

.edit-text {
  font-size: 22rpx;
  color: #666666;
}

.edit-modify {
  background-color: #E3F2FD;
}

.edit-modify .edit-text {
  color: #2196F3;
}

.edit-toggle {
  background-color: #FFF3E0;
}

.edit-toggle .edit-text {
  color: #FF9800;
}

.edit-delete {
  background-color: #FFEBEE;
}

.edit-delete .edit-text {
  color: #F44336;
}

/* 分类底部信息 */
.category-bottom {
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  padding: 15rpx 0;
  margin-bottom: 0; /* 移除底部外边距 */
}

/* 空分类提示 */
.empty-category-tip {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 26rpx;
}

/* 分类底部边距 */
.category-bottom-space {
  height: 40rpx;
  width: 100%;
}

/* 菜品底部边距 */
.dish-bottom-space {
  height: 40rpx;
  width: 100%;
}

/* 菜品列表加载动画（参考发现页面和订单页面） */
.slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}