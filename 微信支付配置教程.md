# 微信支付APIv3配置教程

## 📋 功能说明

本项目已集成微信支付APIv3，支持小程序内大米充值的真实支付功能。用户可以通过微信支付购买不同数量的大米（积分）。

### 充值套餐
- 1000大米 = ¥10
- 3000大米 = ¥28  
- 5000大米 = ¥45
- 10000大米 = ¥88

## 🚀 快速开始

### 第一步：微信商户平台配置

1. **申请微信支付商户号**
   - 访问 [微信支付商户平台](https://pay.weixin.qq.com/)
   - 按照要求提交资料申请商户号
   - 等待审核通过（通常1-7个工作日）

2. **获取商户信息**
   - 登录微信支付商户平台
   - 在「商户信息」中获取：
     - 商户号（mchid）
     - 商户APIv3密钥（可自定义设置）

3. **下载API证书**
   - 在「API安全」→「API证书」中下载证书
   - 下载得到：
     - `apiclient_cert.pem`（商户证书）
     - `apiclient_key.pem`（商户私钥）
   - 记录证书序列号

### 第二步：微信小程序配置

1. **开通微信支付**
   - 登录 [微信公众平台](https://mp.weixin.qq.com/)
   - 在「微信支付」中关联商户号
   - 确保小程序已发布并通过审核

2. **获取小程序信息**
   - AppID：在「设置」→「基本设置」中查看
   - AppSecret：在「开发」→「开发设置」中查看

### 第三步：服务器证书配置

1. **创建证书目录**
   ```bash
   mkdir -p server/certs/wechatpay
   ```

2. **上传证书文件**
   - 将下载的 `apiclient_cert.pem` 上传到 `server/certs/wechatpay/`
   - 将下载的 `apiclient_key.pem` 上传到 `server/certs/wechatpay/`

3. **设置文件权限**（Linux/Mac）
   ```bash
   chmod 600 server/certs/wechatpay/*.pem
   ```

### 第四步：环境变量配置

在服务器根目录创建 `.env` 文件（如果没有），添加以下配置：

```env
# 原有配置...

# 微信小程序配置
WECHAT_APPID=wx41b8de9ea1e51474
WECHAT_APP_SECRET=your_app_secret_here

# 微信支付配置
WECHAT_PAY_APPID=wx41b8de9ea1e51474
WECHAT_PAY_MCHID=your_merchant_id_here
WECHAT_PAY_MCHID_V3=your_merchant_id_here
WECHAT_PAY_API_KEY=your_api_key_here
WECHAT_PAY_API_V3_KEY=your_api_v3_key_here
WECHAT_PAY_SERIAL_NO=your_certificate_serial_number_here
WECHAT_PAY_NOTIFY_URL=https://your-domain.com/api/payment/notify

# 服务器配置
BASE_URL=https://your-domain.com
CORS_ORIGIN=https://your-domain.com
```

### 第五步：配置说明

#### 必填参数说明

| 参数名 | 说明 | 获取方式 |
|--------|------|----------|
| `WECHAT_APPID` | 小程序AppID | 微信公众平台 → 设置 → 基本设置 |
| `WECHAT_APP_SECRET` | 小程序AppSecret | 微信公众平台 → 开发 → 开发设置 |
| `WECHAT_PAY_MCHID` | 商户号 | 微信支付商户平台 → 商户信息 |
| `WECHAT_PAY_API_V3_KEY` | APIv3密钥 | 微信支付商户平台 → API安全 → APIv3密钥 |
| `WECHAT_PAY_SERIAL_NO` | 证书序列号 | 下载证书时显示的序列号 |
| `WECHAT_PAY_NOTIFY_URL` | 支付回调地址 | 你的服务器域名+/api/payment/notify |

#### 证书文件路径

系统会自动在以下路径查找证书：
- 商户证书：`server/certs/wechatpay/apiclient_cert.pem`
- 商户私钥：`server/certs/wechatpay/apiclient_key.pem`

### 第六步：服务器部署

1. **安装依赖**
   ```bash
   cd server
   npm install
   ```

2. **启动服务**
   ```bash
   npm start
   ```

3. **验证配置**
   - 检查服务器日志，确认"微信支付服务初始化成功"
   - 确认证书文件读取无误

### 第七步：小程序配置

1. **配置服务器域名**
   - 在微信公众平台「开发」→「开发设置」中
   - 将你的服务器域名添加到「request合法域名」

2. **测试支付功能**
   - 在小程序中进入"我的大米"页面
   - 选择充值金额
   - 点击"立即充值"测试支付流程

## 🔧 常见问题

### Q1: 证书文件读取失败
**现象**: 服务器启动时提示"读取微信支付私钥失败"

**解决方案**:
1. 检查证书文件路径是否正确
2. 确认文件权限设置（建议600）
3. 验证证书文件内容完整性

### Q2: 签名验证失败
**现象**: 支付时提示"签名验证失败"

**解决方案**:
1. 检查APIv3密钥配置是否正确
2. 确认证书序列号是否匹配
3. 验证商户号配置

### Q3: 支付回调不生效
**现象**: 支付成功但用户大米未增加

**解决方案**:
1. 检查回调URL是否可外网访问
2. 确认服务器防火墙设置
3. 查看服务器日志中的回调信息

### Q4: 开发环境测试
**现象**: 本地开发时无法接收支付回调

**解决方案**:
1. 使用内网穿透工具（如ngrok）
2. 临时将回调URL指向可访问的测试服务器
3. 使用微信支付沙箱环境

## 📝 测试流程

### 开发环境测试

1. **准备工作**
   - 确保所有配置正确
   - 启动服务器并确认无错误

2. **功能测试**
   - 测试创建订单接口
   - 测试支付回调接口
   - 验证数据库记录

3. **集成测试**
   - 在小程序中完整测试充值流程
   - 验证用户大米余额变化
   - 检查交易记录

### 生产环境部署

1. **域名和SSL证书**
   - 确保使用HTTPS域名
   - SSL证书有效

2. **服务器配置**
   - 生产环境配置
   - 数据库连接
   - 日志记录

3. **监控和维护**
   - 设置支付异常监控
   - 定期检查证书过期时间
   - 备份重要配置

## 🚀 上线检查清单

- [ ] 微信商户号已申请并通过审核
- [ ] 小程序已发布并开通微信支付
- [ ] 证书文件已正确上传并设置权限
- [ ] 环境变量配置完整且正确
- [ ] 服务器域名已添加到小程序合法域名
- [ ] 支付回调URL可正常访问
- [ ] 开发环境测试通过
- [ ] 生产环境部署完成
- [ ] 支付流程端到端测试通过

## 📞 技术支持

如遇到问题，请按以下顺序排查：

1. **检查服务器日志** - 查看详细错误信息
2. **验证配置参数** - 确认所有配置项正确
3. **测试网络连接** - 确保服务器可访问微信API
4. **查阅官方文档** - [微信支付APIv3文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)

---

## 📚 相关文档

- [微信支付APIv3官方文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [微信小程序支付文档](https://developers.weixin.qq.com/miniprogram/dev/api/payment/wx.requestPayment.html)
- [微信商户平台](https://pay.weixin.qq.com/)
- [微信公众平台](https://mp.weixin.qq.com/)

配置完成后，用户即可在小程序中使用真实的微信支付进行大米充值！ 🎉 