"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 日志处理工具
 * 使用winston实现日志记录
 */
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = __importDefault(require("../config/config"));
// 确保日志目录存在
const logDir = path_1.default.resolve(process.cwd(), config_1.default.log.dir);
if (!fs_1.default.existsSync(logDir)) {
    fs_1.default.mkdirSync(logDir, { recursive: true });
}
// 日志级别
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
};
// 日志级别颜色
const colors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue',
};
// 添加颜色支持
winston_1.default.addColors(colors);
// 中文日志级别映射
const levelMapping = {
    error: '错误',
    warn: '警告',
    info: '信息',
    debug: '调试',
};
// 创建自定义日志格式
const customFormat = winston_1.default.format.printf(({ level, message, timestamp }) => {
    const chineseLevel = levelMapping[level] || level;
    return `[${timestamp}] [${chineseLevel}] ${message}`;
});
// 创建日志记录器
const logger = winston_1.default.createLogger({
    levels,
    level: config_1.default.log.level,
    format: winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.splat(), customFormat),
    transports: [
        // 控制台输出
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize({ all: true }), winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.splat(), customFormat),
        }),
        // 错误日志文件
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'error.log'),
            level: 'error',
        }),
        // 所有日志文件
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'combined.log'),
        }),
    ],
});
// 过滤敏感信息的函数
const filterSensitiveInfo = (data) => {
    if (!data)
        return data;
    // 如果是字符串，检查是否包含敏感信息
    if (typeof data === 'string') {
        // 替换密码
        return data.replace(/("password"\s*:\s*")([^"]+)(")/g, '$1******$3')
            .replace(/("token"\s*:\s*")([^"]+)(")/g, '$1******$3');
    }
    // 如果是对象，递归处理
    if (typeof data === 'object') {
        const result = Array.isArray(data) ? [] : {};
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                // 敏感字段直接替换为******
                if (['password', 'token', 'secret'].includes(key)) {
                    result[key] = '******';
                }
                else {
                    result[key] = filterSensitiveInfo(data[key]);
                }
            }
        }
        return result;
    }
    return data;
};
// 扩展logger，添加过滤敏感信息的功能
const safeLogger = {
    error: (message, ...args) => {
        logger.error(message, ...args.map(filterSensitiveInfo));
    },
    warn: (message, ...args) => {
        logger.warn(message, ...args.map(filterSensitiveInfo));
    },
    info: (message, ...args) => {
        logger.info(message, ...args.map(filterSensitiveInfo));
    },
    debug: (message, ...args) => {
        logger.debug(message, ...args.map(filterSensitiveInfo));
    },
};
exports.default = safeLogger;
//# sourceMappingURL=logger.js.map