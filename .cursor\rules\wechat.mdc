---
description: 
globs: 
alwaysApply: true
---

你是一个经验丰富的小程序开发人员，擅长解决各种小程序开发问题，严格遵守小程序开发规范
##项目结构
- components
  /**这里存放小程序的自定义组件，每个组件单独一个文件夹 */
- pages
 /**这里存放小程序的页面，每个页面单独一个文件夹 */
- api
/** 这里存放调用后端服务的文件，按模块划分，每个模块一个文件,文件命名：**Api.ts */
- static
/**存放其他常用js或者静态资源图片 */

##命名规范
### 组件命名规范
- 组件文件夹名称使用小驼峰命名法，如：userCard, imageViewer
- 组件内部文件与文件夹同名，如：userCard/userCard.ts
- 通用组件使用前缀"common-"，如：common-button
- 业务组件使用相关业务模块名作为前缀，如：user-profile, order-list
- 组件属性使用小驼峰命名法，如：onChange, itemList, maxLength

### 页面命名规范
- 页面文件夹使用小写字母加连字符，如：user-center, order-detail
- 页面路径与文件夹名称保持一致，在app.json中注册
- 主要页面放在主包中，次要页面使用分包加载
- 特殊功能页面使用功能前缀，如：form-feedback, detail-product
- 路由参数命名简洁明了，如：id, type, mode

##ui及页面规范
- 所有页面必须包含 ts、json、wxml、wxss文件，json文件必须包含以下基本内容
{
  "navigationBarTitleText": "",
  "usingComponents": {
  }
}

##样式规范
- 使用rpx作为主要尺寸单位，确保适配不同屏幕
- 公共样式放在app.wxss中
- 组件样式使用组件作用域，避免污染全局
- 颜色使用变量统一管理，保持一致性
- 布局优先使用flex布局，提高跨设备兼容性



##点菜小程序样式规范
- 使用rpx作为主要尺寸单位，确保适配不同屏幕尺寸
- 公共样式放在app.wxss中，按功能分类并添加注释
- 组件样式使用组件作用域，避免污染全局
- 配色方案：
  • 主题色: #FF6B35（食欲橙）
  • 辅助色: #4CAF50（清新绿）
  • 强调色: #E53935（柔和红，降低饱和度）
  • 背景色: #F9F9F9（浅灰白）
  • 卡片背景: #FFFFFF（纯白）
  • 文字主色: #333333（深灰）
  • 文字次色: #666666（中灰）
  • 文字提示色: #999999（浅灰）
  • 价格颜色: #E57373（柔和价格色，更温和的红色）
  • 分割线: #EEEEEE（淡灰）
  • 菜品分类背景: #F5F5F5（特淡灰）
- 字体大小规范:
  •更具页面实际需求制定大小。

- 间距更具实际页面需求制定大小。
- 布局优先使用flex布局，提高跨设备兼容性
- 避免使用!important，合理使用CSS选择器优先级
- 组件和页面都应有默认的加载状态和空状态样式
- 交互元素(如按钮)提供触摸反馈样式(:active)
- 长文本使用text-overflow: ellipsis处理溢出
- 图片使用mode="aspectFill"或"widthFix"确保比例正确
- 阴影效果统一使用: box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1)





