"use strict";

/**
 * Module dependencies.
 */

const _require = require('string_decoder'),
  StringDecoder = _require.StringDecoder;
const Stream = require('stream');
const zlib = require('zlib');

/**
 * Buffers response data events and re-emits when they're unzipped.
 *
 * @param {Request} req
 * @param {Response} res
 * @api private
 */

exports.unzip = (request, res) => {
  const unzip = zlib.createUnzip();
  const stream = new Stream();
  let decoder;

  // make node responseOnEnd() happy
  stream.req = request;
  unzip.on('error', error => {
    if (error && error.code === 'Z_BUF_ERROR') {
      // unexpected end of file is ignored by browsers and curl
      stream.emit('end');
      return;
    }
    stream.emit('error', error);
  });

  // pipe to unzip
  res.pipe(unzip);

  // override `setEncoding` to capture encoding
  res.setEncoding = type => {
    decoder = new StringDecoder(type);
  };

  // decode upon decompressing with captured encoding
  unzip.on('data', buf => {
    if (decoder) {
      const string_ = decoder.write(buf);
      if (string_.length > 0) stream.emit('data', string_);
    } else {
      stream.emit('data', buf);
    }
  });
  unzip.on('end', () => {
    stream.emit('end');
  });

  // override `on` to capture data listeners
  const _on = res.on;
  res.on = function (type, fn) {
    if (type === 'data' || type === 'end') {
      stream.on(type, fn.bind(res));
    } else if (type === 'error') {
      stream.on(type, fn.bind(res));
      _on.call(res, type, fn);
    } else {
      _on.call(res, type, fn);
    }
    return this;
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJyZXF1aXJlIiwiU3RyaW5nRGVjb2RlciIsIlN0cmVhbSIsInpsaWIiLCJleHBvcnRzIiwidW56aXAiLCJyZXF1ZXN0IiwicmVzIiwiY3JlYXRlVW56aXAiLCJzdHJlYW0iLCJkZWNvZGVyIiwicmVxIiwib24iLCJlcnJvciIsImNvZGUiLCJlbWl0IiwicGlwZSIsInNldEVuY29kaW5nIiwidHlwZSIsImJ1ZiIsInN0cmluZ18iLCJ3cml0ZSIsImxlbmd0aCIsIl9vbiIsImZuIiwiYmluZCIsImNhbGwiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvbm9kZS91bnppcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE1vZHVsZSBkZXBlbmRlbmNpZXMuXG4gKi9cblxuY29uc3QgeyBTdHJpbmdEZWNvZGVyIH0gPSByZXF1aXJlKCdzdHJpbmdfZGVjb2RlcicpO1xuY29uc3QgU3RyZWFtID0gcmVxdWlyZSgnc3RyZWFtJyk7XG5jb25zdCB6bGliID0gcmVxdWlyZSgnemxpYicpO1xuXG4vKipcbiAqIEJ1ZmZlcnMgcmVzcG9uc2UgZGF0YSBldmVudHMgYW5kIHJlLWVtaXRzIHdoZW4gdGhleSdyZSB1bnppcHBlZC5cbiAqXG4gKiBAcGFyYW0ge1JlcXVlc3R9IHJlcVxuICogQHBhcmFtIHtSZXNwb25zZX0gcmVzXG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLnVuemlwID0gKHJlcXVlc3QsIHJlcykgPT4ge1xuICBjb25zdCB1bnppcCA9IHpsaWIuY3JlYXRlVW56aXAoKTtcbiAgY29uc3Qgc3RyZWFtID0gbmV3IFN0cmVhbSgpO1xuICBsZXQgZGVjb2RlcjtcblxuICAvLyBtYWtlIG5vZGUgcmVzcG9uc2VPbkVuZCgpIGhhcHB5XG4gIHN0cmVhbS5yZXEgPSByZXF1ZXN0O1xuXG4gIHVuemlwLm9uKCdlcnJvcicsIChlcnJvcikgPT4ge1xuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlID09PSAnWl9CVUZfRVJST1InKSB7XG4gICAgICAvLyB1bmV4cGVjdGVkIGVuZCBvZiBmaWxlIGlzIGlnbm9yZWQgYnkgYnJvd3NlcnMgYW5kIGN1cmxcbiAgICAgIHN0cmVhbS5lbWl0KCdlbmQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzdHJlYW0uZW1pdCgnZXJyb3InLCBlcnJvcik7XG4gIH0pO1xuXG4gIC8vIHBpcGUgdG8gdW56aXBcbiAgcmVzLnBpcGUodW56aXApO1xuXG4gIC8vIG92ZXJyaWRlIGBzZXRFbmNvZGluZ2AgdG8gY2FwdHVyZSBlbmNvZGluZ1xuICByZXMuc2V0RW5jb2RpbmcgPSAodHlwZSkgPT4ge1xuICAgIGRlY29kZXIgPSBuZXcgU3RyaW5nRGVjb2Rlcih0eXBlKTtcbiAgfTtcblxuICAvLyBkZWNvZGUgdXBvbiBkZWNvbXByZXNzaW5nIHdpdGggY2FwdHVyZWQgZW5jb2RpbmdcbiAgdW56aXAub24oJ2RhdGEnLCAoYnVmKSA9PiB7XG4gICAgaWYgKGRlY29kZXIpIHtcbiAgICAgIGNvbnN0IHN0cmluZ18gPSBkZWNvZGVyLndyaXRlKGJ1Zik7XG4gICAgICBpZiAoc3RyaW5nXy5sZW5ndGggPiAwKSBzdHJlYW0uZW1pdCgnZGF0YScsIHN0cmluZ18pO1xuICAgIH0gZWxzZSB7XG4gICAgICBzdHJlYW0uZW1pdCgnZGF0YScsIGJ1Zik7XG4gICAgfVxuICB9KTtcblxuICB1bnppcC5vbignZW5kJywgKCkgPT4ge1xuICAgIHN0cmVhbS5lbWl0KCdlbmQnKTtcbiAgfSk7XG5cbiAgLy8gb3ZlcnJpZGUgYG9uYCB0byBjYXB0dXJlIGRhdGEgbGlzdGVuZXJzXG4gIGNvbnN0IF9vbiA9IHJlcy5vbjtcbiAgcmVzLm9uID0gZnVuY3Rpb24gKHR5cGUsIGZuKSB7XG4gICAgaWYgKHR5cGUgPT09ICdkYXRhJyB8fCB0eXBlID09PSAnZW5kJykge1xuICAgICAgc3RyZWFtLm9uKHR5cGUsIGZuLmJpbmQocmVzKSk7XG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAnZXJyb3InKSB7XG4gICAgICBzdHJlYW0ub24odHlwZSwgZm4uYmluZChyZXMpKTtcbiAgICAgIF9vbi5jYWxsKHJlcywgdHlwZSwgZm4pO1xuICAgIH0gZWxzZSB7XG4gICAgICBfb24uY2FsbChyZXMsIHR5cGUsIGZuKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpcztcbiAgfTtcbn07XG4iXSwibWFwcGluZ3MiOiI7O0FBQUE7QUFDQTtBQUNBOztBQUVBLGlCQUEwQkEsT0FBTyxDQUFDLGdCQUFnQixDQUFDO0VBQTNDQyxhQUFhLFlBQWJBLGFBQWE7QUFDckIsTUFBTUMsTUFBTSxHQUFHRixPQUFPLENBQUMsUUFBUSxDQUFDO0FBQ2hDLE1BQU1HLElBQUksR0FBR0gsT0FBTyxDQUFDLE1BQU0sQ0FBQzs7QUFFNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUFJLE9BQU8sQ0FBQ0MsS0FBSyxHQUFHLENBQUNDLE9BQU8sRUFBRUMsR0FBRyxLQUFLO0VBQ2hDLE1BQU1GLEtBQUssR0FBR0YsSUFBSSxDQUFDSyxXQUFXLEVBQUU7RUFDaEMsTUFBTUMsTUFBTSxHQUFHLElBQUlQLE1BQU0sRUFBRTtFQUMzQixJQUFJUSxPQUFPOztFQUVYO0VBQ0FELE1BQU0sQ0FBQ0UsR0FBRyxHQUFHTCxPQUFPO0VBRXBCRCxLQUFLLENBQUNPLEVBQUUsQ0FBQyxPQUFPLEVBQUdDLEtBQUssSUFBSztJQUMzQixJQUFJQSxLQUFLLElBQUlBLEtBQUssQ0FBQ0MsSUFBSSxLQUFLLGFBQWEsRUFBRTtNQUN6QztNQUNBTCxNQUFNLENBQUNNLElBQUksQ0FBQyxLQUFLLENBQUM7TUFDbEI7SUFDRjtJQUVBTixNQUFNLENBQUNNLElBQUksQ0FBQyxPQUFPLEVBQUVGLEtBQUssQ0FBQztFQUM3QixDQUFDLENBQUM7O0VBRUY7RUFDQU4sR0FBRyxDQUFDUyxJQUFJLENBQUNYLEtBQUssQ0FBQzs7RUFFZjtFQUNBRSxHQUFHLENBQUNVLFdBQVcsR0FBSUMsSUFBSSxJQUFLO0lBQzFCUixPQUFPLEdBQUcsSUFBSVQsYUFBYSxDQUFDaUIsSUFBSSxDQUFDO0VBQ25DLENBQUM7O0VBRUQ7RUFDQWIsS0FBSyxDQUFDTyxFQUFFLENBQUMsTUFBTSxFQUFHTyxHQUFHLElBQUs7SUFDeEIsSUFBSVQsT0FBTyxFQUFFO01BQ1gsTUFBTVUsT0FBTyxHQUFHVixPQUFPLENBQUNXLEtBQUssQ0FBQ0YsR0FBRyxDQUFDO01BQ2xDLElBQUlDLE9BQU8sQ0FBQ0UsTUFBTSxHQUFHLENBQUMsRUFBRWIsTUFBTSxDQUFDTSxJQUFJLENBQUMsTUFBTSxFQUFFSyxPQUFPLENBQUM7SUFDdEQsQ0FBQyxNQUFNO01BQ0xYLE1BQU0sQ0FBQ00sSUFBSSxDQUFDLE1BQU0sRUFBRUksR0FBRyxDQUFDO0lBQzFCO0VBQ0YsQ0FBQyxDQUFDO0VBRUZkLEtBQUssQ0FBQ08sRUFBRSxDQUFDLEtBQUssRUFBRSxNQUFNO0lBQ3BCSCxNQUFNLENBQUNNLElBQUksQ0FBQyxLQUFLLENBQUM7RUFDcEIsQ0FBQyxDQUFDOztFQUVGO0VBQ0EsTUFBTVEsR0FBRyxHQUFHaEIsR0FBRyxDQUFDSyxFQUFFO0VBQ2xCTCxHQUFHLENBQUNLLEVBQUUsR0FBRyxVQUFVTSxJQUFJLEVBQUVNLEVBQUUsRUFBRTtJQUMzQixJQUFJTixJQUFJLEtBQUssTUFBTSxJQUFJQSxJQUFJLEtBQUssS0FBSyxFQUFFO01BQ3JDVCxNQUFNLENBQUNHLEVBQUUsQ0FBQ00sSUFBSSxFQUFFTSxFQUFFLENBQUNDLElBQUksQ0FBQ2xCLEdBQUcsQ0FBQyxDQUFDO0lBQy9CLENBQUMsTUFBTSxJQUFJVyxJQUFJLEtBQUssT0FBTyxFQUFFO01BQzNCVCxNQUFNLENBQUNHLEVBQUUsQ0FBQ00sSUFBSSxFQUFFTSxFQUFFLENBQUNDLElBQUksQ0FBQ2xCLEdBQUcsQ0FBQyxDQUFDO01BQzdCZ0IsR0FBRyxDQUFDRyxJQUFJLENBQUNuQixHQUFHLEVBQUVXLElBQUksRUFBRU0sRUFBRSxDQUFDO0lBQ3pCLENBQUMsTUFBTTtNQUNMRCxHQUFHLENBQUNHLElBQUksQ0FBQ25CLEdBQUcsRUFBRVcsSUFBSSxFQUFFTSxFQUFFLENBQUM7SUFDekI7SUFFQSxPQUFPLElBQUk7RUFDYixDQUFDO0FBQ0gsQ0FBQyJ9