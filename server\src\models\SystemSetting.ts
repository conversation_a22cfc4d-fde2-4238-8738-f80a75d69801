/**
 * 系统设置模型
 * 存储系统功能开关配置
 */
import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/database';

// 系统设置属性接口
interface SystemSettingAttributes {
  id: number;
  key: string;
  value: string;
  type: 'boolean' | 'string' | 'number' | 'json';
  description: string;
  created_at: Date;
  updated_at: Date;
}

// 创建时可选属性
interface SystemSettingCreationAttributes extends Optional<SystemSettingAttributes, 'id' | 'created_at' | 'updated_at'> {}

// 系统设置模型类
class SystemSetting extends Model<SystemSettingAttributes, SystemSettingCreationAttributes> implements SystemSettingAttributes {
  public id!: number;
  public key!: string;
  public value!: string;
  public type!: 'boolean' | 'string' | 'number' | 'json';
  public description!: string;
  public created_at!: Date;
  public updated_at!: Date;
}

// 初始化系统设置模型
SystemSetting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '设置ID',
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '设置键名',
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '设置值',
    },
    type: {
      type: DataTypes.ENUM('boolean', 'string', 'number', 'json'),
      allowNull: false,
      defaultValue: 'string',
      comment: '值类型',
    },
    description: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '设置描述',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'SystemSetting',
    tableName: 'system_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_key',
        fields: ['key'],
        unique: true,
      },
      {
        name: 'idx_type',
        fields: ['type'],
      },
    ],
  }
);

export default SystemSetting; 