/**
 * 敏感词检查辅助函数
 * 提供常用的敏感词检查场景和统一的错误处理
 */

import { checkSensitiveWord, hasSensitiveWord, SensitiveWordCheckResult } from './sensitiveWordChecker';

/**
 * 显示敏感词错误提示
 */
export function showSensitiveWordError(title: string = '内容审核', content: string = '内容包含不当内容，请重新输入') {
  wx.showModal({
    title,
    content,
    showCancel: false,
    confirmText: '我知道了',
    confirmColor: '#FF6B35'
  });
}

/**
 * 用户昵称检查
 */
export function checkNickname(nickname: string): SensitiveWordCheckResult {
  if (!nickname || !nickname.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(nickname.trim(), 'username');
}

/**
 * 厨房名称检查
 */
export function checkKitchenName(name: string): SensitiveWordCheckResult {
  if (!name || !name.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(name.trim(), 'content');
}

/**
 * 厨房公告检查
 */
export function checkKitchenNotice(notice: string): SensitiveWordCheckResult {
  if (!notice || !notice.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(notice.trim(), 'content');
}

/**
 * 菜品名称检查
 */
export function checkDishName(name: string): SensitiveWordCheckResult {
  if (!name || !name.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(name.trim(), 'dish');
}

/**
 * 菜品描述检查
 */
export function checkDishDescription(description: string): SensitiveWordCheckResult {
  if (!description || !description.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(description.trim(), 'content');
}

/**
 * 评论内容检查
 */
export function checkComment(comment: string): SensitiveWordCheckResult {
  if (!comment || !comment.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(comment.trim(), 'comment');
}

/**
 * 反馈内容检查
 */
export function checkFeedback(feedback: string): SensitiveWordCheckResult {
  if (!feedback || !feedback.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(feedback.trim(), 'content');
}

/**
 * 通用内容检查
 */
export function checkContent(content: string): SensitiveWordCheckResult {
  if (!content || !content.trim()) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }
  
  return checkSensitiveWord(content.trim(), 'content');
}

/**
 * 批量表单验证
 */
export function validateFormFields(
  formData: Record<string, any>, 
  fieldConfigs: Array<{
    field: string;
    type: 'username' | 'content' | 'dish' | 'comment';
    required?: boolean;
    label?: string;
  }>
): {
  isValid: boolean;
  errors: string[];
  firstError?: string;
} {
  const errors: string[] = [];
  
  for (let i = 0; i < fieldConfigs.length; i++) {
    const config = fieldConfigs[i];
    const value = formData[config.field];
    
    // 检查必填字段
    if (config.required && (!value || !value.trim())) {
      errors.push(`${config.label || config.field}不能为空`);
      continue;
    }
    
    // 如果有值，进行敏感词检查
    if (value && value.trim()) {
      const result = checkSensitiveWord(value.trim(), config.type);
      if (result.hasSensitiveWord) {
        errors.push(`${config.label || config.field}包含不当内容`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors,
    firstError: errors.length > 0 ? errors[0] : undefined
  };
}

/**
 * 快速检查并显示错误
 */
export function quickCheckAndShow(
  content: string, 
  type: 'username' | 'content' | 'dish' | 'comment' = 'content',
  title: string = '内容审核'
): boolean {
  const result = checkSensitiveWord(content, type);
  if (result.hasSensitiveWord) {
    showSensitiveWordError(title, `内容包含不当词汇：${result.sensitiveWords.join('、')}，请重新输入`);
    return false;
  }
  return true;
}

/**
 * 实时检查提示（用于输入时的实时检查）
 */
export function realtimeCheck(
  content: string, 
  type: 'username' | 'content' | 'dish' | 'comment' = 'content',
  callback?: (hasSensitive: boolean, words: string[]) => void
): boolean {
  if (!content || content.length < 2) {
    return true;
  }
  
  const result = checkSensitiveWord(content, type);
  
  if (callback) {
    callback(result.hasSensitiveWord, result.sensitiveWords);
  }
  
  if (result.hasSensitiveWord) {
    console.warn('检测到敏感词:', result.sensitiveWords);
  }
  
  return !result.hasSensitiveWord;
} 