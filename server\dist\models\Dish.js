"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 菜品模型
 * 存储菜品信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 菜品模型类
class Dish extends sequelize_1.Model {
    // 获取标签数组
    getTagsArray() {
        return this.tags ? this.tags.split(',') : [];
    }
    // 设置标签数组
    setTagsArray(tagsArray) {
        this.tags = tagsArray.join(',');
    }
    // 检查菜品是否上架
    isOnSale() {
        return this.status === 'on';
    }
}
// 初始化菜品模型
Dish.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '菜品ID',
    },
    kitchen_id: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '厨房ID',
        references: {
            model: 'kitchens',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    category_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '分类ID',
        references: {
            model: 'categories',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        comment: '菜品名称',
    },
    image: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        comment: '菜品图片',
    },
    price: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '价格（大米）',
    },
    original_price: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        comment: '原价',
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: '描述',
    },
    tags: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '标签（逗号分隔）',
    },
    sales: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '销量',
    },
    rating: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 5.0,
        comment: '评分',
    },
    status: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        defaultValue: 'on',
        comment: '状态(on:上架,off:下架)',
        validate: {
            isIn: [['on', 'off']],
        },
    },
    sort: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序值',
    },
    created_by: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true, // 允许为空，因为onDelete设置为SET NULL
        comment: '创建者ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Dish',
    tableName: 'dishes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_kitchen_category',
            fields: ['kitchen_id', 'category_id'],
        },
        {
            name: 'idx_name',
            fields: ['name'],
        },
        {
            name: 'idx_status',
            fields: ['status'],
        },
        {
            name: 'idx_sort',
            fields: ['sort'],
        },
        {
            name: 'idx_created_by',
            fields: ['created_by'],
        },
        {
            name: 'idx_kitchen_sort',
            fields: ['kitchen_id', 'sort'],
        },
        {
            name: 'idx_category_sort',
            fields: ['category_id', 'sort'],
        },
        {
            name: 'ftx_dish_search',
            type: 'FULLTEXT',
            fields: ['name', 'description', 'tags'],
        },
    ],
});
exports.default = Dish;
//# sourceMappingURL=Dish.js.map