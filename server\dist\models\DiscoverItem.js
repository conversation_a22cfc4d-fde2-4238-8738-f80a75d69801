"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 发现项模型
 * 存储发现页的菜品信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 发现项模型类
class DiscoverItem extends sequelize_1.Model {
}
// 初始化发现项模型
DiscoverItem.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '记录ID',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    kitchen_id: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '厨房ID',
        references: {
            model: 'kitchens',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    tab_type: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        comment: '标签类型(recommend:推荐,newest:最新,popular:流行)',
        validate: {
            isIn: [['recommend', 'newest', 'popular']],
        },
    },
    sort_value: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序值',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        comment: '用户ID(可选，用于记录用户已读状态)',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    is_read: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否已读',
    },
    last_read_time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '最后读取时间',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'DiscoverItem',
    tableName: 'discover_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_kitchen_id',
            fields: ['kitchen_id'],
        },
        {
            name: 'idx_tab_sort',
            fields: ['tab_type', 'sort_value'],
        },
        {
            name: 'idx_user_read',
            fields: ['user_id', 'is_read'],
        },
        {
            name: 'idx_created_at',
            fields: ['created_at'],
        },
    ],
});
exports.default = DiscoverItem;
//# sourceMappingURL=DiscoverItem.js.map