/**
 * 辅助函数
 * 提供各种工具函数
 */
import crypto from 'crypto';
import moment from 'moment';
import config from '../config/config';
import { 
  getDefaultImageUrl as urlManagerGetDefaultImageUrl,
  processImageUrl as urlManagerProcessImageUrl,
  getRandomUserAvatarUrl,
  getRandomKitchenAvatarUrl,
  getRandomKitchenBackgroundUrl
} from './urlManager';

/**
 * 生成用户ID
 * 规则：5位数字，从10001开始依次递增，用完增加位数
 * @param lastId 最后一个用户ID
 * @returns 新的用户ID
 */
export const generateUserId = (lastId?: number): number => {
  if (!lastId) {
    return config.idRules.userIdStart; // 默认从10001开始
  }

  // 计算当前ID长度
  const currentLength = lastId.toString().length;

  // 计算当前长度的最大ID
  const maxId = Math.pow(10, currentLength) - 1;

  // 如果已经达到最大值，增加一位
  if (lastId >= maxId) {
    return Math.pow(10, currentLength); // 返回下一位数的第一个ID
  }

  // 否则返回递增的ID
  return lastId + 1;
};

/**
 * 生成厨房ID
 * 规则：6位字母数字组合，用完增加位数
 * @param length ID长度，默认为6
 * @returns 新的厨房ID
 */
export const generateKitchenId = (length: number = config.idRules.kitchenIdLength): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars.charAt(randomIndex);
  }

  return result;
};

/**
 * 生成订单ID
 * 规则：基于时间戳生成，格式为年月日+4位随机数
 * @returns 新的订单ID
 */
export const generateOrderId = (): string => {
  const dateStr = moment().format('YYYYMMDD');
  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${dateStr}${randomNum}`;
};

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
export const generateRandomString = (length: number = 16): string => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * 生成随机数字
 * @param min 最小值
 * @param max 最大值
 * @returns 随机数字
 */
export const generateRandomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 格式化日期时间
 * @param date 日期对象或字符串
 * @param format 格式化模式，默认为YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return moment(date).format(format);
};

/**
 * 获取当前日期时间
 * @param format 格式化模式，默认为YYYY-MM-DD HH:mm:ss
 * @returns 当前日期时间字符串
 */
export const getCurrentDateTime = (format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return moment().format(format);
};

/**
 * 获取当前日期
 * @param format 格式化模式，默认为YYYY-MM-DD
 * @returns 当前日期字符串
 */
export const getCurrentDate = (format: string = 'YYYY-MM-DD'): string => {
  return moment().format(format);
};

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export const getDaysDiff = (startDate: Date | string, endDate: Date | string): number => {
  const start = moment(startDate);
  const end = moment(endDate);
  return end.diff(start, 'days');
};

/**
 * 添加天数到日期
 * @param date 原始日期
 * @param days 添加的天数
 * @param format 返回格式，默认为YYYY-MM-DD
 * @returns 新的日期字符串
 */
export const addDays = (date: Date | string, days: number, format: string = 'YYYY-MM-DD'): string => {
  return moment(date).add(days, 'days').format(format);
};

/**
 * 检查对象是否为空
 * @param obj 要检查的对象
 * @returns 是否为空
 */
export const isEmptyObject = (obj: any): boolean => {
  return obj === null || obj === undefined || (Object.keys(obj).length === 0 && obj.constructor === Object);
};

/**
 * 过滤对象中的空值
 * @param obj 要过滤的对象
 * @returns 过滤后的对象
 */
export const filterEmptyValues = (obj: any): any => {
  const result: any = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      if (value !== null && value !== undefined && value !== '') {
        result[key] = value;
      }
    }
  }

  return result;
};

/**
 * 分页辅助函数
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 分页参数
 */
export const getPagination = (page: number = 1, pageSize: number = 10) => {
  const limit = pageSize > 0 ? pageSize : 10;
  const offset = (page > 0 ? page - 1 : 0) * limit;

  return {
    limit,
    offset,
  };
};

/**
 * 构建分页结果
 * @param data 数据列表
 * @param total 总记录数
 * @param page 当前页码
 * @param pageSize 每页大小
 * @returns 分页结果
 */
export const buildPaginationResult = (data: any[], total: number, page: number, pageSize: number) => {
  return {
    list: data,
    total,
    page,
    pageSize,
    pages: Math.ceil(total / pageSize),
  };
};

/**
 * 将字符串日期转换为Date对象
 * @param dateStr 日期字符串
 * @param format 日期格式，默认为YYYY-MM-DD
 * @returns Date对象
 */
export const parseDate = (dateStr: string, format: string = 'YYYY-MM-DD'): Date => {
  return moment(dateStr, format).toDate();
};

/**
 * 获取默认图片URL
 * @param type 图片类型：'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'
 * @returns 默认图片URL
 */
export const getDefaultImageUrl = (type: 'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'): string => {
  // 使用URL管理器
  return urlManagerGetDefaultImageUrl(type);
};

/**
 * 处理图片URL，如果为空则返回默认图片
 * @param imageUrl 图片URL
 * @param defaultType 默认图片类型
 * @returns 处理后的图片URL
 */
export const processImageUrl = (imageUrl: string | null, defaultType: 'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'): string => {
  // 使用URL管理器处理
  return urlManagerProcessImageUrl(imageUrl, defaultType);
};

// 生成随机用户头像URL
export const getRandomUserAvatar = (): string => {
  return getRandomUserAvatarUrl();
};

// 生成随机厨房头像URL
export const getRandomKitchenAvatar = (): string => {
  return getRandomKitchenAvatarUrl();
};

// 生成随机厨房背景图URL
export const getRandomKitchenBackground = (): string => {
  return getRandomKitchenBackgroundUrl();
};

export default {
  generateUserId,
  generateKitchenId,
  generateOrderId,
  generateRandomString,
  generateRandomNumber,
  formatDateTime,
  getCurrentDateTime,
  getCurrentDate,
  getDaysDiff,
  addDays,
  isEmptyObject,
  filterEmptyValues,
  getPagination,
  buildPaginationResult,
  parseDate,
  getDefaultImageUrl,
  processImageUrl,
  getRandomUserAvatar,
  getRandomKitchenAvatar,
  getRandomKitchenBackground,
};
