"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const kitchenService_1 = __importDefault(require("../services/kitchenService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取厨房列表
 * @route GET /api/kitchen/list
 */
const getKitchenList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const kitchens = yield kitchenService_1.default.getKitchenList(userId);
        (0, response_1.success)(res, kitchens);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户创建的厨房列表
 * @route GET /api/kitchen/owned
 */
const getOwnedKitchenList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const kitchens = yield kitchenService_1.default.getOwnedKitchenList(userId);
        (0, response_1.success)(res, kitchens);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户加入的厨房列表
 * @route GET /api/kitchen/joined
 */
const getJoinedKitchenList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const kitchens = yield kitchenService_1.default.getJoinedKitchenList(userId);
        (0, response_1.success)(res, kitchens);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取厨房详细信息
 * @route GET /api/kitchen/info
 */
const getKitchenInfo = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, id } = req.query;
        // 兼容两种参数名：id和kitchenId
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const kitchen = yield kitchenService_1.default.getKitchenInfo(userId, kitchenIdentifier);
        (0, response_1.success)(res, kitchen);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取厨房基本信息
 * @route GET /api/kitchen/baseInfo
 */
const getKitchenBaseInfo = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // 支持匿名访问，userId可能为undefined
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { kitchenId, id } = req.query;
        // 兼容两种参数名：id和kitchenId
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const kitchen = yield kitchenService_1.default.getKitchenBaseInfo(kitchenIdentifier, userId);
        (0, response_1.success)(res, kitchen);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 创建厨房
 * @route POST /api/kitchen/create
 */
const createKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { name, notice, avatarUrl } = req.body;
        if (!name) {
            throw new error_1.BusinessError('缺少参数: name', response_1.ResponseCode.VALIDATION);
        }
        // 验证厨房名称长度
        if (name.length > 10) {
            throw new error_1.BusinessError('厨房名称不能超过10个字符', response_1.ResponseCode.VALIDATION);
        }
        // 验证厨房公告长度
        if (notice && notice.length > 30) {
            throw new error_1.BusinessError('厨房公告不能超过30个字符', response_1.ResponseCode.VALIDATION);
        }
        const result = yield kitchenService_1.default.createKitchen(userId, name, notice, avatarUrl);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新厨房信息
 * @route POST /api/kitchen/update
 */
const updateKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId, name, notice, avatarUrl, backgroundUrl } = req.body;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        // 验证厨房名称长度
        if (name && name.length > 10) {
            throw new error_1.BusinessError('厨房名称不能超过10个字符', response_1.ResponseCode.VALIDATION);
        }
        // 验证厨房公告长度
        if (notice && notice.length > 30) {
            throw new error_1.BusinessError('厨房公告不能超过30个字符', response_1.ResponseCode.VALIDATION);
        }
        yield kitchenService_1.default.updateKitchen(userId, kitchenIdentifier, { name, notice, avatarUrl, backgroundUrl });
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 加入厨房
 * @route POST /api/kitchen/join
 */
const joinKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const result = yield kitchenService_1.default.joinKitchen(userId, kitchenIdentifier);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 退出厨房
 * @route POST /api/kitchen/leave
 */
const leaveKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        yield kitchenService_1.default.leaveKitchen(userId, kitchenIdentifier);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 解散厨房
 * @route POST /api/kitchen/dismiss
 */
const dismissKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        yield kitchenService_1.default.dismissKitchen(userId, kitchenIdentifier);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 升级厨房
 * @route POST /api/kitchen/upgrade
 */
const upgradeKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const result = yield kitchenService_1.default.upgradeKitchen(userId, kitchenIdentifier);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取厨房成员列表
 * @route GET /api/kitchen/members
 */
const getKitchenMembers = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, id } = req.query;
        // 兼容两种参数名：id和kitchenId
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const members = yield kitchenService_1.default.getKitchenMembers(userId, kitchenIdentifier);
        (0, response_1.success)(res, { members });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新成员权限
 * @route POST /api/kitchen/updateMemberPermission
 */
const updateMemberPermission = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, memberId, role } = req.body;
        if (!kitchenId || !memberId || !role) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield kitchenService_1.default.updateMemberPermission(userId, kitchenId, parseInt(memberId), role);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 移除成员
 * @route POST /api/kitchen/removeMember
 */
const removeMember = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, memberId } = req.body;
        if (!kitchenId || !memberId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield kitchenService_1.default.removeMember(userId, kitchenId, parseInt(memberId));
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取厨房二维码
 * @route GET /api/kitchen/qrcode
 */
const getKitchenQrcode = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // 支持匿名访问，userId可能为undefined
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { id, kitchenId, refresh, compress } = req.query;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        // 判断是否强制刷新
        const forceRefresh = refresh === 'true';
        // 判断是否启用压缩
        const enableCompress = compress === 'true';
        // 添加调试日志
        console.log('二维码请求参数:', { kitchenIdentifier, forceRefresh, enableCompress, compress });
        const qrcodeData = yield kitchenService_1.default.getKitchenQrcode(userId, kitchenIdentifier, forceRefresh, enableCompress);
        (0, response_1.success)(res, qrcodeData);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 根据ID查找厨房
 * @route GET /api/kitchen/findById
 */
const findKitchenById = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { kitchenId, id } = req.query;
        // 兼容两种参数名：id和kitchenId
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const kitchen = yield kitchenService_1.default.findKitchenById(kitchenIdentifier, userId);
        (0, response_1.success)(res, kitchen);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 搜索厨房信息
 * @route GET /api/kitchen/search
 */
const searchKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { keyword } = req.query;
        if (!keyword) {
            throw new error_1.BusinessError('缺少参数: keyword', response_1.ResponseCode.VALIDATION);
        }
        const kitchens = yield kitchenService_1.default.searchKitchen(userId, keyword);
        (0, response_1.success)(res, { kitchens });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取源厨房分类列表
 * @route GET /api/kitchen/categories
 */
const getKitchenCategories = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.query;
        // 兼容两种参数名
        const kitchenIdentifier = kitchenId || id;
        if (!kitchenIdentifier) {
            throw new error_1.BusinessError('缺少参数: id或kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const categories = yield kitchenService_1.default.getKitchenCategories(userId, kitchenIdentifier);
        // 直接返回分类数组，不再包装在categories字段中
        (0, response_1.success)(res, categories);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取源厨房分类下的菜品
 * @route GET /api/kitchen/categoryDishes
 */
const getCategoryDishes = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, categoryId } = req.query;
        // 增强参数验证
        if (!kitchenId) {
            throw new error_1.BusinessError('厨房ID不能为空', response_1.ResponseCode.VALIDATION);
        }
        if (!categoryId || categoryId === 'undefined') {
            throw new error_1.BusinessError('分类ID不能为空', response_1.ResponseCode.VALIDATION);
        }
        const dishes = yield kitchenService_1.default.getCategoryDishes(userId, kitchenId, parseInt(categoryId));
        (0, response_1.success)(res, dishes);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 克隆菜品到目标厨房
 * @route POST /api/kitchen/cloneDishes
 */
const cloneDishes = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { sourceKitchenId, targetKitchenId, targetCategoryId, dishIds } = req.body;
        if (!sourceKitchenId || !targetKitchenId || !targetCategoryId || !dishIds || !Array.isArray(dishIds)) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield kitchenService_1.default.cloneDishes(userId, sourceKitchenId, targetKitchenId, parseInt(targetCategoryId), dishIds);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getKitchenList,
    getOwnedKitchenList,
    getJoinedKitchenList,
    getKitchenInfo,
    getKitchenBaseInfo,
    createKitchen,
    updateKitchen,
    joinKitchen,
    leaveKitchen,
    dismissKitchen,
    upgradeKitchen,
    getKitchenMembers,
    updateMemberPermission,
    removeMember,
    getKitchenQrcode,
    findKitchenById,
    searchKitchen,
    getKitchenCategories,
    getCategoryDishes,
    cloneDishes
};
//# sourceMappingURL=kitchenController.js.map