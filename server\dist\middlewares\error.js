"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotFoundError = exports.ForbiddenError = exports.UnauthorizedError = exports.BusinessError = exports.errorHandler = exports.notFoundHandler = void 0;
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../utils/logger"));
/**
 * 404错误处理中间件
 * 处理未找到的路由
 * @param req 请求对象
 * @param res 响应对象
 */
const notFoundHandler = (req, res) => {
    logger_1.default.warn(`未找到路由: ${req.method} ${req.originalUrl}`);
    (0, response_1.error)(res, response_1.ResponseCode.NOT_FOUND, `未找到路由: ${req.method} ${req.originalUrl}`);
};
exports.notFoundHandler = notFoundHandler;
/**
 * 全局错误处理中间件
 * 捕获并处理应用中的所有错误
 * @param err 错误对象
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const errorHandler = (err, req, res, next) => {
    // 记录错误日志 - 增加更详细的信息
    logger_1.default.error(`请求处理错误: ${req.method} ${req.originalUrl}`, {
        error: err.message,
        stack: err.stack,
        body: req.body,
        params: req.params,
        query: req.query
    });
    // 根据错误类型返回不同的响应
    if (err.name === 'ValidationError') {
        // 参数验证错误
        return (0, response_1.error)(res, response_1.ResponseCode.VALIDATION, err.message, err.errors);
    }
    else if (err.name === 'UnauthorizedError' || err.name === 'JsonWebTokenError') {
        // JWT验证错误
        return (0, response_1.error)(res, response_1.ResponseCode.UNAUTHORIZED, '未登录或登录过期，需要重新登录');
    }
    else if (err.name === 'ForbiddenError') {
        // 权限错误
        return (0, response_1.error)(res, response_1.ResponseCode.FORBIDDEN, err.message || '没有权限执行此操作');
    }
    else if (err.name === 'NotFoundError') {
        // 资源不存在错误
        return (0, response_1.error)(res, response_1.ResponseCode.NOT_FOUND, err.message || '请求的资源不存在');
    }
    else if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
        // Sequelize验证错误
        const validationErrors = err.errors.reduce((acc, e) => {
            acc[e.path] = e.message;
            return acc;
        }, {});
        return (0, response_1.error)(res, response_1.ResponseCode.VALIDATION, '数据验证失败', validationErrors);
    }
    else if (err.name === 'BusinessError') {
        // 业务逻辑错误
        return (0, response_1.error)(res, err.code || response_1.ResponseCode.BUSINESS_ERROR, err.message);
    }
    // 默认服务器错误
    return (0, response_1.error)(res, response_1.ResponseCode.SERVER_ERROR, '服务器内部错误');
};
exports.errorHandler = errorHandler;
/**
 * 业务错误类
 * 用于抛出业务逻辑相关的错误
 */
class BusinessError extends Error {
    constructor(message, code = response_1.ResponseCode.BUSINESS_ERROR) {
        super(message);
        this.name = 'BusinessError';
        this.code = code;
    }
}
exports.BusinessError = BusinessError;
/**
 * 未授权错误类
 * 用于抛出未授权相关的错误
 */
class UnauthorizedError extends Error {
    constructor(message = '未登录或登录过期，需要重新登录') {
        super(message);
        this.name = 'UnauthorizedError';
    }
}
exports.UnauthorizedError = UnauthorizedError;
/**
 * 禁止访问错误类
 * 用于抛出权限相关的错误
 */
class ForbiddenError extends Error {
    constructor(message = '没有权限执行此操作') {
        super(message);
        this.name = 'ForbiddenError';
    }
}
exports.ForbiddenError = ForbiddenError;
/**
 * 资源不存在错误类
 * 用于抛出资源不存在相关的错误
 */
class NotFoundError extends Error {
    constructor(message = '请求的资源不存在') {
        super(message);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
exports.default = {
    notFoundHandler: exports.notFoundHandler,
    errorHandler: exports.errorHandler,
    BusinessError,
    UnauthorizedError,
    ForbiddenError,
    NotFoundError,
};
//# sourceMappingURL=error.js.map