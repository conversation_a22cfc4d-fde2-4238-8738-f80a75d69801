"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 应用入口文件
 * 配置Express应用，中间件和路由
 */
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("./config/config"));
const database_1 = require("./config/database");
const logger_1 = __importDefault(require("./utils/logger"));
const error_1 = require("./middlewares/error");
// 创建Express应用
const app = (0, express_1.default)();
// 配置中间件
app.use(express_1.default.json()); // 解析JSON请求体
app.use(express_1.default.urlencoded({ extended: true })); // 解析URL编码的请求体
app.use((0, cors_1.default)({
    origin: config_1.default.server.corsOrigin,
    credentials: true
})); // 启用CORS
// 配置静态文件服务
// 添加图片访问监控中间件
app.use('/uploads', (req, res, next) => {
    const startTime = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        if (res.statusCode !== 200) {
            logger_1.default.warn(`图片访问失败: ${req.path}, 状态码: ${res.statusCode}, 耗时: ${duration}ms, IP: ${req.ip}`);
        }
        else if (duration > 3000) {
            logger_1.default.warn(`图片加载缓慢: ${req.path}, 耗时: ${duration}ms, IP: ${req.ip}`);
        }
    });
    next();
});
// 配置静态文件服务，启用缓存
app.use('/uploads', express_1.default.static(path_1.default.join(process.cwd(), config_1.default.upload.dir), {
    maxAge: '1d', // 缓存1天
    etag: true, // 启用ETag验证
    lastModified: true, // 启用Last-Modified头
    setHeaders: (res, path) => {
        // 为图片文件设置更强的缓存策略
        if (path.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
            res.setHeader('Cache-Control', 'public, max-age=86400, s-maxage=86400'); // 1天缓存
            res.setHeader('Vary', 'Accept-Encoding');
        }
    }
}));
// 请求日志中间件
app.use((req, res, next) => {
    logger_1.default.info(`${req.method} ${req.originalUrl}`);
    next();
});
// 路由配置
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const dishRoutes_1 = __importDefault(require("./routes/dishRoutes"));
const categoryRoutes_1 = __importDefault(require("./routes/categoryRoutes"));
const cartRoutes_1 = __importDefault(require("./routes/cartRoutes"));
const orderRoutes_1 = __importDefault(require("./routes/orderRoutes"));
const kitchenRoutes_1 = __importDefault(require("./routes/kitchenRoutes"));
const tableRoutes_1 = __importDefault(require("./routes/tableRoutes"));
const messageRoutes_1 = __importDefault(require("./routes/messageRoutes"));
const discoverRoutes_1 = __importDefault(require("./routes/discoverRoutes"));
const uploadRoutes_1 = __importDefault(require("./routes/uploadRoutes"));
const adminRoutes_1 = __importDefault(require("./routes/adminRoutes"));
const feedbackRoutes_1 = __importDefault(require("./routes/feedbackRoutes"));
const systemRoutes_1 = __importDefault(require("./routes/systemRoutes"));
const paymentRoutes_1 = __importDefault(require("./routes/paymentRoutes"));
// 注册API路由
app.use('/api/user', userRoutes_1.default);
app.use('/api/dish', dishRoutes_1.default);
app.use('/api/category', categoryRoutes_1.default);
app.use('/api/cart', cartRoutes_1.default);
app.use('/api/order', orderRoutes_1.default);
app.use('/api/kitchen', kitchenRoutes_1.default);
// 将桌号路由作为厨房路由的子路由
kitchenRoutes_1.default.use('/table', tableRoutes_1.default);
app.use('/api/message', messageRoutes_1.default);
app.use('/api/discover', discoverRoutes_1.default);
app.use('/api/upload', uploadRoutes_1.default);
app.use('/api/admin', adminRoutes_1.default);
app.use('/api/feedback', feedbackRoutes_1.default);
app.use('/api/system', systemRoutes_1.default);
app.use('/api/payment', paymentRoutes_1.default);
// 根路由
app.get('/', (req, res) => {
    res.json({
        message: '餐厅点菜小程序API服务',
        version: '1.0.0',
        status: 'running',
    });
});
// 404处理
app.use(error_1.notFoundHandler);
// 错误处理
app.use(error_1.errorHandler);
// 导入模型关联初始化函数
const models_1 = require("./models");
// 启动服务器
const startServer = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 测试数据库连接
        yield (0, database_1.testConnection)();
        // 初始化模型关联关系
        (0, models_1.initializeAssociations)();
        // 启动HTTP服务器
        const PORT = config_1.default.server.port;
        app.listen(PORT, () => {
            logger_1.default.info(`服务器启动成功，监听端口: ${PORT}`);
            logger_1.default.info(`环境: ${config_1.default.server.env}`);
            logger_1.default.info(`API服务就绪，包含管理员接口`);
        });
    }
    catch (error) {
        logger_1.default.error('服务器启动失败:', error);
        process.exit(1);
    }
});
// 如果直接运行此文件，则启动服务器
if (require.main === module) {
    startServer();
}
exports.default = app;
//# sourceMappingURL=app.js.map