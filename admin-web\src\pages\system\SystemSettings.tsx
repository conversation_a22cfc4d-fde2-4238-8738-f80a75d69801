import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  message,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  Dropdown,
  Typography,
  Tabs,
  Progress,
  Descriptions,
  Alert,
  Badge,
  Switch
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  ReloadOutlined,
  UserOutlined,
  KeyOutlined,
  TeamOutlined,
  DownloadOutlined,
  ClearOutlined,
  MoreOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import {
  getAdminList,
  getAdminRoles,
  getSystemInfo,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  updateAdminStatus,
  resetAdminPassword,
  clearSystemCache,
  backupDatabase,
  type Admin,
  type AdminRole,
  type SystemInfo,
  type AdminListParams,
  getSystemSettings,
  updateSystemSetting as apiUpdateSystemSetting
} from '@/services/systemService'

const { Option } = Select
const { Text } = Typography

const SystemManage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('admins')
  const [admins, setAdmins] = useState<Admin[]>([])
  const [roles, setRoles] = useState<AdminRole[]>([])
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [modalVisible, setModalVisible] = useState(false)
  const [passwordModalVisible, setPasswordModalVisible] = useState(false)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [isEditing, setIsEditing] = useState(false)

  // 系统设置相关状态
  const [systemSettings, setSystemSettings] = useState<any[]>([])
  const [settingsLoading, setSettingsLoading] = useState(false)

  const [form] = Form.useForm()
  const [passwordForm] = Form.useForm()

  // 管理员列表
  const adminColumns: ColumnsType<Admin> = [
    {
      title: '管理员信息',
      key: 'adminInfo',
      width: 200,
      render: (_, admin) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar src={admin.avatar} icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{admin.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{admin.username}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{admin.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 120,
      render: (role: string) => {
        const roleMap = {
          super_admin: { color: 'red', text: '超级管理员' },
          admin: { color: 'blue', text: '管理员' },
          operator: { color: 'green', text: '操作员' },
        }
        const info = roleMap[role as keyof typeof roleMap] || roleMap.operator
        return <Tag color={info.color}>{info.text}</Tag>
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          active: { color: 'green', text: '正常' },
          inactive: { color: 'default', text: '禁用' },
          locked: { color: 'red', text: '锁定' },
        }
        const info = statusMap[status as keyof typeof statusMap] || statusMap.inactive
        return <Tag color={info.color}>{info.text}</Tag>
      },
    },
    {
      title: '最后登录',
      key: 'lastLogin',
      width: 150,
      render: (_, admin) => (
        <div>
          {admin.lastLoginTime && (
            <div style={{ fontSize: '12px' }}>
              {dayjs(admin.lastLoginTime).format('MM-DD HH:mm')}
            </div>
          )}
          {admin.lastLoginIp && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              {admin.lastLoginIp}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (_, admin) => {
        const items = [
          {
            key: 'edit',
            label: '编辑',
            icon: <EditOutlined />,
            onClick: () => handleEditAdmin(admin),
          },
          {
            key: 'password',
            label: '重置密码',
            icon: <KeyOutlined />,
            onClick: () => handleResetPassword(admin),
          },
        ]

        if (admin.status === 'active') {
          items.push({
            key: 'lock',
            label: '锁定',
            icon: <LockOutlined />,
            onClick: () => handleUpdateAdminStatus(admin.id, 'locked'),
          } as any)
        } else {
          items.push({
            key: 'unlock',
            label: '解锁',
            icon: <UnlockOutlined />,
            onClick: () => handleUpdateAdminStatus(admin.id, 'active'),
          } as any)
        }

        items.push({
          key: 'delete',
          label: '删除',
          icon: <DeleteOutlined />,
          onClick: () => handleDeleteAdmin(admin.id),
        } as any)

        return (
          <Dropdown menu={{ items }} trigger={['click']}>
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        )
      },
    },
  ]

  // 加载数据函数
  const loadAdmins = async (params: AdminListParams = {}) => {
    try {
      setLoading(true)
      const response = await getAdminList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      })
      setAdmins(response.admins)
      setPagination(prev => ({
        ...prev,
        total: response.total,
        current: response.page,
      }))
    } catch (error) {
      message.error('加载管理员列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadRoles = async () => {
    try {
      const rolesData = await getAdminRoles()
      setRoles(rolesData)
    } catch (error) {
      message.error('加载角色列表失败')
    }
  }

  const loadSystemInfo = async () => {
    try {
      setLoading(true)
      const info = await getSystemInfo()
      setSystemInfo(info)
    } catch (error) {
      message.error('获取系统信息失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载系统设置
  const loadSystemSettings = async () => {
    try {
      setSettingsLoading(true)
      // 调用真实API获取系统设置
      const response = await getSystemSettings()
      
      // 检查响应数据格式并获取settings数组
      let settings: any[] = []
      if (response && Array.isArray(response.settings)) {
        settings = response.settings
      } else {
        console.warn('API响应格式异常:', response)
        message.warning('系统设置数据格式异常')
        return
      }
      
      // 为评论功能添加友好的标签
      const enhancedSettings = settings.map((setting: any) => ({
        ...setting,
        label: setting.key === 'comment_enabled' ? '评论功能' : setting.description || setting.key
      }))
      
      setSystemSettings(enhancedSettings)
    } catch (error) {
      console.error('获取系统设置失败:', error)
      message.error('获取系统设置失败')
    } finally {
      setSettingsLoading(false)
    }
  }

  // 更新系统设置
  const updateSystemSetting = async (key: string, value: any, type: string = 'boolean') => {
    try {
      // 调用真实API更新系统设置
      await apiUpdateSystemSetting(key, value, type)
      
      // 更新本地状态
      setSystemSettings(prev => 
        prev.map(setting => 
          setting.key === key 
            ? { ...setting, value } 
            : setting
        )
      )
      
      message.success('设置更新成功')
    } catch (error) {
      console.error('更新系统设置失败:', error)
      message.error('设置更新失败')
      
      // 发生错误时，重新加载设置以确保状态同步
      loadSystemSettings()
    }
  }

  // 处理函数
  const handleEditAdmin = (admin: Admin) => {
    setSelectedItem(admin)
    setIsEditing(true)
    form.setFieldsValue({
      username: admin.username,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      status: admin.status,
    })
    setModalVisible(true)
  }

  const handleResetPassword = (admin: Admin) => {
    setSelectedItem(admin)
    passwordForm.resetFields()
    setPasswordModalVisible(true)
  }

  const handleUpdateAdminStatus = async (adminId: string, status: 'active' | 'inactive' | 'locked') => {
    try {
      await updateAdminStatus(adminId, status)
      message.success(`管理员状态更新成功`)
      loadAdmins()
    } catch (error) {
      message.error('状态更新失败')
    }
  }

  const handleDeleteAdmin = async (adminId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个管理员吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await deleteAdmin(adminId)
          message.success('管理员删除成功')
          loadAdmins()
        } catch (error) {
          message.error('删除失败')
        }
      },
    })
  }

  useEffect(() => {
    if (activeTab === 'admins') {
      loadAdmins()
      loadRoles()
    } else if (activeTab === 'system') {
      loadSystemInfo()
    } else if (activeTab === 'settings') {
      loadSystemSettings()
    }
  }, [activeTab])

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="管理员总数" value={admins.length} prefix={<TeamOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线管理员"
              value={admins.filter(a => a.status === 'active').length}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="角色数量"
              value={roles.length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统版本"
              value={systemInfo?.version || 'v1.0.0'}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主内容区域 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.TabPane tab="管理员管理" key="admins">
            <div style={{ marginBottom: 16 }}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setModalVisible(true)}>
                添加管理员
              </Button>
            </div>
            <Table
              columns={adminColumns}
              dataSource={admins}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 10 }))
                  loadAdmins({ page, pageSize })
                },
              }}
            />
          </Tabs.TabPane>
          
          <Tabs.TabPane tab="功能设置" key="settings">
            <Card title="功能开关管理" loading={settingsLoading}>
              <Row gutter={[16, 16]}>
                {systemSettings.map(setting => (
                  <Col span={24} key={setting.key}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      padding: '16px 20px',
                      border: '1px solid #f0f0f0',
                      borderRadius: '8px',
                      backgroundColor: '#fafafa'
                    }}>
                      <div>
                        <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                          {setting.label || setting.key}
                        </div>
                        <div style={{ fontSize: '14px', color: '#666' }}>
                          {setting.description}
                        </div>
                      </div>
                      <Switch
                        checked={setting.value}
                        onChange={(checked) => updateSystemSetting(setting.key, checked, setting.type)}
                        checkedChildren="开启"
                        unCheckedChildren="关闭"
                      />
                    </div>
                  </Col>
                ))}
                {systemSettings.length === 0 && !settingsLoading && (
                  <Col span={24}>
                    <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                      暂无可配置的功能开关
                    </div>
                  </Col>
                )}
              </Row>
            </Card>
          </Tabs.TabPane>
          
          <Tabs.TabPane tab="系统信息" key="system">
            {systemInfo && (
              <div>
                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="系统状态" style={{ marginBottom: 16 }}>
                      <Descriptions column={1}>
                        <Descriptions.Item label="系统版本">{systemInfo.version}</Descriptions.Item>
                        <Descriptions.Item label="运行环境">{systemInfo.environment}</Descriptions.Item>
                        <Descriptions.Item label="运行时间">{systemInfo.server.uptime}</Descriptions.Item>
                        <Descriptions.Item label="数据库状态">
                          <Badge 
                            status={systemInfo.database.status === 'connected' ? 'success' : 'error'}
                            text={systemInfo.database.status === 'connected' ? '已连接' : '连接失败'}
                          />
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="服务器资源" style={{ marginBottom: 16 }}>
                      <div style={{ marginBottom: 16 }}>
                        <Text>内存使用率</Text>
                        <Progress 
                          percent={parseInt(systemInfo.server.memory.used) / parseInt(systemInfo.server.memory.total) * 100}
                          status="active"
                        />
                      </div>
                      <div style={{ marginBottom: 16 }}>
                        <Text>存储使用率</Text>
                        <Progress 
                          percent={systemInfo.storage.usage}
                          status={systemInfo.storage.usage > 80 ? 'exception' : 'active'}
                        />
                      </div>
                      <div>
                        <Text>CPU使用率</Text>
                        <Progress 
                          percent={systemInfo.server.cpu.usage}
                          status={systemInfo.server.cpu.usage > 80 ? 'exception' : 'active'}
                        />
                      </div>
                    </Card>
                  </Col>
                </Row>
                
                <Card title="系统操作">
                  <Space>
                    <Button 
                      icon={<ClearOutlined />} 
                      onClick={() => clearSystemCache()}
                    >
                      清理缓存
                    </Button>
                    <Button 
                      icon={<DownloadOutlined />} 
                      onClick={() => backupDatabase()}
                    >
                      数据备份
                    </Button>
                    <Button 
                      icon={<ReloadOutlined />} 
                      onClick={loadSystemInfo}
                    >
                      刷新信息
                    </Button>
                  </Space>
                </Card>
              </div>
            )}
          </Tabs.TabPane>
        </Tabs>
      </Card>

      {/* 管理员编辑弹窗 */}
      <Modal
        title={isEditing ? '编辑管理员' : '添加管理员'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setSelectedItem(null)
          setIsEditing(false)
          form.resetFields()
        }}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={async (values) => {
            try {
              if (isEditing && selectedItem) {
                await updateAdmin(selectedItem.id, values)
                message.success('管理员更新成功')
              } else {
                await createAdmin(values)
                message.success('管理员创建成功')
              }
              
              setModalVisible(false)
              loadAdmins()
            } catch (error) {
              message.error(isEditing ? '更新失败' : '创建失败')
            }
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="请输入用户名" disabled={isEditing} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效邮箱' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          {!isEditing && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }, { min: 6, message: '密码至少6位' }]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="请选择角色">
                  <Option value="super_admin">超级管理员</Option>
                  <Option value="admin">管理员</Option>
                  <Option value="operator">操作员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">正常</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="locked">锁定</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 密码重置弹窗 */}
      <Modal
        title="重置密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false)
          setSelectedItem(null)
          passwordForm.resetFields()
        }}
        onOk={() => passwordForm.submit()}
        width={400}
      >
        {selectedItem && (
          <div>
            <Alert
              message={`重置管理员 "${selectedItem.name}" 的密码`}
              type="info"
              style={{ marginBottom: 16 }}
            />
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={async (values) => {
                try {
                  await resetAdminPassword(selectedItem.id, values.newPassword)
                  message.success('密码重置成功')
                  setPasswordModalVisible(false)
                } catch (error) {
                  message.error('密码重置失败')
                }
              }}
            >
              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码至少6位' }
                ]}
              >
                <Input.Password placeholder="请输入新密码" />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                label="确认密码"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve()
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'))
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="请确认新密码" />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default SystemManage 