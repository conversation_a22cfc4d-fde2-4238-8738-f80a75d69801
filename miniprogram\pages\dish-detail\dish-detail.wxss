/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F9F9F9;
  padding-bottom: 120rpx;
}

/* 菜品图片区域 - 扩展到状态栏 */
.dish-image-container {
  position: relative;
  width: 100%;
  height: 600rpx; /* 增加高度以覆盖状态栏区域 */
  margin-top: -88rpx; /* 向上偏移覆盖状态栏，88rpx约为状态栏高度 */
  padding-top: 88rpx; /* 添加内边距保持内容位置 */
}

.dish-swiper {
  width: 100%;
  height: 100%;
}

.dish-image {
  width: 100%;
  height: 100%;
}

/* 浮动返回按钮 - 使用公共组件样式 */
.floating-back-btn {
  position: fixed;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.75);
  border-radius: 50% !important;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease, opacity 0.3s ease;
}

.back-icon {
  color: #333333;
  font-size: 28rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2rpx; /* 微调图标位置 */
  transition: color 0.3s ease;
}

.floating-back-btn:active {
  background-color: rgba(255, 255, 255, 0.7);
  transform: scale(0.95);
}

/* 菜品基本信息卡片 */
.dish-info-card {
  margin: -40rpx 30rpx 30rpx; /* 向上移动40rpx，覆盖图片底部 */
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 16rpx 16rpx; /* 顶部圆角更大，增强层叠效果 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12); /* 增强阴影效果 */
  position: relative; /* 确保卡片在图片上方 */
  z-index: 10; /* 确保内容卡片显示在图片上方 */
}

.dish-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.dish-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

/* 举报按钮 */
.report-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.report-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.report-icon {
  width: 32rpx;
  height: 32rpx;
}

.report-text {
  font-size: 22rpx;
}

.report-btn:active {
  opacity: 0.8;
}

.dish-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.tag {
  font-size: 24rpx;
  color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.dish-rating-sales {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.rating {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.star {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.rating text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 6rpx;
}

.sales {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
}

.dish-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.current-price {
  font-size: 44rpx;
  font-weight: bold;
  color: #E53935;
}

.original-price {
  font-size: 28rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 16rpx;
}

.dish-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 通用部分卡片样式 */
.section-card {
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.title-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

/* 配料表样式 */
.ingredients-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.ingredient-name {
  font-size: 28rpx;
  color: #333333;
  margin-right: 20rpx;
}

.ingredient-amount {
  font-size: 28rpx;
  color: #666666;
}

/* 营养信息样式 */
.nutrition-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx 30rpx;
}

.nutrition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nutrition-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
}

.nutrition-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* 评论样式 */
.section-title .comment-count {
  font-size: 24rpx;
  color: #999999;
  margin-left: auto;
  font-weight: normal;
}

.comment-list {
  padding: 10rpx 0;
}

.comment-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.comment-content {
  flex: 1;
}

.comment-name-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.comment-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.comment-time {
  font-size: 24rpx;
  color: #999999;
}

.comment-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

.more-comments {
  text-align: center;
  padding: 20rpx 0 0;
  font-size: 28rpx;
  color: #4CAF50;
}

/* 底部操作栏样式 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #EEEEEE;
  z-index: 999;
  transition: bottom 0.3s ease;
}

.comment-section {
  flex: 1;
  margin-right: 20rpx;
  min-width: 0; /* 允许flex项目收缩 */
  position: relative; /* 为内部元素提供定位上下文 */
}

/* 评论框样式 */
.comment-placeholder {
  color: #999999;
}

.comment-input {
  height: 70rpx;
  background: #F6F6F6;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  width: 100%;
  line-height: 70rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  align-items: center;
  /* 当没有评论输入框时，让按钮居中显示 */
  margin: 0 auto;
}

.like-btn, .add-to-kitchen-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 80rpx;
}

.like-btn .icon, .add-to-kitchen-btn .icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.like-btn text, .add-to-kitchen-btn text {
  font-size: 22rpx;
  color: #666;
}

.like-btn.liked .icon {
  /* 点赞成功时的图标颜色通过图片本身控制 */
}

.like-btn.liked text {
  color: #FF6B35;
}

.add-to-kitchen-btn {
  margin-left: 10rpx;
}

.add-to-kitchen-btn.added .icon {
  /* 已添加时的图标颜色通过图片本身控制 */
}

.add-to-kitchen-btn.added text {
  color: #4CAF50;
}

.share-btn {
  /* 超强力重置button的所有默认样式 - 使用!important强制覆盖 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  background: none !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  box-sizing: border-box !important;
  line-height: normal !important;
  font-size: inherit !important;
  text-align: center !important;
  color: inherit !important;
  font-weight: normal !important;
  
  /* 彻底移除button的默认宽度和最小宽度 */
  width: auto !important;
  min-width: 0 !important;
  max-width: none !important;
  
  /* 设置与其他按钮完全一致的布局 */
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 80rpx !important;
  
  /* 精确控制边距，与其他按钮保持一致 */
  margin-left: 10rpx !important;
  margin-right: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-left: 20rpx !important;
  padding-right: 20rpx !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* 彻底移除button的伪元素边框 */
.share-btn::before,
.share-btn::after {
  display: none !important;
  border: none !important;
  background: none !important;
}

.share-btn:active {
  opacity: 0.8;
}

.share-btn .icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.share-btn text {
  font-size: 22rpx;
  color: #666;
}

.send-button {
  width: 120rpx;
  height: 70rpx;
  background-color: #FF6B35;
  color: white;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

/* 分类选择对话框 */
.category-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.category-item {
  padding: 24rpx;
  font-size: 28rpx;
  color: #333333;
  border-bottom: 1rpx solid #F5F5F5;
  transition: background-color 0.3s;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item.selected {
  background-color: #FFF1EB;
  color: #FF6B35;
}

.category-item:active {
  background-color: #F9F9F9;
}

.category-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.category-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  object-fit: contain;
}

.category-name {
  font-size: 28rpx;
  color: inherit;
}

.category-count {
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
}

.category-item.selected .category-count {
  color: #FF6B35;
}

/* 空分类提示 */
.empty-categories {
  padding: 40rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

/* 加入购物车操作面板 */
.cart-action-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
}

.cart-action-popup.show {
  visibility: visible;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.cart-action-popup.show .popup-mask {
  opacity: 1;
}

.popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.cart-action-popup.show .popup-content {
  transform: translateY(0);
}

.popup-header {
  padding: 30rpx;
  display: flex;
  border-bottom: 1rpx solid #EEEEEE;
  position: relative;
}

.popup-dish-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.popup-dish-info {
  flex: 1;
}

.popup-dish-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.popup-dish-price {
  font-size: 36rpx;
  color: #E53935;
  font-weight: bold;
}

.popup-close {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
}

.popup-body {
  padding: 30rpx;
}

.quantity-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-label {
  font-size: 30rpx;
  color: #333333;
}

.quantity-actions {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 1rpx solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  color: #333333;
}

.quantity-btn.disabled {
  color: #CCCCCC;
}

.quantity-value {
  font-size: 30rpx;
  width: 80rpx;
  text-align: center;
  color: #333333;
}

.popup-footer {
  padding: 20rpx 30rpx 50rpx;
}

.confirm-btn {
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #FF6B35;
  color: white;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 购物车内容弹窗 */
.cart-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
}

.cart-popup.show {
  visibility: visible;
}

.cart-popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.cart-popup.show .cart-popup-content {
  transform: translateY(0);
}

.cart-popup-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #EEEEEE;
}

.cart-popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.cart-popup-clear {
  font-size: 28rpx;
  color: #999999;
}

.cart-items {
  padding: 0 30rpx;
  max-height: 50vh;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.cart-item-name {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
}

.cart-item-price {
  font-size: 30rpx;
  color: #E53935;
  font-weight: bold;
  margin: 0 30rpx;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
}

.cart-popup-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #EEEEEE;
}

.cart-total {
  font-size: 30rpx;
  color: #333333;
}

.cart-total-price {
  font-size: 34rpx;
  color: #E53935;
  font-weight: bold;
}

.checkout-btn {
  height: 72rpx;
  width: 200rpx;
  border-radius: 36rpx;
  background-color: #FF6B35;
  color: white;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 烹饪步骤样式 */
.cooking-steps {
  padding: 10rpx 0;
}

.step-item {
  margin-bottom: 30rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #FF6B35;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.step-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.step-content {
  padding-left: 56rpx;
}

.step-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.step-image {
  width: 100%;
  border-radius: 12rpx;
  margin-top: 10rpx;
  display: block;
  box-sizing: border-box;
}

/* 确保smart-image组件在步骤中正常显示 */
.step-content .step-image {
  margin-top: 16rpx;
}

.step-content .step-image .smart-image-container {
  border-radius: 12rpx;
  overflow: hidden;
}

/* 图片指示器 */
.image-indicator {
  position: absolute;
  right: 30rpx;
  bottom: 60rpx; /* 往上移动40rpx */
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
  z-index: 15; /* 提高层级，确保显示在内容卡片上方 */
}

/* 举报弹窗样式 */
.report-content {
  padding: 10rpx 0 30rpx;
}

.report-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.report-reason {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.reason-text {
  font-size: 28rpx;
  color: #333;
}

.report-desc-area {
  margin-top: 30rpx;
  position: relative;
}

.report-desc-input {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.report-placeholder {
  color: #999;
  font-size: 28rpx;
}

.report-desc-counter {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 小字体大米价格样式 */
.dish-price-mini {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: 500;
}