"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middlewares/auth");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("../config/config"));
const router = (0, express_1.Router)();
// 配置图片上传
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, path_1.default.join(process.cwd(), config_1.default.upload.dir, 'feedback'));
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'feedback-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只支持图片文件格式 (jpeg, jpg, png, gif)'));
        }
    }
});
// 导入控制器方法 - 移到这里避免循环依赖
const feedbackController_1 = require("../controllers/feedbackController");
// 提交反馈
router.post('/submit', auth_1.verifyToken, feedbackController_1.submitFeedback);
// 上传反馈图片
router.post('/upload-image', auth_1.verifyToken, upload.single('image'), feedbackController_1.uploadImage);
// 获取反馈列表（管理员用）
router.post('/list', auth_1.verifyToken, feedbackController_1.getFeedbackList);
// 更新反馈状态（管理员用）
router.post('/update-status', auth_1.verifyToken, feedbackController_1.updateFeedbackStatus);
exports.default = router;
//# sourceMappingURL=feedbackRoutes.js.map