---
description: 
globs: 
alwaysApply: true
---

## 前后端分离开发规范

** 前端先行开发流程
- 前端开发阶段使用Mock数据进行开发，避免等待后端API
- 在api/目录下创建对应模块的API文件，使用统一命名：**Api.ts（如orderApi.ts）
- 每个接口函数命名规范：用动词+名词描述功能（如getDishList, submitOrder）
- 所有API均采用Promise封装，便于异步调用

** Mock数据规范
- 在api/mock/目录下创建与API文件同名的Mock文件（如api/mock/orderApi.ts）
- Mock数据应尽量模拟真实业务数据，包含各种边界情况
- 使用条件判断模拟不同请求参数下的返回结果
- 在开发环境下使用Mock数据，生产环境使用真实API

** 入参要求
- header中必须设置 auth， 值为当前登录后保存的token值
- 请求的参数使用json 格式，就算是参数为空，也需要使用 {} 来代替
- 参数命名使用小驼峰，与后端字段名保持一致

** http请求
- 请求方式默认是post，除非有明确要求
- 使用统一的request封装函数处理所有请求
- 统一处理loading状态、超时和错误重试

** 返参
- 后端统一返回的参数为json对象，格式如下
```json
{
  "error": 0,
  "body": object,
  "message": ""
}
```
- error = 0, 表示没有任何异常
- error = 500, 表示系统异常，需要弹出系统异常的错误
- error = 401，表示需要登录
- error 其它值，表示业务异常，直接弹出 message内容
- body 是一个对象

** 设计一个通用函数来处理后端API返回值，所有的API文件都使用这个通用函数

** API接口文档维护
- 当设计新接口时，先在 接口文档.md中定义接口规范
- 前端完成后，接口标记为"已实现-待联调"
- 后端完成后，接口标记为"已实现-可联调"
- 联调完成后，接口标记为"已完成"


