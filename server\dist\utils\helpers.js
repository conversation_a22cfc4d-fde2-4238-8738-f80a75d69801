"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRandomKitchenBackground = exports.getRandomKitchenAvatar = exports.getRandomUserAvatar = exports.processImageUrl = exports.getDefaultImageUrl = exports.parseDate = exports.buildPaginationResult = exports.getPagination = exports.filterEmptyValues = exports.isEmptyObject = exports.addDays = exports.getDaysDiff = exports.getCurrentDate = exports.getCurrentDateTime = exports.formatDateTime = exports.generateRandomNumber = exports.generateRandomString = exports.generateOrderId = exports.generateKitchenId = exports.generateUserId = void 0;
/**
 * 辅助函数
 * 提供各种工具函数
 */
const crypto_1 = __importDefault(require("crypto"));
const moment_1 = __importDefault(require("moment"));
const config_1 = __importDefault(require("../config/config"));
const urlManager_1 = require("./urlManager");
/**
 * 生成用户ID
 * 规则：5位数字，从10001开始依次递增，用完增加位数
 * @param lastId 最后一个用户ID
 * @returns 新的用户ID
 */
const generateUserId = (lastId) => {
    if (!lastId) {
        return config_1.default.idRules.userIdStart; // 默认从10001开始
    }
    // 计算当前ID长度
    const currentLength = lastId.toString().length;
    // 计算当前长度的最大ID
    const maxId = Math.pow(10, currentLength) - 1;
    // 如果已经达到最大值，增加一位
    if (lastId >= maxId) {
        return Math.pow(10, currentLength); // 返回下一位数的第一个ID
    }
    // 否则返回递增的ID
    return lastId + 1;
};
exports.generateUserId = generateUserId;
/**
 * 生成厨房ID
 * 规则：6位字母数字组合，用完增加位数
 * @param length ID长度，默认为6
 * @returns 新的厨房ID
 */
const generateKitchenId = (length = config_1.default.idRules.kitchenIdLength) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length);
        result += chars.charAt(randomIndex);
    }
    return result;
};
exports.generateKitchenId = generateKitchenId;
/**
 * 生成订单ID
 * 规则：基于时间戳生成，格式为年月日+4位随机数
 * @returns 新的订单ID
 */
const generateOrderId = () => {
    const dateStr = (0, moment_1.default)().format('YYYYMMDD');
    const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${dateStr}${randomNum}`;
};
exports.generateOrderId = generateOrderId;
/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
const generateRandomString = (length = 16) => {
    return crypto_1.default.randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length);
};
exports.generateRandomString = generateRandomString;
/**
 * 生成随机数字
 * @param min 最小值
 * @param max 最大值
 * @returns 随机数字
 */
const generateRandomNumber = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};
exports.generateRandomNumber = generateRandomNumber;
/**
 * 格式化日期时间
 * @param date 日期对象或字符串
 * @param format 格式化模式，默认为YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
const formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
    return (0, moment_1.default)(date).format(format);
};
exports.formatDateTime = formatDateTime;
/**
 * 获取当前日期时间
 * @param format 格式化模式，默认为YYYY-MM-DD HH:mm:ss
 * @returns 当前日期时间字符串
 */
const getCurrentDateTime = (format = 'YYYY-MM-DD HH:mm:ss') => {
    return (0, moment_1.default)().format(format);
};
exports.getCurrentDateTime = getCurrentDateTime;
/**
 * 获取当前日期
 * @param format 格式化模式，默认为YYYY-MM-DD
 * @returns 当前日期字符串
 */
const getCurrentDate = (format = 'YYYY-MM-DD') => {
    return (0, moment_1.default)().format(format);
};
exports.getCurrentDate = getCurrentDate;
/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
const getDaysDiff = (startDate, endDate) => {
    const start = (0, moment_1.default)(startDate);
    const end = (0, moment_1.default)(endDate);
    return end.diff(start, 'days');
};
exports.getDaysDiff = getDaysDiff;
/**
 * 添加天数到日期
 * @param date 原始日期
 * @param days 添加的天数
 * @param format 返回格式，默认为YYYY-MM-DD
 * @returns 新的日期字符串
 */
const addDays = (date, days, format = 'YYYY-MM-DD') => {
    return (0, moment_1.default)(date).add(days, 'days').format(format);
};
exports.addDays = addDays;
/**
 * 检查对象是否为空
 * @param obj 要检查的对象
 * @returns 是否为空
 */
const isEmptyObject = (obj) => {
    return obj === null || obj === undefined || (Object.keys(obj).length === 0 && obj.constructor === Object);
};
exports.isEmptyObject = isEmptyObject;
/**
 * 过滤对象中的空值
 * @param obj 要过滤的对象
 * @returns 过滤后的对象
 */
const filterEmptyValues = (obj) => {
    const result = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            if (value !== null && value !== undefined && value !== '') {
                result[key] = value;
            }
        }
    }
    return result;
};
exports.filterEmptyValues = filterEmptyValues;
/**
 * 分页辅助函数
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 分页参数
 */
const getPagination = (page = 1, pageSize = 10) => {
    const limit = pageSize > 0 ? pageSize : 10;
    const offset = (page > 0 ? page - 1 : 0) * limit;
    return {
        limit,
        offset,
    };
};
exports.getPagination = getPagination;
/**
 * 构建分页结果
 * @param data 数据列表
 * @param total 总记录数
 * @param page 当前页码
 * @param pageSize 每页大小
 * @returns 分页结果
 */
const buildPaginationResult = (data, total, page, pageSize) => {
    return {
        list: data,
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize),
    };
};
exports.buildPaginationResult = buildPaginationResult;
/**
 * 将字符串日期转换为Date对象
 * @param dateStr 日期字符串
 * @param format 日期格式，默认为YYYY-MM-DD
 * @returns Date对象
 */
const parseDate = (dateStr, format = 'YYYY-MM-DD') => {
    return (0, moment_1.default)(dateStr, format).toDate();
};
exports.parseDate = parseDate;
/**
 * 获取默认图片URL
 * @param type 图片类型：'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'
 * @returns 默认图片URL
 */
const getDefaultImageUrl = (type) => {
    // 使用URL管理器
    return (0, urlManager_1.getDefaultImageUrl)(type);
};
exports.getDefaultImageUrl = getDefaultImageUrl;
/**
 * 处理图片URL，如果为空则返回默认图片
 * @param imageUrl 图片URL
 * @param defaultType 默认图片类型
 * @returns 处理后的图片URL
 */
const processImageUrl = (imageUrl, defaultType) => {
    // 使用URL管理器处理
    return (0, urlManager_1.processImageUrl)(imageUrl, defaultType);
};
exports.processImageUrl = processImageUrl;
// 生成随机用户头像URL
const getRandomUserAvatar = () => {
    return (0, urlManager_1.getRandomUserAvatarUrl)();
};
exports.getRandomUserAvatar = getRandomUserAvatar;
// 生成随机厨房头像URL
const getRandomKitchenAvatar = () => {
    return (0, urlManager_1.getRandomKitchenAvatarUrl)();
};
exports.getRandomKitchenAvatar = getRandomKitchenAvatar;
// 生成随机厨房背景图URL
const getRandomKitchenBackground = () => {
    return (0, urlManager_1.getRandomKitchenBackgroundUrl)();
};
exports.getRandomKitchenBackground = getRandomKitchenBackground;
exports.default = {
    generateUserId: exports.generateUserId,
    generateKitchenId: exports.generateKitchenId,
    generateOrderId: exports.generateOrderId,
    generateRandomString: exports.generateRandomString,
    generateRandomNumber: exports.generateRandomNumber,
    formatDateTime: exports.formatDateTime,
    getCurrentDateTime: exports.getCurrentDateTime,
    getCurrentDate: exports.getCurrentDate,
    getDaysDiff: exports.getDaysDiff,
    addDays: exports.addDays,
    isEmptyObject: exports.isEmptyObject,
    filterEmptyValues: exports.filterEmptyValues,
    getPagination: exports.getPagination,
    buildPaginationResult: exports.buildPaginationResult,
    parseDate: exports.parseDate,
    getDefaultImageUrl: exports.getDefaultImageUrl,
    processImageUrl: exports.processImageUrl,
    getRandomUserAvatar: exports.getRandomUserAvatar,
    getRandomKitchenAvatar: exports.getRandomKitchenAvatar,
    getRandomKitchenBackground: exports.getRandomKitchenBackground,
};
//# sourceMappingURL=helpers.js.map