/**
 * 智能图片组件 - 支持懒加载和版本控制缓存
 * 功能：自动处理图片加载状态，提供占位图和错误处理，支持懒加载
 * 版本控制缓存：只在图片真正更新时才重新下载
 */

// 版本控制缓存机制 - 永久缓存策略
const versionCache = new Map<string, {
  version: string,           // 图片版本号
  cacheTime: number,        // 缓存时间
  status: 'loading' | 'loaded' | 'error',
  etag?: string,            // ETag信息
  lastModified?: string     // 最后修改时间
}>();

// 缓存策略配置
const CACHE_STRATEGIES = {
  // 有版本号的图片永久缓存（应用生命周期内）
  VERSIONED: -1,
  // 静态图标缓存24小时
  STATIC_ICONS: 24 * 60 * 60 * 1000,
  // 菜品图片缓存12小时
  DISH_IMAGES: 12 * 60 * 60 * 1000,
  // 用户头像缓存6小时
  USER_AVATARS: 6 * 60 * 60 * 1000,
  // 背景图片缓存1小时
  BACKGROUNDS: 60 * 60 * 1000,
  // 默认缓存2小时
  DEFAULT: 2 * 60 * 60 * 1000
};

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图片地址
    src: {
      type: String,
      value: ''
    },
    // 是否启用懒加载
    lazy: {
      type: Boolean,
      value: false
    },
    // 懒加载延迟时间（ms）
    lazyDelay: {
      type: Number,
      value: 300
    },
    // 图片裁剪模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 宽度
    width: {
      type: String,
      value: '100%'
    },
    // 高度
    height: {
      type: String,
      value: '200rpx'
    },
    // 圆角
    borderRadius: {
      type: String,
      value: '0'
    },
    // 是否显示动画
    showAnimation: {
      type: Boolean,
      value: true
    },
    // 自定义占位图
    placeholder: {
      type: String,
      value: ''
    },
    // 错误占位图
    errorPlaceholder: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: true,     // 加载状态
    error: false,      // 错误状态
    loaded: false,     // 加载完成状态
    currentSrc: '',    // 当前加载的图片地址
    realSrc: '',       // 真实要加载的图片地址（懒加载用）
    adaptiveHeight: false,  // 是否自适应高度
    hasRetried: false  // 是否已经重试过（避免无限重试）
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      // 组件实例被创建
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      this.initComponent();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'src': function(newSrc: string) {
      if (newSrc !== this.data.currentSrc) {
        this.initComponent();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 解析图片版本信息
     */
    parseImageVersion(src: string): { cleanUrl: string, version: string, hasVersion: boolean } {
      if (!src) return { cleanUrl: '', version: '', hasVersion: false };
      
      // 检查URL中的版本参数
      const versionMatches = [
        src.match(/[?&]v=([^&]+)/),           // ?v=xxx 或 &v=xxx
        src.match(/[?&]version=([^&]+)/),     // ?version=xxx
        src.match(/[?&]t=([^&]+)/),           // ?t=xxx (时间戳)
        src.match(/[?&]_v=([^&]+)/)           // ?_v=xxx
      ];
      
      for (const match of versionMatches) {
        if (match) {
          return {
            cleanUrl: src.replace(match[0], ''),
            version: match[1],
            hasVersion: true
          };
        }
      }
      
      return { cleanUrl: src, version: '', hasVersion: false };
    },

    /**
     * 获取图片类型对应的缓存时间
     */
    getCacheTime(src: string, hasVersion: boolean): number {
      // 有版本号的图片永久缓存
      if (hasVersion) {
        return CACHE_STRATEGIES.VERSIONED;
      }
      
      // 根据URL判断图片类型
      if (src.includes('/static/images/icons/')) {
        return CACHE_STRATEGIES.STATIC_ICONS;
      }
      
      if (src.includes('/uploads/dish/') || src.includes('dish')) {
        return CACHE_STRATEGIES.DISH_IMAGES;
      }
      
      if (src.includes('/uploads/avatar/') || src.includes('avatar')) {
        return CACHE_STRATEGIES.USER_AVATARS;
      }
      
      if (src.includes('/uploads/background/') || src.includes('background')) {
        return CACHE_STRATEGIES.BACKGROUNDS;
      }
      
      return CACHE_STRATEGIES.DEFAULT;
    },

    /**
     * 检查缓存是否有效
     */
    isCacheValid(src: string, version: string, hasVersion: boolean): boolean {
      const cacheInfo = versionCache.get(src);
      
      if (!cacheInfo || cacheInfo.status !== 'loaded') {
        return false;
      }
      
      // 有版本号的图片，版本一致则永久有效
      if (hasVersion) {
        return cacheInfo.version === version;
      }
      
      // 无版本号的图片，检查缓存时间
      const cacheTime = this.getCacheTime(src, hasVersion);
      if (cacheTime === CACHE_STRATEGIES.VERSIONED) {
        return true; // 永久缓存
      }
      
      const now = Date.now();
      return (now - cacheInfo.cacheTime) < cacheTime;
    },

    /**
     * 清理过期缓存
     */
    cleanExpiredCache() {
      const now = Date.now();
      const entries = Array.from(versionCache.entries());
      
      for (const [url, info] of entries) {
        const { version, hasVersion } = this.parseImageVersion(url);
        const cacheTime = this.getCacheTime(url, hasVersion);
        
        // 永久缓存的不清理
        if (cacheTime === CACHE_STRATEGIES.VERSIONED) {
          continue;
        }
        
        // 检查是否过期
        if ((now - info.cacheTime) > cacheTime) {
          versionCache.delete(url);
          console.log('清理过期缓存:', url);
        }
      }
    },

    /**
     * 初始化组件
     */
    initComponent() {
      const src = this.properties.src;
      
      if (!src || src.trim() === '') {
        // 空src时不显示任何内容，包括占位符
        this.setData({
          loading: false,
          error: false,
          loaded: false,
          currentSrc: '',
          realSrc: '',
          adaptiveHeight: false
        });
        return;
      }

      // 清理过期缓存
      this.cleanExpiredCache();

      const trimmedSrc = src.trim();
      const { cleanUrl, version, hasVersion } = this.parseImageVersion(trimmedSrc);
      
      // 检查版本控制缓存
      if (this.isCacheValid(cleanUrl, version, hasVersion)) {
        // 缓存有效，直接使用
        this.setData({
          loading: false,
          error: false,
          loaded: true,
          currentSrc: trimmedSrc,
          realSrc: trimmedSrc,
          adaptiveHeight: this.shouldAdaptHeight()
        });
        console.log('使用版本控制缓存:', trimmedSrc);
        return;
      }

      // 需要加载新图片
      this.setData({
        loading: true,
        error: false,
        loaded: false,
        currentSrc: trimmedSrc,
        hasRetried: false
      });

      if (this.properties.lazy) {
        // 懒加载模式
        this.setData({
          realSrc: ''
        });
        this.checkIfInViewport();
      } else {
        // 立即加载
        this.setData({
          realSrc: trimmedSrc,
          adaptiveHeight: this.shouldAdaptHeight()
        });
      }
    },

    /**
     * 图片加载成功回调
     */
    onImageLoad(e: any) {
      console.log('图片加载成功:', this.properties.src);
      
      // 获取图片信息
      const detail = e.detail || {};
      const width = detail.width || 0;
      const height = detail.height || 0;
      
      this.setData({
        loading: false,
        error: false,
        loaded: true
      });
      
      // 更新版本控制缓存
      const originalSrc = this.properties.src.trim();
      const { cleanUrl, version, hasVersion } = this.parseImageVersion(originalSrc);
      
      versionCache.set(cleanUrl, {
        version: version,
        cacheTime: Date.now(),
        status: 'loaded'
      });
      
      // 触发成功事件
      this.triggerEvent('imageLoad', { 
        src: originalSrc, 
        width, 
        height 
      });
    },

    /**
     * 图片加载失败回调
     */
    onImageError(e: any) {
      console.log('图片加载失败:', this.properties.src, e);
      
      // 如果是第一次失败，尝试重新加载
      if (!this.data.hasRetried) {
        console.log('图片加载失败，尝试重试:', this.properties.src);
        
        this.setData({
          hasRetried: true
        });
        
        // 从缓存中移除失败的图片
        const originalSrc = this.properties.src.trim();
        const { cleanUrl } = this.parseImageVersion(originalSrc);
        versionCache.delete(cleanUrl);
        
        // 延迟500ms后重试，给网络一点恢复时间
        setTimeout(() => {
          // 强制重新加载（添加时间戳）
          const retryUrl = this.addCacheBuster(this.properties.src, true);
          this.setData({
            realSrc: retryUrl,
            loading: true,
            error: false
          });
          
          // 更新缓存状态
          const { cleanUrl, version } = this.parseImageVersion(originalSrc);
          versionCache.set(cleanUrl, {
            version: version,
            cacheTime: Date.now(),
            status: 'loading'
          });
        }, 500);
        
        return;
      }
      
      // 重试后仍然失败，显示错误状态
      this.setData({
        loading: false,
        error: true,
        loaded: false
      });
      
      // 更新版本控制缓存为错误状态
      const originalSrc = this.properties.src.trim();
      const { cleanUrl, version } = this.parseImageVersion(originalSrc);
      versionCache.set(cleanUrl, {
        version: version,
        cacheTime: Date.now(),
        status: 'error'
      });
      
      // 触发错误事件
      this.triggerEvent('imageError', { 
        src: originalSrc, 
        error: e 
      });
    },

    /**
     * 错误图片点击事件
     */
    onErrorTap(e: any) {
      // 点击错误图片时重新加载
      this.resetAndReload();
    },

    /**
     * 重置状态并重新加载
     */
    resetAndReload() {
      console.log('重置并重新加载图片:', this.properties.src);
      
      // 从缓存中移除当前图片
      const originalSrc = this.properties.src.trim();
      const { cleanUrl } = this.parseImageVersion(originalSrc);
      versionCache.delete(cleanUrl);
      
      const processedSrc = this.addCacheBuster(this.properties.src, true);
      
      this.setData({
        loading: true,
        error: false,
        loaded: false,
        realSrc: processedSrc,
        hasRetried: false
      });
    },

    /**
     * 重新加载图片
     */
    reload() {
      this.resetAndReload();
    },

    /**
     * 检查是否为背景图片URL
     */
    isBackgroundImage(src: string): boolean {
      return src.includes('/uploads/background/') || 
             src.includes('/uploads/avatar/') ||
             src.includes('backgroundUrl') ||
             src.includes('background');
    },

    /**
     * 处理图片URL，智能添加缓存破解参数
     * 只在真正需要更新时才添加时间戳
     */
    addCacheBuster(src: string, forceAdd: boolean = false): string {
      if (!src) return src;
      
      // 只在强制刷新时添加时间戳
      if (forceAdd) {
        const separator = src.includes('?') ? '&' : '?';
        const timestamp = Date.now();
        return `${src}${separator}t=${timestamp}`;
      }
      
      // 对于背景图片，检查是否有版本参数，如果没有则正常加载（允许缓存）
      if (this.isBackgroundImage(src)) {
        // 如果URL中已经包含版本参数（v=xxx），说明是有意更新，保持原样
        if (src.includes('v=') || src.includes('version=')) {
          return src;
        }
        // 否则正常加载，允许缓存
        return src;
      }
      
      // 对于普通图片（如菜品图片），直接返回原URL，允许正常缓存
      return src;
    },

    /**
     * 判断是否需要自适应高度
     */
    shouldAdaptHeight(): boolean {
      return this.properties.mode === 'widthFix';
    },

    /**
     * 检查图片是否在视口中（懒加载用）
     */
    checkIfInViewport() {
      if (!this.properties.lazy) return;
      
      // 简化实现：延迟指定时间后开始加载
      setTimeout(() => {
        if (this.data.currentSrc && !this.data.realSrc) {
          this.setData({
            loading: true,
            realSrc: this.data.currentSrc
          });
        }
      }, this.properties.lazyDelay);
    }
  }
}); 