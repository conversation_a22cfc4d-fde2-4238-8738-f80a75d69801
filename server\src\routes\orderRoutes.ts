/**
 * 订单模块路由
 * 处理订单相关的API路由
 */
import express from 'express';
import { submitOrder, getOrderList, getOrderDetail, getPublicOrderDetail, cancelOrder, rejectOrder, acceptOrder, startCooking, completeOrder, cancelCooking, cancelAcceptedOrder } from '../controllers/orderController';
import { verifyToken } from '../middlewares/auth';

const router = express.Router();

// 提交订单
router.post('/submit', verifyToken, submitOrder);

// 获取订单列表
router.get('/list', verifyToken, getOrderList);

// 获取订单详情
router.get('/detail', verifyToken, getOrderDetail);

// 获取订单详情（公开访问，用于分享）
router.get('/public/:id', getPublicOrderDetail);

// 取消订单
router.post('/cancel', verifyToken, cancelOrder);

// 拒绝订单
router.post('/reject', verifyToken, rejectOrder);

// 接单
router.post('/accept', verifyToken, acceptOrder);

// 开始烹饪
router.post('/startCooking', verifyToken, startCooking);

// 完成订单
router.post('/complete', verifyToken, completeOrder);

// 取消烹饪
router.post('/cancelCooking', verifyToken, cancelCooking);

// 取消已接单订单
router.post('/cancelAcceptedOrder', verifyToken, cancelAcceptedOrder);

export default router;
