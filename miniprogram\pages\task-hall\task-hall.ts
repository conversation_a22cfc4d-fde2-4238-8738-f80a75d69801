import { getUserInfo, getUserSignInData, signIn, watchAd, compensateSignIn } from '../../api/userApi'

// 定义签到数据项的接口
interface SignInDataItem {
  date: string;
  day: number;
  weekday: string;
  signed: boolean;
  canSignIn: boolean;
  isToday: boolean;
}

Page({
  data: {
    // 用户信息
    userInfo: {},
    userId: '',
    isLoggedIn: false,

    // 背景设置
    navBgStyle: '',
    textColorStyle: '',

    // 签到数据
    signInData: [] as SignInDataItem[],
    signInCount: 0,
    continuousSignIn: 0,
    signInRewards: 0,
    todaySigned: false,

    // 观看广告次数
    adWatchCount: 0,

    // 分享文案
    shareText: '我发现了一款超棒的点菜小程序！厨房管理超便捷，菜品丰富多样，还能自定义分类和排序。推荐给大家尝试，快来体验吧！点击链接或搜索"餐厅点菜小程序"立即体验！',

    // 弹窗显示控制
    showSignInDetail: false,
    showShareModal: false,
    showReviewModal: false
  },

  onLoad() {
    // 加载背景设置
    this.loadBackgroundSettings()

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 如果已登录，获取最新数据
    if (this.data.isLoggedIn) {
      this.fetchSignInData()
      this.fetchAdWatchCount()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo,
        userId: userInfo.userId,
        isLoggedIn: true
      })

      // 获取签到数据和广告观看次数
      this.fetchSignInData()
      this.fetchAdWatchCount()
    } else {
      this.setData({
        isLoggedIn: false
      })
      // 移除强制登录逻辑，允许用户浏览页面
    }
  },

  // 显示需要登录的提示
  showNeedLoginToast() {
    wx.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 1500
    })
  },

  // 获取签到数据
  async fetchSignInData() {
    try {
      // 调用获取签到数据API
      const result = await getUserSignInData()

      if (result.error === 0) {
        this.setData({
          signInData: result.body.signInData,
          signInCount: result.body.signInCount,
          continuousSignIn: result.body.continuousSignIn,
          signInRewards: result.body.signInRewards,
          todaySigned: result.body.todaySigned
        })
      } else {
        wx.showToast({
          title: result.message || '获取签到数据失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取签到数据失败', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  // 获取广告观看次数
  fetchAdWatchCount() {
    // 从本地存储获取今日广告观看次数
    const today = this.formatDate(new Date())
    const adWatchData = wx.getStorageSync('adWatchData') || {}

    // 如果不是今天的记录，重置次数
    if (adWatchData.date !== today) {
      const newAdWatchData = {
        date: today,
        count: 0
      }
      wx.setStorageSync('adWatchData', newAdWatchData)
      this.setData({
        adWatchCount: 0
      })
    } else {
      this.setData({
        adWatchCount: adWatchData.count || 0
      })
    }
  },

  // 签到功能
  async signIn() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    try {
      // 调用签到API
      const result = await signIn()

      if (result.error === 0) {
        // 更新签到数据 - 使用后端返回的完整数据
        this.setData({
          signInData: result.body.signInData,
          signInCount: result.body.signInCount,
          continuousSignIn: result.body.continuousSignIn,
          signInRewards: result.body.signInRewards,
          todaySigned: result.body.todaySigned
        })

        // 同时更新用户的大米数量
        const userInfo = wx.getStorageSync('userInfo')
        if (userInfo && result.body.coins) {
          userInfo.coins = result.body.coins
          wx.setStorageSync('userInfo', userInfo)
          this.setData({
            userInfo
          })
        }

        // 显示toast消息
        wx.showToast({
          title: `签到成功 +${result.body.reward || 20}大米`,
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.message || '签到失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('签到失败', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  // 观看广告
  async watchAd() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    if (this.data.adWatchCount >= 15) {
      wx.showToast({
        title: '今日观看次数已达上限',
        icon: 'none'
      })
      return
    }

    try {
      // 创建激励视频广告实例
      const videoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-id-here' // 替换为实际的广告单元ID
      })

      // 监听加载事件
      videoAd.onLoad(() => {
        console.log('广告加载成功')
      })

      // 监听错误事件
      videoAd.onError(err => {
        console.error('广告加载失败', err)
        wx.showToast({
          title: '广告加载失败，请重试',
          icon: 'none'
        })
      })

      // 监听关闭事件
      videoAd.onClose(async res => {
        // 用户完整观看广告
        if (res && res.isEnded) {
          // 调用观看广告API
          const result = await watchAd()

          if (result.error === 0) {
            // 更新广告观看次数
            const today = this.formatDate(new Date())
            const adWatchData = {
              date: today,
              count: this.data.adWatchCount + 1
            }
            wx.setStorageSync('adWatchData', adWatchData)

            // 更新界面
            this.setData({
              adWatchCount: adWatchData.count
            })

            // 更新用户大米数量
            const userInfo = wx.getStorageSync('userInfo')
            if (userInfo) {
              userInfo.coins = (userInfo.coins || 0) + result.body.reward
              wx.setStorageSync('userInfo', userInfo)
              this.setData({
                userInfo
              })
            }

            wx.showToast({
              title: `获得${result.body.reward}大米奖励`,
              icon: 'success'
            })
          } else {
            wx.showToast({
              title: result.message || '奖励发放失败',
              icon: 'none'
            })
          }
        } else {
          wx.showToast({
            title: '需完整观看广告才能获得奖励',
            icon: 'none'
          })
        }
      })

      // 显示广告
      videoAd.show().catch(err => {
        videoAd.load().then(() => videoAd.show()).catch(err => {
          console.error('广告显示失败', err)
          wx.showToast({
            title: '广告显示失败，请重试',
            icon: 'none'
          })
        })
      })
    } catch (error) {
      console.error('观看广告失败', error)
      wx.showToast({
        title: '当前环境不支持广告功能',
        icon: 'none'
      })
    }
  },

  // 显示签到详情
  showSignInDetail() {
    this.setData({
      showSignInDetail: true
    })
  },

  // 关闭签到详情
  closeSignInDetail() {
    this.setData({
      showSignInDetail: false
    })
  },

  // 显示分享弹窗
  showShareModal() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    this.setData({
      showShareModal: true
    })
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({
      showShareModal: false
    })
  },

  // 复制分享文案
  copyShareText() {
    wx.setClipboardData({
      data: this.data.shareText,
      success: () => {
        wx.showToast({
          title: '文案已复制',
          icon: 'success'
        })
        this.closeShareModal()
      }
    })
  },

  // 显示评价弹窗
  showReviewModal() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    this.setData({
      showReviewModal: true
    })
  },

  // 关闭评价弹窗
  closeReviewModal() {
    this.setData({
      showReviewModal: false
    })
  },

  // 前往评价
  goToReview() {
    // 关闭弹窗
    this.closeReviewModal()

    // 跳转到小程序评分页面
    wx.showToast({
      title: '请在小程序界面长按，选择"进入小程序评价"',
      icon: 'none',
      duration: 3000
    })
  },

  // 复制用户ID
  copyUserId() {
    if (!this.data.userId) {
      this.showNeedLoginToast()
      return
    }

    wx.setClipboardData({
      data: this.data.userId,
      success: () => {
        wx.showToast({
          title: 'ID已复制',
          icon: 'success'
        })
      }
    })
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)' // 默认为第一个渐变

    this.setData({
      navBgStyle
    })

    // 根据背景颜色计算合适的文字颜色
    this.updateTextColorByBackground(navBgStyle)

    // 监听全局背景设置变化
    this.listenForBackgroundChanges()
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景颜色值
    let bgColor = '#FFFFFF' // 默认白色背景

    if (background) {
      // 尝试提取渐变的起始颜色
      const match = background.match(/linear-gradient\(to\s+\w+,\s+(#[A-Fa-f0-9]+),\s+/)
      if (match && match.length > 1) {
        bgColor = match[1]
      }
    }

    // 计算颜色亮度 (简化的亮度计算，仅用于本例)
    const r = parseInt(bgColor.substring(1, 3), 16)
    const g = parseInt(bgColor.substring(3, 5), 16)
    const b = parseInt(bgColor.substring(5, 7), 16)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF'
    const textColorStyle = `--text-color: ${textColor};`

    this.setData({ textColorStyle })

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    })
  },

  // 监听全局背景设置变化
  listenForBackgroundChanges() {
    // 简化实现，使用轮询方式检查本地存储的变化
    setInterval(() => {
      const navBgStyle = wx.getStorageSync('navBgStyle')
      if (navBgStyle && navBgStyle !== this.data.navBgStyle) {
        this.setData({ navBgStyle })
        this.updateTextColorByBackground(navBgStyle)
      }
    }, 1000) // 每秒检查一次
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      Promise.all([
        this.fetchSignInData(),
        this.fetchAdWatchCount()
      ]).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
      this.showNeedLoginToast()
    }
  },

  // 日期格式化为 YYYY-MM-DD
  formatDate(date: Date): string {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 判断是否是未来日期
  isFutureDate(date: Date): boolean {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    date.setHours(0, 0, 0, 0)
    return date > today
  },

  // 处理日期点击
  handleDateClick(e: WechatMiniprogram.BaseEvent) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.signInData[index];

    // 如果是已签到的日期，不做任何操作
    if (item.signed) {
      wx.showToast({
        title: '已完成签到',
        icon: 'success'
      });
      return;
    }

    // 如果是可补签的日期，直接调用广告补签
    if (item.canSignIn) {
      this.compensateSignIn(index);
      return;
    }

    // 如果是当天且未签到，执行签到
    if (item.isToday && !item.signed) {
      this.signIn();
    }
  },

  // 执行补签 - 通过观看广告完成
  compensateSignIn(index: number) {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast();
      return;
    }

    const signInItem = this.data.signInData[index];
    const dateToSign = signInItem.date;

    wx.showLoading({
      title: '加载广告中...'
    });

    try {
      // 创建激励视频广告实例
      const videoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-id-here' // 替换为实际的广告单元ID
      });

      // 监听加载事件
      videoAd.onLoad(() => {
        wx.hideLoading();
        console.log('广告加载成功');
      });

      // 监听错误事件
      videoAd.onError(err => {
        wx.hideLoading();
        console.error('广告加载失败', err);
        wx.showToast({
          title: '广告加载失败，请重试',
          icon: 'none'
        });
      });

      // 监听关闭事件
      videoAd.onClose(async res => {
        // 用户完整观看广告
        if (res && res.isEnded) {
          try {
            wx.showLoading({
              title: '补签中...'
            })

            // 调用真实的补签API
            const result = await compensateSignIn(dateToSign)

            wx.hideLoading()

            if (result.error === 0) {
              // 更新签到数据 - 使用后端返回的完整数据
              this.setData({
                signInData: result.body.signInData,
                signInCount: result.body.signInCount,
                continuousSignIn: result.body.continuousSignIn,
                signInRewards: result.body.signInRewards,
                todaySigned: result.body.todaySigned
              })

              // 更新用户的大米数量
              const userInfo = wx.getStorageSync('userInfo')
              if (userInfo && result.body.coins) {
                userInfo.coins = result.body.coins
                wx.setStorageSync('userInfo', userInfo)
                this.setData({
                  userInfo
                })
              }

              wx.showToast({
                title: `补签成功 +${result.body.reward || 20}大米`,
                icon: 'success'
              })
            } else {
              wx.showToast({
                title: result.message || '补签失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('补签失败', error)
            wx.showToast({
              title: '网络异常，请重试',
              icon: 'none'
            })
          }
        } else {
          wx.showToast({
            title: '需完整观看广告才能补签',
            icon: 'none'
          })
        }
      });

      // 显示广告
      videoAd.show().catch(err => {
        wx.hideLoading();
        videoAd.load().then(() => videoAd.show()).catch(err => {
          console.error('广告显示失败', err);
          wx.showToast({
            title: '广告显示失败，请重试',
            icon: 'none'
          });
        });
      });
    } catch (error) {
      wx.hideLoading();
      console.error('补签失败', error);
      wx.showToast({
        title: '当前环境不支持广告功能',
        icon: 'none'
      });
    }
  },
})