/**
 * 用户模型
 * 存储用户基本信息
 */
import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/database';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import config from '../config/config';

// 用户属性接口
interface UserAttributes {
  id: number;
  open_id: string;
  nick_name: string;
  avatar_url: string;
  gender: number;
  coins: number;
  likes: number;
  dishes: number;
  kitchens: number;
  token: string;
  created_at: Date;
  updated_at: Date;
}

// 创建时可选属性
interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'likes' | 'dishes' | 'kitchens' | 'token' | 'created_at' | 'updated_at'> {}

// 用户模型类
class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number;
  public open_id!: string;
  public nick_name!: string;
  public avatar_url!: string;
  public gender!: number;
  public coins!: number;
  public likes!: number;
  public dishes!: number;
  public kitchens!: number;
  public token!: string;
  public created_at!: Date;
  public updated_at!: Date;

  // 设置令牌
  public setToken(token: string): void {
    this.token = token;
  }

  // 转换为JSON时排除敏感字段
  public toJSON(): object {
    const values: any = Object.assign({}, this.get());
    delete values.token;
    delete values.open_id;
    return values;
  }
}

// 初始化用户模型
User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      comment: '用户ID',
    },
    open_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '微信OpenID',
    },
    nick_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '昵称',
    },
    avatar_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '头像URL',
    },
    gender: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0,
      comment: '性别(0:未知,1:男,2:女)',
    },
    coins: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1000, // 默认1000大米（实际值由系统设置控制）
      comment: '大米数量',
    },
    likes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '获赞数量',
    },
    dishes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '添加的菜品数量',
    },
    kitchens: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '厨房数量',
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '用户令牌',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_open_id',
        fields: ['open_id'],
      },
    ],
  }
);

export default User;
