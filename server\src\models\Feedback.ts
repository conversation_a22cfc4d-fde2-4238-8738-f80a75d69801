/**
 * 反馈模型
 * 存储用户提交的问题反馈和功能建议
 */
import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';

// 反馈属性接口
interface FeedbackAttributes {
  id: string;
  user_id: string;
  type: 'bug' | 'feature' | 'other';
  description: string;
  images?: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  reply?: string;
  device_info?: string;
  created_at: Date;
  updated_at: Date;
  reply_at?: Date;
}

// 创建反馈时的可选属性
interface FeedbackCreationAttributes extends Optional<FeedbackAttributes, 'id' | 'created_at' | 'updated_at' | 'status' | 'reply' | 'reply_at'> {}

// 反馈模型类
class Feedback extends Model<FeedbackAttributes, FeedbackCreationAttributes> implements FeedbackAttributes {
  public id!: string;
  public user_id!: string;
  public type!: 'bug' | 'feature' | 'other';
  public description!: string;
  public images?: string;
  public status!: 'pending' | 'processing' | 'completed' | 'rejected';
  public reply?: string;
  public device_info?: string;
  public created_at!: Date;
  public updated_at!: Date;
  public reply_at?: Date;

  // 关联属性
  public readonly user?: any;
}

// 初始化反馈模型
Feedback.init(
  {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      defaultValue: () => {
        // 生成反馈ID: FB + 时间戳 + 随机数
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `FB${timestamp}${random}`;
      },
    },
    user_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '提交反馈的用户ID',
    },
    type: {
      type: DataTypes.ENUM('bug', 'feature', 'other'),
      allowNull: false,
      comment: '反馈类型：bug-问题反馈，feature-功能建议，other-其他',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '问题描述',
    },
    images: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '图片URL列表，JSON格式存储',
    },
    status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
      comment: '处理状态：pending-待处理，processing-处理中，completed-已完成，rejected-已拒绝',
    },
    reply: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '管理员回复',
    },
    device_info: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '设备信息，JSON格式存储',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
    reply_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '回复时间',
    },
  },
  {
    sequelize,
    modelName: 'Feedback',
    tableName: 'feedbacks',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '用户反馈表',
    indexes: [
      {
        fields: ['user_id'],
        name: 'idx_feedback_user_id',
      },
      {
        fields: ['type'],
        name: 'idx_feedback_type',
      },
      {
        fields: ['status'],
        name: 'idx_feedback_status',
      },
      {
        fields: ['created_at'],
        name: 'idx_feedback_created_at',
      },
    ],
  }
);

export default Feedback; 