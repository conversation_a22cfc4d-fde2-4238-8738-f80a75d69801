<!-- 背景设置组件 -->
<modal-dialog 
  visible="{{visible}}" 
  title="背景设置" 
  showCancel="{{true}}" 
  cancelText="取消" 
  confirmText="保存"
  themeColor="#FF6B35"
  bind:close="onClose"
  bind:cancel="onClose"
  bind:confirm="onSave"
>
  <!-- 标签页切换 -->
  <view class="tabs fade-in-up" style="animation-delay: 0.1s;">
    <view class="tab-item {{activeTab === 'shop' ? 'active' : ''}}" bindtap="switchTab" data-tab="shop">
      店铺背景图
    </view>
    <view class="tab-item {{activeTab === 'nav' ? 'active' : ''}}" bindtap="switchTab" data-tab="nav">
      导航栏背景
    </view>
  </view>
  
  <!-- 会员功能锁定层 -->
  <view class="vip-lock-container" wx:if="{{!isVip}}">
    <view class="vip-lock-overlay"></view>
    <view class="vip-lock-text">兑换会员后解锁</view>
  </view>
  
  <!-- 店铺背景图设置 -->
  <view class="tab-content {{!isVip ? 'locked' : ''}}" hidden="{{activeTab !== 'shop'}}">
    <view class="bg-preview fade-in-up" style="animation-delay: 0.2s;">
      <smart-image 
        class="preview-image background-preview-image" 
        src="{{shopBgPreview || defaultShopBg}}" 
        mode="aspectFill"
        width="100%"
        height="100%"
        border-radius="8"
      />
      <view class="preview-label">当前背景预览</view>
    </view>
    
    <view class="upload-section fade-in-up" style="animation-delay: 0.3s;">
      <button class="upload-btn" bindtap="chooseShopBg">
        <text class="upload-text">选择图片</text>
      </button>
    </view>
  </view>
  
  <!-- 导航栏背景设置 -->
  <view class="tab-content {{!isVip ? 'locked' : ''}}" hidden="{{activeTab !== 'nav'}}">
    <!-- 预设渐变背景选择 -->
    <view class="gradient-grid fade-in-up" style="animation-delay: 0.2s;">
      <view 
        wx:for="{{gradients}}" 
        wx:key="index" 
        class="gradient-item {{navBgIndex === index ? 'selected' : ''}}" 
        style="background: {{item}}"
        bindtap="selectGradient"
        data-index="{{index}}"
      ></view>
    </view>
    
    <!-- 自定义颜色选择器 -->
    <view class="custom-section fade-in-up" style="animation-delay: 0.3s;">
      <view class="section-title">自定义颜色</view>
      
      <view class="color-inputs">
        <view class="color-input-group">
          <text class="color-label">开始颜色</text>
          <input class="color-input" type="text" value="{{startColor}}" placeholder="#FFFFFF" bindinput="onStartColorInput" />
          <view class="color-preview" style="background-color: {{startColor}}"></view>
        </view>
        
        <view class="color-input-group">
          <text class="color-label">结束颜色</text>
          <input class="color-input" type="text" value="{{endColor}}" placeholder="#000000" bindinput="onEndColorInput" />
          <view class="color-preview" style="background-color: {{endColor}}"></view>
        </view>
      </view>
      
      <!-- 自定义渐变预览 -->
      <view class="custom-preview fade-in-up" style="animation-delay: 0.4s;">
        <view class="custom-gradient {{navBgIndex === -1 ? 'selected' : ''}}" style="background: linear-gradient(to bottom, {{startColor || '#FFFFFF'}}, {{endColor || '#FF6B35'}})" bindtap="onTapCustomGradient"></view>
        <view class="preview-label">自定义渐变预览</view>
      </view>
    </view>
  </view>
</modal-dialog> 