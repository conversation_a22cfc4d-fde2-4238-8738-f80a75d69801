"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 系统设置种子数据
 * 创建默认的系统设置
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
/**
 * 创建系统设置种子数据
 */
function seedSystemSettings() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            logger_1.default.info('开始创建系统设置种子数据...');
            // 默认系统设置
            const defaultSettings = [
                {
                    key: 'comment_enabled',
                    value: 'true',
                    type: 'boolean',
                    description: '是否开启评论功能'
                },
                {
                    key: 'registration_enabled',
                    value: 'true',
                    type: 'boolean',
                    description: '是否开启用户注册'
                },
                {
                    key: 'maintenance_mode',
                    value: 'false',
                    type: 'boolean',
                    description: '是否开启维护模式'
                },
                {
                    key: 'max_upload_size',
                    value: '10',
                    type: 'number',
                    description: '最大上传文件大小(MB)'
                },
                {
                    key: 'site_title',
                    value: '电子菜单助手',
                    type: 'string',
                    description: '网站标题'
                },
                {
                    key: 'site_description',
                    value: '智能厨房管理系统',
                    type: 'string',
                    description: '网站描述'
                },
                {
                    key: 'contact_email',
                    value: '<EMAIL>',
                    type: 'string',
                    description: '联系邮箱'
                },
                {
                    key: 'default_coins',
                    value: '1000',
                    type: 'number',
                    description: '新用户默认大米数量'
                },
                {
                    key: 'daily_signin_coins',
                    value: '10',
                    type: 'number',
                    description: '每日签到奖励大米数量'
                },
                {
                    key: 'ad_enabled',
                    value: 'true',
                    type: 'boolean',
                    description: '是否开启广告功能'
                }
            ];
            // 检查是否已存在设置，如果存在则跳过
            for (const setting of defaultSettings) {
                const existingSetting = yield models_1.SystemSetting.findOne({
                    where: { key: setting.key }
                });
                if (existingSetting) {
                    logger_1.default.info(`系统设置 ${setting.key} 已存在，跳过创建`);
                    continue;
                }
                yield models_1.SystemSetting.create(setting);
                logger_1.default.info(`创建系统设置: ${setting.key} = ${setting.value} (${setting.type})`);
            }
            logger_1.default.info(`系统设置种子数据创建完成，共处理 ${defaultSettings.length} 个设置`);
        }
        catch (error) {
            logger_1.default.error('创建系统设置种子数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建系统设置
if (require.main === module) {
    seedSystemSettings()
        .then(() => {
        logger_1.default.info('创建系统设置种子数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建系统设置种子数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedSystemSettings;
//# sourceMappingURL=seedSystemSettings.js.map