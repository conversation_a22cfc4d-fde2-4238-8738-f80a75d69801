"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeInput = exports.filterXss = exports.filterSqlInjection = exports.filterSpecialChars = exports.validateEmail = exports.validatePhone = exports.validateObject = exports.validateArray = exports.validateId = exports.validateDate = exports.validateUrl = exports.validateEnum = exports.validateNumberRange = exports.validateStringLength = void 0;
/**
 * 参数验证工具
 * 提供各种参数验证函数
 */
const error_1 = require("../middlewares/error");
const response_1 = require("./response");
/**
 * 验证字符串长度
 * @param value 要验证的字符串
 * @param min 最小长度
 * @param max 最大长度
 * @param fieldName 字段名称
 * @throws BusinessError 如果验证失败
 */
const validateStringLength = (value, min, max, fieldName) => {
    if (!value) {
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    if (value.length < min || value.length > max) {
        throw new error_1.BusinessError(`${fieldName}长度必须在${min}到${max}个字符之间`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateStringLength = validateStringLength;
/**
 * 验证数值范围
 * @param value 要验证的数值
 * @param min 最小值
 * @param max 最大值
 * @param fieldName 字段名称
 * @throws BusinessError 如果验证失败
 */
const validateNumberRange = (value, min, max, fieldName) => {
    if (value === undefined || value === null) {
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    if (value < min || value > max) {
        throw new error_1.BusinessError(`${fieldName}必须在${min}到${max}之间`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateNumberRange = validateNumberRange;
/**
 * 验证枚举值
 * @param value 要验证的值
 * @param allowedValues 允许的值数组
 * @param fieldName 字段名称
 * @throws BusinessError 如果验证失败
 */
const validateEnum = (value, allowedValues, fieldName) => {
    if (!value) {
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    if (!allowedValues.includes(value)) {
        throw new error_1.BusinessError(`${fieldName}必须是以下值之一: ${allowedValues.join(', ')}`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateEnum = validateEnum;
/**
 * 验证URL格式
 * @param value 要验证的URL
 * @param fieldName 字段名称
 * @param allowEmpty 是否允许为空
 * @throws BusinessError 如果验证失败
 */
const validateUrl = (value, fieldName, allowEmpty = false) => {
    if (!value) {
        if (allowEmpty) {
            return;
        }
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    try {
        // 简单的URL格式验证
        const url = new URL(value);
        if (!url.protocol || !url.host) {
            throw new Error('Invalid URL');
        }
    }
    catch (error) {
        throw new error_1.BusinessError(`${fieldName}格式不正确`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateUrl = validateUrl;
/**
 * 验证日期格式
 * @param value 要验证的日期字符串
 * @param fieldName 字段名称
 * @param allowEmpty 是否允许为空
 * @throws BusinessError 如果验证失败
 */
const validateDate = (value, fieldName, allowEmpty = false) => {
    if (!value) {
        if (allowEmpty) {
            return;
        }
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    const date = new Date(value);
    if (isNaN(date.getTime())) {
        throw new error_1.BusinessError(`${fieldName}格式不正确`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateDate = validateDate;
/**
 * 验证ID格式
 * @param value 要验证的ID
 * @param fieldName 字段名称
 * @throws BusinessError 如果验证失败
 */
const validateId = (value, fieldName) => {
    if (value === undefined || value === null || value === '') {
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    if (typeof value === 'string') {
        // 如果是字符串ID，检查是否只包含字母和数字
        if (!/^[a-zA-Z0-9]+$/.test(value)) {
            throw new error_1.BusinessError(`${fieldName}格式不正确`, response_1.ResponseCode.VALIDATION);
        }
    }
    else if (typeof value === 'number') {
        // 如果是数字ID，检查是否为正整数
        if (!Number.isInteger(value) || value <= 0) {
            throw new error_1.BusinessError(`${fieldName}必须是正整数`, response_1.ResponseCode.VALIDATION);
        }
    }
    else {
        throw new error_1.BusinessError(`${fieldName}类型不正确`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateId = validateId;
/**
 * 验证数组
 * @param value 要验证的数组
 * @param fieldName 字段名称
 * @param minLength 最小长度
 * @param maxLength 最大长度
 * @throws BusinessError 如果验证失败
 */
const validateArray = (value, fieldName, minLength = 0, maxLength = Infinity) => {
    if (!Array.isArray(value)) {
        throw new error_1.BusinessError(`${fieldName}必须是数组`, response_1.ResponseCode.VALIDATION);
    }
    if (value.length < minLength) {
        throw new error_1.BusinessError(`${fieldName}至少需要${minLength}个元素`, response_1.ResponseCode.VALIDATION);
    }
    if (value.length > maxLength) {
        throw new error_1.BusinessError(`${fieldName}最多只能有${maxLength}个元素`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateArray = validateArray;
/**
 * 验证对象
 * @param value 要验证的对象
 * @param fieldName 字段名称
 * @param requiredFields 必需的字段数组
 * @throws BusinessError 如果验证失败
 */
const validateObject = (value, fieldName, requiredFields = []) => {
    if (!value || typeof value !== 'object' || Array.isArray(value)) {
        throw new error_1.BusinessError(`${fieldName}必须是对象`, response_1.ResponseCode.VALIDATION);
    }
    for (const field of requiredFields) {
        if (value[field] === undefined || value[field] === null) {
            throw new error_1.BusinessError(`${fieldName}中的${field}字段不能为空`, response_1.ResponseCode.VALIDATION);
        }
    }
};
exports.validateObject = validateObject;
/**
 * 验证手机号格式
 * @param value 要验证的手机号
 * @param fieldName 字段名称
 * @param allowEmpty 是否允许为空
 * @throws BusinessError 如果验证失败
 */
const validatePhone = (value, fieldName, allowEmpty = false) => {
    if (!value) {
        if (allowEmpty) {
            return;
        }
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    // 中国大陆手机号格式验证
    if (!/^1[3-9]\d{9}$/.test(value)) {
        throw new error_1.BusinessError(`${fieldName}格式不正确`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validatePhone = validatePhone;
/**
 * 验证邮箱格式
 * @param value 要验证的邮箱
 * @param fieldName 字段名称
 * @param allowEmpty 是否允许为空
 * @throws BusinessError 如果验证失败
 */
const validateEmail = (value, fieldName, allowEmpty = false) => {
    if (!value) {
        if (allowEmpty) {
            return;
        }
        throw new error_1.BusinessError(`${fieldName}不能为空`, response_1.ResponseCode.VALIDATION);
    }
    // 邮箱格式验证
    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
        throw new error_1.BusinessError(`${fieldName}格式不正确`, response_1.ResponseCode.VALIDATION);
    }
};
exports.validateEmail = validateEmail;
/**
 * 过滤特殊字符
 * @param value 要过滤的字符串
 * @returns 过滤后的字符串
 */
const filterSpecialChars = (value) => {
    if (!value) {
        return value;
    }
    // 过滤特殊字符，只保留字母、数字、中文和基本标点
    return value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s.,?!，。？！、；：""''（）()]/g, '');
};
exports.filterSpecialChars = filterSpecialChars;
/**
 * 过滤SQL注入字符
 * @param value 要过滤的字符串
 * @returns 过滤后的字符串
 */
const filterSqlInjection = (value) => {
    if (!value) {
        return value;
    }
    // 过滤可能的SQL注入字符
    return value.replace(/['";\\%]/g, '');
};
exports.filterSqlInjection = filterSqlInjection;
/**
 * 过滤XSS字符
 * @param value 要过滤的字符串
 * @returns 过滤后的字符串
 */
const filterXss = (value) => {
    if (!value) {
        return value;
    }
    // 过滤可能的XSS字符
    return value
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/`/g, '&#96;');
};
exports.filterXss = filterXss;
/**
 * 安全过滤输入
 * @param value 要过滤的字符串
 * @returns 过滤后的字符串
 */
const sanitizeInput = (value) => {
    if (!value) {
        return value;
    }
    // 组合过滤
    return (0, exports.filterXss)((0, exports.filterSqlInjection)(value));
};
exports.sanitizeInput = sanitizeInput;
exports.default = {
    validateStringLength: exports.validateStringLength,
    validateNumberRange: exports.validateNumberRange,
    validateEnum: exports.validateEnum,
    validateUrl: exports.validateUrl,
    validateDate: exports.validateDate,
    validateId: exports.validateId,
    validateArray: exports.validateArray,
    validateObject: exports.validateObject,
    validatePhone: exports.validatePhone,
    validateEmail: exports.validateEmail,
    filterSpecialChars: exports.filterSpecialChars,
    filterSqlInjection: exports.filterSqlInjection,
    filterXss: exports.filterXss,
    sanitizeInput: exports.sanitizeInput,
};
//# sourceMappingURL=validator.js.map