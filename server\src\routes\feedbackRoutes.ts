import { Router } from 'express';
import { verifyToken } from '../middlewares/auth';
import multer from 'multer';
import path from 'path';
import config from '../config/config';

const router = Router();

// 配置图片上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(process.cwd(), config.upload.dir, 'feedback'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'feedback-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只支持图片文件格式 (jpeg, jpg, png, gif)'));
    }
  }
});

// 导入控制器方法 - 移到这里避免循环依赖
import { 
  submitFeedback, 
  uploadImage, 
  getFeedbackList, 
  updateFeedbackStatus 
} from '../controllers/feedbackController';

// 提交反馈
router.post('/submit', verifyToken, submitFeedback);

// 上传反馈图片
router.post('/upload-image', verifyToken, upload.single('image'), uploadImage);

// 获取反馈列表（管理员用）
router.post('/list', verifyToken, getFeedbackList);

// 更新反馈状态（管理员用）
router.post('/update-status', verifyToken, updateFeedbackStatus);

export default router; 