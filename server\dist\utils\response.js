"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.businessError = exports.validationError = exports.notFound = exports.forbidden = exports.unauthorized = exports.error = exports.success = exports.ResponseCode = void 0;
// 响应状态码
var ResponseCode;
(function (ResponseCode) {
    ResponseCode[ResponseCode["SUCCESS"] = 0] = "SUCCESS";
    ResponseCode[ResponseCode["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    ResponseCode[ResponseCode["FORBIDDEN"] = 403] = "FORBIDDEN";
    ResponseCode[ResponseCode["NOT_FOUND"] = 404] = "NOT_FOUND";
    ResponseCode[ResponseCode["CONFLICT"] = 409] = "CONFLICT";
    ResponseCode[ResponseCode["VALIDATION"] = 422] = "VALIDATION";
    ResponseCode[ResponseCode["SERVER_ERROR"] = 500] = "SERVER_ERROR";
    ResponseCode[ResponseCode["BUSINESS_ERROR"] = 1000] = "BUSINESS_ERROR";
})(ResponseCode || (exports.ResponseCode = ResponseCode = {}));
/**
 * 成功响应
 * @param res Express响应对象
 * @param data 响应数据
 * @param message 响应消息
 */
const success = (res, data = {}, message = '') => {
    const response = {
        error: ResponseCode.SUCCESS,
        body: data,
        message,
    };
    res.status(200).json(response);
};
exports.success = success;
/**
 * 错误响应
 * @param res Express响应对象
 * @param code 错误码
 * @param message 错误消息
 * @param data 错误数据
 */
const error = (res, code = ResponseCode.SERVER_ERROR, message = '服务器错误', data = {}) => {
    const response = {
        error: code,
        body: data,
        message,
    };
    // 根据错误码设置HTTP状态码
    let httpStatus = 500;
    switch (code) {
        case ResponseCode.UNAUTHORIZED:
            httpStatus = 401;
            break;
        case ResponseCode.FORBIDDEN:
            httpStatus = 403;
            break;
        case ResponseCode.NOT_FOUND:
            httpStatus = 404;
            break;
        case ResponseCode.CONFLICT:
            httpStatus = 409;
            break;
        case ResponseCode.VALIDATION:
            httpStatus = 422;
            break;
        default:
            httpStatus = 500;
    }
    res.status(httpStatus).json(response);
};
exports.error = error;
/**
 * 未授权响应
 * @param res Express响应对象
 * @param message 错误消息
 */
const unauthorized = (res, message = '未登录或登录过期，需要重新登录') => {
    (0, exports.error)(res, ResponseCode.UNAUTHORIZED, message);
};
exports.unauthorized = unauthorized;
/**
 * 禁止访问响应
 * @param res Express响应对象
 * @param message 错误消息
 */
const forbidden = (res, message = '没有权限执行此操作') => {
    (0, exports.error)(res, ResponseCode.FORBIDDEN, message);
};
exports.forbidden = forbidden;
/**
 * 资源不存在响应
 * @param res Express响应对象
 * @param message 错误消息
 */
const notFound = (res, message = '请求的资源不存在') => {
    (0, exports.error)(res, ResponseCode.NOT_FOUND, message);
};
exports.notFound = notFound;
/**
 * 参数验证错误响应
 * @param res Express响应对象
 * @param message 错误消息
 * @param errors 错误详情
 */
const validationError = (res, message = '参数验证失败', errors = {}) => {
    (0, exports.error)(res, ResponseCode.VALIDATION, message, errors);
};
exports.validationError = validationError;
/**
 * 业务错误响应
 * @param res Express响应对象
 * @param message 错误消息
 * @param code 业务错误码
 */
const businessError = (res, message, code = ResponseCode.BUSINESS_ERROR) => {
    (0, exports.error)(res, code, message);
};
exports.businessError = businessError;
exports.default = {
    success: exports.success,
    error: exports.error,
    unauthorized: exports.unauthorized,
    forbidden: exports.forbidden,
    notFound: exports.notFound,
    validationError: exports.validationError,
    businessError: exports.businessError,
    ResponseCode,
};
//# sourceMappingURL=response.js.map