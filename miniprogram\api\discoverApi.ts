import { request } from '../utils/request'

// 获取发现页菜品列表
export const getDiscoverList = (params: {
  page: number,
  pageSize: number,
  sortType: string,
  searchValue?: string
}) => {
  return request({
    url: '/api/discover/list',
    method: 'GET',
    data: params,
    showLoading: false
  })
}

// 添加菜品到指定分类
export const addToDish = (dishId: string, categoryId: string, kitchenId?: string) => {
  const userInfo = wx.getStorageSync('userInfo')
  const defaultKitchenId = userInfo && userInfo.currentKitchenId ? userInfo.currentKitchenId : ''

  return request({
    url: '/api/discover/addToDish',
    method: 'POST',
    data: {
      dishId,
      targetCategoryId: categoryId,
      targetKitchenId: kitchenId || defaultKitchenId
    }
  })
}

// 获取所有未读消息数量
export const getMessageCount = () => {
  return request({
    url: '/api/discover/messageCount',
    method: 'GET',
    data: {}
  })
}

// 获取各标签页未读消息数量
export const getUnreadCounts = () => {
  return request({
    url: '/api/discover/unreadCounts',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 获取发现页标签页内容
export const getTabContent = (tab: string, page: number, pageSize: number) => {
  return request({
    url: '/api/discover/tabContent',
    method: 'GET',
    data: { tab, page, pageSize },
    showLoading: false
  })
}