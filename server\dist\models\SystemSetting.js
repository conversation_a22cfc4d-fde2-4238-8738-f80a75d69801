"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 系统设置模型
 * 存储系统功能开关配置
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 系统设置模型类
class SystemSetting extends sequelize_1.Model {
}
// 初始化系统设置模型
SystemSetting.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '设置ID',
    },
    key: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '设置键名',
    },
    value: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        comment: '设置值',
    },
    type: {
        type: sequelize_1.DataTypes.ENUM('boolean', 'string', 'number', 'json'),
        allowNull: false,
        defaultValue: 'string',
        comment: '值类型',
    },
    description: {
        type: sequelize_1.DataTypes.STRING(500),
        allowNull: true,
        comment: '设置描述',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'SystemSetting',
    tableName: 'system_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_key',
            fields: ['key'],
            unique: true,
        },
        {
            name: 'idx_type',
            fields: ['type'],
        },
    ],
});
exports.default = SystemSetting;
//# sourceMappingURL=SystemSetting.js.map