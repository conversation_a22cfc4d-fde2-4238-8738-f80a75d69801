"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 菜品模块路由
 * 处理菜品相关的API路由
 */
const express_1 = __importDefault(require("express"));
const dishController_1 = __importDefault(require("../controllers/dishController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取菜品分类列表
router.get('/categories', auth_1.optionalAuth, dishController_1.default.getCategories);
// 获取菜品列表
router.get('/list', auth_1.optionalAuth, dishController_1.default.getDishList);
// 获取菜品详情
router.get('/detail', auth_1.optionalAuth, dishController_1.default.getDishDetail);
// 搜索菜品
router.get('/search', auth_1.optionalAuth, dishController_1.default.searchDishes);
// 获取热门关键词
router.get('/hotKeywords', dishController_1.default.getHotKeywords);
// 添加菜品
router.post('/add', auth_1.verifyToken, dishController_1.default.addDish);
// 更新菜品
router.post('/update', auth_1.verifyToken, dishController_1.default.updateDish);
// 删除菜品
router.post('/delete', auth_1.verifyToken, dishController_1.default.deleteDish);
// 更新菜品状态
router.post('/updateStatus', auth_1.verifyToken, dishController_1.default.updateDishStatus);
// 更新菜品排序
router.post('/updateSort', auth_1.verifyToken, dishController_1.default.updateDishSort);
// 点赞菜品
router.post('/like', auth_1.verifyToken, dishController_1.default.likeDish);
// 举报菜品
router.post('/report', auth_1.verifyToken, dishController_1.default.reportDish);
// 添加评论
router.post('/comment', auth_1.verifyToken, dishController_1.default.addComment);
// 添加菜品到厨房
router.post('/addToKitchen', auth_1.verifyToken, dishController_1.default.addToKitchen);
exports.default = router;
//# sourceMappingURL=dishRoutes.js.map