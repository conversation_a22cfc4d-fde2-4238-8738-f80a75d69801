{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/app.js", "build": "tsc", "dev": "nodemon --exec ts-node src/app.ts", "db:init": "ts-node src/scripts/setupDatabase.ts --init", "db:migrate": "ts-node src/scripts/setupDatabase.ts --migrate", "db:seed": "ts-node src/scripts/setupDatabase.ts --seed", "db:setup": "ts-node src/scripts/setupDatabase.ts --all", "db:backup": "ts-node src/scripts/backupDatabase.ts", "db:restore": "ts-node src/scripts/restoreDatabase.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/qrcode": "^1.5.5", "@types/sharp": "^0.31.1", "@types/xml2js": "^0.4.14", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^2.0.0", "mysql2": "^3.14.1", "node-rsa": "^1.1.1", "qrcode": "^1.5.4", "sequelize": "^6.37.7", "sharp": "^0.34.2", "uuid": "^11.1.0", "wechatpay-axios-plugin": "^0.9.4", "wechatpay-node-v3": "^2.2.1", "winston": "^3.17.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.19", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}