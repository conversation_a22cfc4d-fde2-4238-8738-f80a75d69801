/**
 * 应用入口文件
 * 配置Express应用，中间件和路由
 */
import express, { Application } from 'express';
import cors from 'cors';
import path from 'path';
import config from './config/config';
import { testConnection } from './config/database';
import logger from './utils/logger';
import { notFoundHandler, errorHandler } from './middlewares/error';

// 创建Express应用
const app: Application = express();

// 配置中间件
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码的请求体
app.use(cors({
  origin: config.server.corsOrigin,
  credentials: true
})); // 启用CORS

// 配置静态文件服务
// 添加图片访问监控中间件
app.use('/uploads', (req, res, next) => {
  const startTime = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    if (res.statusCode !== 200) {
      logger.warn(`图片访问失败: ${req.path}, 状态码: ${res.statusCode}, 耗时: ${duration}ms, IP: ${req.ip}`);
    } else if (duration > 3000) {
      logger.warn(`图片加载缓慢: ${req.path}, 耗时: ${duration}ms, IP: ${req.ip}`);
    }
  });
  next();
});

// 配置静态文件服务，启用缓存
app.use('/uploads', express.static(path.join(process.cwd(), config.upload.dir), {
  maxAge: '1d',           // 缓存1天
  etag: true,             // 启用ETag验证
  lastModified: true,     // 启用Last-Modified头
  setHeaders: (res, path) => {
    // 为图片文件设置更强的缓存策略
    if (path.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      res.setHeader('Cache-Control', 'public, max-age=86400, s-maxage=86400'); // 1天缓存
      res.setHeader('Vary', 'Accept-Encoding');
    }
  }
}));

// 请求日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.originalUrl}`);
  next();
});

// 路由配置
import userRoutes from './routes/userRoutes';
import dishRoutes from './routes/dishRoutes';
import categoryRoutes from './routes/categoryRoutes';
import cartRoutes from './routes/cartRoutes';
import orderRoutes from './routes/orderRoutes';
import kitchenRoutes from './routes/kitchenRoutes';
import tableRoutes from './routes/tableRoutes';
import messageRoutes from './routes/messageRoutes';
import discoverRoutes from './routes/discoverRoutes';
import uploadRoutes from './routes/uploadRoutes';
import adminRoutes from './routes/adminRoutes';
import feedbackRoutes from './routes/feedbackRoutes';
import systemRoutes from './routes/systemRoutes';
import paymentRoutes from './routes/paymentRoutes';

// 注册API路由
app.use('/api/user', userRoutes);
app.use('/api/dish', dishRoutes);
app.use('/api/category', categoryRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/order', orderRoutes);
app.use('/api/kitchen', kitchenRoutes);
// 将桌号路由作为厨房路由的子路由
kitchenRoutes.use('/table', tableRoutes);
app.use('/api/message', messageRoutes);
app.use('/api/discover', discoverRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/feedback', feedbackRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/payment', paymentRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '餐厅点菜小程序API服务',
    version: '1.0.0',
    status: 'running',
  });
});

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 导入模型关联初始化函数
import { initializeAssociations } from './models';

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();
    
    // 初始化模型关联关系
    initializeAssociations();
    
    // 启动HTTP服务器
    const PORT = config.server.port;
    app.listen(PORT, () => {
      logger.info(`服务器启动成功，监听端口: ${PORT}`);
      logger.info(`环境: ${config.server.env}`);
      logger.info(`API服务就绪，包含管理员接口`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export default app;
