// 导入API
import { getUserInfo, updateUserInfo, getMembershipStatus, uploadUserAvatar } from '../../api/userApi'
import { checkLogin } from '../../utils/util'
import { clearCart } from '../../api/dishApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { formatDateTime } from '../../utils/dateFormat'
import { getMessageCount, markAllRead } from '../../api/messageApi'
import { getKitchenList } from '../../api/kitchenApi'
// 导入反馈相关API
import { submitFeedback, uploadFeedbackImage, FeedbackData, FeedbackType } from '../../api/feedbackApi'
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker'

// 定义用户数据接口
interface UserInfo {
  userId: string;
  nickName: string;
  avatarUrl: string;
  gender: number;
  coins: number;    // 大米数量
  likes: number;    // 获赞数量
  dishes: number;   // 添加的菜品数量
  kitchens: number; // 厨房数量
}

// 定义会员信息接口
interface MemberInfo {
  isMember: boolean;
  name: string;
  expireDate: string;
  memberType: string;
  privileges?: {
    unlimitedCategory?: boolean;
    unlimitedKitchen?: boolean;
    unlimitedDish?: boolean;
  }
}

// 页面配置
Page({
  // 页面数据
  data: {
    // 用户信息
    isLoggedIn: false,
    userInfo: {} as UserInfo,
    statusBarHeight: 0,
    // 编辑资料弹窗相关
    showEditProfile: false,
    editUserInfo: {} as UserInfo,
    // 退出登录确认弹窗
    showLogoutConfirm: false,
    // 清除缓存确认弹窗
    showClearCacheConfirm: false,
    // 背景设置相关
    showBackgroundSettings: false,
    shopBg: '',
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 默认为第一个渐变
    navBgIndex: 0,
    // 文字颜色 (light或dark)
    textColorStyle: '--text-color: #333333;',
    // 背景监听轮询
    bgCheckInterval: null as any,
    // 会员相关
    showMemberModal: false,
    isMember: false,
    memberInfo: {} as MemberInfo,
    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
    // 当前厨房ID
    currentKitchenId: '',
    // 问题反馈相关
    showFeedbackModal: false,
    feedbackData: {
      type: 'bug' as FeedbackType,
      description: '',
      images: [] as string[]
    } as FeedbackData,
    isSubmittingFeedback: false,
    // 联系客服模态框
    showServiceModal: false
  },

  // 生命周期函数--监听页面加载
  onLoad() {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight
    this.setData({ statusBarHeight })

    // 检查用户是否登录
    this.checkLoginStatus()

    // 加载背景设置
    this.loadBackgroundSettings()
    
    // 获取当前厨房ID
    this.getCurrentKitchenId()

    // 加载会员信息
    if (this.data.isLoggedIn) {
      this.loadMemberInfo()
    }
  },

  // 生命周期函数--页面显示时
  onShow() {
    // 检查登录状态是否发生变化
    const wasLoggedIn = this.data.isLoggedIn;
    this.checkLoginStatus();
    
    // 获取当前厨房ID
    this.getCurrentKitchenId();

    // 如果已登录
    if (this.data.isLoggedIn) {
      // 如果是刚登录，checkLoginStatus已经会调用fetchUserInfo了
      if (!wasLoggedIn) {
        // 刚登录，还需要加载会员信息
        this.loadMemberInfo();
      } else {
        // 如果已经登录过，只更新会员信息，不重新获取用户信息
        this.loadMemberInfo();
      }
    }
  },

  // 刷新所有数据
  async refreshAllData() {
    try {
      // 并行获取用户信息和会员信息
      await Promise.all([
        this.fetchUserInfo(),
        this.loadMemberInfo()
      ])
    } catch (error) {
      console.error('刷新数据失败', error)
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = checkLogin()
    const wasLoggedIn = this.data.isLoggedIn
    
    this.setData({ isLoggedIn })

    // 只有在刚登录时才获取用户信息，避免重复获取
    if (isLoggedIn && !wasLoggedIn) {
      this.fetchUserInfo()
    }
  },

  // 获取用户信息
  async fetchUserInfo() {
    try {
      let result
      result = await getUserInfo()

      if (result.error === 0) {
        this.setData({
          userInfo: result.body
        })
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', result.body)

        // 预加载用户头像
        this.preloadUserAvatar(result.body)
      }
    } catch (error) {
      console.error('获取用户信息失败', error)
    }
  },

  // 登录点击
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login',
    })
  },

  // 显示退出登录确认弹窗
  showLogoutConfirm() {
    this.setData({
      showLogoutConfirm: true
    })
  },

  // 取消退出登录
  cancelLogout() {
    this.setData({
      showLogoutConfirm: false
    })
  },

  // 确认退出登录
  confirmLogout() {
    this.setData({
      showLogoutConfirm: false
    })

    // 执行退出登录操作
    this.logout()
  },

  // 退出登录
  async logout() {
    try {
      // 清除本地存储的token和用户信息
      wx.removeStorageSync('token')
      wx.removeStorageSync('userInfo')
      
      // 清除厨房相关信息
      wx.removeStorageSync('currentKitchen')
      wx.removeStorageSync('last_selected_kitchen')
      wx.removeStorageSync('current_kitchen_bg')
      
      // 不清除全局背景设置，但将当前厨房背景设置为空
      // 这样可以保留用户的全局背景偏好，但不保留特定厨房的背景
      const app = getApp<IAppOption>()
      if (app.globalData.backgroundSettings) {
        app.globalData.backgroundSettings.shopBg = ''
      }

      // 更新页面状态
      this.setData({
        isLoggedIn: false,
        userInfo: {} as UserInfo
      })

      // 更新全局数据
      app.setLoginStatus(false)

      // 清空购物车
      try {
        const result = await clearCart()
      } catch (error) {
        console.error('清空购物车失败', error)
      }

      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
      // 刷新应用状态，确保餐厅页面更新
      setTimeout(() => {
        // 如果在餐厅页面，退出登录后需要刷新页面
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        if (currentPage && currentPage.route && currentPage.route.includes('restaurant')) {
          wx.reLaunch({
            url: '/pages/restaurant/restaurant'
          })
        }
      }, 500)
    } catch (error) {
      console.error('退出登录失败', error)
      wx.showToast({
        title: '退出失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示编辑资料弹窗
  showEditProfileModal() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    // 克隆用户信息，避免直接修改
    const editUserInfo = JSON.parse(JSON.stringify(this.data.userInfo))

    this.setData({
      showEditProfile: true,
      editUserInfo
    })
  },

  // 关闭编辑资料弹窗
  closeEditProfileModal() {
    this.setData({
      showEditProfile: false
    })
  },

  // 选择头像
  async chooseAvatar() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      const tempFilePath = res.tempFilePaths[0];

      // 检查是否登录
      if (!this.data.isLoggedIn) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      try {
        // 上传头像到服务器
        const uploadResult: any = await uploadUserAvatar(tempFilePath);

        if (uploadResult.error === 0) {
          const newAvatarUrl = uploadResult.body && uploadResult.body.imageUrl;
          
          // 立即保存新头像到后端，这样会删除旧的头像文件
          try {
            const updateData = {
              nick_name: this.data.editUserInfo.nickName || this.data.userInfo.nickName,
              avatar_url: newAvatarUrl,
              gender: this.data.editUserInfo.gender !== undefined ? this.data.editUserInfo.gender : this.data.userInfo.gender
            };

            const saveResult: any = await updateUserInfo(updateData);
            
            if (saveResult.error === 0) {
              console.log('头像上传并保存成功:', newAvatarUrl);
              
              // 添加时间戳参数破坏缓存，确保新头像立即显示
              const avatarUrlWithCacheBuster = `${newAvatarUrl}?v=${Date.now()}`;
              
              // 更新编辑用户信息中的头像URL
              this.setData({
                'editUserInfo.avatarUrl': avatarUrlWithCacheBuster
              });

              // 直接更新当前用户信息，避免重复请求
              this.setData({
                'userInfo.avatarUrl': avatarUrlWithCacheBuster
              });
              
              // 保存到本地存储（存储不带缓存参数的原始URL）
              const updatedUserInfo = { ...this.data.userInfo, avatarUrl: newAvatarUrl };
              wx.setStorageSync('userInfo', updatedUserInfo);
            } else {
              console.warn('头像保存部分失败，但上传成功:', saveResult);
              // 即使保存失败，也继续显示新头像，用户可以手动再次保存
              const avatarUrlWithCacheBuster = `${newAvatarUrl}?v=${Date.now()}`;
              this.setData({
                'editUserInfo.avatarUrl': avatarUrlWithCacheBuster
              });
            }
          } catch (saveError) {
            console.error('保存头像设置失败:', saveError);
            // 即使保存失败，也继续显示新头像，用户可以手动再次保存
            const avatarUrlWithCacheBuster = `${newAvatarUrl}?v=${Date.now()}`;
            this.setData({
              'editUserInfo.avatarUrl': avatarUrlWithCacheBuster
            });
          }

          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: (uploadResult && uploadResult.message) || '上传失败',
            icon: 'none'
          });
        }
      } catch (uploadError) {
        console.error('上传头像失败:', uploadError);
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('选择头像失败:', error);
      wx.showToast({
        title: '选择头像失败',
        icon: 'none'
      });
    }
  },

  // 输入昵称
  onNicknameInput(e: any) {
    const value = e.detail.value;
    
    this.setData({
      'editUserInfo.nickName': value
    });

    // 实时敏感词检查
    if (value && value.length > 1) {
      const checkResult = checkSensitiveWord(value, 'username');
      if (checkResult.hasSensitiveWord) {
        // 可以在这里添加实时提示
        console.warn('昵称包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 选择性别
  selectGender(e: any) {
    const gender = parseInt(e.currentTarget.dataset.gender)
    this.setData({
      'editUserInfo.gender': gender
    })
  },

  // 保存个人资料
  async saveProfile() {
    const { editUserInfo } = this.data

    // 表单验证
    if (!editUserInfo.nickName || editUserInfo.nickName.trim() === '') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    // 敏感词检查
    const nicknameCheck = checkSensitiveWord(editUserInfo.nickName.trim(), 'username');
    if (nicknameCheck.hasSensitiveWord) {
      wx.showModal({
        title: '昵称审核',
        content: '昵称包含不当内容，请重新输入',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
      return;
    }

    try {
      // 构建更新数据
      const updateData = {
        nick_name: editUserInfo.nickName,
        avatar_url: editUserInfo.avatarUrl,
        gender: editUserInfo.gender
      }

      let result
      result = await updateUserInfo(updateData)

      if (result.error === 0) {
        // 更新成功
        wx.showToast({
          title: '资料已更新',
          icon: 'success'
        })

        // 关闭编辑弹窗
        this.setData({
          showEditProfile: false
        })

        // 直接更新当前用户信息，避免重复请求
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            nickName: editUserInfo.nickName,
            avatarUrl: editUserInfo.avatarUrl,
            gender: editUserInfo.gender
          }
        });
        
        // 保存到本地存储
        const updatedUserInfo = {
          ...this.data.userInfo,
          nickName: editUserInfo.nickName,
          avatarUrl: editUserInfo.avatarUrl,
          gender: editUserInfo.gender
        };
        wx.setStorageSync('userInfo', updatedUserInfo);
      } else {
        // 更新失败
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('保存个人资料失败', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 优先检查是否有当前厨房背景
    const currentKitchenBg = wx.getStorageSync('current_kitchen_bg') || ''
    
    // 从本地存储中获取背景设置
    const globalShopBg = wx.getStorageSync('shopBg') || ''
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)' // 默认为第一个渐变
    const navBgIndex = wx.getStorageSync('navBgIndex') !== undefined ? wx.getStorageSync('navBgIndex') : 0

    // 优先使用厨房背景，其次是全局背景设置
    const shopBg = currentKitchenBg || globalShopBg || ''

    this.setData({
      shopBg,
      navBgStyle,
      navBgIndex
    })

    // 根据背景颜色计算合适的文字颜色
    this.updateTextColorByBackground(navBgStyle)

    // 注册应用主题变更监听
    const app = getApp<IAppOption>();
    if (app && app.addThemeChangeListener) {
      app.addThemeChangeListener(this.onThemeChanged.bind(this));
    }
  },

  // 主题变更回调函数
  onThemeChanged(settings: any) {
    if (settings) {
      // 获取当前厨房背景
      const currentKitchenBg = wx.getStorageSync('current_kitchen_bg') || ''
      
      // 如果存在当前厨房背景，优先使用厨房背景
      const shopBg = this.data.currentKitchenId ? 
        (currentKitchenBg || settings.shopBg || this.data.shopBg) : 
        (settings.shopBg || this.data.shopBg)
      
      this.setData({
        navBgStyle: settings.navBgStyle || this.data.navBgStyle,
        shopBg: shopBg,
        navBgIndex: settings.navBgIndex || this.data.navBgIndex
      });

      this.updateTextColorByBackground(settings.navBgStyle || this.data.navBgStyle);
    }
  },

  // 页面卸载时
  onUnload() {
    // 移除主题变更监听
    const app = getApp<IAppOption>();
    if (app && app.removeThemeChangeListener) {
      app.removeThemeChangeListener(this.onThemeChanged);
    }
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景颜色值
    let bgColor = '#FFFFFF' // 默认白色背景

    if (background) {
      // 尝试提取渐变的起始颜色
      const match = background.match(/linear-gradient\(to\s+\w+,\s+(#[A-Fa-f0-9]+),\s+/)
      if (match && match.length > 1) {
        bgColor = match[1]
      }
    }

    // 计算颜色亮度 (简化的亮度计算，仅用于本例)
    const r = parseInt(bgColor.substring(1, 3), 16)
    const g = parseInt(bgColor.substring(3, 5), 16)
    const b = parseInt(bgColor.substring(5, 7), 16)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF'
    const textColorStyle = `--text-color: ${textColor};`

    this.setData({ textColorStyle })

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    })
  },

  // 导航到功能页面
  navigateToFunction(e: any) {
    const functionType = e.currentTarget.dataset.function

    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    switch (functionType) {
      case 'kitchen':
        // 厨房管理
        wx.navigateTo({
          url: '/pages/kitchen-manage/kitchen-manage'
        })
        break
      case 'coins':
        // 我的大米
        wx.navigateTo({
          url: '/pages/my-coins/my-coins'
        })
        break
      case 'tasks':
        // 任务大厅
        wx.navigateTo({
          url: '/pages/task-hall/task-hall'
        })
        break
      case 'service':
        // 联系客服 - 显示微信二维码
        this.showServiceModal()
        break
      case 'more':
        // 更多功能
        wx.showToast({
          title: '如果你有好的想法，可以联系我们！',
          icon: 'none',
          duration: 3000
        })
        break
    }
  },

  // 导航到特性页面
  navigateToFeature(e: any) {
    const feature = e.currentTarget.dataset.feature

    switch (feature) {
      case 'message':
        // 消息绑定
        wx.showToast({
          title: '消息绑定功能开发中',
          icon: 'none'
        })
        break
      case 'about':
        // 关于我们
        wx.showToast({
          title: '关于我们页面开发中',
          icon: 'none'
        })
        break
      case 'feedback':
        // 问题反馈
        wx.showToast({
          title: '问题反馈功能开发中',
          icon: 'none'
        })
        break
    }
  },

  // 清除缓存
  clearCache() {
    this.setData({
      showClearCacheConfirm: true
    })
  },

  // 取消清除缓存
  cancelClearCache() {
    this.setData({
      showClearCacheConfirm: false
    })
  },

  // 确认清除缓存
  confirmClearCache() {
    try {
      // 保留登录相关信息
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')

      // 清除所有缓存
      wx.clearStorageSync()

      // 恢复登录信息
      if (token) {
        wx.setStorageSync('token', token)
      }
      if (userInfo) {
        wx.setStorageSync('userInfo', userInfo)
      }

      // 重置页面背景设置
      this.setData({
        showClearCacheConfirm: false,
        navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
        navBgIndex: 0,
        shopBg: ''
      })

      // 更新文字颜色
      this.updateTextColorByBackground('linear-gradient(to bottom, #F8F9FA, #E2F3FF)')

      // 使用全局事件系统通知其它页面
      const app = getApp<IAppOption>()
      app.updateBackgroundSettings({
        shopBg: '',
        navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
        navBgIndex: 0
      })

      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      })
    } catch (error) {
      console.error('清除缓存失败', error)
      wx.showToast({
        title: '清除缓存失败',
        icon: 'none'
      })
      this.setData({
        showClearCacheConfirm: false
      })
    }
  },

  // 显示需要登录提示
  showNeedLoginToast() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
  },

  // 下拉刷新
  async onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      try {
        // 刷新所有数据
        await this.refreshAllData()
        
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('下拉刷新失败', error)
        wx.showToast({
          title: '刷新失败',
          icon: 'none',
          duration: 1500
        })
      }
    }
    wx.stopPullDownRefresh()
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '美食点餐 - 个人中心',
      path: '/pages/mine/mine'
    }
  },

  // 复制用户ID
  copyUserId() {
    const { userId } = this.data.userInfo
    if (userId) {
      wx.setClipboardData({
        data: userId,
        success() {
          wx.showToast({
            title: '已复制用户ID',
            icon: 'success'
          })
        }
      })
    }
  },

  // 显示背景设置弹窗
  showBackgroundSettings() {
    this.setData({
      showBackgroundSettings: true
    })
  },

  // 关闭背景设置弹窗
  closeBackgroundSettings() {
    this.setData({
      showBackgroundSettings: false
    })
  },

  // 保存背景设置
  saveBackgroundSettings(e: any) {
    const { shopBg, navBgIndex, navBg } = e.detail

    // 保存到本地存储
    wx.setStorageSync('shopBg', shopBg)
    wx.setStorageSync('navBgStyle', navBg)
    wx.setStorageSync('navBgIndex', navBgIndex)

    // 更新页面数据
    this.setData({
      shopBg,
      navBgStyle: navBg,
      navBgIndex
    })

    // 更新全局应用数据
    const app = getApp<IAppOption>()
    if (app.globalData) {
      app.globalData.backgroundSettings = {
        shopBg,
        navBgStyle: navBg,
        navBgIndex
      }
    }

    // 同步更新到其他页面
    app.updateBackgroundSettings({
      shopBg,
      navBgStyle: navBg,
      navBgIndex
    })

    // 强制刷新当前页面的背景图片组件
    setTimeout(() => {
      const bgImageComponents = this.selectAllComponents('.bg-image');
      if (bgImageComponents && bgImageComponents.length > 0) {
        bgImageComponents.forEach((component: any) => {
          if (component && component.resetAndReload) {
            component.resetAndReload();
          }
        });
      }
    }, 100);

    // 关闭设置面板
    this.setData({
      showBackgroundSettings: false
    })

    wx.showToast({
      title: '主题设置已保存',
      icon: 'success'
    })
  },

  // 加载会员信息
  async loadMemberInfo() {
    try {
      // 调用会员状态API，不传参数
      const res = await getMembershipStatus();
      if (res.error === 0) {
        this.setData({
          isMember: res.body.isMember,
          memberInfo: {
            isMember: res.body.isMember,
            name: res.body.memberType === 'monthly' ? '月度' : '年度',
            expireDate: res.body.expireDate || '',
            memberType: res.body.memberType || '',
            privileges: res.body.privileges || {}
          }
        })
      }
    } catch (error) {
      console.error('获取会员信息失败', error)
    }
  },

  // 显示会员弹窗
  showMemberModal() {
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    this.setData({
      showMemberModal: true
    })
  },

  // 关闭会员弹窗
  closeMemberModal() {
    this.setData({
      showMemberModal: false
    })
  },

  // 会员订阅成功回调
  async onMembershipSuccess(e: WechatMiniprogram.CustomEvent) {
    const memberData = e.detail

    // 显示成功提示
    wx.showToast({
      title: '会员开通成功',
      icon: 'success',
      duration: 2000
    })

    // 重新获取最新的用户信息和会员信息，确保数据同步
    try {
      await this.refreshAllData()
    } catch (error) {
      console.error('刷新会员数据失败', error)
    }
  },

  // 获取会员价格
  getMemberPrice(memberType: string): number {
    const prices: {[key: string]: number} = {
      monthly: 666,
      yearly: 5888
    }
    return prices[memberType] || 0
  },

  // 获取当前厨房ID
  getCurrentKitchenId() {
    try {
      // 从本地存储中获取当前选择的厨房ID
      const currentKitchenId = wx.getStorageSync('last_selected_kitchen') || ''
      
      this.setData({
        currentKitchenId
      })

      console.log('当前厨房ID:', currentKitchenId)
    } catch (error) {
      console.error('获取当前厨房ID失败', error)
    }
  },

  // 预加载用户头像
  preloadUserAvatar(userInfo: UserInfo) {
    if (!userInfo.avatarUrl) return;
    
    // 检查是否已经预加载过相同的头像，避免重复加载
    const currentAvatarUrl = this.data.userInfo && this.data.userInfo.avatarUrl;
    if (currentAvatarUrl === userInfo.avatarUrl) {
      console.log('头像未变化，跳过预加载');
      return;
    }
    
    wx.getImageInfo({
      src: userInfo.avatarUrl,
      success: () => {
        console.log('用户头像预加载成功');
      },
      fail: () => {
        console.log('用户头像预加载失败');
      }
    });
  },

  // ==================== 问题反馈相关方法 ====================

  // 显示问题反馈弹窗
  showFeedbackModal() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      this.showNeedLoginToast()
      return
    }

    // 重置反馈数据
    this.setData({
      showFeedbackModal: true,
      feedbackData: {
        type: 'bug' as FeedbackType,
        description: '',
        images: [] as string[]
      }
    })
  },

  // 关闭问题反馈弹窗
  closeFeedbackModal() {
    this.setData({
      showFeedbackModal: false
    })
  },

  // 选择反馈类型
  selectFeedbackType(e: any) {
    const type = e.currentTarget.dataset.type as FeedbackType
    this.setData({
      'feedbackData.type': type
    })
  },

  // 输入问题描述
  onDescriptionInput(e: any) {
    const value = e.detail.value;
    
    this.setData({
      'feedbackData.description': value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('反馈内容包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 选择图片
  async chooseImages() {
    try {
      const res = await wx.chooseMedia({
        count: 3 - (this.data.feedbackData.images || []).length,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed'], // 使用压缩图片
        camera: 'back',
        maxDuration: 30 // 限制视频时长（虽然这里只选图片）
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        wx.showLoading({ title: '上传中...' })
        
        const uploadPromises = res.tempFiles.map(async (file) => {
          try {
            // 检查文件大小，如果超过5MB则提示
            if (file.size > 5 * 1024 * 1024) {
              throw new Error('图片大小不能超过5MB')
            }
            
            let result: any
            result = await uploadFeedbackImage(file.tempFilePath)
            
            if (result && result.error === 0) {
              return result.body && result.body.imageUrl
            } else {
              throw new Error((result && result.message) || '上传失败')
            }
          } catch (error) {
            console.error('上传图片失败:', error)
            throw error
          }
        })

        try {
          const imageUrls = await Promise.all(uploadPromises)
          const validImageUrls = imageUrls.filter(url => url) // 过滤掉undefined
          const newImages = [...(this.data.feedbackData.images || []), ...validImageUrls]
          
          this.setData({
            'feedbackData.images': newImages
          })
          
          wx.hideLoading()
          wx.showToast({
            title: '图片上传成功',
            icon: 'success'
          })
        } catch (error) {
          wx.hideLoading()
          wx.showToast({
            title: (error instanceof Error ? error.message : '图片上传失败'),
            icon: 'none'
          })
        }
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      if ((error as any).errMsg && (error as any).errMsg.includes('cancel')) {
        // 用户取消选择，不显示错误提示
        return
      }
      wx.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  },

  // 删除图片
  deleteImage(e: any) {
    const index = e.currentTarget.dataset.index
    const images = [...(this.data.feedbackData.images || [])]
    images.splice(index, 1)
    
    this.setData({
      'feedbackData.images': images
    })
  },

  // 预览图片
  previewImage(e: any) {
    const index = e.currentTarget.dataset.index
    const images = this.data.feedbackData.images || []
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  // 提交反馈
  async submitFeedback() {
    const { feedbackData } = this.data

    // 验证必填字段
    if (!feedbackData.description.trim()) {
      wx.showToast({
        title: '请输入问题描述',
        icon: 'none'
      })
      return
    }

    // 敏感词检查
    const descriptionCheck = checkSensitiveWord(feedbackData.description.trim(), 'content');
    if (descriptionCheck.hasSensitiveWord) {
      wx.showModal({
        title: '内容审核',
        content: `反馈内容包含不当内容，请修改后重新提交`,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
      return;
    }

    if (this.data.isSubmittingFeedback) {
      return
    }

    this.setData({ isSubmittingFeedback: true })

    try {
      // 获取设备信息
      const systemInfo = wx.getSystemInfoSync()
      const deviceInfo = {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight
      }

      // 准备提交数据
      const submitData: FeedbackData = {
        type: feedbackData.type,
        description: feedbackData.description.trim(),
        images: feedbackData.images || [],
        userId: this.data.userInfo.userId,
        deviceInfo
      }

      let result: any
      result = await submitFeedback(submitData)

      if (result && result.error === 0) {
        wx.showToast({
          title: result.message || '反馈提交成功',
          icon: 'success'
        })
        
        // 关闭弹窗
        this.closeFeedbackModal()
      } else {
        wx.showToast({
          title: (result && result.message) || '提交失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交反馈失败:', error)
      wx.showToast({
        title: '提交失败，请稍后再试',
        icon: 'none'
      })
    } finally {
      this.setData({ isSubmittingFeedback: false })
    }
  },

  // 显示联系客服模态框
  showServiceModal() {
    this.setData({
      showServiceModal: true
    })
  },

  // 关闭联系客服模态框
  closeServiceModal() {
    this.setData({
      showServiceModal: false
    })
  },

  // 长按二维码保存到相册
  onQrcodeLongPress(e: any) {
    const src = e.currentTarget.dataset.src;
    
    if (!src) {
      wx.showToast({
        title: '二维码地址错误',
        icon: 'none'
      });
      return;
    }

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['保存二维码到相册', '识别二维码'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 保存到相册
          this.saveQrcodeToAlbum(src);
        } else if (res.tapIndex === 1) {
          // 识别二维码
          this.scanQrcode(src);
        }
      }
    });
  },

  // 保存二维码到相册
  saveQrcodeToAlbum(src: string) {
    // 先获取图片信息
    wx.getImageInfo({
      src: src,
      success: (res) => {
        // 保存图片到相册
        wx.saveImageToPhotosAlbum({
          filePath: res.path,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (err) => {
            console.error('保存失败:', err);
            if (err.errMsg.includes('auth')) {
              // 权限问题，引导用户开启权限
              wx.showModal({
                title: '权限不足',
                content: '需要您授权保存图片到相册的权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          // 重新尝试保存
                          this.saveQrcodeToAlbum(src);
                        }
                      }
                    });
                  }
                }
              });
            } else {
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '图片加载失败',
          icon: 'none'
        });
      }
    });
  },

  // 识别二维码
  scanQrcode(src: string) {
    // 使用微信内置的二维码识别
    wx.previewImage({
      urls: [src],
      current: src
    });
  },
})