/**
 * 配置文件
 * 读取环境变量并提供配置对象
 */
import dotenv from 'dotenv';
import path from 'path';

// 加载.env文件
dotenv.config();

// 配置验证函数
function validateConfig() {
  const requiredEnvVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'JWT_SECRET'
  ];

  // 推荐但非必需的环境变量
  const recommendedEnvVars = [
    'BASE_URL',
    'CORS_ORIGIN'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ 缺少必要的环境变量:', missingVars.join(', '));
    console.error('请检查 .env 文件是否包含所有必要的配置项');
    process.exit(1);
  }

  // 验证端口号格式
  const port = parseInt(process.env.PORT || '3000', 10);
  if (isNaN(port) || port < 1 || port > 65535) {
    console.error('❌ PORT 配置无效，必须是 1-65535 之间的数字');
    process.exit(1);
  }

  const dbPort = parseInt(process.env.DB_PORT || '3306', 10);
  if (isNaN(dbPort) || dbPort < 1 || dbPort > 65535) {
    console.error('❌ DB_PORT 配置无效，必须是 1-65535 之间的数字');
    process.exit(1);
  }

  // 检查推荐的环境变量
  const missingRecommendedVars = recommendedEnvVars.filter(varName => !process.env[varName]);
  if (missingRecommendedVars.length > 0) {
    console.warn('⚠️  缺少推荐的环境变量:', missingRecommendedVars.join(', '));
    console.warn('这些变量有默认值，但建议在生产环境中明确设置');
  }

  console.log('✅ 配置验证通过');
}

// 执行配置验证
validateConfig();

// 配置对象
const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || 'http://dzcdd.zj1.natnps.cn',
    baseUrl: process.env.BASE_URL || process.env.CORS_ORIGIN || 'http://dzcdd.zj1.natnps.cn',
  },

  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    name: process.env.DB_NAME || 'restaurant_menu_db',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '123123',
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'restaurant_menu_app_secret_key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },

  // 文件上传配置
  upload: {
    dir: process.env.UPLOAD_DIR || 'uploads',
    path: process.env.UPLOAD_PATH || path.join(__dirname, '../../uploads'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880', 10), // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    dir: process.env.LOG_DIR || 'logs',
  },

  // ID生成规则配置
  idRules: {
    userIdStart: 10001, // 用户ID起始值
    userIdLength: 5,    // 用户ID长度
    kitchenIdLength: 6, // 厨房ID长度
  },

  // 厨房等级配置
  kitchenLevels: [
    { level: 1, kitchenLimit: 1, categoryLimit: 5, dishLimit: 30, memberLimit: 5, price: 0 },
    { level: 2, kitchenLimit: 3, categoryLimit: 8, dishLimit: 50, memberLimit: 10, price: 500 },
    { level: 3, kitchenLimit: 5, categoryLimit: 10, dishLimit: 100, memberLimit: 20, price: 1000 },
    { level: 4, kitchenLimit: 10, categoryLimit: 15, dishLimit: 200, memberLimit: 30, price: 2000 },
    { level: 5, kitchenLimit: 20, categoryLimit: 20, dishLimit: 500, memberLimit: 50, price: 5000 },
  ],

  // 会员配置
  membership: {
    monthly: {
      price: 666,
      days: 30,
    },
    yearly: {
      price: 5888,
      days: 365,
    },
  },

  // 签到奖励配置
  signIn: {
    basicReward: 20,           // 基础奖励
    continuousReward: {
      7: 30,                   // 连续签到7天额外奖励
      15: 60,                  // 连续签到15天额外奖励
    },
    maxDaysPerMonth: 31,       // 每月最大签到天数
  },

  // 广告观看配置
  adView: {
    reward: 10,                // 每次奖励
    maxPerDay: 15,             // 每日最大次数
  },

  // 桌号配置
  table: {
    maxCount: 30,              // 最大桌号数量
  },

  // 厨房配置
  kitchen: {
    defaultLevel: 1,           // 默认等级
    maxLevel: 5,               // 最大等级
    defaultCategoryLimit: 5,   // 默认分类数量限制
    defaultDishLimit: 30,      // 默认菜品数量限制
    defaultMemberLimit: 5,     // 默认成员数量限制
    upgradeDiscount: 0.8,      // 升级折扣
    upgradeCost: 1000,         // 升级基础费用
    categoryLimit: 20,         // 最大分类数量限制
    dishLimit: 500,            // 最大菜品数量限制
    memberLimit: 50,           // 最大成员数量限制
    kitchenLimit: 20,          // 最大厨房数量限制
  },

  // 微信配置
  wechat: {
    appId: process.env.WECHAT_APPID || 'wx41b8de9ea1e51474',
    appSecret: process.env.WECHAT_APP_SECRET || 'dd1e5ca6a7a7df81954cda4813dd2e2a',
  },

  // 微信支付配置
  wechatPay: {
    appId: process.env.WECHAT_PAY_APPID || process.env.WECHAT_APPID || 'wx41b8de9ea1e51474',
    mchId: process.env.WECHAT_PAY_MCHID || '',
    mchIdV3: process.env.WECHAT_PAY_MCHID_V3 || '',
    apiKey: process.env.WECHAT_PAY_API_KEY || '',
    apiV3Key: process.env.WECHAT_PAY_API_V3_KEY || '',
    serialNo: process.env.WECHAT_PAY_SERIAL_NO || '',
    privateKeyPath: path.join(__dirname, '../../certs/wechatpay/apiclient_key.pem'),
    certificatePath: path.join(__dirname, '../../certs/wechatpay/apiclient_cert.pem'),
    notifyUrl: process.env.WECHAT_PAY_NOTIFY_URL || `${process.env.BASE_URL || process.env.CORS_ORIGIN || 'http://dzcdd.zj1.natnps.cn'}/api/payment/notify`,
  },
};

export default config;
