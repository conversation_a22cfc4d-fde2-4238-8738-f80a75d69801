import { request } from '../utils/request'

// 获取消息数量
export const getMessageCount = () => {
  return request({
    url: '/api/message/messageCount',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 获取标签页内容
export const getTabContent = (tab: string, page: number, pageSize: number) => {
  return request({
    url: '/api/message/tabContent',
    method: 'GET',
    data: { type: tab, page, pageSize },
    showLoading: false
  })
}

// 标记消息为已读
export const markMessageRead = (messageId: string) => {
  return request({
    url: '/api/message/markRead',
    method: 'POST',
    data: { id: messageId },
    showLoading: false
  })
}

// 批量标记消息为已读
export const markAllRead = (type?: string) => {
  return request({
    url: '/api/message/markAllRead',
    method: 'POST',
    data: { type },
    showLoading: false
  })
}

// 发送反馈消息
// ... existing code ...