// 导入API
import { getUserCoins, rechargeCoins, getCoinRecords, createPaymentOrder, queryPaymentOrder } from '../../api/userApi'
import { checkLogin } from '../../utils/util'

// 定义用户大米数据接口
interface UserCoins {
  balance: number; // 大米余额
  total: number;   // 累计获得
  used: number;    // 已经使用
}

// 定义大米交易记录接口
interface CoinRecord {
  id: string;
  title: string;
  time: string;
  amount: number;
  type: 'in' | 'out'; // 收入或支出
}

// 页面配置
Page({
  // 页面数据
  data: {
    // 用户是否登录
    isLoggedIn: false,
    // 背景设置相关
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
    textColorStyle: '--text-color: #333333;',
    // 大米信息
    userCoins: {
      balance: 0,
      total: 0,
      used: 0
    } as UserCoins,
    // 充值相关
    showRecharge: false,
    selectedAmount: 3000, // 默认选择3000大米
    customAmount: '', // 自定义金额（元）
    isCustomAmount: false, // 是否使用自定义金额
    paymentMethod: 'wechat',
    showPayConfirm: false,
    // 计算属性
    paymentInfo: {
      amount: 0,
      coins: 0,
      description: ''
    },
    // 明细相关
    showDetail: false,
    currentTab: 'all', // 当前选中的标签页：all, in, out
    coinRecords: [] as CoinRecord[],
    allRecords: [] as CoinRecord[],
  },

  // 生命周期函数--监听页面加载
  onLoad() {
    // 加载背景设置
    this.loadBackgroundSettings()

    // 检查用户是否登录
    this.checkLoginStatus()

    // 监听全局背景设置变化
    this.listenForBackgroundChanges()
  },

  // 生命周期函数--页面显示时
  onShow() {
    // 每次页面显示时检查登录状态，确保退出后再次进入页面状态正确
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = checkLogin()
    this.setData({ isLoggedIn })

    if (isLoggedIn) {
      this.fetchCoinsInfo()
    } else {
      // 移除强制跳转逻辑，允许用户浏览页面
      // 未登录时显示相应的UI状态即可
    }
  },

  // 获取大米信息
  async fetchCoinsInfo() {
    try {
      const result = await getUserCoins()
      if (result.error === 0) {
        this.setData({
          userCoins: result.body
        })
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取大米信息失败', error)
      wx.showToast({
        title: '获取大米信息失败',
        icon: 'none'
      })
    }
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)'

    this.setData({ navBgStyle })

    // 根据背景颜色计算合适的文字颜色
    this.updateTextColorByBackground(navBgStyle)
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景颜色值
    let bgColor = '#FFFFFF' // 默认白色背景

    if (background) {
      // 尝试提取渐变的起始颜色
      const match = background.match(/linear-gradient\(to\s+\w+,\s+(#[A-Fa-f0-9]+),\s+/)
      if (match && match.length > 1) {
        bgColor = match[1]
      }
    }

    // 计算颜色亮度 (简化的亮度计算，仅用于本例)
    const r = parseInt(bgColor.substring(1, 3), 16)
    const g = parseInt(bgColor.substring(3, 5), 16)
    const b = parseInt(bgColor.substring(5, 7), 16)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF'
    const textColorStyle = `--text-color: ${textColor};`

    this.setData({ textColorStyle })

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    })
  },

  // 监听全局背景设置变化
  listenForBackgroundChanges() {
    // 简化实现，直接在app.ts中添加更新通知函数
    // 这里采用轮询方式检查本地存储的变化
    setInterval(() => {
      const navBgStyle = wx.getStorageSync('navBgStyle')
      if (navBgStyle && navBgStyle !== this.data.navBgStyle) {
        this.setData({ navBgStyle })
        this.updateTextColorByBackground(navBgStyle)
      }
    }, 1000) // 每秒检查一次
  },

  // 显示充值弹窗
  showRechargeModal() {
    this.setData({
      showRecharge: true
    })
  },

  // 关闭充值弹窗
  closeRechargeModal() {
    this.setData({
      showRecharge: false,
      customAmount: '',
      isCustomAmount: false
    })
  },

  // 选择充值金额
  selectRechargeAmount(e: any) {
    const amount = parseInt(e.currentTarget.dataset.amount)
    this.setData({
      selectedAmount: amount,
      isCustomAmount: false,
      customAmount: ''
    })
  },

  // 自定义金额输入
  onCustomAmountInput(e: any) {
    const value = e.detail.value
    this.setData({
      customAmount: value,
      isCustomAmount: value.length > 0,
      selectedAmount: 0 // 清除预设选择
    })
  },

  // 自定义金额输入失焦
  onCustomAmountBlur(e: any) {
    const value = e.detail.value
    if (value) {
      const amount = parseFloat(value)
      if (isNaN(amount) || amount < 0.1 || amount > 1000) {
        wx.showToast({
          title: '请输入0.1-1000之间的金额',
          icon: 'none'
        })
        this.setData({
          customAmount: '',
          isCustomAmount: false
        })
        return
      }
      // 保留两位小数
      const formattedAmount = Math.round(amount * 100) / 100
      this.setData({
        customAmount: formattedAmount.toString()
      })
    }
  },

  // 选择支付方式
  selectPaymentMethod(e: any) {
    const method = e.currentTarget.dataset.method
    this.setData({
      paymentMethod: method
    })
  },

  // 更新支付信息
  updatePaymentInfo() {
    let paymentInfo = { amount: 0, coins: 0, description: '' }

    if (this.data.isCustomAmount) {
      const customAmount = parseFloat(this.data.customAmount)
      paymentInfo.amount = customAmount
      paymentInfo.coins = Math.round(customAmount * 100)
      paymentInfo.description = `充值${paymentInfo.coins}大米`
    } else {
      const coinAmount = this.data.selectedAmount
      switch (coinAmount) {
        case 1000:
          paymentInfo = { amount: 10, coins: 1000, description: '充值1000大米' }
          break
        case 3000:
          paymentInfo = { amount: 28, coins: 3000, description: '充值3000大米' }
          break
        case 5000:
          paymentInfo = { amount: 45, coins: 5000, description: '充值5000大米' }
          break
        case 10000:
          paymentInfo = { amount: 88, coins: 10000, description: '充值10000大米' }
          break
      }
    }

    this.setData({ paymentInfo })
  },

  // 确认充值
  confirmRecharge() {
    // 验证选择的金额
    if (this.data.isCustomAmount) {
      const customAmount = parseFloat(this.data.customAmount)
      if (!customAmount || customAmount < 0.1 || customAmount > 1000) {
        wx.showToast({
          title: '请输入0.1-1000之间的金额',
          icon: 'none'
        })
        return
      }
    } else if (!this.data.selectedAmount) {
      wx.showToast({
        title: '请选择充值金额',
        icon: 'none'
      })
      return
    }

    // 更新支付信息
    this.updatePaymentInfo()

    // 显示支付确认弹窗
    this.setData({
      showRecharge: false,
      showPayConfirm: true
    })
  },

  // 取消支付
  cancelPayment() {
    this.setData({
      showPayConfirm: false
    })
  },

  // 确认支付
  async confirmPayment() {
    this.setData({
      showPayConfirm: false
    })

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 只支持微信支付
    if (this.data.paymentMethod !== 'wechat') {
      wx.showToast({
        title: '暂时只支持微信支付',
        icon: 'none'
      })
      return
    }

    try {
      // 显示加载提示
      wx.showLoading({
        title: '正在创建订单...',
        mask: true
      })

      // 使用计算好的支付信息
      const paymentAmount = this.data.paymentInfo.amount
      const description = this.data.paymentInfo.description

      if (!paymentAmount || paymentAmount <= 0) {
        throw new Error('无效的充值金额')
      }

      // 创建支付订单
      const orderResult = await createPaymentOrder(paymentAmount, description)

      if (orderResult.error !== 0) {
        throw new Error(orderResult.message || '创建订单失败')
      }

      wx.hideLoading()

      // 调用微信支付
      const paymentParams = orderResult.body
      console.log('支付参数:', paymentParams)

      await this.requestWechatPayment(paymentParams)

    } catch (error: any) {
      wx.hideLoading()
      console.error('支付失败:', error)
      
      const errorMessage = error.message || '支付失败，请重试'
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 调用微信支付
  async requestWechatPayment(paymentParams: any) {
    return new Promise<void>((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType,
        paySign: paymentParams.paySign,
        success: async (res) => {
          console.log('微信支付成功:', res)
          
          // 支付成功后，等待一下再查询订单状态确保后端处理完成
          setTimeout(async () => {
            try {
              // 验证支付结果
              await this.verifyPaymentResult(paymentParams.outTradeNo)
              resolve()
            } catch (error) {
              console.error('验证支付结果失败:', error)
              // 即使验证失败，也认为支付成功，因为微信已经返回成功
              this.handlePaymentSuccess()
              resolve()
            }
          }, 2000)
        },
        fail: (err) => {
          console.error('微信支付失败:', err)
          
          if (err.errMsg.includes('cancel')) {
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            })
          } else {
            wx.showToast({
              title: '支付失败，请重试',
              icon: 'none'
            })
          }
          
          reject(new Error(err.errMsg || '支付失败'))
        }
      })
    })
  },

  // 验证支付结果
  async verifyPaymentResult(outTradeNo: string) {
    try {
      const queryResult = await queryPaymentOrder(outTradeNo)
      
      if (queryResult.error === 0 && queryResult.body.trade_state === 'SUCCESS') {
        console.log('支付验证成功')
        this.handlePaymentSuccess()
      } else {
        console.warn('支付状态异常:', queryResult)
        // 支付状态不是成功，但不抛出错误，由上级处理
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
      throw error
    }
  },

  // 处理支付成功
  handlePaymentSuccess() {
    // 显示成功提示
    wx.showToast({
      title: '充值成功！',
      icon: 'success',
      duration: 2000
    })

    // 刷新大米信息
    setTimeout(() => {
      this.fetchCoinsInfo()
    }, 1000)
  },

  // 显示明细弹窗
  async showDetailModal() {
    try {
      const result = await getCoinRecords()
      if (result.error === 0) {
        this.setData({
          allRecords: result.body.records,
          coinRecords: result.body.records,
          showDetail: true,
          currentTab: 'all' // 默认显示全部
        })
      } else {
        wx.showToast({
          title: result.message || '获取明细失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取交易记录失败', error)
      wx.showToast({
        title: '获取明细失败',
        icon: 'none'
      })
    }
  },

  // 关闭明细弹窗
  closeDetailModal() {
    this.setData({
      showDetail: false
    })
  },

  // 切换明细标签页
  switchTab(e: any) {
    const tab = e.currentTarget.dataset.tab
    let filteredRecords = this.data.allRecords

    // 根据标签过滤记录
    if (tab !== 'all') {
      filteredRecords = this.data.allRecords.filter(record => record.type === tab)
    }

    this.setData({
      currentTab: tab,
      coinRecords: filteredRecords
    })
  },

  // 导航到任务页面
  navigateToTask(e: any) {
    const task = e.currentTarget.dataset.task

    // 跳转到任务大厅页面
    wx.navigateTo({
      url: '/pages/task-hall/task-hall'
    }).catch(() => {
      // 如果跳转失败，显示提示
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    })
  },

  // 导航到邀请好友页面
  navigateToInvite() {
    // 邀请好友功能，不显示提示文字
    // 可以在这里添加邀请好友的具体逻辑
    console.log('邀请好友功能')
  },

  // 导航到点餐下单页面
  navigateToOrder() {
    // 点餐下单功能，不显示提示文字
    // 可以在这里添加点餐下单的具体逻辑
    console.log('点餐下单功能')
  },

  // 导航到用途页面
  navigateToUsage(e: any) {
    const usage = e.currentTarget.dataset.usage

    switch (usage) {
      case 'vip':
        // 打开会员组件
        const kitchenMemberComponent = this.selectComponent("#kitchen-member")
        if (kitchenMemberComponent) {
          kitchenMemberComponent.showMemberDialog()
        } else {
          // 如果组件未找到，跳转到厨房管理页面
          wx.navigateTo({
            url: '/pages/kitchen-manage/kitchen-manage'
          })
        }
        break
      case 'lottery':
        // 参与抽奖按钮点击不做任何提示
        console.log('参与抽奖功能')
        break
      case 'upgrade':
        // 导航到厨房升级页面
        wx.navigateTo({
          url: '/pages/kitchen-manage/kitchen-manage'
        })
        break
      case 'discount':
        // 导航到餐厅页面
        wx.switchTab({
          url: '/pages/restaurant/restaurant'
        })
        break
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      this.fetchCoinsInfo()
    }
    wx.stopPullDownRefresh()
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '来获取更多大米福利',
      path: '/pages/my-coins/my-coins',
      imageUrl: '../../static/images/share-coins.png' // 这里应有分享图片
    }
  }
})