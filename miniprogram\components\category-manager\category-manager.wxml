<!-- 分类管理组件 -->
<view class="category-manager {{visible ? 'visible' : ''}}">
  <view class="category-manager-mask" bindtap="closeModal"></view>
  
  <!-- 分类管理内容 -->
  <view class="category-manager-content">
    <!-- 标题栏 -->
    <view class="manager-header">
      <text class="manager-title">{{isAddingCategory ? '新增分类' : '分类管理'}}</text>
      <view class="close-btn" bindtap="closeModal">✕</view>
    </view>
    
    <!-- 新增分类表单 -->
    <view class="add-category-form" wx:if="{{isAddingCategory}}">
      <view class="form-item">
        <text class="form-label">分类名称</text>
        <input class="form-input" placeholder="请输入分类名称" value="{{editCategory.name}}" maxlength="20" bindinput="onNameInput" />
      </view>
      
      <view class="form-item">
        <text class="form-label">选择图标</text>
        <scroll-view class="icon-scroll-view" scroll-y enhanced="{{true}}" show-scrollbar="{{false}}">
          <view class="icon-grid">
            <view 
              wx:for="{{icons}}" 
              wx:key="index" 
              class="icon-item {{editCategory.icon === item ? 'selected' : ''}}"
              bindtap="selectIcon"
              data-icon="{{item}}"
            >
              <image src="{{item}}" class="icon-image" mode="aspectFit"></image>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <view class="form-actions">
        <button class="cancel-btn" bindtap="cancelAddCategory">取消</button>
        <button class="save-btn" bindtap="saveCategory">保存</button>
      </view>
    </view>
    
    <!-- 分类列表 -->
    <view class="category-list-container" wx:else>
      <view class="category-count-row">
        <text class="category-count">共{{categories.length}}个分类</text>
        <view style="flex: 1;"></view>
        <button class="add-category-btn" bindtap="showAddCategoryForm">
          <text class="plus-icon">+</text> 新增分类
        </button>
      </view>
      
      <scroll-view class="category-list" scroll-y enhanced="{{true}}" show-scrollbar="{{false}}">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-item {{currentDragId === item.id ? 'dragging' : ''}} {{moveToIndex === index && currentDragId ? 'move-to' : ''}}"
          data-index="{{index}}"
          data-id="{{item.id}}"
          style="--index: {{index}};"
        >
          <view class="drag-handle" 
            bindtouchstart="onTouchStart" 
            bindtouchmove="onTouchMove" 
            bindtouchend="onTouchEnd" 
            data-index="{{index}}" 
            data-id="{{item.id}}">
            <text class="drag-icon">≡</text>
          </view>
          <view class="category-info">
            <image src="{{item.icon}}" class="category-icon-image" mode="aspectFit"></image>
            <text class="category-name">{{item.name}}</text>
          </view>
          <view class="category-actions">
            <view class="action-btn edit-btn" bindtap="editCategory" data-index="{{index}}">编辑</view>
            <view class="action-btn delete-btn" bindtap="deleteCategory" data-index="{{index}}">删除</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view> 