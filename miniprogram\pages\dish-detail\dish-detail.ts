import { getDishDetail, likeDish, addDishToKitchen, getDishCategories, reportDish, addComment } from '../../api/dishApi'
import { addToDish } from '../../api/discoverApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker'

// 获取应用实例
const app = getApp<IAppOption>()

// 定义评论类型接口
interface Comment {
  id: string;
  avatar: string;
  name: string;
  time: string;
  content: string;
}

Page({
  data: {
    dishId: '',
    dishInfo: {} as any,
    isLoading: true,
    navBgStyle: '',  // 导航栏背景样式
    statusBarHeight: 0, // 状态栏高度
    commentText: '', // 评论文本
    isLiked: false,  // 是否已点赞
    isAdded: false,  // 是否已添加到厨房
    showAddToKitchenModal: false, // 是否显示添加到厨房弹窗
    categoryList: [] as any[], // 分类列表
    selectedCategoryId: '', // 已选择的分类ID
    isInputFocused: false, // 输入框是否聚焦
    keyboardHeight: 0, // 键盘高度
    allComments: [] as Comment[], // 所有评论
    commentEnabled: true, // 评论功能开关状态
    // 图片轮播相关
    imageList: [] as string[], // 菜品图片列表
    currentImage: 0, // 当前显示的图片索引
    // 举报相关
    showReportModal: false, // 是否显示举报弹窗
    reportReasons: [
      { label: '虚假信息', value: 'fake' },
      { label: '色情低俗', value: 'porn' },
      { label: '违法违规', value: 'illegal' },
      { label: '侵犯权益', value: 'rights' },
      { label: '其他问题', value: 'other' }
    ],
    selectedReportReason: 'fake', // 选中的举报原因
    reportDesc: '', // 举报详细描述
    fromDiscover: false, // 是否从发现页进入
    showAddButton: false, // 是否显示添加按钮（菜品不属于当前厨房时显示）
    menuButtonHeight: 32, // 胶囊按钮高度
    menuButtonTop: 48, // 胶囊按钮顶部位置
    statusBarColor: '#333333', // 状态栏文字颜色
    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
    // 分享相关
    isSharedPage: false, // 是否是分享页面
  },

  onLoad(options) {
    // 获取胶囊按钮位置信息
    this.getMenuButtonPosition();

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 0
    })

    if (options.id) {
      this.setData({
        dishId: options.id
      })
      this.fetchDishDetail()
    }

    // 判断是否从发现页进入
    if (options.fromDiscover === '1') {
      this.setData({
        fromDiscover: true
      })
    }

    // 检查是否是分享页面（通过scene参数或其他标识）
    if (options.scene || options.shared === '1') {
      this.setData({
        isSharedPage: true
      })
    }

    // 检查登录状态，未登录时隐藏评论功能
    this.checkLoginStatus();

    // 设置导航栏样式
    this.setNavBgStyle()

    // 添加主题变更监听
    app.addThemeChangeListener(this.onThemeChanged)

    // 监听键盘高度变化
    wx.onKeyboardHeightChange((res) => {
      // 记录键盘高度
      this.setData({
        keyboardHeight: res.height
      })

      if (res.height > 0) {
        // 键盘弹起，设置输入框聚焦
        this.setData({
          isInputFocused: true
        })

        // 等待页面布局更新后进行滚动
        setTimeout(() => {
          // 获取页面高度和输入框位置
          const query = wx.createSelectorQuery()
          query.select('.comment-section').boundingClientRect()
          query.selectViewport().scrollOffset()
          query.exec((res) => {
            if (res && res[0] && res[1]) {
              const inputBoxRect = res[0]
              const scrollTop = res[1].scrollTop
              
              // 计算需要滚动到的位置，使输入框在可视区域中间偏上
              const windowHeight = wx.getSystemInfoSync().windowHeight
              const targetPosition = scrollTop + inputBoxRect.top - (windowHeight * 0.4)
              
              // 滚动到目标位置
              wx.pageScrollTo({
                scrollTop: targetPosition,
                duration: 300
              })
            } else {
              // 如果获取不到元素位置，则滚动到一个合理的位置
              const systemInfo = wx.getSystemInfoSync()
              wx.pageScrollTo({
                scrollTop: systemInfo.windowHeight * 0.6,
                duration: 300
              })
            }
          })
        }, 100)
      }
    })
  },

  onShow() {
    // 检查键盘是否已收起
    if (this.data.keyboardHeight > 0) {
      this.setData({
        keyboardHeight: 0,
        isInputFocused: false
      })
    }

    // 每次显示页面时都检查添加状态是否需要更新
    if (this.data.dishId) {
      this.refreshAddedStatus();
    }
  },

  onUnload() {
    // 移除主题变更监听
    app.removeThemeChangeListener(this.onThemeChanged)
  },

  // 主题变更回调
  onThemeChanged(settings: any) {
    this.setNavBgStyle(settings)
  },

  // 设置导航栏样式
  setNavBgStyle(settings?: any) {
    if (!settings) {
      settings = app.globalData.backgroundSettings
    }
    this.setData({
      navBgStyle: settings.navBgStyle
    })
  },

  // 切换轮播图
  onImageChange(e: any) {
    this.setData({
      currentImage: e.detail.current
    })
  },

  // 获取菜品详情
  async fetchDishDetail() {
    try {
      // 获取当前厨房ID
      let currentKitchenId = '';
      try {
        // 优先从last_selected_kitchen获取厨房ID
        currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';

        // 如果还是没有，尝试从userInfo获取
        if (!currentKitchenId) {
          const userInfo = wx.getStorageSync('userInfo');
          if (userInfo && userInfo.currentKitchenId) {
            currentKitchenId = userInfo.currentKitchenId;
          }
        }

        console.log('获取菜品详情时的厨房ID:', currentKitchenId);
      } catch (error) {
        console.log('获取用户厨房信息失败', error);
      }

      // 调用API时传递厨房ID
      const res = await getDishDetail(this.data.dishId, currentKitchenId);

      if (res.error === 0) {
        // 构建图片列表
        let imageList = [];
        
        // 添加主图
        if (res.body.image) {
          imageList.push(res.body.image);
        }

        // 添加多图
        if (res.body.images && res.body.images.length > 0) {
          // 过滤掉与主图重复的图片
          const additionalImages = res.body.images.filter((img: any) => img.url !== res.body.image);
          imageList = imageList.concat(additionalImages.map((img: any) => img.url));
        }

        // 如果没有任何图片，使用默认图片
        if (imageList.length === 0) {
          imageList = [this.data.defaultImages.DISH];
        }

        // 获取评论开关状态
        const commentEnabled = res.body.commentEnabled !== undefined ? res.body.commentEnabled : true;

        // 判断菜品是否属于当前厨房
        const dishKitchenId = res.body.kitchenId || res.body.ownerId; // 菜品所属厨房ID
        
        // 检查登录状态
        const token = wx.getStorageSync('token');
        let belongsToCurrentKitchen = false;
        
        if (token && currentKitchenId && dishKitchenId) {
          // 已登录且有厨房信息，判断菜品是否属于当前厨房
          belongsToCurrentKitchen = (currentKitchenId === dishKitchenId);
        } else if (!token) {
          // 未登录，默认显示添加按钮（点击时会跳转登录）
          belongsToCurrentKitchen = false;
        } else {
          // 已登录但没有厨房信息，显示添加按钮
          belongsToCurrentKitchen = false;
        }
        
        console.log('菜品所属厨房ID:', dishKitchenId);
        console.log('当前用户厨房ID:', currentKitchenId);
        console.log('用户登录状态:', !!token);
        console.log('菜品是否属于当前厨房:', belongsToCurrentKitchen);

        this.setData({
          dishInfo: res.body,
          imageList,
          isLiked: res.body.isLiked || false,
          isAdded: res.body.isAdded || false,
          allComments: this.formatComments(res.body.comments || []),
          commentEnabled, // 设置评论开关状态
          showAddButton: !belongsToCurrentKitchen, // 菜品不属于当前厨房时显示添加按钮
          isLoading: false
        });

        // 如果有图片，分析主图的颜色并设置状态栏
        if (imageList.length > 0 && imageList[0] !== this.data.defaultImages.DISH) {
          this.analyzeImageAndSetStatusBar(imageList[0]);
        }
      } else {
        console.error('获取菜品详情失败:', res.message);
        wx.showToast({
          title: res.message || '获取菜品详情失败',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
    }
  },

  // 评论输入事件
  onCommentInput(e: any) {
    const value = e.detail.value;
    
    this.setData({
      commentText: value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'comment');
      if (checkResult.hasSensitiveWord) {
        console.warn('评论内容包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 评论框聚焦事件
  onCommentFocus() {
    // 检查是否登录
    const token = wx.getStorageSync('token')
    if (!token) {
      // 未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      })
      return
    }

    this.setData({
      isInputFocused: true
    })
  },

  // 评论框失焦事件
  onCommentBlur() {
    // 延迟设置isInputFocused为false，避免点击发送按钮时由于失焦导致按钮消失无法点击
    setTimeout(() => {
      this.setData({
        isInputFocused: false
      })
    }, 100)
  },

  // 提交评论
  async submitComment() {
    // 检查评论功能是否开启
    if (!this.data.commentEnabled) {
      wx.showToast({
        title: '评论功能已关闭',
        icon: 'none'
      });
      return;
    }

    const content = this.data.commentText.trim();
    
    if (!content) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    // 检查敏感词
    if (hasSensitiveWord(content)) {
      wx.showToast({
        title: '评论内容包含敏感词，请修改后重试',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '发送中...' });
      
      const res = await addComment({
        dishId: this.data.dishId,
        content: content,
        rating: 5 // 默认5星评价
      });

      if (res.error === 0) {
        // 添加到评论列表头部
        const newComment: Comment = {
          id: res.body.id,
          avatar: (res.body.user && res.body.user.avatarUrl) || '/static/images/default-avatar.png',
          name: (res.body.user && res.body.user.nickName) || '匿名用户',
          time: this.formatTime(new Date()),
          content: content
        };

        const updatedComments = [newComment, ...this.data.allComments];
        
        this.setData({
          commentText: '',
          isInputFocused: false,
          keyboardHeight: 0,
          allComments: updatedComments
        });

        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });

        // 关闭键盘
        wx.hideKeyboard();
      } else {
        wx.showToast({
          title: res.message || '评论失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('提交评论失败:', error);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 格式化时间
  formatTime(date: Date) {
    const year = date.getFullYear()
    const month = this.padZero(date.getMonth() + 1)
    const day = this.padZero(date.getDate())
    const hour = this.padZero(date.getHours())
    const minute = this.padZero(date.getMinutes())
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  padZero(num: number) {
    return num < 10 ? `0${num}` : num.toString()
  },

  // 格式化评论数据
  formatComments(comments: any[]): Comment[] {
    if (!Array.isArray(comments)) {
      return [];
    }
    
    return comments.map((comment: any) => ({
      id: comment.id.toString(),
      avatar: comment.userAvatar || this.data.defaultImages.USER_AVATAR,
      name: comment.userName || '匿名用户',
      time: this.formatTime(new Date(comment.createdAt)),
      content: comment.content
    }));
  },

  // 点赞按钮点击事件
  async onLikeTap() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      // 未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    try {
      const newLikeStatus = !this.data.isLiked
      const res = await likeDish(this.data.dishId)

      if (res.error === 0) {
        this.setData({
          isLiked: newLikeStatus
        })
        wx.showToast({
          title: newLikeStatus ? '点赞成功' : '取消点赞',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('点赞操作失败', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 显示添加到厨房弹窗
  async showAddToKitchen() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      // 未登录，跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    // 如果已经添加过了，不弹出分类弹窗，只显示提示
    if (this.data.isAdded) {
      wx.showToast({
        title: '已添加到厨房',
        icon: 'success'
      });
      return;
    }

    try {
      // 获取用户当前厨房ID - 参考发现页面的逻辑
      let currentKitchenId = '';
      try {
        // 优先从last_selected_kitchen获取厨房ID
        currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';

        // 如果还是没有，尝试从userInfo获取
        if (!currentKitchenId) {
          const userInfo = wx.getStorageSync('userInfo');
          if (userInfo && userInfo.currentKitchenId) {
            currentKitchenId = userInfo.currentKitchenId;
          }
        }

        console.log('获取到的厨房ID:', currentKitchenId);
      } catch (error) {
        console.log('获取用户厨房信息失败', error);
      }

      // 如果没有厨房ID，提示用户并退出
      if (!currentKitchenId) {
        wx.showToast({
          title: '请先创建或选择厨房',
          icon: 'none'
        });
        return;
      }

      // 获取分类列表
      const res = await getDishCategories(currentKitchenId);

      if (res.error === 0) {
        // 修复：正确处理后端返回的数据结构
        const categoriesData = res.body.categories || res.body;
        console.log('从API获取到的分类数据:', categoriesData);

        if (categoriesData && Array.isArray(categoriesData) && categoriesData.length > 0) {
          this.setData({
            categoryList: categoriesData,
            showAddToKitchenModal: true,
            selectedCategoryId: '' // 重置选中的分类ID
          });
        } else {
          wx.showToast({
            title: '当前厨房还没有分类，请先添加分类',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: res.message || '获取分类失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取分类失败', error);
      wx.showToast({
        title: '获取分类失败',
        icon: 'none'
      });
    }
  },

  // 隐藏添加到厨房弹窗
  hideAddToKitchen() {
    this.setData({
      showAddToKitchenModal: false
    })
  },

  // 分类选择变更
  selectCategory(e: any) {
    const { id } = e.currentTarget.dataset;
    this.setData({
      selectedCategoryId: id
    });
  },

  // 确认添加到厨房
  async confirmAddToKitchen() {
    if (!this.data.selectedCategoryId) {
      wx.showToast({
        title: '请先选择分类',
        icon: 'none'
      });
      return;
    }

    // 获取用户当前厨房ID - 与showAddToKitchen保持一致
    let currentKitchenId = '';
    try {
      // 优先从last_selected_kitchen获取厨房ID
      currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';

      // 如果还是没有，尝试从userInfo获取
      if (!currentKitchenId) {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo && userInfo.currentKitchenId) {
          currentKitchenId = userInfo.currentKitchenId;
        }
      }

      console.log('添加菜品时获取到的厨房ID:', currentKitchenId);
    } catch (error) {
      console.log('获取用户厨房信息失败', error);
    }

    if (!currentKitchenId) {
      wx.showToast({
        title: '请先创建或选择厨房',
        icon: 'none'
      });
      return;
    }

    try {
      // 使用发现页面相同的API确保状态同步
      const res = await addToDish(
        this.data.dishId,
        this.data.selectedCategoryId,
        currentKitchenId
      );

      if (res.error === 0) {
        this.setData({
          isAdded: true,
          showAddToKitchenModal: false
        });
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.message || '添加失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('添加到厨房失败', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 显示举报弹窗
  showReportModal() {
    this.setData({
      showReportModal: true,
      selectedReportReason: 'fake',
      reportDesc: ''
    })
  },

  // 隐藏举报弹窗
  hideReportModal() {
    this.setData({
      showReportModal: false
    })
  },

  // 选择举报原因
  onReportReasonChange(e: any) {
    this.setData({
      selectedReportReason: e.detail.value
    })
  },

  // 举报详细描述输入
  onReportDescInput(e: any) {
    const value = e.detail.value;
    
    this.setData({
      reportDesc: value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('举报描述包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 提交举报
  async submitReport() {
    if (!this.data.selectedReportReason) {
      wx.showToast({
        title: '请选择举报原因',
        icon: 'none'
      })
      return
    }

    // 敏感词检查（如果有描述）
    if (this.data.reportDesc && this.data.reportDesc.trim()) {
      const descCheck = checkSensitiveWord(this.data.reportDesc.trim(), 'content');
      if (descCheck.hasSensitiveWord) {
        wx.showModal({
          title: '内容审核',
          content: '举报描述包含不当内容，请重新输入',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF6B35'
        });
        return;
      }
    }

    try {
      const res = await reportDish({
        dishId: this.data.dishId,
        reason: this.data.selectedReportReason,
        description: this.data.reportDesc
      })

      if (res.error === 0) {
        this.hideReportModal()
        wx.showToast({
          title: '举报成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: res.message || '举报失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('举报失败', error)
      wx.showToast({
        title: '举报失败',
        icon: 'none'
      })
    }
  },

  // 返回按钮点击事件
  goBack() {
    wx.navigateBack({
      delta: 1
    }).catch(() => {
      // 如果无法返回，则跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    })
  },

  onShareAppMessage() {
    return {
      title: `推荐美食：${this.data.dishInfo.name}`,
      path: `/pages/dish-detail/dish-detail?id=${this.data.dishId}&shared=1`,
      imageUrl: this.data.dishInfo.image
    }
  },

  // 分析图片并设置状态栏颜色
  analyzeImageAndSetStatusBar(imageUrl: string) {
    if (!imageUrl) return;

    // 先设置一个默认颜色，让返回按钮先显示出来
    // 通常图片顶部比较暗，先设为白色文字可能更合适
    this.setData({
      statusBarColor: '#ffffff'
    });

    // 使用getImageInfo获取图片信息
    wx.getImageInfo({
      src: imageUrl,
      success: (res) => {
        // 创建canvas上下文
        const ctx = wx.createCanvasContext('dishImageAnalysisCanvas');

        // 在canvas上绘制图片
        ctx.drawImage(res.path, 0, 0, 100, 100);
        ctx.draw(false, () => {
          // 绘制完成后获取图片数据
          setTimeout(() => {
            wx.canvasGetImageData({
              canvasId: 'dishImageAnalysisCanvas',
              x: 0,
              y: 0,
              width: 100,
              height: 100,
              success: (res) => {
                // 使用优化的颜色分析算法
                const colorAnalysis = this.analyzeImageColors(res.data, res.width, res.height);

                // 保存状态栏文字颜色到data
                this.setData({
                  statusBarColor: colorAnalysis.statusBarColor
                });

                // 设置状态栏样式
                wx.setNavigationBarColor({
                  frontColor: colorAnalysis.statusBarColor,
                  backgroundColor: colorAnalysis.dominantColor,
                  animation: {
                    duration: 300,
                    timingFunc: 'easeInOut'
                  }
                });
              },
              fail: (error) => {
                console.error('获取图片数据失败:', error);
              }
            });
          }, 100); // 给canvas绘制一点时间
        });
      },
      fail: (error) => {
        console.error('获取图片信息失败:', error);
        // 失败时设置默认颜色
        this.setData({
          statusBarColor: '#333333'
        });
      }
    });
  },

  // 优化的图片颜色分析算法
  analyzeImageColors(imageData: Uint8ClampedArray, width: number, height: number) {
    const pixelCount = width * height;

    // 1. 计算多种亮度指标
    let totalLuminance = 0;
    let totalBrightness = 0;

    // 2. 颜色分布统计
    const colorBuckets = {
      dark: 0,    // 0-85
      medium: 0,  // 86-170
      light: 0    // 171-255
    };

    // 3. 采样关键区域（顶部区域权重更高，因为状态栏在顶部）
    const topRegionWeight = 2.0;
    const middleRegionWeight = 1.0;
    const bottomRegionWeight = 0.5;

    let weightedLuminance = 0;
    let totalWeight = 0;

    for (let i = 0; i < pixelCount; i++) {
      const r = imageData[i * 4];
      const g = imageData[i * 4 + 1];
      const b = imageData[i * 4 + 2];
      const a = imageData[i * 4 + 3] / 255; // 透明度

      // 计算相对亮度（更准确的感知亮度）
      const luminance = this.calculateRelativeLuminance(r, g, b) * a;

      // 计算传统亮度
      const brightness = (r * 0.299 + g * 0.587 + b * 0.114) * a;

      totalLuminance += luminance;
      totalBrightness += brightness;

      // 根据位置计算权重
      const y = Math.floor(i / width);
      let weight = middleRegionWeight;

      if (y < height * 0.3) {
        weight = topRegionWeight; // 顶部30%
      } else if (y > height * 0.7) {
        weight = bottomRegionWeight; // 底部30%
      }

      weightedLuminance += luminance * weight;
      totalWeight += weight;

      // 颜色分布统计
      if (brightness < 85) {
        colorBuckets.dark++;
      } else if (brightness < 170) {
        colorBuckets.medium++;
      } else {
        colorBuckets.light++;
      }
    }

    // 4. 计算各种指标
    const avgLuminance = totalLuminance / pixelCount;
    const avgBrightness = totalBrightness / pixelCount;
    const weightedAvgLuminance = weightedLuminance / totalWeight;

    // 5. 颜色分布分析
    const darkRatio = colorBuckets.dark / pixelCount;
    const lightRatio = colorBuckets.light / pixelCount;
    const mediumRatio = colorBuckets.medium / pixelCount;

    // 6. 智能阈值计算
    let threshold = 0.5; // 默认阈值

    // 如果图片主要是深色或浅色，调整阈值
    if (darkRatio > 0.6) {
      threshold = 0.3; // 深色图片，更容易选择白色文字
    } else if (lightRatio > 0.6) {
      threshold = 0.7; // 浅色图片，更容易选择黑色文字
    } else {
      threshold = 0.5; // 混合色彩，使用标准阈值
    }

    // 7. 综合判断文字颜色
    const useWeightedLuminance = weightedAvgLuminance;
    const shouldUseDarkText = useWeightedLuminance > threshold;

    // 8. 计算对比度并进行微调
    const whiteContrast = this.calculateContrast(useWeightedLuminance, 1.0);
    const blackContrast = this.calculateContrast(useWeightedLuminance, 0.0);

    // 确保对比度足够（WCAG标准建议至少4.5:1）
    let finalStatusBarColor = shouldUseDarkText ? '#000000' : '#ffffff';

    // 如果对比度不够，强制使用对比度更高的颜色
    if (Math.max(whiteContrast, blackContrast) < 3.0) {
      if (whiteContrast > blackContrast) {
        finalStatusBarColor = '#ffffff';
      } else {
        finalStatusBarColor = '#000000';
      }
    }

    // 9. 计算主色调
    const dominantColor = this.calculateDominantColor(imageData, pixelCount);

    return {
      statusBarColor: finalStatusBarColor,
      dominantColor: dominantColor,
      avgLuminance: avgLuminance,
      avgBrightness: avgBrightness,
      weightedLuminance: useWeightedLuminance,
      colorDistribution: { darkRatio, mediumRatio, lightRatio },
      contrast: { white: whiteContrast, black: blackContrast }
    };
  },

  // 计算相对亮度（更准确的感知亮度）
  calculateRelativeLuminance(r: number, g: number, b: number): number {
    // 将RGB值转换为0-1范围
    const rs = r / 255;
    const gs = g / 255;
    const bs = b / 255;

    // 应用gamma校正
    const rLinear = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);
    const gLinear = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);
    const bLinear = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);

    // 计算相对亮度
    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  },

  // 计算对比度
  calculateContrast(luminance1: number, luminance2: number): number {
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  },

  // 计算主色调
  calculateDominantColor(imageData: Uint8ClampedArray, pixelCount: number): string {
    // 简化的主色调计算，取平均RGB值
    let totalR = 0, totalG = 0, totalB = 0;

    for (let i = 0; i < pixelCount; i++) {
      totalR += imageData[i * 4];
      totalG += imageData[i * 4 + 1];
      totalB += imageData[i * 4 + 2];
    }

    const avgR = Math.round(totalR / pixelCount);
    const avgG = Math.round(totalG / pixelCount);
    const avgB = Math.round(totalB / pixelCount);

    // 转换为十六进制
    const toHex = (n: number) => {
      const hex = n.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(avgR)}${toHex(avgG)}${toHex(avgB)}`;
  },

  // 获取胶囊按钮位置信息
  getMenuButtonPosition() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 20;
      
      // 获取胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      this.setData({
        statusBarHeight: statusBarHeight,
        menuButtonHeight: menuButtonInfo.height,
        menuButtonTop: menuButtonInfo.top
      });
    } catch (error) {
      console.error('获取胶囊按钮位置信息失败', error);
      // 设置默认值
      this.setData({
        statusBarHeight: 20,
        menuButtonHeight: 32,
        menuButtonTop: 48
      });
    }
  },

  // 刷新添加状态
  async refreshAddedStatus() {
    try {
      // 获取当前厨房ID
      let currentKitchenId = '';
      try {
        currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';
        if (!currentKitchenId) {
          const userInfo = wx.getStorageSync('userInfo');
          if (userInfo && userInfo.currentKitchenId) {
            currentKitchenId = userInfo.currentKitchenId;
          }
        }
      } catch (error) {
        console.log('获取用户厨房信息失败', error);
      }

      if (!currentKitchenId) {
        return;
      }

      // 重新获取菜品详情以更新添加状态
      const res = await getDishDetail(this.data.dishId, currentKitchenId);
      if (res.error === 0) {
        this.setData({
          isAdded: res.body.isAdded || false
        });
      }
    } catch (error) {
      console.error('刷新添加状态失败', error);
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const isLoggedIn = !!token
    
    // 根据登录状态设置评论功能是否可用
    this.setData({
      commentEnabled: isLoggedIn
    });
  },

  // 返回主页（分享页面专用）
  goBackToHome() {
    wx.switchTab({
      url: '/pages/restaurant/restaurant'
    })
  }
})