"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshToken = exports.getUserIdFromToken = exports.verifyToken = exports.generateToken = void 0;
/**
 * Token工具函数
 * 用于生成和验证JWT令牌
 */
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../config/config"));
const models_1 = require("../models");
const logger_1 = __importDefault(require("./logger"));
/**
 * 生成JWT令牌
 * @param user 用户对象
 * @returns JWT令牌
 */
const generateToken = (user) => {
    try {
        // 创建payload，只保留必要信息减少token大小
        const payload = {
            id: user.id,
            // 移除不必要的数据减小token大小
            // openId: user.open_id,
            // nickName: user.nick_name,
            // createdAt: new Date().toISOString() // 记录令牌创建时间
        };
        // 生成token
        // 使用Buffer类型作为secret
        const secret = Buffer.from(String(config_1.default.jwt.secret));
        // 设置过期时间，使用更精简的选项
        const options = {
            expiresIn: config_1.default.jwt.expiresIn,
            // 移除额外的JWT ID以减小token大小
            // jwtid: `user_${user.id}_${Date.now()}` // 添加JWT ID增强安全性
        };
        // 签名生成token
        const token = jsonwebtoken_1.default.sign(payload, secret, options);
        // 记录token长度
        logger_1.default.info(`成功为用户 ${user.id} 生成令牌，有效期 ${config_1.default.jwt.expiresIn}，长度: ${token.length}`);
        return token;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        logger_1.default.error(`生成令牌失败: ${errorMessage}`);
        throw new Error('生成用户令牌失败');
    }
};
exports.generateToken = generateToken;
/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @returns 解码后的payload或null
 */
const verifyToken = (token) => {
    try {
        if (!token || token.trim() === '') {
            logger_1.default.warn('尝试验证空令牌');
            return null;
        }
        // 使用Buffer类型作为secret
        const secret = Buffer.from(String(config_1.default.jwt.secret));
        const decoded = jsonwebtoken_1.default.verify(token, secret);
        // 记录验证结果，但不记录敏感信息
        if (decoded && typeof decoded === 'object' && 'id' in decoded) {
            logger_1.default.debug(`令牌验证成功: 用户ID ${decoded.id}`);
        }
        return decoded;
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            logger_1.default.warn(`令牌已过期: ${error.expiredAt}`);
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            logger_1.default.warn(`令牌验证失败: ${error.message}`);
        }
        else {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            logger_1.default.warn(`令牌验证出错: ${errorMessage}`);
        }
        return null;
    }
};
exports.verifyToken = verifyToken;
/**
 * 从令牌中解析用户ID
 * @param token JWT令牌
 * @returns 用户ID或null
 */
const getUserIdFromToken = (token) => {
    const decoded = (0, exports.verifyToken)(token);
    if (decoded && typeof decoded === 'object' && 'id' in decoded) {
        return Number(decoded.id);
    }
    return null;
};
exports.getUserIdFromToken = getUserIdFromToken;
/**
 * 刷新令牌
 * @param token 原令牌
 * @returns 新令牌或null（如果原令牌无效）
 */
const refreshToken = (token) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 验证原令牌
        const decoded = (0, exports.verifyToken)(token);
        if (!decoded || typeof decoded !== 'object' || !('id' in decoded)) {
            logger_1.default.warn('无法刷新无效令牌');
            return null;
        }
        // 从数据库获取用户信息
        const userId = Number(decoded.id);
        const user = yield models_1.User.findByPk(userId);
        if (!user) {
            logger_1.default.warn(`刷新令牌失败: 用户 ${userId} 不存在`);
            return null;
        }
        // 生成新令牌
        const newToken = (0, exports.generateToken)(user);
        logger_1.default.info(`已刷新用户 ${userId} 的令牌`);
        return newToken;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        logger_1.default.error(`刷新令牌失败: ${errorMessage}`);
        return null;
    }
});
exports.refreshToken = refreshToken;
exports.default = {
    generateToken: exports.generateToken,
    verifyToken: exports.verifyToken,
    getUserIdFromToken: exports.getUserIdFromToken,
    refreshToken: exports.refreshToken
};
//# sourceMappingURL=tokenUtils.js.map