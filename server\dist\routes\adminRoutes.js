"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 管理员模块路由
 * 处理后台管理系统相关的API路由
 */
const express_1 = __importDefault(require("express"));
const adminController_1 = __importDefault(require("../controllers/adminController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 管理员登录接口（不需要验证token）
router.post('/login', adminController_1.default.adminLogin);
// 仪表盘数据接口（需要验证admin token）
router.get('/dashboard/stats', auth_1.verifyAdminToken, adminController_1.default.getDashboardStats);
router.get('/dashboard/charts', auth_1.verifyAdminToken, adminController_1.default.getDashboardCharts);
router.get('/dashboard/recent-orders', auth_1.verifyAdminToken, adminController_1.default.getRecentOrders);
// 用户管理接口
router.get('/users', auth_1.verifyAdminToken, adminController_1.default.getUserList);
router.get('/users/stats', auth_1.verifyAdminToken, adminController_1.default.getUserStats);
router.get('/users/:id', auth_1.verifyAdminToken, adminController_1.default.getUserDetail);
router.put('/users/:id/status', auth_1.verifyAdminToken, adminController_1.default.updateUserStatus);
router.post('/users/:id/adjust-coins', auth_1.verifyAdminToken, adminController_1.default.adjustUserCoins);
router.post('/users/:id/reset-password', auth_1.verifyAdminToken, adminController_1.default.resetUserPassword);
// 厨房管理接口
router.get('/kitchens', auth_1.verifyAdminToken, adminController_1.default.getKitchenList);
router.get('/kitchens/stats', auth_1.verifyAdminToken, adminController_1.default.getKitchenStats);
router.put('/kitchens/:id/status', auth_1.verifyAdminToken, adminController_1.default.updateKitchenStatus);
// 菜品管理接口
router.get('/dishes', auth_1.verifyAdminToken, adminController_1.default.getDishList);
router.get('/dish-categories', auth_1.verifyAdminToken, adminController_1.default.getDishCategories);
router.get('/dishes/stats', auth_1.verifyAdminToken, adminController_1.default.getDishStats);
router.put('/dishes/:dishId', auth_1.verifyAdminToken, adminController_1.default.updateDish);
router.put('/dishes/:dishId/status', auth_1.verifyAdminToken, adminController_1.default.updateDishStatus);
router.delete('/dishes/:dishId', auth_1.verifyAdminToken, adminController_1.default.deleteDish);
router.post('/dishes/discover', auth_1.verifyAdminToken, adminController_1.default.addDishToDiscover);
router.delete('/dishes/:dishId/discover', auth_1.verifyAdminToken, adminController_1.default.removeDishFromDiscover);
// 订单管理接口
router.get('/orders', auth_1.verifyAdminToken, adminController_1.default.getOrderList);
router.get('/orders/stats', auth_1.verifyAdminToken, adminController_1.default.getOrderStats);
// 内容管理接口
router.get('/messages', auth_1.verifyAdminToken, adminController_1.default.getMessageList);
router.get('/messages/stats', auth_1.verifyAdminToken, adminController_1.default.getMessageStats);
router.get('/messages/:messageId', auth_1.verifyAdminToken, adminController_1.default.getMessage);
router.post('/messages', auth_1.verifyAdminToken, adminController_1.default.createMessage);
router.put('/messages/:messageId', auth_1.verifyAdminToken, adminController_1.default.updateMessage);
router.post('/messages/:messageId/publish', auth_1.verifyAdminToken, adminController_1.default.publishMessage);
router.post('/messages/:messageId/archive', auth_1.verifyAdminToken, adminController_1.default.archiveMessage);
router.delete('/messages/:messageId', auth_1.verifyAdminToken, adminController_1.default.deleteMessage);
router.get('/reports', auth_1.verifyAdminToken, adminController_1.default.getReportList);
router.get('/reports/stats', auth_1.verifyAdminToken, adminController_1.default.getReportStats);
router.get('/reports/:reportId', auth_1.verifyAdminToken, adminController_1.default.getReport);
router.put('/reports/:id/handle', auth_1.verifyAdminToken, adminController_1.default.handleReport);
router.put('/reports/:reportId', auth_1.verifyAdminToken, adminController_1.default.updateReport);
router.delete('/reports/:reportId', auth_1.verifyAdminToken, adminController_1.default.deleteReport);
// 评论管理接口
router.get('/comments', auth_1.verifyAdminToken, adminController_1.default.getCommentList);
router.get('/comments/stats', auth_1.verifyAdminToken, adminController_1.default.getCommentStats);
router.get('/comments/:commentId', auth_1.verifyAdminToken, adminController_1.default.getComment);
router.put('/comments/:commentId', auth_1.verifyAdminToken, adminController_1.default.updateComment);
router.delete('/comments/:commentId', auth_1.verifyAdminToken, adminController_1.default.deleteComment);
// 发现管理接口
router.get('/discover', auth_1.verifyAdminToken, adminController_1.default.getDiscoverList);
router.get('/discover/manage-stats', auth_1.verifyAdminToken, adminController_1.default.getDiscoverManageStats);
router.delete('/discover/:itemId', auth_1.verifyAdminToken, adminController_1.default.removeFromDiscover);
router.put('/discover/:itemId/type', auth_1.verifyAdminToken, adminController_1.default.updateDiscoverType);
router.put('/discover/:itemId/sort', auth_1.verifyAdminToken, adminController_1.default.updateDiscoverSort);
router.post('/discover', auth_1.verifyAdminToken, adminController_1.default.createDiscoverItem);
// 发现内容管理接口（保留兼容性）
router.get('/discover/contents', auth_1.verifyAdminToken, adminController_1.default.getDiscoverList);
router.get('/discover/stats', auth_1.verifyAdminToken, adminController_1.default.getDiscoverStats);
// 添加缺失的tags路由（使用discover列表代替）
router.get('/discover/tags', auth_1.verifyAdminToken, adminController_1.default.getDiscoverList);
// 任务管理接口
router.get('/tasks/signin/stats', auth_1.verifyAdminToken, adminController_1.default.getSignInStats);
router.get('/tasks/ads/stats', auth_1.verifyAdminToken, adminController_1.default.getAdStats);
// 签到管理接口
router.get('/checkin/config', auth_1.verifyAdminToken, adminController_1.default.getCheckInConfig);
router.get('/checkin/stats', auth_1.verifyAdminToken, adminController_1.default.getCheckInStats);
router.get('/checkin/records', auth_1.verifyAdminToken, adminController_1.default.getCheckInRecords);
// 广告观看记录接口
router.get('/adviews/records', auth_1.verifyAdminToken, adminController_1.default.getAdViewRecords);
// 搜索管理接口
router.get('/search/keywords', auth_1.verifyAdminToken, adminController_1.default.getHotKeywords);
router.get('/search/stats', auth_1.verifyAdminToken, adminController_1.default.getSearchStats);
// 管理员和角色管理接口
router.get('/admins', auth_1.verifyAdminToken, adminController_1.default.getAdminList);
router.get('/roles', auth_1.verifyAdminToken, adminController_1.default.getAdminRoles);
// 系统信息接口
router.get('/system/info', auth_1.verifyAdminToken, adminController_1.default.getSystemInfo);
exports.default = router;
//# sourceMappingURL=adminRoutes.js.map