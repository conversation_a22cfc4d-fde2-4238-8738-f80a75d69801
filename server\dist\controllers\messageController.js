"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const messageService_1 = __importDefault(require("../services/messageService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取消息数量
 * @route GET /api/message/messageCount
 */
const getMessageCount = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const counts = yield messageService_1.default.getMessageCount(userId);
        (0, response_1.success)(res, counts);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取标签页内容
 * @route GET /api/message/tabContent
 */
const getTabContent = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { type, page = 1, pageSize = 10 } = req.query;
        if (!type) {
            throw new error_1.BusinessError('缺少参数: type', response_1.ResponseCode.VALIDATION);
        }
        const content = yield messageService_1.default.getTabContent(userId, type, parseInt(page), parseInt(pageSize));
        (0, response_1.success)(res, content);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 标记消息为已读
 * @route POST /api/message/markRead
 */
const markMessageRead = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, type } = req.body;
        if ((!id && !type) || (id && type)) {
            throw new error_1.BusinessError('参数错误: 需要提供id或type其中之一', response_1.ResponseCode.VALIDATION);
        }
        yield messageService_1.default.markMessageRead(userId, id ? parseInt(id) : undefined, type);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取所有未读消息数量
 * @route GET /api/message/unreadCounts
 */
const getUnreadCounts = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const counts = yield messageService_1.default.getUnreadCounts(userId);
        (0, response_1.success)(res, counts);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 批量标记消息为已读
 * @route POST /api/message/markAllRead
 */
const markAllRead = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { type } = req.body;
        yield messageService_1.default.markAllRead(userId, type);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getMessageCount,
    getTabContent,
    markMessageRead,
    getUnreadCounts,
    markAllRead
};
//# sourceMappingURL=messageController.js.map