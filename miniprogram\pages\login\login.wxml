<!-- 登录页面 -->
<view class="login-container">
  <view class="login-header">
    <view class="logo-container">
      <image class="logo" src="{{defaultImages.LOGO}}" mode="aspectFill"></image>
    </view>
    <text class="title">电子菜单助手</text>
    <text class="subtitle">登录获取更多权益</text>
  </view>

  <view class="login-content">
    <button
      class="login-btn"
      bindtap="onLogin"
    >微信一键登录</button>

    <view class="privacy-tip">
      <text class="tip-text">登录代表您已同意</text>
      <text class="link-text" bindtap="showPrivacyDialog">《用户协议和隐私政策》</text>
    </view>
  </view>

  <view class="login-footer">
    <button class="cancel-btn" bindtap="navigateBack">暂不登录</button>
  </view>
</view>

<!-- 用户协议和隐私政策弹窗 -->
<modal-dialog 
  visible="{{showPrivacyModal}}" 
  title="用户协议和隐私政策" 
  showCancel="{{false}}" 
  confirmText="我知道了"
  themeColor="#FF6B35"
  bind:close="closePrivacyDialog"
  bind:confirm="closePrivacyDialog"
>
  <scroll-view scroll-y class="privacy-content">
    <view class="privacy-section">
      <view class="privacy-title">一、用户协议</view>
      <view class="privacy-text">
        <view class="paragraph">欢迎使用"电子菜单助手"小程序，在使用我们的产品与服务前，请您务必仔细阅读并了解本《用户协议》。</view>
        
        <view class="paragraph">1. 服务说明：本小程序为用户提供电子菜单、在线点餐等服务。</view>
        
        <view class="paragraph">2. 账号规范：用户需要使用微信账号登录本小程序，并对账号下的所有行为负责。</view>
        
        <view class="paragraph">3. 用户行为：用户应当遵守中华人民共和国相关法律法规，不得利用本小程序从事违法违规活动。</view>
        
        <view class="paragraph">4. 知识产权：本小程序的所有内容，包括但不限于文字、图片、音频、视频、代码等均受知识产权法律法规保护。</view>
      </view>
    </view>
    
    <view class="privacy-section">
      <view class="privacy-title">二、隐私政策</view>
      <view class="privacy-text">
        <view class="paragraph">我们非常重视用户的隐私和个人信息保护，您使用我们的服务时，我们可能会收集和使用您的相关信息。</view>
        
        <view class="paragraph">1. 信息收集：我们会收集您的登录凭证，用于识别您的身份。</view>
        
        <view class="paragraph">2. 信息使用：收集的信息将用于向您提供服务、优化产品体验以及保障账号安全。</view>
        
        <view class="paragraph">3. 信息共享：除法律法规规定或经您同意外，我们不会与任何第三方分享您的个人信息。</view>
        
        <view class="paragraph">4. 信息安全：我们会采取各种安全技术和程序，尽力保护您的个人信息不被泄露、毁损或丢失。</view>
        
        <view class="paragraph">5. 儿童隐私：本小程序不针对13岁以下的儿童提供服务，我们建议儿童在父母或监护人的指导下使用我们的服务。</view>
      </view>
    </view>
  </scroll-view>
</modal-dialog>