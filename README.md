# 电子菜单助手 - 微信小程序

## 项目简介

电子菜单助手是一个基于微信小程序的餐厅点菜系统，支持多厨房管理、菜品管理、订单处理等功能。现已集成微信支付APIv3，支持真实的大米充值功能。

## 🎉 新功能：微信支付APIv3集成 (2025-01-17)

### 功能概述

项目已完整集成微信支付APIv3，支持小程序内真实的大米充值功能。用户可以通过微信支付购买不同数量的大米（积分），用于系统内的各种服务。

### 充值套餐

- **1000大米** = ¥10 
- **3000大米** = ¥28 （推荐）
- **5000大米** = ¥45 
- **10000大米** = ¥88 

### 技术实现

#### 后端实现

**1. 微信支付服务** (`server/src/services/paymentService.ts`):
```typescript
class PaymentService {
  // 小程序统一下单
  async jsapiOrder(params: UnifiedOrderParams)
  
  // 查询订单状态
  async queryOrder(outTradeNo: string)
  
  // 关闭订单
  async closeOrder(outTradeNo: string)
  
  // 验证支付回调签名
  verifyNotification(signature, timestamp, nonce, body, serial)
  
  // 解密支付通知数据
  decryptNotification(encryptedData, nonce, associatedData)
  
  // 申请退款
  async refund(outTradeNo, outRefundNo, totalFee, refundFee, reason)
}
```

**2. 支付控制器** (`server/src/controllers/paymentController.ts`):
- `POST /api/payment/create` - 创建支付订单
- `GET /api/payment/query/:outTradeNo` - 查询订单状态
- `POST /api/payment/close` - 关闭订单
- `POST /api/payment/notify` - 微信支付回调
- `POST /api/payment/refund` - 申请退款
- `GET /api/payment/refund/:outRefundNo` - 查询退款状态

**3. 配置管理** (`server/src/config/config.ts`):
```typescript
wechatPay: {
  appId: process.env.WECHAT_PAY_APPID,
  mchId: process.env.WECHAT_PAY_MCHID,
  apiV3Key: process.env.WECHAT_PAY_API_V3_KEY,
  serialNo: process.env.WECHAT_PAY_SERIAL_NO,
  privateKeyPath: 'server/certs/wechatpay/apiclient_key.pem',
  certificatePath: 'server/certs/wechatpay/apiclient_cert.pem',
  notifyUrl: 'https://your-domain.com/api/payment/notify'
}
```

#### 前端实现

**1. 支付API** (`miniprogram/api/userApi.ts`):
```typescript
// 创建支付订单
export const createPaymentOrder = (amount: number, description: string)

// 查询支付订单状态
export const queryPaymentOrder = (outTradeNo: string)

// 关闭支付订单
export const closePaymentOrder = (outTradeNo: string)
```

**2. 支付流程** (`miniprogram/pages/my-coins/my-coins.ts`):
```typescript
async confirmPayment() {
  // 1. 创建支付订单
  const orderResult = await createPaymentOrder(paymentAmount, description)
  
  // 2. 调用微信支付
  await this.requestWechatPayment(orderResult.body)
}

async requestWechatPayment(paymentParams) {
  wx.requestPayment({
    timeStamp: paymentParams.timeStamp,
    nonceStr: paymentParams.nonceStr,
    package: paymentParams.package,
    signType: paymentParams.signType,
    paySign: paymentParams.paySign,
    success: () => this.handlePaymentSuccess(),
    fail: (err) => this.handlePaymentError(err)
  })
}
```

### 安全特性

✅ **APIv3签名验证** - 使用RSA签名确保请求安全性  
✅ **证书双向验证** - 客户端和服务端证书双重校验  
✅ **回调数据加密** - AES-256-GCM加密回调通知数据  
✅ **订单状态验证** - 支付成功后主动查询订单状态确认  
✅ **幂等性处理** - 防止重复支付和数据不一致  

### 支付流程

1. **用户选择充值金额** → 选择1000/3000/5000/10000大米套餐
2. **创建支付订单** → 后端调用微信支付统一下单API
3. **调用微信支付** → 前端调用wx.requestPayment()
4. **支付结果处理** → 成功后验证订单状态
5. **更新用户余额** → 微信回调触发大米充值
6. **记录交易明细** → 生成充值记录供用户查看

### 错误处理

- **网络异常** - 自动重试和友好提示
- **支付取消** - 优雅处理用户取消支付
- **签名验证失败** - 详细日志记录和异常处理
- **重复支付** - 订单号去重和幂等性保护
- **回调异常** - 多重验证确保数据一致性

### 配置要求

要启用微信支付功能，需要完成以下配置：

1. **微信商户号申请和配置**
2. **API证书下载和部署** 
3. **环境变量配置**
4. **服务器域名配置**

详细配置步骤请参考：[微信支付配置教程.md](./微信支付配置教程.md)

### 测试建议

开发环境测试：
- 使用微信支付沙箱环境
- 配置内网穿透接收回调
- 验证完整支付流程

生产环境部署：
- 确保HTTPS证书有效
- 配置正确的回调URL
- 监控支付成功率和异常

这个功能为小程序提供了完整的商业化能力，用户可以通过微信支付购买大米，用于系统内的各种服务和功能升级。

## 最新更新

### 🔧 菜品详情页分享功能 (2025-01-15)

在菜品详情页新增了分享功能，用户可以轻松分享美食菜品给好友。

#### 功能特点

**1. 分享按钮**：
- 位置：底部操作栏右侧，添加按钮旁边
- 图标：使用 `fenxiang.png` 分享图标
- 点击后提示用户通过右上角菜单分享

**2. 分享内容优化**：
- 分享标题：`推荐美食：{菜品名称}`
- 分享路径：包含 `shared=1` 参数标识分享页面
- 分享图片：使用菜品主图作为分享图片

**3. 分享页面特殊处理**：
- **返回按钮**：分享页面的返回按钮直接返回首页（餐厅页面）
- **评论功能**：保持后台控制，未登录用户点击评论框自动跳转登录页面
- **按钮可见性**：分享页面不隐藏操作按钮，保持完整功能

#### 技术实现

**前端实现** (`miniprogram/pages/dish-detail/`):
```typescript
// 数据字段
isSharedPage: false, // 标识是否是分享页面

// 分享点击事件
onShareTap() {
  // 提示用户使用右上角菜单分享
  wx.showModal({
    title: '分享菜品',
    content: '点击右上角菜单即可分享此菜品给好友'
  })
}

// 分享内容配置
onShareAppMessage() {
  return {
    title: `推荐美食：${this.data.dishInfo.name}`,
    path: `/pages/dish-detail/dish-detail?id=${this.data.dishId}&shared=1`,
    imageUrl: this.data.dishInfo.image
  }
}
```

**页面检测逻辑**：
```typescript
// 检查是否是分享页面
if (options.scene || options.shared === '1') {
  this.setData({ isSharedPage: true })
}

// 分享页面返回首页
goBackToHome() {
  wx.switchTab({ url: '/pages/restaurant/restaurant' })
}
```

**评论登录检查**：
```typescript
onCommentFocus() {
  const token = wx.getStorageSync('token')
  if (!token) {
    wx.navigateTo({ url: '/pages/login/login' })
    return
  }
  // 继续评论功能...
}
```

#### 用户体验

✅ **便捷分享**：底部明显的分享按钮，操作直观  
✅ **智能导航**：分享页面返回逻辑优化，直达首页  
✅ **登录引导**：未登录用户自动引导到登录页面  
✅ **完整功能**：分享页面保持所有功能可用  

#### 使用场景

1. **美食推荐**：用户发现好菜品，分享给朋友
2. **社交传播**：通过微信好友、群聊传播菜品信息
3. **引流获客**：分享页面引导新用户进入小程序
4. **口碑营销**：通过用户分享扩大小程序影响力

这个功能大大提升了菜品的传播能力，有助于小程序的用户增长和活跃度提升。

### 🔧 我的页面头像加载优化 (2025-01-15)

修复了我的页面切换时头像重复加载的问题，提升用户体验和性能。

#### 问题描述

**问题症状**：
- 每次切换到"我的"页面时，头像都会重新预加载
- 造成不必要的网络请求和资源浪费
- 用户体验不够流畅

**根本原因**：
```typescript
// 问题代码：每次onShow都重新获取所有数据
onShow() {
  if (this.data.isLoggedIn) {
    this.refreshAllData() // 总是调用
  }
}

// 总是预加载头像，不检查是否已缓存
preloadUserAvatar(userInfo) {
  wx.getImageInfo({ src: userInfo.avatarUrl }) // 重复加载
}
```

#### 修复方案

**1. 智能刷新机制** (`miniprogram/pages/mine/mine.ts`)：
```typescript
onShow() {
  const wasLoggedIn = this.data.isLoggedIn;
  this.checkLoginStatus()
  
  if (this.data.isLoggedIn) {
    // 只有刚登录时才获取完整数据
    if (!wasLoggedIn) {
      this.refreshAllData()
    } else {
      // 已登录状态只更新会员信息
      this.loadMemberInfo()
    }
  }
}
```

**2. 头像缓存检查**：
```typescript
preloadUserAvatar(userInfo) {
  if (!userInfo.avatarUrl) return;
  
  // 检查头像是否已变化
  const currentAvatarUrl = this.data.userInfo?.avatarUrl;
  if (currentAvatarUrl === userInfo.avatarUrl) {
    console.log('头像未变化，跳过预加载');
    return;
  }
  
  // 只有头像变化时才预加载
  wx.getImageInfo({ src: userInfo.avatarUrl });
}
```

**3. 本地数据更新**：
- 头像上传成功后直接更新本地数据，避免重新获取用户信息
- 个人资料保存后直接更新本地数据
- 同步更新本地存储，确保数据一致性

#### 性能提升

✅ **减少网络请求**：避免重复的用户信息获取和头像预加载  
✅ **提升响应速度**：页面切换更流畅，无不必要的等待时间  
✅ **优化用户体验**：头像显示更稳定，无闪烁或重加载现象  
✅ **降低服务器负载**：减少不必要的API调用  

#### 验证步骤

1. 登录后进入"我的"页面
2. 切换到其他页面再回来，确认头像不重复加载
3. 更换头像后，确认新头像正确显示且不重复获取数据
4. 修改个人资料后，确认信息更新且无重复请求

这个优化显著提升了"我的"页面的性能和用户体验，特别是对于网络较慢的用户环境。

### 🔧 背景图片缓存问题修复 (2025-01-15)

修复了背景设置组件中严重的图片缓存问题，解决了上传新背景图片时显示旧图片的问题。

#### 问题症状

1. **背景设置预览异常**：上传新背景图片后，预览显示的是之前上传过的旧图片
2. **后端文件正确但前端显示错误**：后端确实保存了正确的新图片，但前端显示的是随机的旧图片
3. **餐厅页面背景不更新**：保存背景设置后，餐厅页面显示的仍然是旧背景图
4. **随机性问题**：每次打开背景设置弹窗都可能显示不同的旧图片

#### 根本原因分析

**smart-image组件的全局缓存机制存在设计缺陷：**

1. **全局Set缓存污染**：
   ```typescript
   // 问题代码
   const imageCache = new Set<string>();
   ```
   - 使用简单的Set存储所有图片URL，无法区分加载状态
   - 对于背景图片等需要实时更新的内容，缓存机制反而造成了干扰

2. **缓存逻辑不当**：
   ```typescript
   // 问题逻辑
   const isCached = imageCache.has(cleanSrc);
   imageCache.add(cleanSrc); // 无论成功失败都加入缓存
   ```
   - 无论图片加载成功与否都会添加到缓存
   - 没有考虑相同路径但内容已更新的情况
   - 缺乏缓存过期机制

3. **时间戳处理不完整**：
   - 只在重试时添加时间戳，正常加载时不添加
   - 对于背景图片这种可能频繁更新的内容，应该总是添加时间戳

#### 修复方案

**1. 改进smart-image组件缓存机制 (`miniprogram/components/smart-image/smart-image.ts`)：**

- **升级缓存数据结构**：
  ```typescript
  // 新的缓存机制
  const imageCache = new Map<string, {
    loadTime: number,
    status: 'loading' | 'loaded' | 'error'
  }>();
  ```

- **添加缓存过期机制**：
  ```typescript
  const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟过期
  cleanExpiredCache(); // 定期清理过期缓存
  ```

- **背景图片特殊处理**：
  ```typescript
  isBackgroundImage(src: string): boolean {
    return src.includes('/uploads/background/') || 
           src.includes('/uploads/avatar/') ||
           src.includes('backgroundUrl') ||
           src.includes('background');
  }
  
  // 背景图片总是添加时间戳确保最新内容
  const processedSrc = this.isBackgroundImage(src) ? 
    this.addCacheBuster(src.trim(), true) : src.trim();
  ```

**2. 优化background-settings组件 (`miniprogram/components/background-settings/background-settings.ts`)：**

- **移除过度智能的缓存保护**：不再判断"最近上传"，总是使用最新的后端数据
- **添加强制刷新机制**：在获取到新背景图时，主动清除smart-image缓存
- **改进fetchBackgroundSettings方法**：确保总是显示最新的背景图

**3. 增强页面级缓存清理 (`miniprogram/pages/restaurant/restaurant.ts`, `miniprogram/pages/mine/mine.ts`)：**

- **餐厅页面背景更新**：在updateKitchenBackground时清除图片组件缓存
- **设置保存后强制刷新**：在保存背景设置后立即刷新所有相关图片组件

#### 技术改进详情

1. **智能缓存识别**：自动识别背景图片、头像等需要实时更新的图片类型
2. **缓存生命周期管理**：5分钟缓存过期机制，平衡性能和实时性
3. **组件级缓存控制**：提供resetAndReload方法强制刷新特定图片
4. **兼容性保障**：对于普通菜品图片等静态内容，仍保持高效缓存

#### 解决的问题

✅ **背景设置预览正确**：上传新背景图片后立即显示正确的预览  
✅ **餐厅页面背景同步**：保存设置后餐厅页面立即更新背景  
✅ **消除随机显示**：不再出现随机显示旧图片的问题  
✅ **后端前端一致**：前端显示与后端存储保持一致  
✅ **用户头像正常**：头像上传功能继续正常工作  

#### 验证步骤

1. **上传新背景图片**：检查预览是否立即显示新图片
2. **保存并查看餐厅页面**：确认餐厅页面背景已更新
3. **多次打开背景设置**：确认每次都显示最新背景图
4. **检查服务器文件**：确认旧背景图文件被正确删除
5. **测试其他图片功能**：确认菜品图片、头像等功能不受影响

这次修复从根本上解决了smart-image组件的缓存机制设计问题，确保了背景图片等动态内容的实时性，同时保持了静态内容的缓存性能优势。

### 敏感词检查功能 (2025-01-15)

新增了完整的敏感词检查功能，自动检测和过滤用户输入的不当内容，确保平台内容健康和合规。

#### 功能特点

1. **全面的敏感词库**
   - **政治敏感词**：政治、政府、官员等相关词汇
   - **色情内容**：不当性相关词汇
   - **暴力内容**：暴力、伤害等词汇
   - **违法犯罪**：毒品、赌博、诈骗等违法行为
   - **歧视性语言**：种族、性别、地域歧视等
   - **宗教极端**：宗教仇恨、极端主义等
   - **网络欺凌**：恶意攻击、侮辱性词汇
   - **广告营销**：垃圾广告、联系方式等

2. **智能检测算法**
   - 基于Trie树（前缀树）的高效匹配算法
   - 支持去除空格、特殊字符的模糊匹配
   - 大小写不敏感检测
   - 实时检测和批量检测

3. **分类型检测策略**
   - **用户名检测**：严格检查政治、色情、歧视、网络欺凌
   - **内容检测**：除广告外的所有敏感内容
   - **菜品名称检测**：相对宽松，主要检查明显不当内容
   - **评论检测**：包含所有类型的敏感词

4. **多种处理方式**
   - **检测模式**：仅检测是否包含敏感词
   - **过滤模式**：用*号或自定义字符替换敏感词
   - **建议模式**：提供过滤后的建议文本
   - **阻止模式**：直接阻止包含敏感词的内容提交

#### 已实施的检查点（高优先级）

✅ **用户个人信息**
- `pages/mine/mine.ts` - 用户昵称输入检查
- 实时检测和提交前验证
- 友好的错误提示

✅ **反馈系统**
- `pages/mine/mine.ts` - 问题反馈描述检查
- 实时检测敏感内容
- 提交前统一验证

✅ **厨房管理**
- `pages/kitchen-manage/kitchen-manage.ts` - 厨房名称和公告检查
- 创建厨房时的敏感词验证
- 编辑厨房信息时的内容审核

✅ **评论系统**
- `pages/dish-detail/dish-detail.ts` - 菜品评论内容检查
- 举报描述内容审核
- 实时输入提示

✅ **菜品管理**
- `pages/add-dish/add-dish.ts` - 菜品名称、描述、配料、步骤检查
- 全面的表单验证
- 保存前统一审核

✅ **分类管理**
- `components/category-manager/category-manager.ts` - 分类名称检查
- 字数限制：最多20个字符
- 实时敏感词检测和提交前验证

✅ **桌号管理**
- `components/table-manager/table-manager.ts` - 桌号名称检查
- 字数限制：最多10个字符
- 实时敏感词检测和提交前验证

✅ **订单系统**
- `pages/submit-order/submit-order.ts` - 订单备注检查
- 字数限制：最多200个字符
- 实时敏感词检测和提交前验证

#### 辅助工具

✅ **统一检查工具**
- `utils/sensitiveWordHelper.ts` - 提供常用检查场景的便捷函数
- 统一的错误处理和提示
- 批量表单验证功能

#### 使用场景

1. **菜品发布**：菜品名称、描述、配料名称、烹饪步骤等内容检查
2. **用户信息**：用户昵称、个性签名、个人简介等检查
3. **互动内容**：评论、留言、反馈内容检查
4. **实时输入**：输入框实时检测和表单提交前检查
5. **厨房管理**：厨房名称、公告等内容审核

#### 技术实现

- **前端**：使用微信小程序原生框架开发
- **API设计**：遵循RESTful规范
- **文件上传**：支持图片上传到服务器
- **状态管理**：完整的反馈状态流转（待处理→处理中→已完成/已拒绝）

#### 性能优化

- **内存效率**：使用Trie树减少内存占用
- **检测速度**：O(n)时间复杂度的高效算法
- **延迟初始化**：按需加载敏感词库
- **缓存机制**：常用检测结果缓存

### 问题反馈功能 (2025-01-15)

新增了完整的问题反馈功能，用户可以通过小程序向开发团队反馈问题和建议。

#### 功能特点

1. **多种反馈类型**
   - 问题反馈：报告系统bug或使用问题
   - 功能建议：提出新功能需求或改进建议
   - 其他：其他类型的反馈

2. **丰富的反馈内容**
   - 文字描述：最多500字符的详细描述
   - 图片上传：最多3张截图，支持jpg、png、gif格式
   - 联系方式：可选填写联系方式便于回复
   - 设备信息：自动收集设备信息便于问题定位

3. **用户友好的界面**
   - 模态框弹窗设计，不影响主要功能使用
   - 实时字符计数，避免超出限制
   - 图片预览和删除功能
   - 优雅的动画效果

#### 技术实现

- **前端**：使用微信小程序原生框架开发
- **API设计**：遵循RESTful规范
- **文件上传**：支持图片上传到服务器
- **状态管理**：完整的反馈状态流转（待处理→处理中→已完成/已拒绝）

#### 使用方法

1. 在"我的"页面点击"问题反馈"按钮
2. 选择反馈类型（问题反馈/功能建议/其他）
3. 填写详细的问题描述（必填）
4. 可选择上传相关截图（最多3张）
5. 可选填写联系方式
6. 点击"提交反馈"完成提交

#### 管理功能

管理员可以通过后台管理系统：
- 查看所有用户反馈
- 按类型和状态筛选反馈
- 更新反馈处理状态
- 回复用户反馈

### 数据状态管理优化 (2025-01-15)

修复了切换厨房时的数据残留问题，提升了用户体验的一致性。

#### 问题描述

在餐厅页面和其他相关页面中，当用户切换到没有分类或菜品的厨房时，页面会保留上一个厨房的数据，导致用户看到错误的信息。

#### 修复内容

1. **餐厅页面优化** (`miniprogram/pages/restaurant/restaurant.ts`)
   - 修复切换厨房时的数据清理逻辑
   - 在厨房切换时立即清空所有相关数据
   - 完善 `fetchCategories` 和 `fetchDishList` 的错误处理
   - 确保在API失败或数据为空时正确清空状态

2. **发现页面优化** (`miniprogram/pages/discover/discover.ts`)
   - 修复分类数据的清理逻辑
   - 在无厨房ID或获取分类失败时清空分类数据
   - 改进错误提示信息

3. **添加菜品页面优化** (`miniprogram/pages/add-dish/add-dish.ts`)
   - 完善分类数据的状态管理
   - 在获取分类失败时正确清空数据
   - 添加用户友好的提示信息

#### 技术改进

- **状态清理机制**：在切换厨房时主动清空所有相关数据
- **错误处理优化**：在API调用失败或数据为空时正确处理状态
- **用户体验提升**：避免显示错误的历史数据，确保数据一致性
- **代码健壮性**：增强了对异常情况的处理能力

#### 修复的状态包括

- 分类列表 (`categories`)
- 菜品列表 (`dishList`)
- 购物车数据 (`cart`)
- 搜索状态 (`isSearching`, `searchKeyword`, `searchResults`)
- 排序模式 (`sortModeCategory`)
- 当前标签页 (`currentTab`)

这些改进确保了用户在切换厨房时获得干净、准确的数据展示，避免了数据混乱问题。

### 用户体验优化 (2025-01-15)

优化了发现页面的分类弹窗数据同步和餐厅页面的搜索动画效果。

#### 修复内容

1. **发现页面分类弹窗数据同步** (`miniprogram/pages/discover/discover.ts`)
   - 新增厨房变更检查机制
   - 在页面显示时自动检测厨房ID变化
   - 强制重新加载分类数据，确保数据一致性
   - 在点击添加按钮时也进行厨房变更检查

2. **餐厅页面搜索动画优化** (`miniprogram/pages/restaurant/restaurant.wxss`)
   - 优化搜索容器的展开动画，使用更平滑的缓动函数
   - 移除跳动效果，改用scale缩放动画
   - 优化搜索输入框和取消按钮的淡入效果
   - 删除不必要的关键帧动画，减少代码复杂度

#### 技术改进

- **厨房变更监听**：自动检测用户切换厨房的操作
- **数据强制刷新**：确保分类弹窗中显示的是当前厨房的分类
- **动画优化**：使用`cubic-bezier`缓动函数实现更自然的动画效果
- **性能提升**：减少不必要的DOM操作和重复请求

#### 解决的问题

- ✅ 切换厨房后发现页面分类弹窗数据不更新
- ✅ 餐厅页面搜索展开时的跳动效果
- ✅ 搜索动画的流畅度和用户体验

现在用户在切换厨房后，发现页面的分类弹窗会自动更新为当前厨房的分类数据，搜索功能也有了更加流畅自然的展开动画效果。

### 菜品详情页面UI优化 (2025-01-15)

优化了菜品详情页面的视觉效果，提升用户体验和页面美观性。

#### 优化内容

1. **内容区域层叠效果** (`miniprogram/pages/dish-detail/dish-detail.wxss`)
   - 菜品信息卡片向上移动40rpx，覆盖图片底部
   - 增加层级管理，确保内容正确显示
   - 优化圆角设计，顶部采用更大圆角增强层叠感

2. **阴影效果增强**
   - 加强内容卡片阴影，提升立体感
   - 优化阴影参数，使层叠效果更自然

3. **图片指示器位置调整**
   - 调整图片指示器位置，避免被内容卡片遮挡
   - 提高层级，确保正确显示在最上层

#### 视觉效果

- ✨ **层次感**：内容卡片覆盖图片底部，创造自然的层叠效果
- ✨ **立体感**：增强的阴影和圆角设计，提升视觉深度
- ✨ **美观性**：更现代化的卡片式设计，符合当前设计趋势
- ✨ **兼容性**：保持所有功能不变，仅优化视觉表现

这种设计广泛应用于现代移动应用中，如美团、饿了么等，能够有效提升用户的视觉体验。

### 真实微信登录实现 (2025-01-15)

完成了从模拟登录到真实微信API登录的升级，现在使用微信官方API进行用户身份验证。

#### 更新内容

1. **后端微信服务** (`server/src/services/wechatService.ts`)
   - 新增微信服务模块，封装微信API调用
   - 实现 `jscode2session` 接口调用
   - 完善的错误处理和日志记录
   - 微信配置验证功能

2. **用户登录服务优化** (`server/src/services/userService.ts`)
   - 移除模拟登录逻辑
   - 集成真实微信API调用
   - 使用真实的 `openId` 作为用户唯一标识
   - 保持原有的用户创建和初始化流程

3. **前端登录优化** (`miniprogram/pages/login/login.ts`)
   - 移除Mock模式判断逻辑
   - 直接使用真实后端API
   - 优化用户信息处理流程
   - 使用后端返回的真实用户信息

#### 技术特点

- **安全性**：使用微信官方认证体系，确保用户身份真实性
- **稳定性**：完善的错误处理机制，包括网络超时、API错误等
- **兼容性**：保持原有的用户数据结构，平滑升级
- **可维护性**：模块化设计，便于后续功能扩展

#### 微信API错误码处理

- `40013`: 微信AppID无效
- `40029`: 登录code无效  
- `40125`: 微信AppSecret无效
- `45011`: API调用频率限制

#### 配置要求

确保在环境变量中正确配置：
```