.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F9F9F9;
  padding-bottom: 40rpx;
}

/* 公告样式 - 修改公告边距，使其与导航栏衔接 */
.notice-container {
  display: flex;
  align-items: center;
  background-color: #FFF8F5;
  padding: 16rpx 0rpx;
  margin: 0 0 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.notice-icon {
  width: 48rpx;
  height: 48rpx;
  flex-shrink: 0;
  margin: 0 16rpx 0 30rpx;
}

.notice-content {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
}

.scrolling-text {
  display: inline-block;
  color: #FF6B35;
  font-size: 26rpx;
}

.animate-scroll {
  animation: scrollText 15s linear infinite;
}

@keyframes scrollText {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 表单容器 */
.form-container {
  padding: 0 30rpx 40rpx;
  flex: 1;
}

/* 表单部分通用样式 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  animation: fadeInUp 0.4s ease-out;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

/* 添加section-header样式用于包含标题和添加按钮 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.section-header .section-title {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 16rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  color: #FF6B35;
  font-size: 24rpx;
}

.add-btn .add-icon {
  margin-right: 4rpx;
  font-size: 24rpx;
}

/* 上传图片区域 - 居中样式 */
.upload-center-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.upload-container {
  width: 240rpx;
  height: 240rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 16rpx;
  overflow: hidden;
  border: 2rpx dashed #CCCCCC;
  margin-bottom: 16rpx;
}

/* 添加文字上传按钮样式 */
.text-upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 30rpx;
  background-color: #F5F5F5;
  color: #FF6B35;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin: 16rpx 0;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999999;
}

.upload-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.upload-text-hint {
  font-size: 26rpx;
  color: #999999;
  margin-top: 10rpx;
  text-align: center;
}

/* 图片预览列表 */
.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  margin-bottom: 16rpx;
}

.image-preview-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  margin: 8rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-image-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 0 0 0 8rpx;
  font-size: 30rpx;
  z-index: 10;
}

.upload-preview {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

/* 表单组 */
.form-group {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333333;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-textarea {
  flex: 1;
  height: 120rpx;
  font-size: 28rpx;
  color: #333333;
  width: 100%;
  padding: 16rpx 0;
  line-height: 1.4;
}

/* 星级评分 - 调整间距 */
.rating-stars {
  display: flex;
  align-items: center;
  padding: 0rpx 0; /* 添加和价格栏一致的上下边距 */
}

.star {
  font-size: 40rpx;
  color: #DDDDDD;
  margin-right: 2rpx; /* 减少星星之间的间距，使其更紧凑 */
}

.star.filled {
  color: #FFBA49;
}

/* 星级图标样式 */
.star-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
  object-fit: contain;
}

/* 分类选择 */
.category-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 10rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999999;
}

/* 配料表样式 */
.ingredients-container {
  padding: 10rpx 0;
}

.ingredient-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.ingredient-input-group {
  display: flex;
  flex: 1;
  border: 1rpx solid #EEEEEE;
  border-radius: 8rpx;
  overflow: hidden;
}

.ingredient-name {
  flex: 2;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border-right: 1rpx solid #EEEEEE;
}

.ingredient-amount {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #E53935;
  font-size: 36rpx;
  margin-left: 16rpx;
}

/* 删除图标样式 */
.delete-icon {
  width: 40rpx;
  height: 40rpx;
  object-fit: contain;
}

.add-ingredient-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  color: #FF6B35;
  font-size: 26rpx;
  margin-top: 16rpx;
}

.add-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 烹饪步骤样式 - 修改布局和配色，与配料表一致 */
.steps-container {
  padding: 10rpx 0;
}

.step-item {
  border: 1rpx solid #EEEEEE;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #FFFFFF;
}

/* 减小步骤标题上下边距并添加排序和白色背景 */
.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 20rpx;
  background-color: #F5F5F5;
  border-bottom: 1rpx solid #EEEEEE;
}

/* 添加白色背景类 */
.white-bg {
  background-color: #FFFFFF;
}

.step-number {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

/* 步骤操作按钮组 */
.step-actions {
  display: flex;
  align-items: center;
}

/* 排序按钮样式 */
.sort-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 28rpx;
  margin-left: 10rpx;
}

/* 排序图标样式 */
.sort-icon {
  width: 40rpx;
  height: 40rpx;
  object-fit: contain;
}

.step-content {
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center; /* 从flex-start改为center，使图片居中 */
}

.step-image-wrapper {
  width: 160rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-image-container {
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  border: 1rpx solid #EEEEEE;
  overflow: hidden;
}

.step-image {
  width: 100%;
  height: 100%;
}

.step-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #999999;
  font-size: 40rpx;
}

.step-camera-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
  opacity: 0.6;
}

.step-upload-text {
  font-size: 20rpx;
  color: #666666;
  text-align: center;
}

.step-text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-title {
  font-size: 28rpx;
  font-weight: bold;
  height: 60rpx;
  margin-bottom: 16rpx;
  border-bottom: 1rpx solid #FF6B35;
  color: #333333;
}

.step-description {
  font-size: 26rpx;
  width: 100%;
  height: 120rpx;
  line-height: 1.4;
}

.add-step-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  color: #FF6B35;
  font-size: 26rpx;
}

/* 营养信息样式 */
.nutrition-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
}

.nutrition-item .form-label {
  width: 100%;
  margin-bottom: 12rpx;
}

.nutrition-item .form-input {
  height: 70rpx;
  border: 1rpx solid #EEEEEE;
  border-radius: 8rpx;
  padding: 0 16rpx;
}

/* 底部固定按钮 */
.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 10rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
}

/* 使用view作为按钮 */
.cancel-btn, .save-btn {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  margin: 0 50rpx;
  text-align: center;
  box-sizing: border-box;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.save-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
}

/* 添加点击效果 */
.cancel-btn:active {
  opacity: 0.8;
}

.save-btn:active {
  opacity: 0.8;
}

/* 分类选择弹窗内容 */
.category-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 16rpx 0;
  max-height: 400rpx; /* 限制高度，约显示三行 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #F9F9F9;
  transition: all 0.2s ease;
}

.category-item.active {
  background-color: #FFF1EB;
  border: 1rpx solid #FF6B35;
}

.category-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 12rpx;
  object-fit: contain;
}

.category-name {
  font-size: 26rpx;
  color: #333333;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 