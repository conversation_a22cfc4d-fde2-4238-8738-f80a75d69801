/* 发现页面样式 */
.discover-page {
  width: 100%;
  height: 100vh;
  background-color: #F9F9F9;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 固定顶部区域 */
.fixed-header {
  position: relative;
  width: 100%;
  background-color: #FFFFFF;
  z-index: 100;
}

/* 顶部标签页样式 - 修改为图标+文字按钮 */
.tab-header {
  display: flex;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
  position: relative;
}

.tab-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 10rpx 0;
}

.tab-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666666;
}

.tab-button.active .tab-text {

  font-weight: 500;
}

.badge {
  position: absolute;
  top: 0;
  right: 50%;
  margin-right: -35rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #E53935;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-sizing: border-box;
}

/* 搜索栏和排序按钮样式 */
.search-sort-container {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #EEEEEE;
  position: relative;
  z-index: 10;
}

.search-container {
  flex: 1;
  height: 72rpx;
  background-color: #F5F5F5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 26rpx;
  color: #333333;
  margin-left: 10rpx;
}

.cancel-text {
  display: flex;
  align-items: center;
  justify-content: center;
  color:rgba(0, 0, 0, 0.49);
  font-size: 25rpx;
  padding: 8rpx 12rpx;
  background-color: transparent;
}

.sort-button {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
  padding: 0 10rpx;
  position: relative;
}

.sort-arrow {
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #666666;
  margin-left: 8rpx;
  transition: transform 0.3s;
}

.sort-arrow.open {
  transform: rotate(180deg);
}

.sort-dropdown {
  position: absolute;
  top: 60rpx;
  right: 0;
  width: 200rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sort-option {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666666;
  border-bottom: 1rpx solid #F5F5F5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #FF6B35;
}

/* 可滚动区域 */
.scrollable-content {
  flex: 1;
  height: calc(100vh - 360rpx); /* 调整高度以适应不同设备 */
  background-color: #F9F9F9;
}

/* 下拉刷新动画由scroll-view内置管理，无需自定义样式 */

/* 菜品网格布局 */
.dish-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

/* 菜品卡片样式 */
.dish-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.dish-card:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.dish-image {
  width: 100%;
  height: 240rpx;
  background-color: #F5F5F5;
}

.dish-info {
  padding: 16rpx;
}

.dish-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.dish-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  max-width: calc(100% - 50rpx);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.add-button {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}

.add-button.added {
  /* 已添加状态保持一致 */
}

.add-icon {
  width: 60rpx;
  height: 60rpx;
}

.user-info {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999999;
}

.user-info .avatar-area {
  margin-right: 8rpx;
  flex-shrink: 0;
}

.user-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
}

.user-name {
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10rpx;
}

.added-count {
  flex: 1;
  text-align: right;
}

/* 无数据状态 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 分类选择对话框 */
.category-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.category-item {
  padding: 24rpx;
  font-size: 28rpx;
  color: #333333;
  border-bottom: 1rpx solid #F5F5F5;
  transition: background-color 0.3s;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item.selected {
  background-color: #FFF1EB;
  color: #FF6B35;
}

.category-item:active {
  background-color: #F9F9F9;
}

.category-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.category-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  object-fit: contain;
}

.category-name {
  font-size: 28rpx;
  color: inherit;
}

.category-count {
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
}

.category-item.selected .category-count {
  color: #FF6B35;
}

/* 空分类提示 */
.empty-categories {
  padding: 40rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

/* 排序菜单样式 */
.sort-menu {
  position: absolute;
  top: 220%;
  right: 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200rpx;
  opacity: 0;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  pointer-events: none;
}

.sort-menu.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.sort-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #F5F5F5;
  transition: background-color 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sort-item:last-child {
  border-bottom: none;
}

.sort-item.active {
  background-color: #FFF1EB;
}

.sort-item.active .sort-text {
  color: #FF6B35;
}

.sort-item.active .sort-desc {
  color: #FF6B35;
}

.sort-item:active {
  background-color: #F9F9F9;
}

.sort-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.sort-desc {
  font-size: 24rpx;
  color: #999999;
}

/* 加载动画效果（参考订单页面） */
.slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}