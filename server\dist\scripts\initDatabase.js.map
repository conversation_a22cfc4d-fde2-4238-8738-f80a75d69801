{"version": 3, "file": "initDatabase.js", "sourceRoot": "", "sources": ["../../src/scripts/initDatabase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;GAGG;AACH,6DAAmC;AACnC,8DAAsC;AACtC,6DAAqC;AAErC;;;GAGG;AACH,SAAe,YAAY;;QACzB,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE3B,cAAc;QACd,MAAM,UAAU,GAAG,MAAM,iBAAK,CAAC,gBAAgB,CAAC;YAC9C,IAAI,EAAE,gBAAM,CAAC,QAAQ,CAAC,IAAI;YAC1B,IAAI,EAAE,gBAAM,CAAC,QAAQ,CAAC,IAAI;YAC1B,IAAI,EAAE,gBAAM,CAAC,QAAQ,CAAC,IAAI;YAC1B,QAAQ,EAAE,gBAAM,CAAC,QAAQ,CAAC,QAAQ;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,eAAe;YACf,gBAAM,CAAC,IAAI,CAAC,WAAW,gBAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC;YAClD,MAAM,UAAU,CAAC,KAAK,CACpB,mCAAmC,gBAAM,CAAC,QAAQ,CAAC,IAAI;;kCAE3B,CAC7B,CAAC;YACF,gBAAM,CAAC,IAAI,CAAC,OAAO,gBAAM,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC;YAEpD,QAAQ;YACR,MAAM,UAAU,CAAC,KAAK,CAAC,SAAS,gBAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;YAE1D,UAAU;YACV,gBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5B,MAAM,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC5C,MAAM,UAAU,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACpD,MAAM,UAAU,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAE/D,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,UAAU,CAAC,GAAG,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;CAAA;AAED,mBAAmB;AACnB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,YAAY,EAAE;SACX,IAAI,CAAC,GAAG,EAAE;QACT,gBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,gBAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kBAAe,YAAY,CAAC"}