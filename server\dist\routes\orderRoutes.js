"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 订单模块路由
 * 处理订单相关的API路由
 */
const express_1 = __importDefault(require("express"));
const orderController_1 = require("../controllers/orderController");
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 提交订单
router.post('/submit', auth_1.verifyToken, orderController_1.submitOrder);
// 获取订单列表
router.get('/list', auth_1.verifyToken, orderController_1.getOrderList);
// 获取订单详情
router.get('/detail', auth_1.verifyToken, orderController_1.getOrderDetail);
// 获取订单详情（公开访问，用于分享）
router.get('/public/:id', orderController_1.getPublicOrderDetail);
// 取消订单
router.post('/cancel', auth_1.verifyToken, orderController_1.cancelOrder);
// 拒绝订单
router.post('/reject', auth_1.verifyToken, orderController_1.rejectOrder);
// 接单
router.post('/accept', auth_1.verifyToken, orderController_1.acceptOrder);
// 开始烹饪
router.post('/startCooking', auth_1.verifyToken, orderController_1.startCooking);
// 完成订单
router.post('/complete', auth_1.verifyToken, orderController_1.completeOrder);
// 取消烹饪
router.post('/cancelCooking', auth_1.verifyToken, orderController_1.cancelCooking);
// 取消已接单订单
router.post('/cancelAcceptedOrder', auth_1.verifyToken, orderController_1.cancelAcceptedOrder);
exports.default = router;
//# sourceMappingURL=orderRoutes.js.map