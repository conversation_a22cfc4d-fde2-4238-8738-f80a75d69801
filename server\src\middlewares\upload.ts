/**
 * 文件上传中间件
 * 处理文件上传相关功能
 */
import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import config from '../config/config';
import logger from '../utils/logger';
import { BusinessError } from './error';
import { generateFileName, compressImage } from '../utils/imageCompressor';
import { getRelativePath } from '../utils/urlManager'; // 引入URL管理器

// 确保上传目录存在
const uploadDir = path.resolve(process.cwd(), config.upload.dir);
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 创建子目录
const createSubDir = (subDir: string): string => {
  const dir = path.join(uploadDir, subDir);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return dir;
};

// 创建各类型文件的子目录
createSubDir('dish'); // 菜品图片目录
createSubDir('avatar'); // 头像图片目录
createSubDir('background'); // 背景图片目录
createSubDir('category'); // 分类图片目录
createSubDir('kitchen'); // 厨房图片目录

// 临时存储配置（用于接收原始文件）
const tempStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 临时目录
    const tempDir = path.join(uploadDir, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // 临时文件名
    const tempName = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${path.extname(file.originalname)}`;
    cb(null, tempName);
  },
});

// 文件过滤器
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 检查文件类型
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new BusinessError(`不支持的文件类型: ${file.mimetype}，仅支持 ${config.upload.allowedTypes.join(', ')}`));
  }
};

// 创建multer实例
const upload = multer({
  storage: tempStorage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize, // 文件大小限制
  },
});

/**
 * 获取文件类型
 * @param requestPath 请求路径
 * @returns 文件类型和子目录
 */
const getFileTypeFromPath = (requestPath: string): { type: string, subDir: string } => {
  if (requestPath.includes('avatar') || requestPath.includes('userAvatar')) {
    return { type: 'avatar', subDir: 'avatar' };
  } else if (requestPath.includes('background')) {
    return { type: 'background', subDir: 'background' };
  } else if (requestPath.includes('category')) {
    return { type: 'category', subDir: 'category' };
  } else if (requestPath.includes('kitchen')) {
    return { type: 'kitchen', subDir: 'kitchen' };
  } else {
    return { type: 'dish', subDir: 'dish' };
  }
};

/**
 * 处理图片压缩和重命名
 * @param req 请求对象
 * @param file 文件对象
 * @returns 处理后的文件信息
 */
const processImage = async (req: Request, file: Express.Multer.File) => {
  const { type, subDir } = getFileTypeFromPath(req.path);
  const userId = req.user?.id;
  
  if (!userId) {
    throw new BusinessError('用户未登录');
  }
  
  // 获取厨房ID（如果需要）
  let kitchenId: string | undefined;
  if (type === 'kitchen' || type === 'dish' || type === 'category') {
    kitchenId = req.body.kitchenId || req.query.kitchenId as string;
    if (!kitchenId) {
      throw new BusinessError(`${type}类型的图片需要提供厨房ID`);
    }
  }
  
  // 生成新文件名
  const newFileName = generateFileName(
    userId, 
    type as 'avatar' | 'background' | 'kitchen' | 'dish' | 'category',
    kitchenId,
    '.jpg' // 压缩后统一为jpg格式
  );
  
  // 目标文件路径
  const targetDir = path.join(uploadDir, subDir);
  const targetPath = path.join(targetDir, newFileName);
  
  // 压缩图片
  await compressImage(file.path, targetPath, type as any);
  
  // 删除临时文件
  if (fs.existsSync(file.path)) {
    fs.unlinkSync(file.path);
  }
  
  return {
    filename: newFileName,
    path: targetPath,
    type,
    subDir
  };
};

/**
 * 单文件上传中间件
 * @param fieldName 文件字段名
 */
export const uploadSingleFile = (fieldName: string = 'file') => {
  return (req: Request, res: Response, next: NextFunction) => {
    logger.debug(`开始处理文件上传请求: ${req.path}, 字段名: ${fieldName}`);

    upload.single(fieldName)(req, res, async (err: any) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          // Multer错误
          if (err.code === 'LIMIT_FILE_SIZE') {
            logger.warn(`文件大小超过限制: ${config.upload.maxFileSize / 1024 / 1024}MB`);
            return next(new BusinessError(`文件大小超过限制 (${config.upload.maxFileSize / 1024 / 1024}MB)`));
          } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            logger.warn(`意外的文件字段: ${err.field}, 期望: ${fieldName}`);
            return next(new BusinessError(`上传失败: 表单字段名称错误，应为 "${fieldName}"`));
          }
          logger.warn(`文件上传错误: ${err.code} - ${err.message}`);
          return next(new BusinessError(`文件上传错误: ${err.message}`));
        }
        logger.error(`上传处理错误:`, err);
        return next(err);
      }
      
      // 检查请求中是否有文件
      if (!req.file) {
        logger.warn('请求中没有文件');
        return next(new BusinessError('未提供文件'));
      }
      
      try {
        // 处理图片压缩和重命名
        const processedFile = await processImage(req, req.file);
        
        // 更新req.file信息
        req.file.filename = processedFile.filename;
        req.file.path = processedFile.path;
        
        // 记录上传信息
        logger.info(`文件上传并压缩成功: ${processedFile.filename}`, {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size,
          type: processedFile.type,
          subDir: processedFile.subDir,
          newPath: processedFile.path
        });
        
        next();
      } catch (error) {
        // 清理临时文件
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        
        logger.error(`图片处理失败:`, error);
        return next(error);
      }
    });
  };
};

/**
 * 多文件上传中间件
 * @param fieldName 文件字段名
 * @param maxCount 最大文件数量
 */
export const uploadMultipleFiles = (fieldName: string = 'files', maxCount: number = 5) => {
  return (req: Request, res: Response, next: NextFunction) => {
    upload.array(fieldName, maxCount)(req, res, async (err: any) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          // Multer错误
          if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new BusinessError(`文件大小超过限制 (${config.upload.maxFileSize / 1024 / 1024}MB)`));
          } else if (err.code === 'LIMIT_FILE_COUNT') {
            return next(new BusinessError(`文件数量超过限制 (${maxCount})`));
          }
          return next(new BusinessError(`文件上传错误: ${err.message}`));
        }
        return next(err);
      }
      
      // 如果没有上传文件
      if (!req.files || (req.files as Express.Multer.File[]).length === 0) {
        return next(new BusinessError('未提供文件'));
      }
      
      try {
        const files = req.files as Express.Multer.File[];
        const processedFiles = [];
        
        // 处理每个文件
        for (const file of files) {
          const processedFile = await processImage(req, file);
          
          // 更新文件信息
          file.filename = processedFile.filename;
          file.path = processedFile.path;
          
          processedFiles.push(processedFile);
        }
        
        // 记录上传信息
        logger.info(`多文件上传并压缩成功: ${processedFiles.length}个文件`, {
          files: processedFiles.map(f => ({
            filename: f.filename,
            type: f.type
          }))
        });
        
        next();
      } catch (error) {
        // 清理临时文件
        if (req.files) {
          const files = req.files as Express.Multer.File[];
          files.forEach(file => {
            if (fs.existsSync(file.path)) {
              fs.unlinkSync(file.path);
            }
          });
        }
        
        logger.error(`多文件处理失败:`, error);
        return next(error);
      }
    });
  };
};

/**
 * 获取文件URL（返回相对路径）
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 相对路径
 */
export const getFileUrl = (filename: string, subDir: string = 'dish'): string => {
  // 使用URL管理器生成相对路径
  return getRelativePath(filename, subDir);
};

/**
 * 删除文件
 * @param filename 文件名
 * @param subDir 子目录
 */
export const deleteFile = (filename: string, subDir: string = 'dish'): void => {
  try {
    const filePath = path.join(uploadDir, subDir, filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`文件删除成功: ${filePath}`);
    }
  } catch (error) {
    logger.error(`文件删除失败: ${error}`);
  }
};

export default {
  uploadSingleFile,
  uploadMultipleFiles,
  getFileUrl,
  deleteFile,
};
