<view class="container" style="{{textColorStyle}}">
  <!-- 自定义导航栏 -->
  <custom-navbar 
    title="{{pageTitle}}" 
    showBack="{{true}}" 
    navBgStyle="{{navBgStyle}}"
    useShadow="{{true}}"
  />
  
  <!-- 公告区域 - 修改公告样式，取消左右边距，从图标处开始滚动 -->
  <view class="notice-container">
    <image class="notice-icon" src="/static/images/icons/gong.png" mode="aspectFit"></image>
    <view class="notice-content">
      <view class="scrolling-text {{noticeOverflow ? 'animate-scroll' : ''}}">{{notice}}</view>
    </view>
  </view>
  
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 菜品图片上传 -->
    <view class="form-section" id="image-section">
      <view class="upload-center-container">
        <!-- 上传按钮改为文本按钮，仅当没有图片或图片少于5张时显示 -->
        <view class="image-preview-list" wx:if="{{imageList.length > 0}}">
          <view class="image-preview-item" wx:for="{{imageList}}" wx:key="index">
            <smart-image 
              src="{{item}}" 
              mode="aspectFill" 
              width="100%" 
              height="100%" 
              border-radius="8"
              class="preview-image"
            />
            <view class="delete-image-btn" catchtap="deleteImage" data-index="{{index}}">×</view>
          </view>
        </view>
        <view wx:if="{{!tempFilePath || imageList.length === 0}}" class="upload-container" bindtap="chooseImage">
          <view class="upload-placeholder">
            <image class="upload-icon" src="/static/images/icons/camera.png" mode="aspectFit"></image>
            <view class="upload-text">上传图片</view>
          </view>
        </view>
        <view wx:if="{{imageList.length > 0 && imageList.length < 5}}" class="text-upload-btn" bindtap="chooseImage">
          <text>继续添加图片</text>
        </view>
        <view class="upload-text-hint">点击上传菜品图片(最多5张) *</view>
      </view>
    </view>
    
    <!-- 基本信息表单 -->
    <view class="form-section">
      <view class="form-group" id="name-section">
        <view class="form-label">菜品名称 *</view>
        <input 
          id="name-input"
          class="form-input" 
          value="{{formData.name}}" 
          placeholder="请输入菜品名称" 
          maxlength="20"
          adjust-position="{{true}}"
          bindinput="onInputChange" 
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          data-field="name" 
          data-element-id="name-section"
        />
      </view>
      
      <view class="form-group" id="category-section">
        <view class="form-label">所属分类 *</view>
        <view class="form-input category-select" bindtap="openCategorySelect">
          <text>{{formData.categoryName || '请选择分类'}}</text>
          <text class="arrow-icon">▼</text>
        </view>
      </view>
      
      <view class="form-group" id="price-section">
        <view class="form-label">价格（大米）</view>
        <input 
          id="price-input"
          class="form-input" 
          type="digit" 
          value="{{formData.price}}" 
          placeholder="请输入价格，默认为0" 
          maxlength="8"
          adjust-position="{{true}}"
          bindinput="onInputChange" 
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          data-field="price" 
          data-element-id="price-section"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">星级评分</view>
        <view class="rating-stars">
          <image class="star-icon" src="/static/images/icons/{{formData.rating >= 1 ? 'star' : 'star1'}}.png" bindtap="onRatingChange" data-rating="1" mode="aspectFit"></image>
          <image class="star-icon" src="/static/images/icons/{{formData.rating >= 2 ? 'star' : 'star1'}}.png" bindtap="onRatingChange" data-rating="2" mode="aspectFit"></image>
          <image class="star-icon" src="/static/images/icons/{{formData.rating >= 3 ? 'star' : 'star1'}}.png" bindtap="onRatingChange" data-rating="3" mode="aspectFit"></image>
          <image class="star-icon" src="/static/images/icons/{{formData.rating >= 4 ? 'star' : 'star1'}}.png" bindtap="onRatingChange" data-rating="4" mode="aspectFit"></image>
          <image class="star-icon" src="/static/images/icons/{{formData.rating >= 5 ? 'star' : 'star1'}}.png" bindtap="onRatingChange" data-rating="5" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="form-group" id="description-section">
        <view class="form-label">菜品介绍</view>
        <textarea 
          id="description-input"
          class="form-textarea" 
          value="{{formData.description}}" 
          placeholder="请输入菜品介绍" 
          maxlength="200"
          adjust-position="{{true}}"
          bindinput="onInputChange" 
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          data-field="description"
          data-element-id="description-section"
        ></textarea>
      </view>
    </view>
    
    <!-- 配料信息 -->
    <view class="form-section" id="ingredients-section">
      <view class="section-header">
        <view class="section-title">配料表 *</view>
        <view class="add-btn" bindtap="addIngredient">
          <text class="add-icon">+</text>
          <text>添加配料</text>
        </view>
      </view>
      <view class="ingredients-container">
        <view class="ingredient-item" wx:for="{{formData.ingredients}}" wx:key="index">
          <view class="ingredient-input-group">
            <input 
              id="ingredient-name-{{index}}"
              class="ingredient-name" 
              value="{{item.name}}" 
              placeholder="配料名称" 
              maxlength="10"
              adjust-position="{{true}}"
              bindinput="onIngredientChange" 
              bindfocus="onInputFocus"
              bindblur="onInputBlur"
              data-field="name" 
              data-index="{{index}}" 
              data-element-id="ingredients-section"
            />
            <input 
              id="ingredient-amount-{{index}}"
              class="ingredient-amount" 
              value="{{item.amount}}" 
              placeholder="用量" 
              maxlength="10"
              adjust-position="{{true}}"
              bindinput="onIngredientChange" 
              bindfocus="onInputFocus"
              bindblur="onInputBlur"
              data-field="amount" 
              data-index="{{index}}" 
              data-element-id="ingredients-section"
            />
          </view>
          <view class="delete-btn" bindtap="deleteIngredient" data-index="{{index}}">
            <image class="delete-icon" src="/static/images/icons/quxiao.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 烹饪步骤 - 修改布局结构，将图片框移到左侧 -->
    <view class="form-section" id="steps-section">
      <view class="section-header">
        <view class="section-title">烹饪步骤</view>
        <view class="add-btn" bindtap="addStep">
          <text class="add-icon">+</text>
          <text>添加步骤</text>
        </view>
      </view>
      <view class="steps-container">
        <view class="step-item" wx:for="{{formData.cookingSteps}}" wx:key="index">
          <view class="step-header white-bg">
            <view class="step-number">步骤 {{index + 1}}</view>
            <view class="step-actions">
              <view class="sort-btn" bindtap="moveStepUp" data-index="{{index}}" wx:if="{{index > 0}}">
                <image class="sort-icon" src="/static/images/icons/shang.png" mode="aspectFit"></image>
              </view>
              <view class="sort-btn" bindtap="moveStepDown" data-index="{{index}}" wx:if="{{index < formData.cookingSteps.length - 1}}">
                <image class="sort-icon" src="/static/images/icons/xia.png" mode="aspectFit"></image>
              </view>
              <view class="delete-btn" bindtap="deleteStep" data-index="{{index}}">
                <image class="delete-icon" src="/static/images/icons/quxiao.png" mode="aspectFit"></image>
              </view>
            </view>
          </view>
          <view class="step-content">
            <view class="step-image-wrapper">
              <view class="step-image-container" bindtap="chooseStepImage" data-index="{{index}}">
                <smart-image 
                  wx:if="{{item.image}}" 
                  src="{{item.image}}" 
                  mode="aspectFill" 
                  width="100%" 
                  height="100%" 
                  border-radius="8"
                  class="step-image"
                />
                <view wx:else class="step-image-placeholder">
                  <image class="step-camera-icon" src="/static/images/icons/camera.png" mode="aspectFit"></image>
                  <view class="step-upload-text">添加图片</view>
                </view>
              </view>
            </view>
            <view class="step-text-content">
              <input 
                id="step-title-{{index}}"
                class="step-title" 
                value="{{item.title}}" 
                placeholder="步骤标题" 
                maxlength="30"
                adjust-position="{{true}}"
                bindinput="onStepChange" 
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="title" 
                data-index="{{index}}" 
                data-element-id="steps-section"
              />
              <textarea 
                id="step-description-{{index}}"
                class="step-description" 
                value="{{item.description}}" 
                placeholder="步骤描述" 
                maxlength="200"
                adjust-position="{{true}}"
                bindinput="onStepChange" 
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="description" 
                data-index="{{index}}"
                data-element-id="steps-section"
              ></textarea>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 营养信息 -->
    <view class="form-section" id="nutrition-section">
      <view class="section-title">营养信息</view>
      <view class="nutrition-container">
        <view class="nutrition-item">
          <view class="form-label">热量（千卡）</view>
          <input 
            id="nutrition-calories"
            class="form-input" 
            type="digit" 
            value="{{formData.nutrition.calories}}" 
            placeholder="请输入热量" 
            maxlength="6"
            adjust-position="{{true}}"
            bindinput="onNutritionChange" 
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            data-field="calories" 
            data-element-id="nutrition-section"
          />
        </view>
        <view class="nutrition-item">
          <view class="form-label">蛋白质</view>
          <input 
            id="nutrition-protein"
            class="form-input" 
            value="{{formData.nutrition.protein}}" 
            placeholder="例：28克" 
            maxlength="10"
            adjust-position="{{true}}"
            bindinput="onNutritionChange" 
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            data-field="protein" 
            data-element-id="nutrition-section"
          />
        </view>
        <view class="nutrition-item">
          <view class="form-label">碳水化合物</view>
          <input 
            id="nutrition-carbs"
            class="form-input" 
            value="{{formData.nutrition.carbs}}" 
            placeholder="例：75克" 
            maxlength="10"
            adjust-position="{{true}}"
            bindinput="onNutritionChange" 
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            data-field="carbs" 
            data-element-id="nutrition-section"
          />
        </view>
        <view class="nutrition-item">
          <view class="form-label">脂肪</view>
          <input 
            id="nutrition-fat"
            class="form-input" 
            value="{{formData.nutrition.fat}}" 
            placeholder="例：25克" 
            maxlength="10"
            adjust-position="{{true}}"
            bindinput="onNutritionChange" 
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            data-field="fat" 
            data-element-id="nutrition-section"
          />
        </view>
      </view>
    </view>
    
    <!-- 页面底部占位 -->
    <view style="height: 120rpx;"></view>
  </view>
  
  <!-- 底部固定按钮 -->
  <view class="fixed-bottom-buttons">
    <view class="cancel-btn" bindtap="onCancel">取消</view>
    <view class="save-btn" bindtap="onSave">保存</view>
  </view>
  
  <!-- 分类选择弹窗 -->
  <modal-dialog 
    visible="{{showCategoryModal}}" 
    title="选择分类" 
    width="90%" 
    themeColor="#FF6B35"
    bind:close="closeCategoryModal"
    bind:cancel="closeCategoryModal"
    bind:confirm="confirmCategorySelect"
  >
    <view class="category-list">
      <view 
        class="category-item {{selectedCategoryId === item.id ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="selectCategory"
        data-id="{{item.id}}"
        data-name="{{item.name}}"
      >
        <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <view class="category-name">{{item.name}}</view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 确认对话框 -->
  <confirm-dialog
    visible="{{showConfirmDialog}}"
    title="{{confirmDialogTitle}}"
    content="{{confirmDialogContent}}"
    cancelText="取消"
    confirmText="确认"
    confirmColor="#FF6B35"
    bind:cancel="onConfirmCancel"
    bind:confirm="onConfirmDialogConfirm"
  />
</view> 