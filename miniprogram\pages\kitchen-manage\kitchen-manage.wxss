/* 厨房管理页面样式 */
.kitchen-manage-container {
  min-height: 100vh;
  background-color: #F9F9F9;
  padding-bottom: 40rpx;
}

/* 顶部厨房信息区域 */
.kitchen-info-area {
  padding: 30rpx;
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}

.kitchen-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

/* 厨房头像和名称区域 */
.kitchen-avatar-name {
  display: flex;
  align-items: center;
}

.kitchen-avatar-name .avatar-area {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.kitchen-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.kitchen-name-area {
  display: flex;
  align-items: center;
}

.kitchen-name {
  font-size: 38rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 15rpx;
}

.kitchen-level {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #FFFFFF;
  background-color: #FF6B35;
  border-radius: 30rpx;
  padding: 4rpx 14rpx;
  height: 36rpx;
}

/* 厨房统计信息卡片 */
.kitchen-stats-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 30rpx 30rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.stats-info {
  display: flex;
  align-items: baseline;
}

.stats-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.stats-label {
  font-size: 26rpx;
  color: #666666;
  margin-top: 10rpx;
}

/* 主要操作按钮 */
.main-action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  gap: 15rpx;
}

.action-button {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.action-button text {
  position: relative;
  z-index: 1;
  letter-spacing: 0.5rpx;
}

.upgrade {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  color: #FFFFFF;
}

.dismiss {
  background: linear-gradient(135deg, #E53935 0%, #EF5350 100%);
  color: #FFFFFF;
}

.manage {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: #FFFFFF;
}

/* 按钮按下效果 */
.action-button:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.12);
}

/* 功能入口区域 */
.function-entry-area {
  background-color: #FFFFFF;
  margin: 0 30rpx;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.function-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  padding: 0 10rpx;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 38rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.function-icon image {
  width: 60rpx;
  height: 60rpx;
  object-fit: contain;
}

.function-label {
  font-size: 25rpx;
  color: #333333;
}

/* 厨房列表样式 */
.kitchen-list {
  max-height: 60vh;
  overflow-y: auto;
}

.kitchen-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.kitchen-item:last-child {
  border-bottom: none;
}

.kitchen-item.active {
  background-color: #F5F5F5;
  border-radius: 12rpx;
  padding: 24rpx 15rpx;
  margin: 0 -15rpx;
}

.kitchen-item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.kitchen-item-info .avatar-area {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.kitchen-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.kitchen-item-details {
  display: flex;
  align-items: center;
}

.kitchen-item-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 15rpx;
}

.kitchen-item-level {
  font-size: 24rpx;
  color: #FFFFFF;
  background-color: #FF6B35;
  border-radius: 30rpx;
  padding: 4rpx 14rpx;
}

.kitchen-item-owner {
  font-size: 26rpx;
  color: #999999;
}

/* 升级厨房弹窗样式 */
.upgrade-content {
  padding: 20rpx 0;
}

.upgrade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 30rpx;
}

.upgrade-label {
  font-size: 28rpx;
  color: #666666;
}

.upgrade-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.upgrade-divider {
  height: 1rpx;
  background-color: #EEEEEE;
  margin: 16rpx 0;
}

.upgrade-benefit-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  padding: 16rpx 30rpx;
  margin-bottom: 10rpx;
}

.upgrade-benefits {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx 20rpx;
}

.upgrade-benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.benefit-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.benefit-value {
  font-size: 30rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 新增/加入厨房弹窗样式 */
.kitchen-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.kitchen-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.kitchen-tab.active {
  color: #FF6B35;
  font-weight: 500;
}

.kitchen-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF6B35;
  border-radius: 2rpx;
}

.kitchen-panel {
  padding: 0 20rpx 20rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

/* 新增厨房头像上传区域 */
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.kitchen-avatar-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
  background-color: #F5F5F5;
  border: 2rpx solid #EEEEEE;
}

.upload-text {
  font-size: 26rpx;
  color: #666666;
}

/* 我的厨房弹窗样式 */
.my-kitchen-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.my-kitchen-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: all 0.3s ease;
}

.my-kitchen-tab.active {
  color: #FF6B35;
  font-weight: 500;
}

.my-kitchen-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF6B35;
  border-radius: 2rpx;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 60rpx;
    opacity: 1;
  }
}

.my-kitchen-panel {
  max-height: 60vh;
  overflow-y: auto;
}

.my-kitchen-list {
  padding: 10rpx 0;
}

.empty-kitchen-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.my-kitchen-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.my-kitchen-item:last-child {
  border-bottom: none;
}

.my-kitchen-item-info {
  display: flex;
  align-items: center;
}

.my-kitchen-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #F5F5F5;
  border: 1rpx solid #EEEEEE;
}

.my-kitchen-item-details {
  display: flex;
  flex-direction: column;
}

.my-kitchen-item-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.my-kitchen-item-meta {
  display: flex;
  align-items: center;
}

.my-kitchen-item-id {
  font-size: 24rpx;
  color: #999999;
  margin-right: 15rpx;
}

.my-kitchen-item-level {
  font-size: 22rpx;
  color: #FFFFFF;
  background-color: #FF6B35;
  border-radius: 30rpx;
  padding: 2rpx 10rpx;
}

.my-kitchen-item-actions {
  display: flex;
}

.my-kitchen-action-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  color: #FFFFFF;
}

.dismiss-btn {
  background-color: #E53935;
}

.leave-btn {
  background-color: #999999;
}

/* 克隆菜谱弹窗样式 */
.clone-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.step-num {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #EEEEEE;
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.step.active .step-num {
  background-color: #FF6B35;
  color: #FFFFFF;
}

.step-text {
  font-size: 24rpx;
  color: #999999;
}

.step.active .step-text {
  color: #333333;
  font-weight: 500;
}

.step-line {
  height: 2rpx;
  background-color: #EEEEEE;
  flex: 1;
  margin: 0 10rpx;
  margin-bottom: 20rpx;
}

.clone-panel {
  min-height: 200rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.search-btn {
  margin-top: 30rpx;
  background-color: #FF6B35;
  color: #FFFFFF;
  border-radius: 44rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.source-kitchen-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.kitchen-avatar-sm {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.kitchen-info-text {
  display: flex;
  flex-direction: column;
}

.kitchen-name-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}

.kitchen-level-text {
  font-size: 24rpx;
  color: #FF6B35;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 10rpx;
  background-color: #F9F9F9;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.category-item.selected {
  background-color: #FFF0EB;
  border-left: 4rpx solid #FF6B35;
}

.category-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx;
  object-fit: contain;
}

.category-name {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.category-count {
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
  min-width: 60rpx;
  text-align: right;
}

.empty-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #999999;
  font-size: 28rpx;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  height: 200rpx;
}

.picker-view {
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

.dish-list {
  margin-top: 20rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.dish-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  transition: background-color 0.2s ease;
}

.dish-item:last-child {
  border-bottom: none;
}

.dish-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin: 0 20rpx;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dish-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}

.dish-desc {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.dish-price {
  font-size: 26rpx;
  color: #FF6B35;
}

.clone-summary {
  text-align: center;
  margin-top: 20rpx;
  padding: 15rpx 0;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  color: #666666;
  font-size: 26rpx;
}

.clone-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.result-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin: 20rpx 0 10rpx;
}

.result-desc {
  font-size: 26rpx;
  color: #666666;
}

/* 分类列表滚动视图 */
.category-list-scroll {
  max-height: 350rpx;
  margin-top: 20rpx;
}

.category-list {
  padding: 10rpx 0;
}

/* 菜品列表滚动视图 */
.dish-list-scroll {
  max-height: 420rpx;
  width: 100%;
  overflow-y: auto;
}

.dish-list {
  margin-top: 20rpx;
}

/* 目标分类选择模态框样式 */
.target-category-scroll {
  max-height: 600rpx;
  width: 100%;
}

.target-category-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10rpx 0;
}

.target-category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.target-category-item:last-child {
  border-bottom: none;
}

.target-category-item.selected {
  background-color: rgba(255, 107, 53, 0.1);
}

.target-category-item .category-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  object-fit: contain;
}

.target-category-item .category-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.target-category-item .category-count {
  font-size: 24rpx;
  color: #999999;
}

/* 圆形气泡勾选框样式 - 优化版 */
.custom-checkbox-alt {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  border: 3rpx solid #D9D9D9;
  background-color: #FFFFFF;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

/* 选中状态 */
.custom-checkbox-alt.checked {
  background-color: #FF6B35;
  border-color: #FF6B35;
  box-shadow: 0 0 0 6rpx rgba(255, 107, 53, 0.15);
  transform: scale(1.05);
}

/* 内部圆点 */
.custom-checkbox-alt .checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 选中状态的内部圆点 */
.custom-checkbox-alt.checked .checkbox-inner {
  opacity: 1;
  transform: scale(1);
}

/* 未选中状态的悬停效果 */
.custom-checkbox-alt:not(.checked):hover {
  border-color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.05);
}

/* 点击反馈效果增强 */
.dish-item:active {
  background-color: #F5F5F5;
  transform: scale(0.98);
}

/* 大米消耗提示区域 */
.rice-cost-tip {
  background: linear-gradient(135deg, #FFF8E1, #FFECB3);
  border: 2rpx solid #FFB74D;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.cost-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 183, 77, 0.3));
}

.cost-icon-img {
  width: 48rpx;
  height: 48rpx;
  object-fit: contain;
}

.cost-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.cost-title {
  font-size: 26rpx;
  color: #F57C00;
  font-weight: 500;
}

.cost-amount {
  font-size: 32rpx;
  color: #E65100;
  font-weight: bold;
}

.balance-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.balance-label {
  font-size: 24rpx;
  color: #8D6E63;
}

.balance-amount {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 600;
}

.switch-kitchen {
  display: flex;
  align-items: center;
  color: #4CAF50;
  font-size: 28rpx;
}

.arrow-icon {
  font-size: 24rpx;
  margin-left: 8rpx;
}

.kitchen-id-row {
  display: flex;
  align-items: center;
}

.kitchen-id {
  font-size: 26rpx;
  color: #666666;
}

.copy-id {
  margin-left: 10rpx;
  padding: 6rpx;
}

.copy-icon {
  width: 26rpx;
  height: 26rpx;
  object-fit: contain;
} 