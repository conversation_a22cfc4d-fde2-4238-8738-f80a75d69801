"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 订单项模型
 * 存储订单中的菜品信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 订单项模型类
class OrderItem extends sequelize_1.Model {
    // 获取标签数组
    getTagsArray() {
        return this.tags ? this.tags.split(',') : [];
    }
    // 计算小计金额
    getSubtotal() {
        return this.price * this.count;
    }
}
// 初始化订单项模型
OrderItem.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '订单项ID',
    },
    order_id: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        comment: '订单ID',
        references: {
            model: 'orders',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true, // 允许为空，因为onDelete设置为SET NULL
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
    },
    name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        comment: '菜品名称',
    },
    image: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        comment: '菜品图片',
    },
    price: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '价格',
    },
    count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '数量',
    },
    tags: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '标签（逗号分隔）',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'OrderItem',
    tableName: 'order_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_order_id',
            fields: ['order_id'],
        },
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
    ],
});
exports.default = OrderItem;
//# sourceMappingURL=OrderItem.js.map