/**
 * 系统设置控制器
 * 处理系统设置相关的请求
 */
import { Request, Response, NextFunction } from 'express';
import systemSettingService from '../services/systemSettingService';
import logger from '../utils/logger';
import { success, error, ResponseCode } from '../utils/response';
import { BusinessError } from '../middlewares/error';

/**
 * 获取所有系统设置
 * @route GET /api/system/settings
 */
const getAllSettings = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const settings = await systemSettingService.getAllSettings();
    success(res, { settings });
  } catch (err) {
    next(err);
  }
};

/**
 * 获取指定设置
 * @route GET /api/system/setting/:key
 */
const getSetting = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { key } = req.params;
    
    if (!key) {
      throw new BusinessError('缺少参数: key', ResponseCode.VALIDATION);
    }

    const value = await systemSettingService.getSetting(key);
    success(res, { value });
  } catch (err) {
    next(err);
  }
};

/**
 * 更新系统设置
 * @route POST /api/system/setting
 */
const updateSetting = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { key, value, type = 'string', description } = req.body;

    if (!key || value === undefined) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    // 验证类型
    const validTypes = ['boolean', 'string', 'number', 'json'];
    if (!validTypes.includes(type)) {
      throw new BusinessError('无效的值类型', ResponseCode.VALIDATION);
    }

    await systemSettingService.setSetting(key, value, type, description);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 检查评论功能是否开启
 * @route GET /api/system/comment-enabled
 */
const isCommentEnabled = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const enabled = await systemSettingService.isCommentEnabled();
    success(res, { enabled });
  } catch (err) {
    next(err);
  }
};

export default {
  getAllSettings,
  getSetting,
  updateSetting,
  isCommentEnabled
}; 