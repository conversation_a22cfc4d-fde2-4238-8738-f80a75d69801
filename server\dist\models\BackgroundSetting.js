"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 背景设置模型
 * 存储用户背景设置
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 背景设置模型类
class BackgroundSetting extends sequelize_1.Model {
}
// 初始化背景设置模型
BackgroundSetting.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '设置ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    shop_bg: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '店铺背景图片URL',
    },
    nav_bg_style: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        defaultValue: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
        comment: '导航栏背景样式',
    },
    nav_bg_index: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '预设背景索引',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'BackgroundSetting',
    tableName: 'background_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            unique: true,
            fields: ['user_id'],
        },
    ],
});
exports.default = BackgroundSetting;
//# sourceMappingURL=BackgroundSetting.js.map