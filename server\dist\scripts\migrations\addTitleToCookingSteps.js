"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 添加title字段到cooking_steps表的迁移脚本
 */
const database_1 = __importDefault(require("../../config/database"));
const logger_1 = __importDefault(require("../../utils/logger"));
/**
 * 添加title字段到cooking_steps表
 */
function addTitleToCookingSteps() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始添加title字段到cooking_steps表...');
        try {
            // 检查字段是否已存在
            const [results] = yield database_1.default.query("SHOW COLUMNS FROM cooking_steps LIKE 'title'");
            if (Array.isArray(results) && results.length > 0) {
                logger_1.default.info('title字段已存在，无需添加');
                return;
            }
            // 添加title字段
            yield database_1.default.query("ALTER TABLE cooking_steps ADD COLUMN title VARCHAR(100) COMMENT '步骤标题' AFTER step_number");
            logger_1.default.info('成功添加title字段到cooking_steps表');
        }
        catch (error) {
            logger_1.default.error('添加title字段失败:');
            if (error instanceof Error) {
                logger_1.default.error(`错误消息: ${error.message}`);
                logger_1.default.error(`错误堆栈: ${error.stack}`);
            }
            else {
                logger_1.default.error(`未知错误: ${error}`);
            }
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行迁移
if (require.main === module) {
    addTitleToCookingSteps()
        .then(() => {
        logger_1.default.info('迁移脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('迁移脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = addTitleToCookingSteps;
//# sourceMappingURL=addTitleToCookingSteps.js.map