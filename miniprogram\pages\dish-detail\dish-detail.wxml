<!-- 菜品详情页面 -->
<view class="container" style="padding-bottom: {{isInputFocused ? keyboardHeight + 120 : 120}}rpx;">
  <!-- 菜品图片区域 - 全屏显示到状态栏 -->
  <view class="dish-image-container">
    <swiper class="dish-swiper" circular="{{true}}" autoplay="{{imageList.length > 1}}" interval="3000"
            bindchange="onImageChange" indicator-dots="{{imageList.length > 1}}">
      <swiper-item wx:for="{{imageList}}" wx:key="index">
        <smart-image 
          class="dish-image" 
          src="{{item}}" 
          mode="aspectFill"
          width="100%"
          height="100%"
          border-radius="0"
        />
      </swiper-item>
    </swiper>

    <!-- 浮动返回按钮 -->
    <view class="floating-back-btn" bindtap="{{isSharedPage ? 'goBackToHome' : 'goBack'}}" style="top: {{menuButtonTop}}px; background-color: {{statusBarColor === '#ffffff' ? 'rgba(0,0,0,0.15)' : 'rgba(255,255,255,0.5)'}};">
      <text class="back-icon" style="color: {{statusBarColor}}">◀</text>
    </view>

    <!-- 图片指示器 -->
    <view class="image-indicator" wx:if="{{imageList.length > 1}}">
      {{currentImage + 1}}/{{imageList.length}}
    </view>
  </view>
  
  <!-- 菜品基本信息 -->
  <view class="dish-info-card">
    <view class="dish-header">
      <view class="dish-name">{{dishInfo.name}}</view>
      <!-- 举报按钮移动到标题右边 -->
      <view class="report-btn" bindtap="showReportModal">
        <image class="report-icon" src="/static/images/icons/jubao.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="dish-rating-sales">
      <view class="rating">
        <block wx:for="{{5}}" wx:key="index">
          <image class="star" src="/static/images/icons/{{index < dishInfo.rating ? 'star' : 'star1'}}.png" mode="aspectFit"></image>
        </block>
        <text>{{dishInfo.rating}}</text>
      </view>
      <view class="sales">已点 {{dishInfo.sales}} 份</view>
      <view class="dish-price-mini">{{dishInfo.price}}大米</view>
    </view>
    
    <view class="dish-desc">{{dishInfo.description}}</view>
  </view>
  
  <!-- 配料表 -->
  <view class="section-card">
    <view class="section-title">
      <image class="title-icon" src="/static/images/icons/task1.png" mode="aspectFit"></image>
      <text>配料表</text>
    </view>
    <view class="ingredients-list">
      <view class="ingredient-item" wx:for="{{dishInfo.ingredients}}" wx:key="name">
        <text class="ingredient-name">{{item.name}}</text>
        <text class="ingredient-amount">{{item.amount}}</text>
      </view>
    </view>
  </view>
  
  <!-- 营养信息 -->
  <view class="section-card" wx:if="{{dishInfo.nutrition}}">
    <view class="section-title">
      <image class="title-icon" src="/static/images/icons/yang.png" mode="aspectFit"></image>
      <text>营养信息</text>
    </view>
    <view class="nutrition-info">
      <view class="nutrition-item">
        <text class="nutrition-label">热量</text>
        <text class="nutrition-value">{{dishInfo.nutrition.calories ? dishInfo.nutrition.calories + '千卡' : '无'}}</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-label">蛋白质</text>
        <text class="nutrition-value">{{dishInfo.nutrition.protein || '无'}}</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-label">碳水化合物</text>
        <text class="nutrition-value">{{dishInfo.nutrition.carbs || '无'}}</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-label">脂肪</text>
        <text class="nutrition-value">{{dishInfo.nutrition.fat || '无'}}</text>
      </view>
    </view>
  </view>
  
  <!-- 烹饪步骤 -->
  <view class="section-card" wx:if="{{dishInfo.cookingSteps && dishInfo.cookingSteps.length > 0}}">
    <view class="section-title">
      <image class="title-icon" src="/static/images/icons/kitchen.png" mode="aspectFit"></image>
      <text>烹饪步骤</text>
    </view>
    <view class="cooking-steps">
      <view class="step-item" wx:for="{{dishInfo.cookingSteps}}" wx:key="index">
        <view class="step-header">
          <view class="step-number">{{index + 1}}</view>
          <view class="step-title">{{item.title}}</view>
        </view>
        <view class="step-content">
          <view class="step-desc">{{item.description}}</view>
          <smart-image 
            wx:if="{{item.image}}" 
            custom-class="step-image"
            src="{{item.image}}" 
            mode="widthFix"
            width="100%"
            height="auto"
            border-radius="12"
            lazy="{{true}}"
            lazy-delay="{{300}}"
          />
        </view>
      </view>
    </view>
  </view>
  
  <!-- 评价预览（仅在评论功能开启且有评论时显示） -->
  <view class="section-card" wx:if="{{commentEnabled && allComments.length > 0}}">
    <view class="section-title">
      <image class="title-icon" src="/static/images/icons/chat.png" mode="aspectFit"></image>
      <text>用户评价</text>
      <view class="comment-count">{{allComments.length}}条评价</view>
    </view>
    <view class="comment-list">
      <view class="comment-item" wx:for="{{allComments}}" wx:key="id">
        <smart-image 
          class="comment-avatar" 
          src="{{item.avatar}}" 
          mode="aspectFill"
          width="100%"
          height="100%"
          border-radius="50"
        />
        <view class="comment-content">
          <view class="comment-name-time">
            <text class="comment-name">{{item.name}}</text>
            <text class="comment-time">{{item.time}}</text>
          </view>
          <view class="comment-text">{{item.content}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="bottom-bar" style="bottom: {{keyboardHeight}}px">
    <!-- 评论输入区域（仅在评论功能开启时显示） -->
    <view class="comment-section" wx:if="{{commentEnabled}}">
      <input 
        class="comment-input" 
        placeholder-class="comment-placeholder"
        placeholder="写评论..." 
        bindinput="onCommentInput" 
        bindfocus="onCommentFocus"
        bindblur="onCommentBlur"
        value="{{commentText}}"
        adjust-position="{{false}}"
      />
    </view>
    
    <!-- 操作按钮区域 -->
    <view class="action-buttons" wx:if="{{!isInputFocused}}">
      <view class="like-btn {{isLiked ? 'liked' : ''}}" bindtap="onLikeTap">
        <image class="icon" src="/static/images/icons/{{isLiked ? 'link' : 'link1'}}.png" mode="aspectFit"></image>
        <text>{{isLiked ? '已赞' : '点赞'}}</text>
      </view>
      <view class="add-to-kitchen-btn {{isAdded ? 'added' : ''}}" bindtap="showAddToKitchen" wx:if="{{showAddButton}}">
        <image class="icon" src="/static/images/icons/{{isAdded ? 'check1' : 'plus'}}.png" mode="aspectFit"></image>
        <text>{{isAdded ? '已添加' : '添加'}}</text>
      </view>
      <button class="share-btn" open-type="share">
        <image class="icon" src="/static/images/icons/fenxiang.png" mode="aspectFit"></image>
        <text>分享</text>
      </button>
    </view>
    
    <!-- 发送按钮（仅在评论功能开启且输入框聚焦时显示） -->
    <view class="send-button" wx:if="{{isInputFocused && commentEnabled}}" bindtap="submitComment">
      发送
    </view>
  </view>
</view>

<!-- 添加到厨房弹窗 -->
<modal-dialog 
  visible="{{showAddToKitchenModal}}" 
  title="选择分类" 
  confirmText="确定"
  bind:close="hideAddToKitchen"
  bind:cancel="hideAddToKitchen"
  bind:confirm="confirmAddToKitchen"
>
  <view class="category-list">
    <view wx:if="{{categoryList.length === 0}}" class="empty-categories">
      <text>暂无分类数据，请先创建分类</text>
    </view>
    <view
      class="category-item {{selectedCategoryId === item.id ? 'selected' : ''}}"
      wx:for="{{categoryList}}"
      wx:key="id"
      bindtap="selectCategory"
      data-id="{{item.id}}"
    >
      <view class="category-content">
        <view class="category-left">
          <image class="category-icon" src="{{item.icon || '/static/images/icons/class/food.png'}}" mode="aspectFit"></image>
          <text class="category-name">{{item.name}}</text>
        </view>
        <text class="category-count">{{item.dishCount || 0}}个菜品</text>
      </view>
    </view>
  </view>
</modal-dialog>

<!-- 举报弹窗 -->
<modal-dialog 
  visible="{{showReportModal}}" 
  title="举报" 
  confirmText="提交"
  bind:close="hideReportModal"
  bind:cancel="hideReportModal"
  bind:confirm="submitReport"
>
  <view class="report-content">
    <view class="report-title">请选择举报原因：</view>
    <radio-group bindchange="onReportReasonChange">
      <label class="report-reason" wx:for="{{reportReasons}}" wx:key="value">
        <view class="reason-text">{{item.label}}</view>
        <radio value="{{item.value}}" checked="{{selectedReportReason === item.value}}" />
      </label>
    </radio-group>
    <view class="report-desc-area">
      <textarea 
        class="report-desc-input" 
        placeholder="详细说明（选填）" 
        placeholder-class="report-placeholder"
        bindinput="onReportDescInput" 
        value="{{reportDesc}}"
        maxlength="200"
      />
      <view class="report-desc-counter">{{reportDesc.length}}/200</view>
    </view>
  </view>
</modal-dialog>

<!-- 隐藏的canvas用于图片颜色分析 -->
<canvas canvas-id="dishImageAnalysisCanvas" style="position: fixed; left: -9999px; top: -9999px; width: 100px; height: 100px;"></canvas>