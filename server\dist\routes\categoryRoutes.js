"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 分类管理模块路由
 * 处理分类相关的API路由
 */
const express_1 = __importDefault(require("express"));
const categoryController_1 = __importDefault(require("../controllers/categoryController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取分类列表
router.get('/list', auth_1.verifyToken, categoryController_1.default.getCategoryList);
// 添加分类
router.post('/add', auth_1.verifyToken, categoryController_1.default.addCategory);
// 更新分类
router.post('/update', auth_1.verifyToken, categoryController_1.default.updateCategory);
// 删除分类
router.post('/delete', auth_1.verifyToken, categoryController_1.default.deleteCategory);
// 更新分类排序
router.post('/updateSort', auth_1.verifyToken, categoryController_1.default.updateCategorySort);
exports.default = router;
//# sourceMappingURL=categoryRoutes.js.map