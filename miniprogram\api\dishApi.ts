import { request } from '../utils/request'

// 获取菜品分类列表
export const getDishCategories = (kitchenId?: string) => {
  console.log('调用获取分类API，参数：', { kitchenId });
  return request({
    url: '/api/dish/categories',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  })
}

// 获取菜品列表
export const getDishList = (categoryId?: string, kitchenId?: string) => {
  return request({
    url: '/api/dish/list',
    method: 'GET',
    data: { categoryId, kitchenId },
    showLoading: false
  })
}

// 获取菜品详情
export const getDishDetail = (dishId: string, kitchenId?: string) => {
  return request({
    url: '/api/dish/detail',
    method: 'GET',
    data: { id: dishId, kitchenId },
    showLoading: false
  })
}

// 添加菜品
export const addDish = (dishInfo: {
  name: string,
  categoryId: string,
  kitchenId: string,
  price: number,
  description: string,
  image: string,
  images?: string[],
  rating?: number,
  ingredients?: Array<{name: string, amount: string}>,
  cookingSteps?: Array<{title: string, description: string, image: string}>,
  nutrition?: {
    calories: string | number,
    protein: string,
    carbs: string,
    fat: string
  }
}) => {
  return request({
    url: '/api/dish/add',
    method: 'POST',
    data: dishInfo,
    showLoading: false
  })
}

// 添加菜品到购物车
export const addToCart = (dishId: string, count: number, kitchenId: string) => {
  if (!kitchenId) {
    return Promise.reject({
      error: 422,
      body: null,
      message: '缺少必要参数: kitchenId'
    });
  }

  return request({
    url: '/api/cart/add',
    method: 'POST',
    data: { dishId, count, kitchenId }
  })
}

// 获取购物车列表
export const getCartList = (kitchenId?: string) => {
  return request({
    url: '/api/cart/list',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  })
}

// 清空购物车
export const clearCart = (kitchenId?: string) => {
  return request({
    url: '/api/cart/clear',
    method: 'POST',
    data: { kitchenId }
  })
}

// 更新购物车菜品数量
export const updateCartItemCount = (dishId: string, count: number, kitchenId: string) => {
  if (!kitchenId) {
    return Promise.reject({
      error: 422,
      body: null,
      message: '缺少必要参数: kitchenId'
    });
  }

  return request({
    url: '/api/cart/updateCount',
    method: 'POST',
    data: { dishId, count, kitchenId }
  })
}

// 提交订单
export const submitOrder = (params: {
  kitchenId: string,
  tableNo: string,
  remark?: string
}) => {
  return request({
    url: '/api/order/submit',
    method: 'POST',
    data: params,
    showLoading: false
  })
}

// 搜索菜品
export const searchDishes = (keyword: string, page = 1, pageSize = 10) => {
  return request({
    url: '/api/dish/search',
    method: 'GET',
    data: { keyword, page, pageSize }
  })
}

// 获取热门搜索关键词
export const getHotKeywords = () => {
  return request({
    url: '/api/dish/hotKeywords',
    method: 'GET',
    data: {}
  })
}

// 点赞菜品
export const likeDish = (dishId: string) => {
  return request({
    url: '/api/dish/like',
    method: 'POST',
    data: { id: dishId },
    showLoading: false
  })
}

// 添加菜品到厨房
export const addDishToKitchen = (dishId: string, kitchenId: string, categoryId: string) => {
  return request({
    url: '/api/dish/addToKitchen',
    method: 'POST',
    data: { dishId, kitchenId, categoryId },
    showLoading: false
  })
}

// 修改菜品信息
export const updateDish = (dishInfo: {
  id: string,
  name: string,
  categoryId: string,
  price: number,
  description: string,
  image: string,
  images?: string[],
  rating?: number,
  ingredients?: Array<{name: string, amount: string}>,
  cookingSteps?: Array<{title: string, description: string, image: string}>,
  nutrition?: {
    calories: string | number,
    protein: string,
    carbs: string,
    fat: string
  }
}) => {
  return request({
    url: '/api/dish/update',
    method: 'POST',
    data: dishInfo,
    showLoading: false
  })
}

// 删除菜品
export const deleteDish = (dishId: string, kitchenId: string) => {
  return request({
    url: '/api/dish/delete',
    method: 'POST',
    data: { id: dishId, kitchenId }
  })
}

// 修改菜品状态（上下架）
export const updateDishStatus = (dishId: string, disabled: boolean, kitchenId: string) => {
  // 将disabled转换为后端需要的status格式
  const status = disabled ? 'off' : 'on';

  return request({
    url: '/api/dish/updateStatus',
    method: 'POST',
    data: { id: dishId, kitchenId, status }
  })
}

// 获取分类列表 - 包含更多详细信息的分类列表(包括菜品数量)
export const getCategoryList = () => {
  // 使用同一分类数据源以保持一致性
  return getDishCategories()
}

// 添加分类
export const addCategory = (name: string, icon: string, kitchenId: string) => {
  return request({
    url: '/api/category/add',
    method: 'POST',
    data: { name, icon, kitchenId },
    showLoading: false
  })
}

// 更新分类
export const updateCategory = (categoryId: string, name: string, icon: string, sort: number, kitchenId: string) => {
  return request({
    url: '/api/category/update',
    method: 'POST',
    data: { id: categoryId, name, icon, sort, kitchenId }
  })
}

// 删除分类
export const deleteCategory = (categoryId: string, kitchenId: string) => {
  return request({
    url: '/api/category/delete',
    method: 'POST',
    data: { id: categoryId, kitchenId }
  })
}

// 批量更新分类
export const batchUpdateCategory = (categories: Array<{
  id: string,
  name: string,
  icon?: string,
  sort?: number
}>) => {
  return request({
    url: '/api/category/batchUpdate',
    method: 'POST',
    data: { categories }
  })
}

// 更新分类排序
export const updateCategorySort = (sortList: {id: string, sort: number}[], kitchenId: string) => {
  return request({
    url: '/api/category/updateSort',
    method: 'POST',
    data: { categories: sortList, kitchenId }
  })
}

// 更新菜品排序
export const updateDishSort = (categoryId: string, sortList: {id: string, sort: number}[], kitchenId: string) => {
  return request({
    url: '/api/dish/updateSort',
    method: 'POST',
    data: { categoryId, dishes: sortList, kitchenId }
  })
}

// 举报菜品
export const reportDish = (params: {
  dishId: string,
  reason: string,  // 举报原因：fake-虚假信息, porn-色情低俗, illegal-违法违规, rights-侵犯权益, other-其他问题
  description: string
}) => {
  // 验证举报原因
  const validReasons = ['fake', 'porn', 'illegal', 'rights', 'other'];
  if (!validReasons.includes(params.reason)) {
    return Promise.reject({
      error: 400,
      body: null,
      message: '无效的举报原因'
    });
  }

  return request({
    url: '/api/dish/report',
    method: 'POST',
    data: {
      id: params.dishId,
      reason: params.reason,
      description: params.description
    },
    showLoading: false
  })
}

// 添加评论
export const addComment = (params: {
  dishId: string,
  content: string,
  rating?: number
}) => {
  return request({
    url: '/api/dish/comment',
    method: 'POST',
    data: {
      dishId: params.dishId,
      content: params.content,
      rating: params.rating || 5
    },
    showLoading: false
  })
}