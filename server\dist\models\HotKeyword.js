"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 热门关键词模型
 * 存储搜索热门关键词
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 热门关键词模型类
class HotKeyword extends sequelize_1.Model {
}
// 初始化热门关键词模型
HotKeyword.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '关键词ID',
    },
    keyword: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '关键词',
    },
    count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '搜索次数',
    },
    sort: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序值',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'HotKeyword',
    tableName: 'hot_keywords',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_keyword',
            unique: true,
            fields: ['keyword'],
        },
        {
            name: 'idx_count',
            fields: ['count'],
        },
        {
            name: 'idx_sort',
            fields: ['sort'],
        },
    ],
});
exports.default = HotKeyword;
//# sourceMappingURL=HotKeyword.js.map