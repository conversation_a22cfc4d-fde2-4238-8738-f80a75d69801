"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const categoryService_1 = __importDefault(require("../services/categoryService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取分类列表
 * @route GET /api/category/list
 */
const getCategoryList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { kitchenId } = req.query;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const categories = yield categoryService_1.default.getCategoryList(kitchenId);
        (0, response_1.success)(res, { categories });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加分类
 * @route POST /api/category/add
 */
const addCategory = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, name, icon } = req.body;
        if (!kitchenId || !name) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield categoryService_1.default.addCategory(userId, kitchenId, name, icon);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新分类
 * @route POST /api/category/update
 */
const updateCategory = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId, name, icon } = req.body;
        if (!id || !kitchenId || !name) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield categoryService_1.default.updateCategory(userId, parseInt(id), kitchenId, name, icon);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除分类
 * @route POST /api/category/delete
 */
const deleteCategory = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        if (!id || !kitchenId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield categoryService_1.default.deleteCategory(userId, parseInt(id), kitchenId);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新分类排序
 * @route POST /api/category/updateSort
 */
const updateCategorySort = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, categories } = req.body;
        if (!kitchenId || !categories || !Array.isArray(categories)) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield categoryService_1.default.updateCategorySort(userId, kitchenId, categories);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getCategoryList,
    addCategory,
    updateCategory,
    deleteCategory,
    updateCategorySort
};
//# sourceMappingURL=categoryController.js.map