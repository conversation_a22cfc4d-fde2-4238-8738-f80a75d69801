"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
const logger_1 = __importDefault(require("../utils/logger"));
const config_1 = __importDefault(require("../config/config"));
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
const sequelize_2 = __importDefault(require("sequelize"));
// 临时的管理员账户 - 生产环境应该存储在数据库中
const ADMIN_ACCOUNTS = [
    {
        id: 'admin_1',
        username: 'admin',
        password: 'admin123', // 生产环境应该加密存储
        role: 'admin',
        avatar: null
    },
    {
        id: 'admin_2',
        username: 'superadmin',
        password: 'super123',
        role: 'super_admin',
        avatar: null
    }
];
/**
 * 管理员登录
 * @route POST /api/admin/login
 */
const adminLogin = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, password } = req.body;
        if (!username || !password) {
            throw new error_1.BusinessError('用户名和密码不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查找管理员账户
        const admin = ADMIN_ACCOUNTS.find(account => account.username === username && account.password === password);
        if (!admin) {
            throw new error_1.BusinessError('用户名或密码错误', response_1.ResponseCode.UNAUTHORIZED);
        }
        // 生成JWT token
        const token = jsonwebtoken_1.default.sign({
            id: admin.id,
            username: admin.username,
            role: admin.role,
            type: 'admin'
        }, config_1.default.jwt.secret, { expiresIn: '24h' });
        logger_1.default.info(`管理员登录成功: ${username}`);
        (0, response_1.success)(res, {
            user: {
                id: admin.id,
                username: admin.username,
                role: admin.role,
                avatar: admin.avatar
            },
            token
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取仪表盘统计数据
 * @route GET /api/admin/dashboard/stats
 */
const getDashboardStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 从数据库查询真实数据
        const [userCount, kitchenCount, dishCount, orderCount] = yield Promise.all([
            models_1.User.count(),
            models_1.Kitchen.count(),
            models_1.Dish.count(),
            models_1.Order.count()
        ]);
        // 获取今日新增数据
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const [todayNewUsers, todayOrders] = yield Promise.all([
            models_1.User.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: today
                    }
                }
            }),
            models_1.Order.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: today
                    }
                }
            })
        ]);
        // 计算今日收入
        const todayRevenue = (yield models_1.Order.sum('total_price', {
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                },
                status: 'completed'
            }
        })) || 0;
        const stats = {
            userCount,
            kitchenCount,
            dishCount,
            orderCount,
            todayNewUsers,
            todayOrders,
            todayRevenue: Number(todayRevenue.toFixed(2))
        };
        (0, response_1.success)(res, stats);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取图表数据
 * @route GET /api/admin/dashboard/charts
 */
const getDashboardCharts = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 获取过去7天的订单趋势
        const orderTrend = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            const orders = yield models_1.Order.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.between]: [startOfDay, endOfDay]
                    }
                }
            });
            orderTrend.push({
                date: startOfDay.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
                orders
            });
        }
        // 获取过去30天的用户增长数据
        const userGrowth = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            const users = yield models_1.User.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.between]: [startOfDay, endOfDay]
                    }
                }
            });
            userGrowth.push({
                date: startOfDay.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
                users
            });
        }
        (0, response_1.success)(res, {
            orderTrend,
            userGrowth
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取最近订单列表
 * @route GET /api/admin/dashboard/recent-orders
 */
const getRecentOrders = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const recentOrders = yield models_1.Order.findAll({
            limit: 10,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.Kitchen,
                    as: 'kitchen',
                    attributes: ['name']
                },
                {
                    model: models_1.User,
                    as: 'user',
                    attributes: ['nick_name']
                }
            ]
        });
        const formattedOrders = recentOrders.map(order => {
            var _a, _b;
            return ({
                id: order.id,
                orderNumber: order.id,
                kitchenName: ((_a = order.kitchen) === null || _a === void 0 ? void 0 : _a.name) || '未知厨房',
                userName: ((_b = order.user) === null || _b === void 0 ? void 0 : _b.nick_name) || '未知用户',
                amount: order.total_price,
                status: order.status,
                tableNo: order.table_no,
                remark: order.remark,
                createTime: new Date(order.created_at).toLocaleString('zh-CN'),
                cookingTime: order.cooking_time,
                completedTime: order.completed_time
            });
        });
        (0, response_1.success)(res, { orders: formattedOrders });
    }
    catch (err) {
        next(err);
    }
});
// ============= 用户管理 =============
/**
 * 获取用户列表
 * @route GET /api/admin/users
 */
const getUserList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, membershipStatus, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (keyword) {
            whereConditions[sequelize_1.Op.or] = [
                { nick_name: { [sequelize_1.Op.like]: `%${keyword}%` } },
                { id: { [sequelize_1.Op.like]: `%${keyword}%` } }
            ];
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.User.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.Membership,
                    as: 'membership',
                    required: false
                }
            ]
        });
        const users = rows.map(user => {
            var _a;
            return ({
                id: user.id,
                username: user.id.toString(),
                nickname: user.nick_name,
                avatar: user.avatar_url,
                gender: user.gender,
                registrationDate: user.created_at,
                lastLoginDate: null,
                membershipStatus: user.membership ?
                    (user.membership.membership_type === 'monthly' ? 'monthly' : 'yearly') : 'none',
                membershipExpiry: (_a = user.membership) === null || _a === void 0 ? void 0 : _a.expiry_date,
                coinsBalance: user.coins,
                status: 'active',
                signInCount: 0,
                adViewCount: 0
            });
        });
        (0, response_1.success)(res, {
            users,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户统计数据
 * @route GET /api/admin/users/stats
 */
const getUserStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalUsers, memberUsers] = yield Promise.all([
            models_1.User.count(),
            models_1.Membership.count({
                where: {
                    expire_date: {
                        [sequelize_1.Op.gt]: new Date()
                    },
                    is_member: true
                }
            })
        ]);
        // 今日新增用户
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const newUsersToday = yield models_1.User.count({
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                }
            }
        });
        // 用户总大米数
        const totalCoins = (yield models_1.User.sum('coins')) || 0;
        (0, response_1.success)(res, {
            totalUsers,
            activeUsers: totalUsers,
            newUsersToday,
            memberUsers,
            totalCoins: Math.round(totalCoins)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新用户状态
 * @route PUT /api/admin/users/:id/status
 */
const updateUserStatus = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!['active', 'inactive', 'banned'].includes(status)) {
            throw new error_1.BusinessError('无效的用户状态', response_1.ResponseCode.VALIDATION);
        }
        const user = yield models_1.User.findByPk(id);
        if (!user) {
            throw new error_1.BusinessError('用户不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 尝试修改用户 ${id} 状态为 ${status} (当前模型不支持状态字段)`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户详情
 * @route GET /api/admin/users/:id
 */
const getUserDetail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        const user = yield models_1.User.findByPk(id, {
            include: [
                {
                    model: models_1.Membership,
                    as: 'membership',
                    required: false
                }
            ]
        });
        if (!user) {
            throw new error_1.BusinessError('用户不存在', response_1.ResponseCode.NOT_FOUND);
        }
        const userDetail = {
            id: user.id,
            username: user.id.toString(),
            nickname: user.nick_name,
            avatar: user.avatar_url,
            gender: user.gender,
            registrationDate: user.created_at,
            lastLoginDate: null,
            membershipStatus: user.membership ?
                (user.membership.membership_type === 'monthly' ? 'monthly' : 'yearly') : 'none',
            membershipExpiry: (_a = user.membership) === null || _a === void 0 ? void 0 : _a.expiry_date,
            coinsBalance: user.coins,
            status: 'active',
            signInCount: 0,
            adViewCount: 0
        };
        (0, response_1.success)(res, userDetail);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 调整用户大米
 * @route POST /api/admin/users/:id/adjust-coins
 */
const adjustUserCoins = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { amount, reason } = req.body;
        if (!amount || !reason) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const user = yield models_1.User.findByPk(Number(id));
        if (!user) {
            throw new error_1.BusinessError('用户不存在', response_1.ResponseCode.NOT_FOUND);
        }
        const newBalance = user.coins + Number(amount);
        if (newBalance < 0) {
            throw new error_1.BusinessError('用户大米余额不足', response_1.ResponseCode.VALIDATION);
        }
        // 更新用户大米余额
        yield user.update({ coins: newBalance });
        // 记录交易 - 根据Transaction模型的实际字段创建
        yield models_1.Transaction.create({
            user_id: Number(id),
            amount: Math.abs(Number(amount)),
            title: reason,
            type: Number(amount) > 0 ? 'in' : 'out',
            time: new Date()
        });
        logger_1.default.info(`管理员 ${req.user.username} 为用户 ${id} 调整大米: ${amount}, 原因: ${reason}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 重置用户密码
 * @route POST /api/admin/users/:id/reset-password
 */
const resetUserPassword = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const user = yield models_1.User.findByPk(Number(id));
        if (!user) {
            throw new error_1.BusinessError('用户不存在', response_1.ResponseCode.NOT_FOUND);
        }
        // 生成随机密码（模拟实现）
        const newPassword = Math.random().toString(36).slice(-8);
        logger_1.default.info(`管理员 ${req.user.username} 重置用户 ${id} 的密码`);
        (0, response_1.success)(res, { newPassword });
    }
    catch (err) {
        next(err);
    }
});
// ============= 厨房管理 =============
/**
 * 获取厨房列表
 * @route GET /api/admin/kitchens
 */
const getKitchenList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (keyword) {
            whereConditions.name = { [sequelize_1.Op.like]: `%${keyword}%` };
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.Kitchen.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.User,
                    as: 'owner',
                    attributes: ['nick_name']
                }
            ]
        });
        const kitchens = rows.map(kitchen => {
            var _a;
            return ({
                id: kitchen.id,
                name: kitchen.name,
                description: kitchen.notice,
                avatar: kitchen.avatar_url,
                ownerName: ((_a = kitchen.owner) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
                location: '未设置',
                status: 'active',
                isOpen: true,
                memberCount: kitchen.member_count || 0,
                dishCount: kitchen.dish_count || 0,
                createTime: kitchen.created_at
            });
        });
        (0, response_1.success)(res, {
            kitchens,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取厨房统计数据
 * @route GET /api/admin/kitchens/stats
 */
const getKitchenStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalKitchens] = yield Promise.all([
            models_1.Kitchen.count()
        ]);
        // 今日新增厨房
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const newKitchensToday = yield models_1.Kitchen.count({
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                }
            }
        });
        (0, response_1.success)(res, {
            totalKitchens,
            activeKitchens: totalKitchens,
            openKitchens: totalKitchens,
            newKitchensToday
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新厨房状态
 * @route PUT /api/admin/kitchens/:id/status
 */
const updateKitchenStatus = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!['active', 'inactive', 'banned'].includes(status)) {
            throw new error_1.BusinessError('无效的厨房状态', response_1.ResponseCode.VALIDATION);
        }
        const kitchen = yield models_1.Kitchen.findByPk(id);
        if (!kitchen) {
            throw new error_1.BusinessError('厨房不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 尝试修改厨房 ${id} 状态为 ${status} (当前模型不支持状态字段)`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
// ============= 菜品管理 =============
/**
 * 获取菜品列表
 * @route GET /api/admin/dishes
 */
const getDishList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, category, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (keyword) {
            whereConditions.name = { [sequelize_1.Op.like]: `%${keyword}%` };
        }
        if (category) {
            whereConditions.category_id = category;
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.Dish.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.Category,
                    as: 'category',
                    attributes: ['name']
                },
                {
                    model: models_1.Kitchen,
                    as: 'kitchen',
                    attributes: ['name']
                }
            ]
        });
        // 获取在发现页的菜品ID列表
        const discoverItems = yield models_1.DiscoverItem.findAll({
            where: {
                dish_id: {
                    [sequelize_1.Op.in]: rows.map(dish => dish.id)
                }
            },
            attributes: ['dish_id']
        });
        const discoverDishIds = new Set(discoverItems.map(item => item.dish_id));
        const dishes = rows.map(dish => {
            var _a, _b;
            return ({
                id: dish.id,
                name: dish.name,
                price: dish.price,
                originalPrice: dish.original_price || dish.price,
                description: dish.description,
                categoryId: dish.category_id,
                categoryName: ((_a = dish.category) === null || _a === void 0 ? void 0 : _a.name) || '未分类',
                kitchenId: dish.kitchen_id,
                kitchenName: ((_b = dish.kitchen) === null || _b === void 0 ? void 0 : _b.name) || '未知厨房',
                image: dish.image,
                status: dish.status === 'on' ? 'available' : 'unavailable',
                isRecommended: false, // 默认非推荐
                isInDiscover: discoverDishIds.has(dish.id), // 是否在发现页
                likeCount: dish.sales || 0,
                viewCount: 0, // 默认浏览量
                salesCount: dish.sales || 0,
                stock: null, // 默认无限库存
                createTime: dish.created_at,
                updateTime: dish.updated_at
            });
        });
        (0, response_1.success)(res, {
            dishes,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取菜品统计数据
 * @route GET /api/admin/dishes/stats
 */
const getDishStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalDishes, totalCategories] = yield Promise.all([
            models_1.Dish.count(),
            models_1.Category.count()
        ]);
        // 今日新增菜品
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const newDishesToday = yield models_1.Dish.count({
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                }
            }
        });
        (0, response_1.success)(res, {
            totalDishes,
            activeDishes: totalDishes, // 所有菜品都视为活跃
            totalCategories,
            newDishesToday
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新菜品状态
 * @route PUT /api/admin/dishes/:dishId/status
 */
const updateDishStatus = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dishId } = req.params;
        const { status } = req.body;
        if (!['available', 'unavailable', 'hidden'].includes(status)) {
            throw new error_1.BusinessError('无效的菜品状态', response_1.ResponseCode.VALIDATION);
        }
        const dish = yield models_1.Dish.findByPk(dishId);
        if (!dish) {
            throw new error_1.BusinessError('菜品不存在', response_1.ResponseCode.NOT_FOUND);
        }
        // 状态值转换：前端状态 -> 数据库状态
        const statusMap = {
            'available': 'on',
            'unavailable': 'off',
            'hidden': 'off'
        };
        const dbStatus = statusMap[status] || 'on';
        yield dish.update({ status: dbStatus });
        logger_1.default.info(`管理员 ${req.user.username} 更新菜品状态: ${dishId} -> ${status}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新菜品信息
 * @route PUT /api/admin/dishes/:dishId
 */
const updateDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { dishId } = req.params;
        const updateData = req.body;
        // 检查菜品是否存在
        const dish = yield models_1.Dish.findByPk(dishId);
        if (!dish) {
            throw new error_1.BusinessError('菜品不存在', response_1.ResponseCode.NOT_FOUND);
        }
        // 字段映射：前端驼峰命名 -> 数据库下划线命名
        const mappedData = {};
        if (updateData.name !== undefined)
            mappedData.name = updateData.name;
        if (updateData.description !== undefined)
            mappedData.description = updateData.description;
        if (updateData.price !== undefined)
            mappedData.price = updateData.price;
        if (updateData.originalPrice !== undefined)
            mappedData.original_price = updateData.originalPrice;
        if (updateData.categoryId !== undefined)
            mappedData.category_id = updateData.categoryId;
        if (updateData.stock !== undefined)
            mappedData.stock = updateData.stock;
        // 状态值转换：前端状态 -> 数据库状态
        if (updateData.status !== undefined) {
            const statusMap = {
                'available': 'on',
                'unavailable': 'off',
                'hidden': 'off'
            };
            mappedData.status = statusMap[updateData.status] || 'on';
        }
        // 更新菜品信息
        yield dish.update(mappedData);
        logger_1.default.info(`管理员 ${req.user.username} 更新菜品信息: ${dishId}`);
        // 返回更新后的菜品信息
        const updatedDish = yield models_1.Dish.findByPk(dishId, {
            include: [
                {
                    model: models_1.Category,
                    as: 'category',
                    attributes: ['name']
                },
                {
                    model: models_1.Kitchen,
                    as: 'kitchen',
                    attributes: ['name']
                }
            ]
        });
        if (!updatedDish) {
            throw new error_1.BusinessError('更新后菜品信息获取失败', response_1.ResponseCode.NOT_FOUND);
        }
        // 状态值转换：数据库状态 -> 前端状态
        const frontendStatus = updatedDish.status === 'on' ? 'available' : 'unavailable';
        const formattedDish = {
            id: updatedDish.id,
            name: updatedDish.name,
            price: updatedDish.price,
            originalPrice: updatedDish.original_price || updatedDish.price,
            description: updatedDish.description,
            categoryId: updatedDish.category_id,
            categoryName: ((_a = updatedDish.category) === null || _a === void 0 ? void 0 : _a.name) || '未分类',
            kitchenId: updatedDish.kitchen_id,
            kitchenName: ((_b = updatedDish.kitchen) === null || _b === void 0 ? void 0 : _b.name) || '未知厨房',
            image: updatedDish.image,
            status: frontendStatus,
            isRecommended: false,
            isInDiscover: false,
            likeCount: updatedDish.sales || 0,
            viewCount: 0,
            salesCount: updatedDish.sales || 0,
            stock: null,
            createTime: updatedDish.created_at,
            updateTime: updatedDish.updated_at
        };
        (0, response_1.success)(res, formattedDish);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除菜品
 * @route DELETE /api/admin/dishes/:dishId
 */
const deleteDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dishId } = req.params;
        // 检查菜品是否存在
        const dish = yield models_1.Dish.findByPk(dishId);
        if (!dish) {
            throw new error_1.BusinessError('菜品不存在', response_1.ResponseCode.NOT_FOUND);
        }
        // 删除相关的发现项
        yield models_1.DiscoverItem.destroy({
            where: { dish_id: dishId }
        });
        // 删除菜品
        yield dish.destroy();
        logger_1.default.info(`管理员 ${req.user.username} 删除菜品: ${dishId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加菜品到发现页
 * @route POST /api/admin/dishes/discover
 */
const addDishToDiscover = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dishId, discoverType = 'recommend' } = req.body;
        logger_1.default.info(`收到添加菜品到发现页请求: dishId=${dishId}, discoverType=${discoverType}`);
        if (!dishId) {
            throw new error_1.BusinessError('缺少菜品ID', response_1.ResponseCode.VALIDATION);
        }
        if (!['recommend', 'newest', 'popular'].includes(discoverType)) {
            throw new error_1.BusinessError('无效的发现类型', response_1.ResponseCode.VALIDATION);
        }
        logger_1.default.info(`开始查找菜品: ${dishId}`);
        // 检查菜品是否存在
        const dish = yield models_1.Dish.findByPk(dishId);
        if (!dish) {
            logger_1.default.error(`菜品不存在: ${dishId}`);
            throw new error_1.BusinessError('菜品不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`菜品找到: ${dish.name}, 厨房ID: ${dish.kitchen_id}`);
        // 检查是否已经在发现页
        const existingItem = yield models_1.DiscoverItem.findOne({
            where: { dish_id: dishId }
        });
        if (existingItem) {
            logger_1.default.warn(`菜品已在发现页: ${dishId}`);
            throw new error_1.BusinessError('菜品已在发现页', response_1.ResponseCode.CONFLICT);
        }
        logger_1.default.info(`开始创建发现项: dish_id=${dishId}, kitchen_id=${dish.kitchen_id}, tab_type=${discoverType}`);
        // 生成合适的排序值（避免超出INTEGER范围）
        // 使用当前时间的最后6位数字作为排序值
        const sortValue = Math.floor(Date.now() / 1000) % 1000000; // 得到0-999999之间的数
        // 添加到发现页 - 修复字段匹配问题
        const discoverItem = yield models_1.DiscoverItem.create({
            dish_id: Number(dishId),
            kitchen_id: dish.kitchen_id,
            tab_type: discoverType,
            sort_value: sortValue,
            is_read: false
        });
        logger_1.default.info(`发现项创建成功: ID=${discoverItem.id}`);
        logger_1.default.info(`管理员 ${req.user.username} 添加菜品到发现页: ${dishId} -> ${discoverType}`);
        (0, response_1.success)(res, {
            id: discoverItem.id,
            success: true
        });
    }
    catch (err) {
        logger_1.default.error(`添加菜品到发现页失败: ${err}`);
        next(err);
    }
});
/**
 * 从发现页移除菜品
 * @route DELETE /api/admin/dishes/:dishId/discover
 */
const removeDishFromDiscover = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dishId } = req.params;
        const discoverItem = yield models_1.DiscoverItem.findOne({
            where: { dish_id: dishId }
        });
        if (!discoverItem) {
            throw new error_1.BusinessError('菜品不在发现页', response_1.ResponseCode.NOT_FOUND);
        }
        yield discoverItem.destroy();
        logger_1.default.info(`管理员 ${req.user.username} 从发现页移除菜品: ${dishId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
// ============= 订单管理 =============
/**
 * 获取订单列表
 * @route GET /api/admin/orders
 */
const getOrderList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (keyword) {
            whereConditions.id = { [sequelize_1.Op.like]: `%${keyword}%` };
        }
        if (status) {
            whereConditions.status = status;
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.Order.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.Kitchen,
                    as: 'kitchen',
                    attributes: ['name']
                },
                {
                    model: models_1.User,
                    as: 'user',
                    attributes: ['nick_name']
                }
            ]
        });
        const orders = rows.map(order => {
            var _a, _b;
            return ({
                id: order.id,
                orderNumber: order.id,
                kitchenName: ((_a = order.kitchen) === null || _a === void 0 ? void 0 : _a.name) || '未知厨房',
                userName: ((_b = order.user) === null || _b === void 0 ? void 0 : _b.nick_name) || '未知用户',
                amount: order.total_price,
                status: order.status,
                tableNo: order.table_no,
                remark: order.remark,
                createTime: order.created_at,
                cookingTime: order.cooking_time,
                completedTime: order.completed_time
            });
        });
        (0, response_1.success)(res, {
            orders,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取订单统计数据
 * @route GET /api/admin/orders/stats
 */
const getOrderStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalOrders, pendingOrders, completedOrders] = yield Promise.all([
            models_1.Order.count(),
            models_1.Order.count({ where: { status: 'pending' } }),
            models_1.Order.count({ where: { status: 'completed' } })
        ]);
        // 今日订单数
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayOrders = yield models_1.Order.count({
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                }
            }
        });
        // 今日收入
        const todayRevenue = (yield models_1.Order.sum('total_price', {
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                },
                status: 'completed'
            }
        })) || 0;
        (0, response_1.success)(res, {
            totalOrders,
            pendingOrders,
            completedOrders,
            todayOrders,
            todayRevenue: Number(todayRevenue.toFixed(2))
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 内容管理 =============
/**
 * 获取消息列表
 * @route GET /api/admin/messages
 */
const getMessageList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, type, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (type) {
            whereConditions.type = type;
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.Message.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']]
        });
        const messages = rows.map(message => ({
            id: message.id,
            title: message.title,
            content: message.content,
            type: message.type,
            priority: 'normal', // 默认优先级
            targetType: 'all', // 默认目标类型
            targetId: null,
            status: 'published', // 默认状态
            readCount: 0, // 暂时固定为0
            sendTime: message.created_at, // 使用创建时间作为发送时间
            createTime: message.created_at,
            updateTime: message.updated_at,
            creatorId: 'admin',
            creatorName: 'Admin',
            isRead: message.is_read || false
        }));
        (0, response_1.success)(res, {
            messages,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 创建消息
 * @route POST /api/admin/messages
 */
const createMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { title, content, type, priority, targetType, targetId, sendTime } = req.body;
        if (!title || !content || !type) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        // 验证消息类型是否有效
        const validTypes = ['system', 'notice', 'promotion', 'warning'];
        if (!validTypes.includes(type)) {
            throw new error_1.BusinessError('无效的消息类型', response_1.ResponseCode.VALIDATION);
        }
        // 创建消息 - 管理员创建的消息使用user_id=0表示系统消息
        const message = yield models_1.Message.create({
            title,
            content,
            type: type, // 保持原始类型
            user_id: 0, // 管理员创建的消息使用0表示系统消息
            image: '', // 默认为空
            is_read: false, // 默认未读
        });
        logger_1.default.info(`管理员 ${((_a = req.user) === null || _a === void 0 ? void 0 : _a.username) || 'admin'} 创建消息: ${title}`);
        (0, response_1.success)(res, { id: message.id });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取消息详情
 * @route GET /api/admin/messages/:messageId
 */
const getMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { messageId } = req.params;
        const message = yield models_1.Message.findByPk(messageId);
        if (!message) {
            throw new error_1.BusinessError('消息不存在', response_1.ResponseCode.NOT_FOUND);
        }
        const messageDetail = {
            id: message.id,
            title: message.title,
            content: message.content,
            type: message.type,
            priority: 'normal', // 默认优先级
            targetType: 'all', // 默认目标类型
            targetId: null,
            status: 'published', // 默认状态为已发布
            readCount: 0, // 暂时固定为0
            sendTime: message.created_at, // 使用创建时间作为发送时间
            createTime: message.created_at,
            updateTime: message.updated_at,
            creatorId: 'admin',
            creatorName: 'Admin'
        };
        (0, response_1.success)(res, messageDetail);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新消息
 * @route PUT /api/admin/messages/:messageId
 */
const updateMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { messageId } = req.params;
        const { title, content, type, priority, targetType, targetId, sendTime } = req.body;
        const message = yield models_1.Message.findByPk(messageId);
        if (!message) {
            throw new error_1.BusinessError('消息不存在', response_1.ResponseCode.NOT_FOUND);
        }
        // 构建更新数据
        const updateData = {};
        if (title)
            updateData.title = title;
        if (content)
            updateData.content = content;
        if (type)
            updateData.type = type;
        yield message.update(updateData);
        logger_1.default.info(`管理员 ${((_a = req.user) === null || _a === void 0 ? void 0 : _a.username) || 'admin'} 更新消息: ${messageId}`);
        const updatedMessage = {
            id: message.id,
            title: message.title,
            content: message.content,
            type: message.type,
            priority: priority || 'normal',
            targetType: targetType || 'all',
            targetId: targetId || null,
            status: 'published',
            readCount: 0,
            sendTime: message.created_at,
            createTime: message.created_at,
            updateTime: message.updated_at,
            creatorId: 'admin',
            creatorName: 'Admin'
        };
        (0, response_1.success)(res, updatedMessage);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 发布消息
 * @route POST /api/admin/messages/:messageId/publish
 */
const publishMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { messageId } = req.params;
        const message = yield models_1.Message.findByPk(messageId);
        if (!message) {
            throw new error_1.BusinessError('消息不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 发布消息: ${messageId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 归档消息
 * @route POST /api/admin/messages/:messageId/archive
 */
const archiveMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { messageId } = req.params;
        const message = yield models_1.Message.findByPk(messageId);
        if (!message) {
            throw new error_1.BusinessError('消息不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 归档消息: ${messageId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除消息
 * @route DELETE /api/admin/messages/:messageId
 */
const deleteMessage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { messageId } = req.params;
        const message = yield models_1.Message.findByPk(messageId);
        if (!message) {
            throw new error_1.BusinessError('消息不存在', response_1.ResponseCode.NOT_FOUND);
        }
        yield message.destroy();
        logger_1.default.info(`管理员 ${req.user.username} 删除消息: ${messageId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取举报列表
 * @route GET /api/admin/reports
 */
const getReportList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        const { count, rows } = yield models_1.Report.findAndCountAll({
            where: whereConditions,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.User,
                    as: 'user',
                    attributes: ['nick_name']
                }
            ]
        });
        const reports = rows.map(report => {
            var _a;
            return ({
                id: report.id,
                targetType: 'dish',
                targetId: report.dish_id,
                reason: report.reason,
                description: report.reason,
                reporterName: ((_a = report.user) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
                status: 'pending',
                createTime: report.created_at
            });
        });
        (0, response_1.success)(res, {
            reports,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 处理举报
 * @route PUT /api/admin/reports/:id/handle
 */
const handleReport = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { action, reason } = req.body;
        if (!action || !['approve', 'reject'].includes(action)) {
            throw new error_1.BusinessError('无效的处理动作', response_1.ResponseCode.VALIDATION);
        }
        const report = yield models_1.Report.findByPk(id);
        if (!report) {
            throw new error_1.BusinessError('举报不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 处理举报 ${id}: ${action}, 原因: ${reason}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取举报详情
 * @route GET /api/admin/reports/:reportId
 */
const getReport = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { reportId } = req.params;
        const report = yield models_1.Report.findByPk(reportId, {
            include: [
                {
                    model: models_1.User,
                    as: 'user',
                    attributes: ['nick_name']
                }
            ]
        });
        if (!report) {
            throw new error_1.BusinessError('举报不存在', response_1.ResponseCode.NOT_FOUND);
        }
        const reportDetail = {
            id: report.id,
            reporterId: report.user_id.toString(),
            reporterName: ((_a = report.user) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
            reporterAvatar: null,
            targetType: 'dish',
            targetId: ((_b = report.dish_id) === null || _b === void 0 ? void 0 : _b.toString()) || '',
            targetTitle: '菜品举报',
            reason: report.reason,
            description: report.reason,
            status: 'pending',
            severity: 'medium',
            createTime: report.created_at,
            updateTime: report.updated_at,
            handlerId: null,
            handlerName: null,
            handleTime: null,
            handleNote: null,
            evidence: []
        };
        (0, response_1.success)(res, reportDetail);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新举报
 * @route PUT /api/admin/reports/:reportId
 */
const updateReport = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { reportId } = req.params;
        const updateData = req.body;
        const report = yield models_1.Report.findByPk(reportId);
        if (!report) {
            throw new error_1.BusinessError('举报不存在', response_1.ResponseCode.NOT_FOUND);
        }
        logger_1.default.info(`管理员 ${req.user.username} 更新举报: ${reportId}`);
        const updatedReport = {
            id: report.id,
            reporterId: report.user_id.toString(),
            reporterName: '未知用户',
            reporterAvatar: null,
            targetType: 'dish',
            targetId: ((_a = report.dish_id) === null || _a === void 0 ? void 0 : _a.toString()) || '',
            targetTitle: '菜品举报',
            reason: report.reason,
            description: report.reason,
            status: updateData.status || 'pending',
            severity: 'medium',
            createTime: report.created_at,
            updateTime: new Date(),
            handlerId: req.user.id,
            handlerName: req.user.username,
            handleTime: new Date(),
            handleNote: updateData.handleNote,
            evidence: []
        };
        (0, response_1.success)(res, updatedReport);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除举报
 * @route DELETE /api/admin/reports/:reportId
 */
const deleteReport = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { reportId } = req.params;
        const report = yield models_1.Report.findByPk(reportId);
        if (!report) {
            throw new error_1.BusinessError('举报不存在', response_1.ResponseCode.NOT_FOUND);
        }
        yield report.destroy();
        logger_1.default.info(`管理员 ${req.user.username} 删除举报: ${reportId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
// ============= 发现管理 =============
/**
 * 获取发现内容列表
 * @route GET /api/admin/discover
 */
const getDiscoverList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, discoverType, kitchenId, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (discoverType) {
            whereConditions.tab_type = discoverType;
        }
        if (kitchenId) {
            whereConditions.kitchen_id = kitchenId;
        }
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        // 构建include条件
        const include = [
            {
                model: models_1.Dish,
                as: 'dish',
                where: keyword ? {
                    name: {
                        [sequelize_1.Op.like]: `%${keyword}%`
                    }
                } : undefined,
                include: [
                    {
                        model: models_1.Kitchen,
                        as: 'kitchen',
                        attributes: ['id', 'name']
                    },
                    {
                        model: models_1.Category,
                        as: 'category',
                        attributes: ['id', 'name']
                    }
                ]
            }
        ];
        const { count, rows } = yield models_1.DiscoverItem.findAndCountAll({
            where: whereConditions,
            include,
            offset,
            limit,
            order: [['sort_value', 'DESC'], ['created_at', 'DESC']],
            distinct: true
        });
        const items = rows.map(item => {
            var _a, _b, _c, _d, _e, _f, _g, _h;
            return ({
                id: item.id,
                dishId: item.dish_id,
                dishName: ((_a = item.dish) === null || _a === void 0 ? void 0 : _a.name) || '未知菜品',
                dishImage: ((_b = item.dish) === null || _b === void 0 ? void 0 : _b.image) || '',
                dishPrice: ((_c = item.dish) === null || _c === void 0 ? void 0 : _c.price) || 0,
                kitchenId: item.kitchen_id,
                kitchenName: ((_e = (_d = item.dish) === null || _d === void 0 ? void 0 : _d.kitchen) === null || _e === void 0 ? void 0 : _e.name) || '未知厨房',
                categoryName: ((_g = (_f = item.dish) === null || _f === void 0 ? void 0 : _f.category) === null || _g === void 0 ? void 0 : _g.name) || '未知分类',
                discoverType: item.tab_type,
                sortValue: item.sort_value,
                likeCount: 0, // 可以后续从Like表统计
                viewCount: 0, // 可以后续添加浏览统计
                salesCount: ((_h = item.dish) === null || _h === void 0 ? void 0 : _h.sales) || 0,
                createTime: item.created_at,
                isRecommended: false // 可以从dish表的推荐字段获取
            });
        });
        (0, response_1.success)(res, {
            items,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取发现管理统计数据
 * @route GET /api/admin/discover/manage-stats
 */
const getDiscoverManageStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalItems, recommendItems, newestItems, popularItems] = yield Promise.all([
            models_1.DiscoverItem.count(),
            models_1.DiscoverItem.count({ where: { tab_type: 'recommend' } }),
            models_1.DiscoverItem.count({ where: { tab_type: 'newest' } }),
            models_1.DiscoverItem.count({ where: { tab_type: 'popular' } })
        ]);
        (0, response_1.success)(res, {
            totalItems,
            recommendItems,
            newestItems,
            popularItems,
            totalViews: 0, // 可以后续添加统计
            totalLikes: 0 // 可以后续添加统计
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取发现统计
 * @route GET /api/admin/discover/stats
 */
const getDiscoverStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalItems] = yield Promise.all([
            models_1.DiscoverItem.count()
        ]);
        (0, response_1.success)(res, {
            totalItems,
            activeItems: totalItems,
            todayItems: 0
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 签到配置管理 =============
/**
 * 获取签到配置
 * @route GET /api/admin/checkin/config
 */
const getCheckInConfig = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 从配置文件返回签到配置
        (0, response_1.success)(res, {
            basicReward: config_1.default.signIn.basicReward,
            continuousReward: config_1.default.signIn.continuousReward,
            maxDaysPerMonth: config_1.default.signIn.maxDaysPerMonth
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取签到记录
 * @route GET /api/admin/checkin/records
 */
const getCheckInRecords = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20 } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        const { count, rows } = yield models_1.SignIn.findAndCountAll({
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.User,
                    attributes: ['id', 'nick_name', 'avatar_url']
                }
            ]
        });
        const records = rows.map(record => {
            var _a, _b;
            return ({
                id: record.id.toString(),
                userId: record.user_id.toString(),
                userName: ((_a = record.User) === null || _a === void 0 ? void 0 : _a.nick_name) || '用户' + record.user_id,
                userAvatar: ((_b = record.User) === null || _b === void 0 ? void 0 : _b.avatar_url) || '',
                reward: record.reward,
                date: record.date,
                created_at: record.created_at
            });
        });
        (0, response_1.success)(res, {
            records,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取广告观看记录
 * @route GET /api/admin/adviews/records
 */
const getAdViewRecords = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20 } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        const { count, rows } = yield models_1.AdView.findAndCountAll({
            offset,
            limit,
            order: [['created_at', 'DESC']],
            include: [
                {
                    model: models_1.User,
                    attributes: ['id', 'nick_name', 'avatar_url']
                }
            ]
        });
        const records = rows.map(record => {
            var _a, _b;
            return ({
                id: record.id.toString(),
                userId: record.user_id.toString(),
                userName: ((_a = record.User) === null || _a === void 0 ? void 0 : _a.nick_name) || '用户' + record.user_id,
                userAvatar: ((_b = record.User) === null || _b === void 0 ? void 0 : _b.avatar_url) || '',
                reward: record.reward,
                date: record.date,
                created_at: record.created_at
            });
        });
        (0, response_1.success)(res, {
            records,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取签到统计（别名）
 * @route GET /api/admin/checkin/stats
 */
const getCheckInStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalSignIns, todaySignIns] = yield Promise.all([
            models_1.SignIn.count(),
            models_1.SignIn.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            })
        ]);
        (0, response_1.success)(res, {
            totalSignIns,
            todaySignIns
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 管理员管理 =============
/**
 * 获取管理员列表
 * @route GET /api/admin/admins
 */
const getAdminList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20 } = req.query;
        // 从临时账户列表返回数据
        const admins = ADMIN_ACCOUNTS.map(admin => ({
            id: admin.id,
            username: admin.username,
            role: admin.role,
            avatar: admin.avatar,
            status: 'active',
            lastLoginTime: null,
            createTime: '2023-01-01T00:00:00.000Z'
        }));
        (0, response_1.success)(res, {
            admins,
            total: admins.length,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取管理员角色列表
 * @route GET /api/admin/roles
 */
const getAdminRoles = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const roles = [
            {
                id: 'admin',
                name: '管理员',
                description: '普通管理员权限',
                permissions: ['users', 'kitchens', 'dishes', 'orders']
            },
            {
                id: 'super_admin',
                name: '超级管理员',
                description: '超级管理员权限',
                permissions: ['*']
            }
        ];
        (0, response_1.success)(res, { roles });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取系统信息
 * @route GET /api/admin/system/info
 */
const getSystemInfo = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const systemInfo = {
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            nodeVersion: process.version,
            platform: process.platform,
            databaseStatus: 'connected',
            serverTime: new Date().toISOString()
        };
        (0, response_1.success)(res, systemInfo);
    }
    catch (err) {
        next(err);
    }
});
// ============= 发现管理中缺失的功能 =============
/**
 * 从发现页移除项目
 * @route DELETE /api/admin/discover/:itemId
 */
const removeFromDiscover = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { itemId } = req.params;
        const item = yield models_1.DiscoverItem.findByPk(itemId);
        if (!item) {
            throw new error_1.BusinessError('发现项不存在', response_1.ResponseCode.NOT_FOUND);
        }
        yield item.destroy();
        logger_1.default.info(`管理员 ${req.user.username} 从发现页移除项目: ${itemId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新发现项类型
 * @route PUT /api/admin/discover/:itemId/type
 */
const updateDiscoverType = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { itemId } = req.params;
        const { discoverType } = req.body;
        if (!['recommend', 'newest', 'popular'].includes(discoverType)) {
            throw new error_1.BusinessError('无效的发现类型', response_1.ResponseCode.VALIDATION);
        }
        const item = yield models_1.DiscoverItem.findByPk(itemId);
        if (!item) {
            throw new error_1.BusinessError('发现项不存在', response_1.ResponseCode.NOT_FOUND);
        }
        yield item.update({ tab_type: discoverType });
        logger_1.default.info(`管理员 ${req.user.username} 更新发现项类型: ${itemId} -> ${discoverType}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新发现项排序
 * @route PUT /api/admin/discover/:itemId/sort
 */
const updateDiscoverSort = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { itemId } = req.params;
        const { sortValue } = req.body;
        if (typeof sortValue !== 'number') {
            throw new error_1.BusinessError('排序值必须是数字', response_1.ResponseCode.VALIDATION);
        }
        const item = yield models_1.DiscoverItem.findByPk(itemId);
        if (!item) {
            throw new error_1.BusinessError('发现项不存在', response_1.ResponseCode.NOT_FOUND);
        }
        yield item.update({ sort_value: sortValue });
        logger_1.default.info(`管理员 ${req.user.username} 更新发现项排序: ${itemId} -> ${sortValue}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 创建发现内容（保留原有接口兼容性）
 * @route POST /api/admin/discover
 */
const createDiscoverItem = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { title, content, type, imageUrl } = req.body;
        if (!title || !content || !type) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const item = yield models_1.DiscoverItem.create({
            dish_id: 1,
            kitchen_id: '1',
            tab_type: type,
            sort_value: 0,
            is_read: false
        });
        logger_1.default.info(`管理员 ${req.user.username} 创建发现内容: ${title}`);
        (0, response_1.success)(res, { id: item.id });
    }
    catch (err) {
        next(err);
    }
});
// ============= 任务管理 =============
/**
 * 获取签到统计
 * @route GET /api/admin/tasks/signin/stats
 */
const getSignInStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalSignIns, todaySignIns] = yield Promise.all([
            models_1.SignIn.count(),
            models_1.SignIn.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            })
        ]);
        (0, response_1.success)(res, {
            totalSignIns,
            todaySignIns
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取广告观看统计
 * @route GET /api/admin/tasks/ads/stats
 */
const getAdStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalAdViews, todayAdViews] = yield Promise.all([
            models_1.AdView.count(),
            models_1.AdView.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            })
        ]);
        (0, response_1.success)(res, {
            totalAdViews,
            todayAdViews
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 搜索管理 =============
/**
 * 获取热门关键词列表
 * @route GET /api/admin/search/keywords
 */
const getHotKeywords = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20 } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        const { count, rows } = yield models_1.HotKeyword.findAndCountAll({
            offset,
            limit,
            order: [['count', 'DESC']]
        });
        const keywords = rows.map(keyword => ({
            id: keyword.id,
            keyword: keyword.keyword,
            searchCount: keyword.count,
            createTime: keyword.created_at
        }));
        (0, response_1.success)(res, {
            keywords,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取搜索历史统计
 * @route GET /api/admin/search/history/stats
 */
const getSearchStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalSearches, todaySearches] = yield Promise.all([
            models_1.SearchHistory.count(),
            models_1.SearchHistory.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            })
        ]);
        (0, response_1.success)(res, {
            totalSearches,
            todaySearches
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 菜品分类管理 =============
/**
 * 获取菜品分类列表
 * @route GET /api/admin/dish-categories
 */
const getDishCategories = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const categories = yield models_1.Category.findAll({
            order: [['created_at', 'DESC']]
        });
        const formattedCategories = categories.map(category => ({
            id: category.id,
            name: category.name,
            description: '',
            dishCount: 0,
            createTime: category.created_at
        }));
        (0, response_1.success)(res, { categories: formattedCategories });
    }
    catch (err) {
        next(err);
    }
});
// ============= 消息统计 =============
/**
 * 获取消息统计
 * @route GET /api/admin/messages/stats
 */
const getMessageStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalMessages, unreadMessages] = yield Promise.all([
            models_1.Message.count(),
            models_1.Message.count({
                where: {
                    is_read: false
                }
            })
        ]);
        (0, response_1.success)(res, {
            totalMessages,
            unreadMessages,
            readMessages: totalMessages - unreadMessages
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 举报统计 =============
/**
 * 获取举报统计
 * @route GET /api/admin/reports/stats
 */
const getReportStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalReports, pendingReports] = yield Promise.all([
            models_1.Report.count(),
            models_1.Report.count()
        ]);
        (0, response_1.success)(res, {
            totalReports,
            pendingReports,
            resolvedReports: 0
        });
    }
    catch (err) {
        next(err);
    }
});
// ============= 评论管理 =============
/**
 * 获取评论列表
 * @route GET /api/admin/comments
 */
const getCommentList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 20, keyword, targetType, status, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(pageSize);
        const limit = Number(pageSize);
        // 构建查询条件
        const whereConditions = {};
        if (startDate && endDate) {
            whereConditions.created_at = {
                [sequelize_1.Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }
        // 构建include条件
        const include = [
            {
                model: models_1.User,
                as: 'user',
                attributes: ['id', 'nick_name', 'avatar_url'],
                where: keyword ? {
                    nick_name: {
                        [sequelize_1.Op.like]: `%${keyword}%`
                    }
                } : undefined
            },
            {
                model: models_1.Dish,
                as: 'dish',
                attributes: ['id', 'name', 'image'],
                include: [
                    {
                        model: models_1.Kitchen,
                        as: 'kitchen',
                        attributes: ['id', 'name']
                    }
                ]
            }
        ];
        const { count, rows } = yield models_1.Comment.findAndCountAll({
            where: whereConditions,
            include,
            offset,
            limit,
            order: [['created_at', 'DESC']],
            distinct: true
        });
        const comments = rows.map(comment => {
            var _a, _b, _c;
            return ({
                id: comment.id.toString(),
                userId: comment.user_id.toString(),
                userName: ((_a = comment.user) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
                userAvatar: (_b = comment.user) === null || _b === void 0 ? void 0 : _b.avatar_url,
                targetType: 'dish',
                targetId: comment.dish_id.toString(),
                targetTitle: ((_c = comment.dish) === null || _c === void 0 ? void 0 : _c.name) || '未知菜品',
                content: comment.content,
                rating: comment.rating,
                status: 'visible', // 默认可见
                isViolation: false, // 默认非违规
                violationReason: null,
                likeCount: 0, // 暂未实现
                replyCount: 0, // 暂未实现
                createTime: comment.created_at,
                updateTime: comment.updated_at,
                parentId: null,
                replies: []
            });
        });
        (0, response_1.success)(res, {
            comments,
            total: count,
            page: Number(page),
            pageSize: Number(pageSize)
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取评论统计
 * @route GET /api/admin/comments/stats
 */
const getCommentStats = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalComments] = yield Promise.all([
            models_1.Comment.count()
        ]);
        // 今日新增评论
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayComments = yield models_1.Comment.count({
            where: {
                created_at: {
                    [sequelize_1.Op.gte]: today
                }
            }
        });
        // 计算平均评分
        const avgRatingResult = yield models_1.Comment.findOne({
            attributes: [[sequelize_2.default.fn('AVG', sequelize_2.default.col('rating')), 'avgRating']],
            raw: true
        });
        const averageRating = parseFloat((avgRatingResult === null || avgRatingResult === void 0 ? void 0 : avgRatingResult.avgRating) || '0');
        (0, response_1.success)(res, {
            totalComments,
            visibleComments: totalComments, // 暂时假设所有评论都可见
            hiddenComments: 0, // 暂未实现隐藏功能
            violationComments: 0, // 暂未实现违规标记功能
            todayComments,
            averageRating: Math.round(averageRating * 100) / 100 // 保留两位小数
        });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取评论详情
 * @route GET /api/admin/comments/:commentId
 */
const getComment = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const { commentId } = req.params;
        const comment = yield models_1.Comment.findByPk(commentId, {
            include: [
                {
                    model: models_1.User,
                    as: 'user',
                    attributes: ['id', 'nick_name', 'avatar_url']
                },
                {
                    model: models_1.Dish,
                    as: 'dish',
                    attributes: ['id', 'name', 'image'],
                    include: [
                        {
                            model: models_1.Kitchen,
                            as: 'kitchen',
                            attributes: ['id', 'name']
                        }
                    ]
                }
            ]
        });
        if (!comment) {
            throw new error_1.BusinessError('评论不存在', response_1.ResponseCode.NOT_FOUND);
        }
        const commentDetail = {
            id: comment.id.toString(),
            userId: comment.user_id.toString(),
            userName: ((_a = comment.user) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
            userAvatar: (_b = comment.user) === null || _b === void 0 ? void 0 : _b.avatar_url,
            targetType: 'dish',
            targetId: comment.dish_id.toString(),
            targetTitle: ((_c = comment.dish) === null || _c === void 0 ? void 0 : _c.name) || '未知菜品',
            content: comment.content,
            rating: comment.rating,
            status: 'visible',
            isViolation: false,
            violationReason: null,
            likeCount: 0,
            replyCount: 0,
            createTime: comment.created_at,
            updateTime: comment.updated_at,
            parentId: null,
            replies: []
        };
        (0, response_1.success)(res, commentDetail);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新评论
 * @route PUT /api/admin/comments/:commentId
 */
const updateComment = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { commentId } = req.params;
        const updateData = req.body;
        logger_1.default.info(`管理员 ${req.user.username} 更新评论: ${commentId}`);
        // 模拟更新后的评论数据
        const updatedComment = {
            id: commentId,
            userId: '1',
            userName: '用户1',
            userAvatar: null,
            targetType: 'dish',
            targetId: '1',
            targetTitle: '测试菜品',
            content: '这道菜很好吃',
            rating: 5,
            status: updateData.status || 'visible',
            isViolation: updateData.isViolation || false,
            violationReason: updateData.violationReason || null,
            likeCount: 0,
            replyCount: 0,
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString(),
            parentId: null,
            replies: []
        };
        (0, response_1.success)(res, updatedComment);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除评论
 * @route DELETE /api/admin/comments/:commentId
 */
const deleteComment = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { commentId } = req.params;
        logger_1.default.info(`管理员 ${req.user.username} 删除评论: ${commentId}`);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    adminLogin,
    getDashboardStats,
    getDashboardCharts,
    getRecentOrders,
    getUserList,
    getUserStats,
    updateUserStatus,
    getUserDetail,
    adjustUserCoins,
    resetUserPassword,
    getKitchenList,
    getKitchenStats,
    updateKitchenStatus,
    getDishList,
    getDishCategories,
    getDishStats,
    updateDish,
    updateDishStatus,
    deleteDish,
    addDishToDiscover,
    removeDishFromDiscover,
    getOrderList,
    getOrderStats,
    getMessageList,
    getMessageStats,
    getMessage,
    createMessage,
    updateMessage,
    publishMessage,
    archiveMessage,
    deleteMessage,
    getReportList,
    getReportStats,
    getReport,
    handleReport,
    updateReport,
    deleteReport,
    getDiscoverList,
    getDiscoverManageStats,
    getDiscoverStats,
    removeFromDiscover,
    updateDiscoverType,
    updateDiscoverSort,
    createDiscoverItem,
    getSignInStats,
    getCheckInStats,
    getCheckInConfig,
    getCheckInRecords,
    getAdViewRecords,
    getAdStats,
    getHotKeywords,
    getSearchStats,
    getCommentList,
    getCommentStats,
    getComment,
    updateComment,
    deleteComment,
    getAdminList,
    getAdminRoles,
    getSystemInfo
};
//# sourceMappingURL=adminController.js.map