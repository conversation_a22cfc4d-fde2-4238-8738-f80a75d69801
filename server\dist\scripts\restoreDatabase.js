"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 数据库恢复脚本
 * 从备份文件恢复数据库
 */
const child_process_1 = require("child_process");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const initDatabase_1 = __importDefault(require("./initDatabase"));
// 备份目录
const BACKUP_DIR = path_1.default.join(__dirname, '../../backups');
/**
 * 获取所有备份文件
 * @returns 备份文件列表
 */
function getBackupFiles() {
    if (!fs_1.default.existsSync(BACKUP_DIR)) {
        return [];
    }
    return fs_1.default.readdirSync(BACKUP_DIR)
        .filter(file => file.startsWith('backup_') && file.endsWith('.sql'))
        .map(file => ({
        name: file,
        path: path_1.default.join(BACKUP_DIR, file),
        time: fs_1.default.statSync(path_1.default.join(BACKUP_DIR, file)).mtime.getTime(),
    }))
        .sort((a, b) => b.time - a.time); // 按时间降序排序
}
/**
 * 从备份文件恢复数据库
 * @param backupFilePath 备份文件路径
 */
function restoreDatabase(backupFilePath) {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info(`开始从备份文件恢复数据库: ${backupFilePath}`);
        // 确保数据库存在
        yield (0, initDatabase_1.default)();
        // 构建 mysql 命令
        const command = `mysql -h ${config_1.default.database.host} -P ${config_1.default.database.port} -u ${config_1.default.database.user} -p${config_1.default.database.password} ${config_1.default.database.name} < "${backupFilePath}"`;
        return new Promise((resolve, reject) => {
            (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                if (error) {
                    logger_1.default.error(`恢复数据库失败: ${error.message}`);
                    reject(error);
                    return;
                }
                if (stderr) {
                    logger_1.default.warn(`恢复过程中的警告: ${stderr}`);
                }
                logger_1.default.info('数据库恢复成功');
                resolve();
            });
        });
    });
}
/**
 * 主函数
 */
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // 获取命令行参数
            const args = process.argv.slice(2);
            let backupFilePath = '';
            if (args.length > 0) {
                // 使用指定的备份文件
                backupFilePath = args[0];
                if (!fs_1.default.existsSync(backupFilePath)) {
                    // 检查是否是备份目录中的文件名
                    const backupDir = path_1.default.join(BACKUP_DIR, args[0]);
                    if (fs_1.default.existsSync(backupDir)) {
                        backupFilePath = backupDir;
                    }
                    else {
                        throw new Error(`备份文件不存在: ${args[0]}`);
                    }
                }
            }
            else {
                // 使用最新的备份文件
                const backupFiles = getBackupFiles();
                if (backupFiles.length === 0) {
                    throw new Error('没有找到备份文件');
                }
                backupFilePath = backupFiles[0].path;
                logger_1.default.info(`使用最新的备份文件: ${backupFiles[0].name}`);
            }
            // 恢复数据库
            yield restoreDatabase(backupFilePath);
            logger_1.default.info('数据库恢复完成');
        }
        catch (error) {
            logger_1.default.error('数据库恢复失败:', error);
            process.exit(1);
        }
    });
}
// 如果直接运行此脚本，则执行恢复
if (require.main === module) {
    main();
}
exports.default = restoreDatabase;
//# sourceMappingURL=restoreDatabase.js.map