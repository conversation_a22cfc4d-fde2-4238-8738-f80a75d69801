/**
 * 前端配置管理
 * 支持不同环境的配置切换
 */

// 环境配置
const ENV_CONFIG = {
  // 开发环境
  development: {
    SERVER_BASE_URL: 'https://dzcdd.zj1.natnps.cn',
    DEBUG: true
  },
  // 测试环境
  testing: {
    SERVER_BASE_URL: 'https://test.dzcdd.zj1.natnps.cn',
    DEBUG: true
  },
  // 生产环境
  production: {
    SERVER_BASE_URL: 'https://dzcdd.zj1.natnps.cn',
    DEBUG: false
  }
};

// 当前环境（可通过编译时配置或其他方式设置）
const CURRENT_ENV = 'production'; // 默认为生产环境

// 获取当前环境配置
const getCurrentConfig = () => {
  return ENV_CONFIG[CURRENT_ENV] || ENV_CONFIG.production;
};

// 导出配置
const config = getCurrentConfig();

module.exports = {
  ...config,
  ENV_CONFIG,
  CURRENT_ENV,
  getCurrentConfig
}; 