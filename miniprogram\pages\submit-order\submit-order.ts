// 提交订单页面
import { getCartList, clearCart, submitOrder } from '../../api/dishApi'
import { getTableList, getCurrentKitchenBaseInfo } from '../../api/kitchenApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker'

// 获取应用实例
const app = getApp<IAppOption>()

// 菜品项接口
interface DishItem {
  id: string;
  name: string;
  image: string;
  price: number;
  count: number;
  tags: string[];
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单商品列表
    orderItems: [] as DishItem[],
    // 餐厅信息
    restaurantInfo: {
      name: '川味小馆',
      logo: ''
    },
    // 总价
    totalPrice: 0,
    // 总菜品数量（包括份数）
    totalItems: 0,
    // 备注
    remark: '',
    // 桌号列表与选中的索引
    tables: [] as Array<{id: string; name: string; sort: number}>,
    tableIndex: -1,
    // 确认下单弹窗显示状态
    showConfirmDialog: false,
    // 桌号选择弹窗显示状态
    showTableDialog: false,
    // 临时选中的桌号索引
    tempTableIndex: 0,
    // 取消下单弹窗显示状态
    showCancelDialog: false,
    // 支付成功弹窗显示状态
    showSuccessDialog: false,
    // 订单ID
    orderId: '',
    // 状态栏高度
    statusBarHeight: 0,
    shareTexts: [
      "救命！这道菜需要你的神之手👨🍳",
      "江湖救急！我的胃在等你下锅🔥",
      "手滑点多了…求接盘侠（会炒菜的那种）！",
      "一键召唤厨神！这份订单就差你了✨",
      "朋友，你妈喊你帮我炒个菜！",
      "想吃这桌菜？先过你这关！🍲",
      "订单已下，就缺个像你一样的大厨了！",
      "这菜谱绝了！但…你会做吗？😏",
      "帮我炒菜，分你一半！骗人是小狗🐶"
    ],
    // 背景图片URL
    shopBg: DEFAULT_IMAGES.KITCHEN_BACKGROUND,
    // 厨房信息
    currentKitchenId: '', // 当前选中的厨房ID
    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查当前厨房ID
    this.checkCurrentKitchen();

    // 获取购物车数据
    this.fetchCartList()
    // 获取餐厅信息
    this.fetchRestaurantInfo()
    // 获取桌号列表
    this.fetchTableList()
    // 获取状态栏高度
    this.getStatusBarHeight()
    // 获取背景图片设置
    this.getBackgroundSettings()

    // 注册主题变更监听
    app.addThemeChangeListener(this.onThemeChanged)
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 注销主题变更监听
    app.removeThemeChangeListener(this.onThemeChanged)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查当前厨房ID是否变更
    this.checkCurrentKitchen();

    // 每次页面显示时检查背景设置是否有更新
    this.getBackgroundSettings()
  },

  /**
   * 检查当前选中的厨房
   */
  checkCurrentKitchen() {
    const lastSelectedKitchen = wx.getStorageSync('last_selected_kitchen');

    // 如果当前厨房ID与存储中的不同，更新并重新加载数据
    if (lastSelectedKitchen && this.data.currentKitchenId !== lastSelectedKitchen) {
      this.setData({
        currentKitchenId: lastSelectedKitchen
      });

      // 重新加载购物车数据
      this.fetchCartList();
      // 重新加载餐厅信息
      this.fetchRestaurantInfo();
      // 重新加载桌号数据
      this.fetchTableList();
    } else if (!this.data.currentKitchenId && lastSelectedKitchen) {
      // 首次加载，设置当前厨房ID
      this.setData({
        currentKitchenId: lastSelectedKitchen
      });
    }
  },

  /**
   * 主题变更回调函数
   */
  onThemeChanged(settings: BackgroundSettings) {
    console.log('下单页面接收到主题变更:', settings)
    if (settings && settings.shopBg) {
      this.setData({
        shopBg: settings.shopBg
      })
    }
  },

  /**
   * 获取背景设置
   */
  getBackgroundSettings() {
    // 检查是否是分享厨房下单模式
    const isSharedKitchenOrder = wx.getStorageSync('is_shared_kitchen_order');
    
    if (isSharedKitchenOrder) {
      // 分享厨房模式下，使用默认背景，等待从餐厅信息中获取真实背景
      console.log('分享厨房下单模式，等待获取分享厨房背景');
      this.setData({
        shopBg: DEFAULT_IMAGES.KITCHEN_BACKGROUND
      });
      return;
    }

    // 普通模式下，优先检查是否有当前厨房的背景图
    const currentKitchenBg = wx.getStorageSync('current_kitchen_bg') || '';
    if (currentKitchenBg) {
      console.log('下单页面使用厨房背景:', currentKitchenBg);
      this.setData({
        shopBg: currentKitchenBg
      });
      return;
    }

    // 其次尝试从全局数据获取背景设置
    const globalSettings = app.globalData.backgroundSettings
    if (globalSettings && globalSettings.shopBg) {
      this.setData({
        shopBg: globalSettings.shopBg
      })
      return
    }

    // 如果全局数据中没有，则从本地存储获取
    const localShopBg = wx.getStorageSync('shopBg')
    if (localShopBg) {
      this.setData({
        shopBg: localShopBg
      })
    } else {
      // 最后使用默认背景
      this.setData({
        shopBg: DEFAULT_IMAGES.KITCHEN_BACKGROUND
      })
    }
  },

  /**
   * 获取购物车列表
   */
  async fetchCartList() {
    try {
      // 传入当前厨房ID
      const result = await getCartList(this.data.currentKitchenId)

      if (result.error === 0 && result.body) {
        // 处理购物车数据格式 - 参考restaurant.ts的处理方式
        let cartData = result.body;

        // 如果返回的是 {cartItems: [...]} 格式，提取cartItems数组
        if (cartData && Array.isArray(cartData.cartItems)) {
          const cartItems = cartData.cartItems;

          // 计算总价和总数量
          const totalPrice = cartItems.reduce((total: number, item: any) => total + (item.dish && item.dish.price ? item.dish.price : 0) * item.count, 0);
          const totalItems = cartItems.reduce((total: number, item: any) => total + item.count, 0);

          // 转换为前端需要的格式
          const cartList = cartItems.map((item: any) => ({
            id: item.dishId || (item.dish && item.dish.id ? item.dish.id : ''),
            name: item.dish && item.dish.name ? item.dish.name : '',
            image: item.dish && item.dish.image ? item.dish.image : '',
            price: item.dish && item.dish.price ? item.dish.price : 0,
            count: item.count,
            tags: item.dish && item.dish.tags ? item.dish.tags : []
          }));

          // 设置订单商品和总价
          this.setData({
            orderItems: cartList,
            totalPrice,
            totalItems
          })
        } else if (Array.isArray(cartData)) {
          // 如果直接返回了数组，也进行格式转换
          const cartItems = cartData;

          // 计算总价和总数量
          const totalPrice = cartItems.reduce((total: number, item: any) => total + (item.dish && item.dish.price ? item.dish.price : 0) * item.count, 0);
          const totalItems = cartItems.reduce((total: number, item: any) => total + item.count, 0);

          // 转换为前端需要的格式
          const cartList = cartItems.map((item: any) => ({
            id: item.dishId || (item.dish && item.dish.id ? item.dish.id : ''),
            name: item.dish && item.dish.name ? item.dish.name : '',
            image: item.dish && item.dish.image ? item.dish.image : '',
            price: item.dish && item.dish.price ? item.dish.price : 0,
            count: item.count,
            tags: item.dish && item.dish.tags ? item.dish.tags : []
          }));

          // 设置订单商品和总价
          this.setData({
            orderItems: cartList,
            totalPrice,
            totalItems
          })
        } else {
          // 兼容旧格式或空数据情况
          console.log('使用兼容格式处理购物车数据:', cartData);

          if (cartData && cartData.cartList && Array.isArray(cartData.cartList)) {
            const { cartList, totalPrice } = cartData

            // 计算总菜品数量（包括多份菜品）
            let totalItems = 0
            cartList.forEach((item: any) => {
              totalItems += item.count
            })

            // 设置订单商品和总价
            this.setData({
              orderItems: cartList,
              totalPrice,
              totalItems
            })
          } else {
            // 如果没有购物车数据，设置为空
            this.setData({
              orderItems: [],
              totalPrice: 0,
              totalItems: 0
            })
          }
        }
      } else {
        wx.showToast({
          title: result.message || '获取购物车失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取购物车失败', error)
      wx.showToast({
        title: '获取购物车失败',
        icon: 'none'
      })
    }
  },

  /**
   * 获取餐厅信息
   */
  async fetchRestaurantInfo() {
    try {
      // 传入当前厨房ID，使用getCurrentKitchenBaseInfo替代getRestaurantInfo
      const result = await getCurrentKitchenBaseInfo(this.data.currentKitchenId)

      if (result.error === 0 && result.body) {
        this.setData({
          restaurantInfo: result.body
        })

        // 在分享厨房模式下，如果获取到厨房背景图，更新背景
        const isSharedKitchenOrder = wx.getStorageSync('is_shared_kitchen_order');
        if (isSharedKitchenOrder && result.body.backgroundUrl) {
          console.log('分享厨房下单页面更新背景:', result.body.backgroundUrl);
          this.setData({
            shopBg: result.body.backgroundUrl
          });
        }
      }
    } catch (error) {
      console.error('获取餐厅信息失败', error)
    }
  },

  /**
   * 获取桌号列表
   */
  async fetchTableList() {
    try {
      if (!this.data.currentKitchenId) {
        console.log('当前没有选择厨房，无法获取桌号');
        return;
      }

      const result = await getTableList(this.data.currentKitchenId);

      if (result.error === 0 && result.body) {
        // 处理桌号数据格式 - 参考kitchen-manage.ts的处理方式
        let tableList = [];
        if (result.body && result.body.tables) {
          // 如果返回的是 { tables: [...] } 格式
          tableList = Array.isArray(result.body.tables) ? result.body.tables : [];
        } else if (Array.isArray(result.body)) {
          // 如果返回的直接是数组
          tableList = result.body;
        }

        console.log('解析后的桌号列表:', tableList);

        // 按照sort排序
        const sortedTables = tableList.sort((a: any, b: any) => a.sort - b.sort);

        this.setData({
          tables: sortedTables,
          // 如果有桌号数据且当前选中的索引超出范围，重置为0
          tableIndex: sortedTables.length > 0 && this.data.tableIndex >= sortedTables.length ? 0 : this.data.tableIndex
        });
      } else {
        console.error('获取桌号列表失败', result.message);
        wx.showToast({
          title: result.message || '获取桌号列表失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取桌号列表失败', error);
      wx.showToast({
        title: '获取桌号列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取状态栏高度，适配不同设备
   */
  getStatusBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;

    // 获取右上角胶囊按钮信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算与胶囊按钮垂直居中的高度
    const buttonTop = menuButtonInfo.top;
    const buttonHeight = menuButtonInfo.height;
    const buttonCenter = buttonTop + buttonHeight / 2;

    // 设置页面顶部标题位置
    this.setData({
      statusBarHeight: buttonCenter - 12 // 减去一半的图标高度以垂直居中
    });
  },

  /**
   * 桌号选择变更
   */
  onTableChange(e: any) {
    this.setData({
      tableIndex: e.detail.value
    })
  },

  /**
   * 备注输入变更
   */
  onRemarkInput(e: any) {
    const value = e.detail.value;
    
    this.setData({
      remark: value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('订单备注包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  /**
   * 取消按钮点击
   */
  onCancel() {
    // 显示取消确认弹窗
    this.setData({
      showCancelDialog: true
    })
  },

  /**
   * 关闭取消弹窗
   */
  onCloseCancelDialog() {
    this.setData({
      showCancelDialog: false
    })
  },

  /**
   * 确认取消订单
   */
  onConfirmCancel() {
    this.setData({
      showCancelDialog: false
    })

    // 如果是分享厨房下单，恢复原来的厨房ID
    this.restoreOriginalKitchen();

    // 返回上一页
    wx.navigateBack()
  },

  /**
   * 确认下单按钮点击
   */
  onConfirmOrder() {
    if (this.data.orderItems.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      })
      return
    }

    // 显示确认弹窗
    this.setData({
      showConfirmDialog: true
    })
  },

  /**
   * 关闭确认弹窗
   */
  onCloseConfirmDialog() {
    this.setData({
      showConfirmDialog: false
    })
  },

  /**
   * 提交订单
   */
  async submitOrder() {
    try {
      // 备注敏感词检查
      if (this.data.remark && this.data.remark.trim()) {
        // 字数限制检查
        if (this.data.remark.trim().length > 200) {
          wx.showToast({
            title: '备注不能超过200个字符',
            icon: 'none'
          });
          return;
        }

        // 敏感词检查
        const remarkCheck = checkSensitiveWord(this.data.remark.trim(), 'content');
        if (remarkCheck.hasSensitiveWord) {
          wx.showModal({
            title: '内容审核',
            content: '订单备注包含不当内容，请重新输入',
            showCancel: false,
            confirmText: '我知道了',
            confirmColor: '#FF6B35'
          });
          return;
        }
      }

      // 获取桌号
      const selectedTable = this.data.tableIndex >= 0 ? this.data.tables[this.data.tableIndex] : null;
      const tableNo = selectedTable ? selectedTable.name : '未选择';

      // 构建订单数据
      const orderData = {
        tableNo,
        tableId: selectedTable ? selectedTable.id : '',
        remark: this.data.remark ? this.data.remark.trim() : '',
        kitchenId: this.data.currentKitchenId
      }

      const result = await submitOrder(orderData)

      if (result.error === 0 && result.body) {
        // 保存订单ID
        this.setData({
          orderId: result.body.orderId,
          showSuccessDialog: true,
          showConfirmDialog: false
        })
      } else {
        wx.showToast({
          title: result.message || '提交订单失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交订单失败', error)
      wx.showToast({
        title: '提交订单失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 恢复原来的厨房ID（分享厨房下单后）
   */
  restoreOriginalKitchen() {
    const isSharedKitchenOrder = wx.getStorageSync('is_shared_kitchen_order');
    if (isSharedKitchenOrder) {
      console.log('分享厨房下单完成，恢复原来的厨房ID');

      // 恢复原来的厨房ID
      const originalKitchenId = wx.getStorageSync('original_kitchen_id');
      if (originalKitchenId) {
        wx.setStorageSync('last_selected_kitchen', originalKitchenId);
      } else {
        // 如果没有原来的厨房ID，清除当前的
        wx.removeStorageSync('last_selected_kitchen');
      }

      // 清除临时标记
      wx.removeStorageSync('is_shared_kitchen_order');
      wx.removeStorageSync('original_kitchen_id');
    }
  },

  /**
   * 订单成功处理
   */
  onOrderSuccess() {
    this.setData({
      showSuccessDialog: false
    })

    // 如果是分享厨房下单，恢复原来的厨房ID
    this.restoreOriginalKitchen();

    // 返回到餐厅页面
    wx.navigateBack()
  },

  /**
   * 查看订单
   */
  onViewOrder() {
    this.setData({
      showSuccessDialog: false
    })

    // 如果是分享厨房下单，恢复原来的厨房ID
    this.restoreOriginalKitchen();

    // 如果有订单ID，使用redirectTo跳转到订单详情页，这样手势返回不会回到下单页面
    if (this.data.orderId) {
      wx.redirectTo({
        url: `/pages/order-detail/order-detail?orderId=${this.data.orderId}&fromSubmit=true`
      })
    } else {
      // 否则返回到餐厅页面
      wx.navigateBack()
    }
  },

  /**
   * 用户点击右上角分享或分享给厨师按钮
   */
  onShareAppMessage() {
    const randomIndex = Math.floor(Math.random() * this.data.shareTexts.length);
    const shareText = this.data.shareTexts[randomIndex];

    // 获取分享图片
    const shareImage = this.data.orderItems && this.data.orderItems.length > 0
      ? this.data.orderItems[0].image
      : '../../static/images/share-default.jpg';

    // 设置要分享的内容
    const shareObj = {
      title: shareText,
      path: `/pages/order-detail/order-detail?orderId=${this.data.orderId}`,
      imageUrl: shareImage
    };

    return shareObj;
  },

  /**
   * 显示桌号选择器弹窗
   */
  showTablePicker() {
    this.setData({
      showTableDialog: true,
      tempTableIndex: this.data.tableIndex
    })
  },

  /**
   * 关闭桌号选择器弹窗
   */
  onCloseTableDialog() {
    this.setData({
      showTableDialog: false
    })
  },

  /**
   * 选择桌号
   */
  onSelectTable(e: any) {
    const index = e.currentTarget.dataset.index
    this.setData({
      tempTableIndex: index
    })
  },

  /**
   * 确认桌号选择
   */
  onConfirmTableSelection() {
    this.setData({
      tableIndex: this.data.tempTableIndex,
      showTableDialog: false
    })
  },
})