"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 分类服务
 * 处理分类相关的业务逻辑
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
/**
 * 获取分类列表
 * @param kitchenId 厨房ID
 * @returns 分类列表
 */
const getCategoryList = (kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    const categories = yield models_1.Category.findAll({
        where: { kitchen_id: kitchenId },
        order: [['sort', 'ASC']],
    });
    return categories.map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        sort: category.sort,
    }));
});
/**
 * 添加分类
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param name 分类名称
 * @param icon 分类图标
 * @returns 添加结果
 */
const addCategory = (userId, kitchenId, name, icon) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查用户是否有权限添加分类
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限添加分类');
    }
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 检查分类数量限制
    if (kitchen.category_count >= kitchen.category_limit) {
        throw new error_1.BusinessError(`分类数量已达上限（${kitchen.category_limit}个），请先升级厨房`);
    }
    // 检查分类名称是否已存在
    const existingCategory = yield models_1.Category.findOne({
        where: {
            kitchen_id: kitchenId,
            name,
        },
    });
    if (existingCategory) {
        throw new error_1.BusinessError('分类名称已存在');
    }
    // 获取最大排序值
    let maxSort = 0;
    try {
        const result = yield models_1.Category.max('sort', {
            where: { kitchen_id: kitchenId },
        });
        // 确保maxSort是数字类型
        if (result !== null && result !== undefined && typeof result === 'number') {
            maxSort = result;
        }
    }
    catch (error) {
        logger_1.default.error('获取最大排序值失败:', error);
        // 使用默认值0
    }
    // 创建分类
    const category = yield models_1.Category.create({
        kitchen_id: kitchenId,
        name,
        icon: icon || '',
        sort: maxSort + 1,
    });
    // 更新厨房分类数量
    yield kitchen.update({
        category_count: kitchen.category_count + 1,
    });
    return {
        id: category.id,
        name: category.name,
        icon: category.icon,
        sort: category.sort,
    };
});
/**
 * 更新分类
 * @param userId 用户ID
 * @param categoryId 分类ID
 * @param kitchenId 厨房ID
 * @param name 分类名称
 * @param icon 分类图标
 */
const updateCategory = (userId, categoryId, kitchenId, name, icon) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查用户是否有权限更新分类
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限更新分类');
    }
    // 检查分类是否存在
    const category = yield models_1.Category.findOne({
        where: {
            id: categoryId,
            kitchen_id: kitchenId,
        },
    });
    if (!category) {
        throw new error_1.BusinessError('分类不存在');
    }
    // 检查分类名称是否已存在（排除当前分类）
    if (name !== category.name) {
        const existingCategory = yield models_1.Category.findOne({
            where: {
                kitchen_id: kitchenId,
                name,
                id: { [sequelize_1.Op.ne]: categoryId },
            },
        });
        if (existingCategory) {
            throw new error_1.BusinessError('分类名称已存在');
        }
    }
    // 如果更新图标，需要删除旧的图标文件
    if (icon !== undefined && category.icon && category.icon !== icon) {
        try {
            // 提取旧图标的文件名
            const oldIconUrl = category.icon;
            const oldFilename = oldIconUrl.split('/').pop();
            if (oldFilename && !oldIconUrl.includes('default') && oldIconUrl.startsWith('http')) {
                // 导入deleteFile函数
                const { deleteFile } = require('../middlewares/upload');
                deleteFile(oldFilename, 'category');
                logger_1.default.info(`已删除旧分类图标: ${oldFilename}`);
            }
        }
        catch (error) {
            logger_1.default.error('删除旧分类图标失败:', error);
            // 不抛出错误，继续更新操作
        }
    }
    // 更新分类
    const updateData = { name };
    if (icon !== undefined) {
        updateData.icon = icon;
    }
    yield category.update(updateData);
});
/**
 * 删除分类
 * @param userId 用户ID
 * @param categoryId 分类ID
 * @param kitchenId 厨房ID
 */
const deleteCategory = (userId, categoryId, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查用户是否有权限删除分类
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限删除分类');
    }
    // 检查分类是否存在
    const category = yield models_1.Category.findOne({
        where: {
            id: categoryId,
            kitchen_id: kitchenId,
        },
    });
    if (!category) {
        throw new error_1.BusinessError('分类不存在');
    }
    // 检查分类下是否有菜品
    const dishCount = yield models_1.Dish.count({
        where: {
            category_id: categoryId,
            kitchen_id: kitchenId,
        },
    });
    if (dishCount > 0) {
        throw new error_1.BusinessError('分类下存在菜品，无法删除');
    }
    // 删除分类
    yield category.destroy();
    // 更新厨房分类数量
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (kitchen && kitchen.category_count > 0) {
        yield kitchen.update({
            category_count: kitchen.category_count - 1,
        });
    }
});
/**
 * 更新分类排序
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param categories 分类排序数据
 */
const updateCategorySort = (userId, kitchenId, categories) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查用户是否有权限更新分类排序
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限更新分类排序');
    }
    // 更新分类排序
    const transaction = yield database_1.default.transaction();
    try {
        for (let i = 0; i < categories.length; i++) {
            yield models_1.Category.update({ sort: i }, {
                where: {
                    id: categories[i].id,
                    kitchen_id: kitchenId,
                },
                transaction,
            });
        }
        yield transaction.commit();
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
exports.default = {
    getCategoryList,
    addCategory,
    updateCategory,
    deleteCategory,
    updateCategorySort,
};
//# sourceMappingURL=categoryService.js.map