"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const tableService_1 = __importDefault(require("../services/tableService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取桌号列表
 * @route GET /api/table/list
 */
const getTableList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // 支持匿名访问，userId可能为undefined
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { kitchenId } = req.query;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const tables = yield tableService_1.default.getTableList(userId, kitchenId);
        (0, response_1.success)(res, { tables });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加桌号
 * @route POST /api/table/add
 */
const addTable = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, name } = req.body;
        if (!kitchenId || !name) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield tableService_1.default.addTable(userId, kitchenId, name);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新桌号
 * @route POST /api/table/update
 */
const updateTable = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId, name } = req.body;
        if (!id || !kitchenId || !name) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield tableService_1.default.updateTable(userId, parseInt(id), kitchenId, name);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 删除桌号
 * @route POST /api/table/delete
 */
const deleteTable = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        if (!id || !kitchenId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield tableService_1.default.deleteTable(userId, parseInt(id), kitchenId);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 桌号排序
 * @route POST /api/table/sort
 */
const sortTables = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, tables } = req.body;
        if (!kitchenId || !tables || !Array.isArray(tables)) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield tableService_1.default.sortTables(userId, kitchenId, tables);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getTableList,
    addTable,
    updateTable,
    deleteTable,
    sortTables
};
//# sourceMappingURL=tableController.js.map