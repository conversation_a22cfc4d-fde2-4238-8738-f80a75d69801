/**
 * 图片压缩工具
 * 处理图片压缩和文件命名
 */
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import logger from './logger';
import { BusinessError } from '../middlewares/error';

// 图片压缩配置
const COMPRESSION_CONFIG = {
  // 用户头像和背景图片
  avatar: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 70,
    format: 'jpeg' as const
  },
  background: {
    maxWidth: 1000,
    maxHeight: 600,
    quality: 70,
    format: 'jpeg' as const
  },
  // 厨房头像
  kitchen: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 70,
    format: 'jpeg' as const
  },
  // 菜品图片
  dish: {
    maxWidth: 800,
    maxHeight: 800,
    quality: 75,
    format: 'jpeg' as const
  },
  // 分类图标
  category: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 85,
    format: 'jpeg' as const
  },
  // 二维码
  qrcode: {
    maxWidth: 430,
    maxHeight: 430,
    quality: 90, // 保留用于配置兼容性，但PNG格式不使用此参数
    format: 'png' as const
  }
};

/**
 * 生成文件名计数器
 */
class FileNameCounter {
  /**
   * 生成唯一的6位数字字母组合
   * @param prefix 前缀
   * @returns 6位数字字母组合
   */
  static getNext(prefix: string): string {
    // 使用时间戳 + 随机数确保唯一性，解决服务器重启后计数器重置的缓存问题
    const timestamp = Date.now().toString(36); // 转换为36进制减少长度
    const random = Math.random().toString(36).substr(2, 4); // 4位随机字符
    const combined = (timestamp + random).toUpperCase();
    
    // 取最后6位，确保长度一致
    return combined.substr(-6).padStart(6, '0');
  }
  
  /**
   * 生成6位数字字母组合（废弃的方法，保留兼容性）
   * @param num 数字
   * @returns 6位编码
   */
  private static generateCode(num: number): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    let value = num;
    
    for (let i = 0; i < 6; i++) {
      result = chars[value % chars.length] + result;
      value = Math.floor(value / chars.length);
    }
    
    return result.padStart(6, '0');
  }
}

/**
 * 生成新的文件名
 * @param userId 用户ID
 * @param type 文件类型
 * @param kitchenId 厨房ID（可选）
 * @param extension 文件扩展名
 * @returns 新文件名
 */
export const generateFileName = (
  userId: number, 
  type: 'avatar' | 'background' | 'kitchen' | 'dish' | 'category',
  kitchenId?: string,
  extension: string = '.jpg'
): string => {
  const timestamp = Date.now();
  
  switch (type) {
    case 'avatar':
    case 'background':
      // 用户头像和背景图片：用户id+6位数字加字母依次递增
      const userCounter = FileNameCounter.getNext(`user_${userId}_${type}`);
      return `${userId}_${userCounter}${extension}`;
      
    case 'kitchen':
      // 厨房头像：用户id+厨房id+6位数字加字母依次递增
      if (!kitchenId) {
        throw new BusinessError('厨房头像需要提供厨房ID');
      }
      const kitchenCounter = FileNameCounter.getNext(`user_${userId}_kitchen_${kitchenId}`);
      return `${userId}_${kitchenId}_${kitchenCounter}${extension}`;
      
    case 'dish':
      // 菜品图片：用户id+厨房id+5位字符+6位数字加字母依次递增
      if (!kitchenId) {
        throw new BusinessError('菜品图片需要提供厨房ID');
      }
      const dishPrefix = 'DISH' + String.fromCharCode(65 + (timestamp % 26)); // DISH + 随机字母
      const dishCounter = FileNameCounter.getNext(`user_${userId}_kitchen_${kitchenId}_dish`);
      return `${userId}_${kitchenId}_${dishPrefix}_${dishCounter}${extension}`;
      
    case 'category':
      // 分类图标：用户id+厨房id+6位数字加字母依次递增
      if (!kitchenId) {
        throw new BusinessError('分类图标需要提供厨房ID');
      }
      const categoryCounter = FileNameCounter.getNext(`user_${userId}_kitchen_${kitchenId}_category`);
      return `${userId}_${kitchenId}_${categoryCounter}${extension}`;
      
    default:
      throw new BusinessError('不支持的文件类型');
  }
};

/**
 * 压缩图片
 * @param inputPath 输入文件路径
 * @param outputPath 输出文件路径
 * @param type 图片类型
 * @returns Promise<void>
 */
export const compressImage = async (
  inputPath: string,
  outputPath: string,
  type: keyof typeof COMPRESSION_CONFIG
): Promise<void> => {
  try {
    const config = COMPRESSION_CONFIG[type];
    
    if (!config) {
      throw new BusinessError(`不支持的图片类型: ${type}`);
    }
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 使用sharp进行图片压缩
    const sharpInstance = sharp(inputPath)
      .resize(config.maxWidth, config.maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      });

    // 根据格式选择输出格式
    if (config.format === 'png') {
      await sharpInstance
        .png({
          compressionLevel: 9,
          adaptiveFiltering: false,
          force: true
        })
        .toFile(outputPath);
    } else {
      await sharpInstance
        .jpeg({
          quality: config.quality,
          progressive: true
        })
        .toFile(outputPath);
    }
    
    logger.info(`图片压缩成功: ${inputPath} -> ${outputPath}`, {
      type,
      config
    });
    
  } catch (error) {
    logger.error(`图片压缩失败: ${error}`, {
      inputPath,
      outputPath,
      type
    });
    throw new BusinessError(`图片压缩失败: ${error}`);
  }
};

/**
 * 获取图片信息
 * @param filePath 文件路径
 * @returns 图片信息
 */
export const getImageInfo = async (filePath: string) => {
  try {
    const metadata = await sharp(filePath).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: fs.statSync(filePath).size
    };
  } catch (error) {
    logger.error(`获取图片信息失败: ${error}`);
    throw new BusinessError(`获取图片信息失败: ${error}`);
  }
};

export default {
  generateFileName,
  compressImage,
  getImageInfo,
  FileNameCounter
}; 