{"version": 3, "file": "urlManager.js", "sourceRoot": "", "sources": ["../../src/utils/urlManager.ts"], "names": [], "mappings": ";;;;;;AAAA;;;GAGG;AACH,8DAAsC;AAEtC;;;;GAIG;AACI,MAAM,qBAAqB,GAAG,CAAC,OAAe,EAAU,EAAE;IAC/D,IAAI,CAAC,OAAO;QAAE,OAAO,EAAE,CAAC;IAExB,iBAAiB;IACjB,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,gBAAgB;IAChB,MAAM,QAAQ,GAAG;QACf,gBAAM,CAAC,MAAM,CAAC,OAAO;QACrB,gBAAM,CAAC,MAAM,CAAC,UAAU;QACxB,6BAA6B;QAC7B,4BAA4B;KAC7B,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,4BAA4B;IAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAClD,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED,gBAAgB;IAChB,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AA9BW,QAAA,qBAAqB,yBA8BhC;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAAC,YAAoB,EAAU,EAAE;IAC/D,IAAI,CAAC,YAAY;QAAE,OAAO,EAAE,CAAC;IAE7B,kBAAkB;IAClB,IAAI,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9E,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,WAAW;IACX,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;IAE9E,OAAO,GAAG,gBAAM,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,EAAE,CAAC;AAC3C,CAAC,CAAC;AAZW,QAAA,gBAAgB,oBAY3B;AAEF;;;;;GAKG;AACI,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAE,SAAiB,MAAM,EAAU,EAAE;IACnF,OAAO,YAAY,MAAM,IAAI,QAAQ,EAAE,CAAC;AAC1C,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF;;;;;GAKG;AACI,MAAM,UAAU,GAAG,CAAC,QAAgB,EAAE,SAAiB,MAAM,EAAU,EAAE;IAC9E,MAAM,YAAY,GAAG,IAAA,uBAAe,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACvD,OAAO,IAAA,wBAAgB,EAAC,YAAY,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,UAAU,cAGrB;AAEF;;;;GAIG;AACI,MAAM,2BAA2B,GAAG,CAAC,IAAsE,EAAU,EAAE;IAC5H,MAAM,QAAQ,GAAG;QACf,aAAa,EAAE,2BAA2B;QAC1C,gBAAgB,EAAE,2BAA2B;QAC7C,oBAAoB,EAAE,0BAA0B;QAChD,MAAM,EAAE,2BAA2B;KACpC,CAAC;IAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC,CAAC;AATW,QAAA,2BAA2B,+BAStC;AAEF;;;;GAIG;AACI,MAAM,kBAAkB,GAAG,CAAC,IAAsE,EAAU,EAAE;IACnH,MAAM,YAAY,GAAG,IAAA,mCAA2B,EAAC,IAAI,CAAC,CAAC;IACvD,OAAO,IAAA,wBAAgB,EAAC,YAAY,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,kBAAkB,sBAG7B;AAEF;;;GAGG;AACI,MAAM,+BAA+B,GAAG,GAAW,EAAE;IAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,OAAO,uBAAuB,WAAW,MAAM,CAAC;AAClD,CAAC,CAAC;AAHW,QAAA,+BAA+B,mCAG1C;AAEF;;;GAGG;AACI,MAAM,sBAAsB,GAAG,GAAW,EAAE;IACjD,MAAM,YAAY,GAAG,IAAA,uCAA+B,GAAE,CAAC;IACvD,OAAO,IAAA,wBAAgB,EAAC,YAAY,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,sBAAsB,0BAGjC;AAEF;;;GAGG;AACI,MAAM,kCAAkC,GAAG,GAAW,EAAE;IAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,OAAO,uBAAuB,WAAW,MAAM,CAAC;AAClD,CAAC,CAAC;AAHW,QAAA,kCAAkC,sCAG7C;AAEF;;;GAGG;AACI,MAAM,yBAAyB,GAAG,GAAW,EAAE;IACpD,MAAM,YAAY,GAAG,IAAA,0CAAkC,GAAE,CAAC;IAC1D,OAAO,IAAA,wBAAgB,EAAC,YAAY,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,yBAAyB,6BAGpC;AAEF;;;GAGG;AACI,MAAM,sCAAsC,GAAG,GAAW,EAAE;IACjE,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1D,OAAO,sBAAsB,eAAe,MAAM,CAAC;AACrD,CAAC,CAAC;AAHW,QAAA,sCAAsC,0CAGjD;AAEF;;;GAGG;AACI,MAAM,6BAA6B,GAAG,GAAW,EAAE;IACxD,MAAM,YAAY,GAAG,IAAA,8CAAsC,GAAE,CAAC;IAC9D,OAAO,IAAA,wBAAgB,EAAC,YAAY,CAAC,CAAC;AACxC,CAAC,CAAC;AAHW,QAAA,6BAA6B,iCAGxC;AAEF;;;;;GAKG;AACI,MAAM,eAAe,GAAG,CAAC,QAAuB,EAAE,WAA6E,EAAU,EAAE;IAChJ,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;QACxC,OAAO,IAAA,0BAAkB,EAAC,WAAW,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,IAAA,wBAAgB,EAAC,QAAQ,CAAC,CAAC;AACpC,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B;AAEF,kBAAe;IACb,qBAAqB,EAArB,6BAAqB;IACrB,gBAAgB,EAAhB,wBAAgB;IAChB,eAAe,EAAf,uBAAe;IACf,UAAU,EAAV,kBAAU;IACV,2BAA2B,EAA3B,mCAA2B;IAC3B,kBAAkB,EAAlB,0BAAkB;IAClB,+BAA+B,EAA/B,uCAA+B;IAC/B,sBAAsB,EAAtB,8BAAsB;IACtB,kCAAkC,EAAlC,0CAAkC;IAClC,yBAAyB,EAAzB,iCAAyB;IACzB,sCAAsC,EAAtC,8CAAsC;IACtC,6BAA6B,EAA7B,qCAA6B;IAC7B,eAAe,EAAf,uBAAe;CAChB,CAAC"}