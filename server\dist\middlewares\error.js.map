{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/middlewares/error.ts"], "names": [], "mappings": ";;;;;;AAKA,gDAAwD;AACxD,6DAAqC;AAErC;;;;;GAKG;AACI,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;IACnE,gBAAM,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;IACvD,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AAChF,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAEF;;;;;;;GAOG;AACI,MAAM,YAAY,GAAG,CAAC,GAAQ,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,oBAAoB;IACpB,gBAAM,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;QACvD,KAAK,EAAE,GAAG,CAAC,OAAO;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;KACjB,CAAC,CAAC;IAEH,gBAAgB;IAChB,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,SAAS;QACT,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAChF,UAAU;QACV,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;IAClE,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QACzC,OAAO;QACP,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,IAAI,WAAW,CAAC,CAAC;IACxE,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QACxC,UAAU;QACV,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC;IACvE,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,0BAA0B,IAAI,GAAG,CAAC,IAAI,KAAK,gCAAgC,EAAE,CAAC;QACpG,gBAAgB;QAChB,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,CAAM,EAAE,EAAE;YAC9D,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;YACxB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACzE,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QACxC,SAAS;QACT,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,uBAAY,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;IAC1E,CAAC;IAED,UAAU;IACV,OAAO,IAAA,gBAAK,EAAC,GAAG,EAAE,uBAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AAC1D,CAAC,CAAC;AArCW,QAAA,YAAY,gBAqCvB;AAEF;;;GAGG;AACH,MAAa,aAAc,SAAQ,KAAK;IAGtC,YAAY,OAAe,EAAE,OAAe,uBAAY,CAAC,cAAc;QACrE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AARD,sCAQC;AAED;;;GAGG;AACH,MAAa,iBAAkB,SAAQ,KAAK;IAC1C,YAAY,UAAkB,iBAAiB;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAClC,CAAC;CACF;AALD,8CAKC;AAED;;;GAGG;AACH,MAAa,cAAe,SAAQ,KAAK;IACvC,YAAY,UAAkB,WAAW;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,wCAKC;AAED;;;GAGG;AACH,MAAa,aAAc,SAAQ,KAAK;IACtC,YAAY,UAAkB,UAAU;QACtC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AALD,sCAKC;AAED,kBAAe;IACb,eAAe,EAAf,uBAAe;IACf,YAAY,EAAZ,oBAAY;IACZ,aAAa;IACb,iBAAiB;IACjB,cAAc;IACd,aAAa;CACd,CAAC"}