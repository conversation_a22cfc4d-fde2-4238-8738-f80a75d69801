import { request } from '../utils/request'

// 厨房信息接口
export interface KitchenInfo {
  id: string;         // 厨房ID
  name: string;       // 厨房名称
  level: number;      // 厨房等级
  owner: string;      // 厨房拥有者ID
  isOwner: boolean;   // 是否是拥有者
  kitchenCount: number; // 厨房数量
  kitchenLimit: number; // 厨房数量上限
  categoryCount: number; // 分类数量
  categoryLimit: number; // 分类数量上限
  dishCount: number;    // 菜品数量
  dishLimit: number;    // 菜品数量上限
  memberCount: number;  // 成员数量
  createdAt: string;    // 创建时间
  notice?: string;      // 厨房公告
  avatarUrl?: string;   // 厨房头像
  backgroundUrl?: string; // 厨房背景图
}

// 桌号信息接口
export interface TableInfo {
  id: string;    // 桌号ID
  name: string;  // 桌号名称
  sort: number;  // 排序顺序
}

// 获取当前用户厨房列表
export const getKitchenList = () => {
  return request({
    url: '/api/kitchen/list',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 获取厨房详细信息
export const getKitchenInfo = (kitchenId?: string) => {
  return request({
    url: '/api/kitchen/info',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  })
}

// 获取当前餐厅(厨房)基本信息，复用餐厅信息API
export const getCurrentKitchenBaseInfo = (kitchenId?: string) => {
  return request({
    url: '/api/kitchen/baseInfo',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  });
}

// 创建新厨房
export const createKitchen = (params: {
  name: string,       // 厨房名称
  notice?: string,    // 厨房公告
  avatarUrl?: string  // 厨房头像
}) => {
  return request({
    url: '/api/kitchen/create',
    method: 'POST',
    data: params,
    showLoading: false
  })
}

/**
 * 更新厨房信息
 * @param kitchenId 厨房ID
 * @param params 要更新的厨房信息
 */
export const updateKitchenInfo = (kitchenId: string, params: {
  name?: string,      // 厨房名称
  notice?: string,    // 厨房公告
  avatarUrl?: string, // 厨房头像
  backgroundUrl?: string // 厨房背景图
}) => {
  return request({
    url: '/api/kitchen/update',
    method: 'POST',
    data: { kitchenId, ...params },
    showLoading: false
  })
}

// 加入厨房
export const joinKitchen = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/join',
    method: 'POST',
    data: { kitchenId },
    showLoading: false
  })
}

// 退出厨房
export const leaveKitchen = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/leave',
    method: 'POST',
    data: { kitchenId },
    showLoading: false
  })
}

// 解散厨房
export const dismissKitchen = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/dismiss',
    method: 'POST',
    data: { kitchenId },
    showLoading: false
  })
}

// 获取厨房成员列表
export const getKitchenMembers = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/members',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  })
}

// 升级厨房
export const upgradeKitchen = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/upgrade',
    method: 'POST',
    data: { kitchenId },
    showLoading: false
  })
}

// 获取用户创建的厨房列表
export const getOwnedKitchenList = () => {
  return request({
    url: '/api/kitchen/owned',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 获取用户加入的厨房列表
export const getJoinedKitchenList = () => {
  return request({
    url: '/api/kitchen/joined',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

/**
 * 通过ID查找厨房
 * @param kitchenId 厨房ID
 */
export const findKitchenById = async (kitchenId: string) => {
  return request({
    url: '/api/kitchen/findById',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  });
}

/**
 * 获取厨房分类列表
 * @param kitchenId 厨房ID
 */
export const getKitchenCategories = async (kitchenId: string) => {
  // 添加参数验证
  if (!kitchenId) {
    return {
      error: 400,
      body: null,
      message: '厨房ID不能为空'
    };
  }

  const result = await request({
    url: '/api/kitchen/categories',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  });

  console.log('获取厨房分类响应:', result);
  return result;
}

/**
 * 获取厨房分类下的菜品
 * @param kitchenId 厨房ID
 * @param categoryId 分类ID
 */
export const getKitchenCategoryDishes = async (kitchenId: string, categoryId: string) => {
  if (!kitchenId || !categoryId) {
    return {
      error: 400,
      body: null,
      message: '厨房ID或分类ID不能为空'
    };
  }

  const result = await request({
    url: '/api/kitchen/categoryDishes',
    method: 'GET',
    data: { kitchenId, categoryId },
    showLoading: false
  });

  // 打印响应数据，用于调试
  console.log('获取分类菜品响应:', result);

  // 适配不同的API响应格式
  if (result.error === 0 && result.body) {
    // 检查body是否为数组，如果不是，尝试从body中获取dishes属性
    if (!Array.isArray(result.body) && result.body.dishes) {
      // 将菜品数据提取出来
      result.body = result.body.dishes;
    }
  }

  return result;
}

/**
 * 克隆菜品到当前厨房
 * @param sourceKitchenId 源厨房ID
 * @param targetKitchenId 目标厨房ID
 * @param targetCategoryId 目标分类ID
 * @param dishIds 要克隆的菜品ID数组
 */
export const cloneDishes = async (sourceKitchenId: string, targetKitchenId: string, targetCategoryId: string, dishIds: string[]) => {
  return request({
    url: '/api/kitchen/cloneDishes',
    method: 'POST',
    data: { sourceKitchenId, targetKitchenId, targetCategoryId, dishIds }
  });
}

// 更新成员权限
export const updateMemberPermission = async (kitchenId: string, memberId: string, hasPermission: boolean) => {
  // 将布尔值转换为角色字符串
  const role = hasPermission ? 'editor' : 'member';

  return request({
    url: '/api/kitchen/updateMemberPermission',
    method: 'POST',
    data: { kitchenId, memberId, role }
  });
}

// 移除成员
export const removeMember = async (kitchenId: string, memberId: string) => {
  return request({
    url: '/api/kitchen/removeMember',
    method: 'POST',
    data: { kitchenId, memberId }
  });
}

// 以下是桌号相关的API

/**
 * 获取厨房桌号列表
 * @param kitchenId 厨房ID
 */
export const getTableList = (kitchenId: string) => {
  return request({
    url: '/api/kitchen/table/list',
    method: 'GET',
    data: { kitchenId },
    showLoading: false
  })
}

/**
 * 添加桌号
 * @param kitchenId 厨房ID
 * @param table 桌号信息
 */
export const addTable = (kitchenId: string, table: { name: string, sort: number }) => {
  return request({
    url: '/api/kitchen/table/add',
    method: 'POST',
    data: { kitchenId, ...table }
  })
}

/**
 * 更新桌号
 * @param kitchenId 厨房ID
 * @param tableId 桌号ID
 * @param table 桌号信息
 */
export const updateTable = (kitchenId: string, tableId: string, table: { name: string, sort: number }) => {
  return request({
    url: '/api/kitchen/table/update',
    method: 'POST',
    data: { kitchenId, tableId, ...table }
  })
}

/**
 * 删除桌号
 * @param kitchenId 厨房ID
 * @param tableId 桌号ID
 */
export const deleteTable = (kitchenId: string, tableId: string) => {
  return request({
    url: '/api/kitchen/table/delete',
    method: 'POST',
    data: { kitchenId, id: tableId }
  })
}

/**
 * 排序桌号
 * @param kitchenId 厨房ID
 * @param tables 排序后的桌号列表
 */
export const sortTables = (kitchenId: string, tables: TableInfo[]) => {
  return request({
    url: '/api/kitchen/table/sort',
    method: 'POST',
    data: { kitchenId, tables }
  })
}

/**
 * 获取厨房二维码
 * @param kitchenId 厨房ID
 * @param refresh 是否刷新二维码
 * @param showLoading 是否显示loading动画
 * @returns 厨房二维码信息
 */
export const getKitchenQrCode = async (kitchenId: string, refresh = false, showLoading = true) => {
  return request({
    url: '/api/kitchen/qrcode',
    method: 'GET',
    data: {
      kitchenId,
      refresh: refresh ? 'true' : 'false',
      compress: 'true' // 启用压缩
    },
    showLoading
  });
}

/**
 * 搜索厨房信息（用于克隆）
 * @param kitchenId 要搜索的厨房ID
 */
export const searchKitchen = async (kitchenId: string) => {
  return request({
    url: '/api/kitchen/search',
    method: 'GET',
    data: { kitchenId }
  });
}

// 订阅厨房会员
// ... existing code ...