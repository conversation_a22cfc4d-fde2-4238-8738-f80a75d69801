.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f8f8f8;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* Tab样式 */
.order-tabs {
  position: fixed;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #fff;
  z-index: 99;
  height: 50px;
  padding: 0 30rpx;
  box-sizing: border-box;
  border-bottom: 1px solid #f2f2f2;
}

.tab {
  position: relative;
  padding: 12rpx 0;
  font-size: 30rpx;
  color: #999;
  font-weight: 400;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.tab.active {
  color: #333;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  background-color: #FFD082;
  border-radius: 3rpx;
}

/* 订单内容区域 */
.order-content {
  flex: 1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  padding-top: calc(50px + 40px); /* 为Tab和状态栏预留空间 */
  margin-top: 0px; /* 为导航栏预留空间 */
}

/* 状态筛选器 */
.status-tabs {
  width: 100%;
  overflow-x: auto;
  background-color: transparent;
  white-space: nowrap;
  height: 40px;
  box-sizing: border-box;
  border-bottom: none;
  margin-bottom: 12rpx;
  position: fixed;
  z-index: 9;
}

.status-tabs-inner {
  display: inline-flex;
  align-items: center;
  height: 100%;
  padding: 0 24rpx;
}

.status-tabs::-webkit-scrollbar {
  display: none;
}

.status-tab {
  padding: 0rpx 20rpx;
  margin: 0 16rpx;
  font-size: 26rpx;
  color: #666;
  display: inline-block;
  transition: all 0.3s ease;
  line-height: 28px;
  border: 1px solid #E0E0E0;
  border-radius: 24rpx;
  background-color: #fff;
  position: relative;
}

.status-tab.active {
  color: #FF6B35;
  font-weight: 500;
  position: relative;
  background-color: #FFF3E0;
  border-color: #FF6B35;
  transform: scale(1.05);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.2);
}

.status-tab.active::after {
  display: none;
}

/* 订单列表滚动区域 */
.order-list-scroll {
  flex: 1;
  width: 100%;
  height: calc(100vh - 170px); /* 计算正确的高度 */
  position: relative;
  padding-bottom: 20rpx;
}

/* 下拉刷新动画由scroll-view内置管理，无需自定义样式 */

/* 订单列表容器 */
.order-list-container {
  width: 100%;
  box-sizing: border-box;
  padding: 12rpx 24rpx 80rpx;
  min-height: calc(100vh - 180px);
  transition: opacity 0.3s ease;
}

/* 平滑更新状态时的微妙效果 */
.order-list-container.smooth-updating {
  opacity: 0.8;
}

/* 通用卡片样式 */
.order-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.order-card:active {
  transform: scale(0.98);
}

/* 平滑更新时的卡片效果 */
.order-card.updating {
  opacity: 0.6;
  transform: scale(0.98);
}

/* 厨房订单头部样式 */
.kitchen-order-header, .my-order-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
}

.avatar-area {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.shop-avatar, .my-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
}

.shop-info, .my-info {
  flex: 1;
  overflow: hidden;
}

.shop-name, .my-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.order-brief {
  font-size: 24rpx;
  color: #999;
}

/* 订单状态标签 */
.order-status-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  margin-left: 10rpx;
}

.order-status-tag.pending {
  background-color: #FFFBF0;
  color: #F76707;
}

.order-status-tag.accepted {
  background-color: #F0F9FF;
  color: #0969DA;
}

.order-status-tag.cooking {
  background-color: #FFF5F5;
  color: #D92D20;
}

.order-status-tag.completed {
  background-color: #F0FDF4;
  color: #16A34A;
}

.order-status-tag.canceled,
.order-status-tag.cancelled {
  background-color: #FAFAFA;
  color: #8B949E;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #f5f5f5;
  margin: 0;
}

/* 菜品横向滚动区域 */
.dishes-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
}

.dishes-container {
  display: inline-flex;
  padding: 0 20rpx;
}

.dish-item {
  width: 150rpx;
  margin-right: 20rpx;
  display: inline-block;
}

.dish-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.dish-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
  margin-right: 16rpx;
  max-width: 65%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 按钮样式优化 */
.btn-cancel, .btn-accept, .btn-complete, .btn-cooking-cancel {
  margin: 0 4rpx;
  padding: 0 14rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
  background-color: #fff;
  line-height: 1.5;
  height: 48rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  max-width: 120rpx;
  box-sizing: border-box;
}

button.btn-cancel::after,
button.btn-accept::after,
button.btn-complete::after,
button.btn-cooking-cancel::after {
  display: none;
}

.btn-cancel, .btn-cooking-cancel {
  border: 1px solid #ddd;
  color: #666666;
}

.btn-cooking-cancel {
  margin-right: 12rpx;
}

/* 为了解决当有两个按钮时的间距问题 */
.btn-cancel + .btn-accept {
  margin-left: 12rpx;
}

/* 增加取消烹饪按钮和完成按钮之间的边距 */
.btn-cooking-cancel + .btn-complete {
  margin-left: 12rpx;
}

.btn-accept {
  background-color: #FF6B35;
  color: #fff;
  border: none;
}

.btn-complete {
  background-color: #4CAF50;
  color: #fff;
  border: none;
}

/* 底部安全区域 */
.safe-bottom-area {
  height: 60rpx;
  width: 100%;
}

/* 加载更多样式已移除 */

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 动画效果 */
.slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部提示文字 */
.order-end-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 20rpx 0;
  width: 100%;
}

/* 空菜品提示 */
.empty-dish-hint {
  padding: 40rpx 20rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}