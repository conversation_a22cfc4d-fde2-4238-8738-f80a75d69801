import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import Layout from '@/components/layout/Layout'
import Login from '@/pages/login/Login'
import Dashboard from '@/pages/dashboard/Dashboard'
import UserList from '@/pages/user/UserList'
import KitchenList from '@/pages/kitchen/KitchenList'
import DishList from '@/pages/dish/DishList'
import OrderList from '@/pages/order/OrderList'
import MessageManage from '@/pages/content/MessageManage'
import ReportManage from '@/pages/content/ReportManage'
import CommentManage from '@/pages/content/CommentManage'
import FeedbackManage from '@/pages/feedback/index'
import DiscoverManage from '@/pages/discover/DiscoverManage'
import TaskManage from '@/pages/task/TaskManage'
import SearchManage from '@/pages/search/SearchManage'
import SystemManage from '@/pages/system/SystemSettings'

function App() {
  const { isAuthenticated } = useAuthStore()

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/*"
          element={
            isAuthenticated ? (
              <Layout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/users" element={<UserList />} />
                  <Route path="/kitchens" element={<KitchenList />} />
                  <Route path="/dishes" element={<DishList />} />
                  <Route path="/orders" element={<OrderList />} />
                  <Route path="/content/messages" element={<MessageManage />} />
                  <Route path="/content/reports" element={<ReportManage />} />
                  <Route path="/content/comments" element={<CommentManage />} />
                  <Route path="/content/feedback" element={<FeedbackManage />} />
                  <Route path="/discover" element={<DiscoverManage />} />
                  <Route path="/tasks" element={<TaskManage />} />
                  <Route path="/search" element={<SearchManage />} />
                  <Route path="/system" element={<SystemManage />} />
                </Routes>
              </Layout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
      </Routes>
    </Router>
  )
}

export default App 