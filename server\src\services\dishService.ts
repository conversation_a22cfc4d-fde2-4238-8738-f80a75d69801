/**
 * 菜品服务
 * 处理菜品相关的业务逻辑
 */
import { Op } from 'sequelize';
import sequelize from '../config/database';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { Dish, Category, DishImage, Ingredient, Nutrition, CookingStep, Like, Report, Comment, HotKeyword, Kitchen, KitchenMember, User } from '../models';
import userService from './userService';
import systemSettingService from './systemSettingService';
import { processImageUrl } from '../utils/helpers'; // 添加图片URL处理函数
import { convertToRelativePath } from '../utils/urlManager'; // 添加URL管理器

/**
 * 获取菜品分类列表
 * @param kitchenId 厨房ID
 * @returns 分类列表
 */
const getCategories = async (kitchenId: string): Promise<any[]> => {
  const categories = await Category.findAll({
    where: { kitchen_id: kitchenId },
    order: [['sort', 'ASC']],
  });

  // 获取每个分类下的菜品数量
  const categoryDishCounts = await Promise.all(
    categories.map(async (category) => {
      const count = await Dish.count({
        where: {
          category_id: category.id,
          kitchen_id: kitchenId,
          status: 'on', // 只统计上架的菜品
        },
      });
      return { categoryId: category.id, count };
    })
  );

  return categories.map(category => {
    // 查找对应分类的菜品数量
    const dishCountObj = categoryDishCounts.find(item => item.categoryId === category.id);
    const dishCount = dishCountObj ? dishCountObj.count : 0;
    
    return {
      id: category.id,
      name: category.name,
      icon: category.icon,
      sort: category.sort,
      dishCount: dishCount, // 添加菜品数量字段
    };
  });
};

/**
 * 获取菜品列表
 * @param kitchenId 厨房ID
 * @param categoryId 分类ID
 * @param userId 用户ID（可选）
 * @returns 菜品列表
 */
const getDishList = async (kitchenId: string, categoryId?: string, userId?: number): Promise<any[]> => {
  const where: any = { kitchen_id: kitchenId };

  if (categoryId) {
    where.category_id = categoryId;
  }

  const dishes = await Dish.findAll({
    where,
    order: [['sort', 'ASC']],
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name'],
      },
      {
        model: Ingredient,
        as: 'ingredients',
        attributes: ['id', 'name', 'amount'],
        order: [['sort', 'ASC']],
      },
    ],
  });

  // 如果提供了用户ID，查询用户是否点赞了这些菜品
  let userLikes: any[] = [];
  if (userId) {
    userLikes = await Like.findAll({
      where: {
        user_id: userId,
        dish_id: {
          [Op.in]: dishes.map(dish => dish.id),
        },
      },
    });
  }

  return dishes.map(dish => {
    const isLiked = userId ? userLikes.some(like => like.dish_id === dish.id) : false;

    // 从配料表生成标签，取前5个配料的名称
    const tags = dish.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];

    return {
      id: dish.id,
      name: dish.name,
      image: processImageUrl(dish.image, 'dish'), // 处理图片URL
      price: dish.price,
      originalPrice: dish.original_price,
      description: dish.description,
      tags: tags, // 使用配料表生成的标签而不是原有的tags字段
      sales: dish.sales,
      rating: dish.rating,
      status: dish.status,
      categoryId: dish.category_id,
      categoryName: dish.category?.name,
      isLiked,
    };
  });
};

/**
 * 获取菜品详情
 * @param dishId 菜品ID
 * @param userId 用户ID（可选）
 * @param kitchenId 厨房ID（可选，用于判断是否已添加到该厨房）
 * @returns 菜品详情
 */
const getDishDetail = async (dishId: number, userId?: number, kitchenId?: string): Promise<any> => {
  const dish = await Dish.findByPk(dishId, {
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name'],
      },
      {
        model: DishImage,
        as: 'images',
        attributes: ['id', 'url', 'sort'],
      },
      {
        model: Ingredient,
        as: 'ingredients',
        attributes: ['id', 'name', 'amount', 'sort'],
      },
      {
        model: Nutrition,
        as: 'nutrition',
      },
      {
        model: CookingStep,
        as: 'cookingSteps',
        attributes: ['id', 'step_number', 'title', 'description', 'image'],
      },
      {
        model: Kitchen,
        as: 'kitchen',
        attributes: ['id', 'name', 'avatar_url'],
      },
    ],
  });

  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 查询点赞数量
  const likeCount = await Like.count({
    where: { dish_id: dishId },
  });

  // 查询用户是否点赞
  let isLiked = false;
  if (userId) {
    const like = await Like.findOne({
      where: {
        user_id: userId,
        dish_id: dishId,
      },
    });
    isLiked = !!like;
  }

  // 查询菜品是否已添加到指定厨房
  let isAdded = false;
  if (kitchenId) {
    // 检查是否存在同名菜品在指定厨房中（表示已添加）
    const addedDish = await Dish.findOne({
      where: {
        name: dish.name,
        kitchen_id: kitchenId,
      },
    });
    isAdded = !!addedDish;
  }

  // 查询评论
  const comments = await Comment.findAll({
    where: { dish_id: dishId },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'nick_name', 'avatar_url'],
      },
    ],
    order: [['created_at', 'DESC']],
    limit: 10,
  });

  // 检查评论功能是否开启
  const commentEnabled = await systemSettingService.isCommentEnabled();

  // 增加浏览次数（这里可以实现，但不是必须的）

  // 从配料表生成标签，取前5个配料的名称
  const tags = dish.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];

  return {
    id: dish.id,
    name: dish.name,
    image: processImageUrl(dish.image, 'dish'), // 处理图片URL
    price: dish.price,
    originalPrice: dish.original_price,
    description: dish.description,
    tags: tags, // 使用配料表生成的标签而不是原有的tags字段
    sales: dish.sales,
    rating: dish.rating,
    status: dish.status,
    categoryId: dish.category_id,
    categoryName: dish.category?.name,
    images: dish.images?.map(img => ({
      id: img.id,
      url: processImageUrl(img.url, 'dish'), // 处理图片URL
      sort: img.sort,
    })) || [],
    ingredients: dish.ingredients?.map(ing => ({
      id: ing.id,
      name: ing.name,
      amount: ing.amount,
      sort: ing.sort,
    })) || [],
    nutrition: dish.nutrition ? {
      calories: dish.nutrition.calories,
      protein: dish.nutrition.protein,
      fat: dish.nutrition.fat,
      carbs: dish.nutrition.carbs,
      fiber: dish.nutrition.fiber,
      sugar: dish.nutrition.sugar,
      sodium: dish.nutrition.sodium,
    } : null,
    cookingSteps: dish.cookingSteps?.map(step => ({
      id: step.id,
      stepNumber: step.step_number,
      title: step.title,
      description: step.description,
      image: processImageUrl(step.image, 'dish'), // 处理步骤图片URL
    })).sort((a, b) => a.stepNumber - b.stepNumber) || [],
    kitchen: {
      id: dish.kitchen?.id,
      name: dish.kitchen?.name,
      avatarUrl: processImageUrl(dish.kitchen?.avatar_url, 'kitchen-avatar'), // 处理厨房头像URL
    },
    likeCount,
    isLiked,
    isAdded,
    commentEnabled, // 添加评论开关状态
    comments: comments.map(comment => ({
      id: comment.id,
      userId: comment.user_id,
      userName: comment.user?.nick_name,
      userAvatar: processImageUrl(comment.user?.avatar_url, 'user-avatar'), // 处理用户头像URL
      content: comment.content,
      rating: comment.rating,
      createdAt: comment.created_at,
    })),
  };
};

/**
 * 搜索菜品
 * @param keyword 关键词
 * @param kitchenId 厨房ID（可选）
 * @param userId 用户ID（可选）
 * @returns 搜索结果
 */
const searchDishes = async (keyword: string, kitchenId?: string, userId?: number): Promise<any> => {
  // 添加搜索历史
  if (userId) {
    await userService.addSearchHistory(userId, keyword);
  }

  // 更新热门关键词
  await updateHotKeyword(keyword);

  // 构建查询条件
  const where: any = {
    [Op.or]: [
      { name: { [Op.like]: `%${keyword}%` } },
      { description: { [Op.like]: `%${keyword}%` } },
      { tags: { [Op.like]: `%${keyword}%` } },
    ],
    status: 'on', // 只搜索上架的菜品
  };

  if (kitchenId) {
    where.kitchen_id = kitchenId;
  }

  // 查询菜品
  const dishes = await Dish.findAll({
    where,
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name'],
      },
      {
        model: Kitchen,
        as: 'kitchen',
        attributes: ['id', 'name', 'avatar_url'],
      },
      {
        model: Ingredient,
        as: 'ingredients',
        attributes: ['id', 'name', 'amount'],
        order: [['sort', 'ASC']],
      },
    ],
    order: [['sales', 'DESC']],
    limit: 20,
  });

  // 如果提供了用户ID，查询用户是否点赞了这些菜品
  let userLikes: any[] = [];
  if (userId) {
    userLikes = await Like.findAll({
      where: {
        user_id: userId,
        dish_id: {
          [Op.in]: dishes.map(dish => dish.id),
        },
      },
    });
  }

  return {
    keyword,
    dishes: dishes.map(dish => {
      const isLiked = userId ? userLikes.some(like => like.dish_id === dish.id) : false;

      // 从配料表生成标签，取前5个配料的名称
      const tags = dish.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];

      return {
        id: dish.id,
        name: dish.name,
        image: processImageUrl(dish.image, 'dish'), // 处理图片URL
        price: dish.price,
        originalPrice: dish.original_price,
        description: dish.description,
        tags: tags, // 使用配料表生成的标签而不是原有的tags字段
        sales: dish.sales,
        rating: dish.rating,
        categoryId: dish.category_id,
        categoryName: dish.category?.name,
        kitchenId: dish.kitchen_id,
        kitchenName: dish.kitchen?.name,
        kitchenAvatar: dish.kitchen?.avatar_url,
        isLiked,
      };
    }),
  };
};

/**
 * 更新热门关键词
 * @param keyword 关键词
 */
const updateHotKeyword = async (keyword: string): Promise<void> => {
  // 查找是否已存在该关键词
  let hotKeyword = await HotKeyword.findOne({
    where: { keyword },
  });

  if (hotKeyword) {
    // 更新搜索次数
    await hotKeyword.update({
      count: hotKeyword.count + 1,
    });
  } else {
    // 创建新的热门关键词
    hotKeyword = await HotKeyword.create({
      keyword,
      count: 1,
    });
  }
};

/**
 * 获取热门关键词
 * @returns 热门关键词列表
 */
const getHotKeywords = async (): Promise<string[]> => {
  const hotKeywords = await HotKeyword.findAll({
    order: [
      ['count', 'DESC'],
      ['sort', 'ASC'],
    ],
    limit: 10,
  });

  return hotKeywords.map(keyword => keyword.keyword);
};

/**
 * 添加菜品
 * @param userId 用户ID
 * @param dishData 菜品数据
 * @returns 添加结果
 */
const addDish = async (userId: number, dishData: any): Promise<any> => {
  // 检查用户是否有权限添加菜品
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: dishData.kitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限添加菜品');
  }

  // 检查厨房是否存在
  const kitchen = await Kitchen.findByPk(dishData.kitchenId);
  if (!kitchen) {
    throw new BusinessError('厨房不存在');
  }

  // 检查菜品数量限制
  if (kitchen.dish_count >= kitchen.dish_limit) {
    throw new BusinessError(`菜品数量已达上限（${kitchen.dish_limit}个），请先升级厨房`);
  }

  // 检查分类是否存在
  const category = await Category.findOne({
    where: {
      id: dishData.categoryId,
      kitchen_id: dishData.kitchenId,
    },
  });

  if (!category) {
    throw new BusinessError('分类不存在');
  }

  // 转换主图片URL为相对路径
  if (dishData.image) {
    dishData.image = convertToRelativePath(dishData.image);
  }

  // 转换多图片URL为相对路径
  if (dishData.images && Array.isArray(dishData.images)) {
    dishData.images = dishData.images.map((imageUrl: string) => convertToRelativePath(imageUrl));
  }

  // 转换烹饪步骤图片URL为相对路径
  if (dishData.cookingSteps && Array.isArray(dishData.cookingSteps)) {
    dishData.cookingSteps = dishData.cookingSteps.map((step: any) => ({
      ...step,
      image: step.image ? convertToRelativePath(step.image) : step.image
    }));
  }

  // 创建菜品
  const dish = await Dish.create({
    kitchen_id: dishData.kitchenId,
    category_id: dishData.categoryId,
    name: dishData.name,
    image: dishData.image,
    price: dishData.price,
    original_price: dishData.originalPrice,
    description: dishData.description || '',
    tags: dishData.tags ? dishData.tags.join(',') : '',
    status: dishData.status || 'on',
    created_by: userId,
  });

  // 更新厨房菜品数量
  await kitchen.update({
    dish_count: kitchen.dish_count + 1,
  });

  // 更新用户菜品数量
  const user = await User.findByPk(userId);
  if (user) {
    await user.update({
      dishes: user.dishes + 1,
    });
  }

  // 处理其他相关数据（图片、配料、营养成分、烹饪步骤等）
  if (dishData.images && Array.isArray(dishData.images)) {
    for (let i = 0; i < dishData.images.length; i++) {
      await DishImage.create({
        dish_id: dish.id,
        url: dishData.images[i],
        sort: i,
      });
    }
  }

  if (dishData.ingredients && Array.isArray(dishData.ingredients)) {
    for (let i = 0; i < dishData.ingredients.length; i++) {
      const ing = dishData.ingredients[i];
      await Ingredient.create({
        dish_id: dish.id,
        name: ing.name,
        amount: ing.amount,
        sort: i,
      });
    }
  }

  if (dishData.nutrition) {
    await Nutrition.create({
      dish_id: dish.id,
      calories: dishData.nutrition.calories,
      protein: dishData.nutrition.protein,
      fat: dishData.nutrition.fat,
      carbs: dishData.nutrition.carbs,
      fiber: dishData.nutrition.fiber,
      sugar: dishData.nutrition.sugar,
      sodium: dishData.nutrition.sodium,
    });
  }

  if (dishData.cookingSteps && Array.isArray(dishData.cookingSteps)) {
    for (let i = 0; i < dishData.cookingSteps.length; i++) {
      const step = dishData.cookingSteps[i];
      await CookingStep.create({
        dish_id: dish.id,
        step_number: i + 1,
        title: step.title || '',
        description: step.description,
        image: step.image,
      });
    }
  }

  return {
    id: dish.id,
    name: dish.name,
  };
};

/**
 * 更新菜品
 * @param userId 用户ID
 * @param dishData 菜品数据
 * @returns 更新结果
 */
const updateDish = async (userId: number, dishData: any): Promise<void> => {
  logger.info(`updateDish被调用，用户ID: ${userId}`);
  logger.info(`接收到的菜品数据: ${JSON.stringify(dishData, null, 2)}`);
  
  // 检查菜品是否存在 - 加载所有关联数据
  const dish = await Dish.findByPk(dishData.id, {
    include: [
      {
        model: DishImage,
        as: 'images',
      },
      {
        model: Ingredient,
        as: 'ingredients',
      },
      {
        model: Nutrition,
        as: 'nutrition',
      },
      {
        model: CookingStep,
        as: 'cookingSteps',
      },
    ],
  });

  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查用户是否有权限更新菜品
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: dish.kitchen_id,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限更新菜品');
  }

  const transaction = await sequelize.transaction();

  try {
    // 转换主图片URL为相对路径
    if (dishData.image) {
      dishData.image = convertToRelativePath(dishData.image);
    }

    // 转换多图片URL为相对路径
    if (dishData.images && Array.isArray(dishData.images)) {
      dishData.images = dishData.images.map((imageUrl: string) => convertToRelativePath(imageUrl));
    }

    // 转换烹饪步骤图片URL为相对路径
    if (dishData.cookingSteps && Array.isArray(dishData.cookingSteps)) {
      dishData.cookingSteps = dishData.cookingSteps.map((step: any) => ({
        ...step,
        image: step.image ? convertToRelativePath(step.image) : step.image
      }));
    }

    // 更新菜品基本信息
    const updateData: any = {};

    if (dishData.categoryId) updateData.category_id = dishData.categoryId;
    if (dishData.name) updateData.name = dishData.name;
    
    // 如果更新主图片，需要删除旧的主图片文件
    if (dishData.image && dish.image && dish.image !== dishData.image) {
      try {
        // 提取旧主图片的文件名
        const oldImageUrl = dish.image;
        const oldFilename = oldImageUrl.split('/').pop();
        
        if (oldFilename && !oldImageUrl.includes('default')) {
          // 导入deleteFile函数
          const { deleteFile } = require('../middlewares/upload');
          deleteFile(oldFilename, 'dish');
          logger.info(`已删除旧菜品主图片: ${oldFilename}`);
        }
      } catch (error) {
        logger.error('删除旧菜品主图片失败:', error);
        // 不抛出错误，继续更新操作
      }
    }
    
    if (dishData.image) updateData.image = dishData.image;
    if (dishData.price !== undefined) updateData.price = dishData.price;
    if (dishData.originalPrice !== undefined) updateData.original_price = dishData.originalPrice;
    if (dishData.description !== undefined) updateData.description = dishData.description;
    if (dishData.tags && Array.isArray(dishData.tags)) updateData.tags = dishData.tags.join(',');
    if (dishData.status) updateData.status = dishData.status;
    if (dishData.rating !== undefined) updateData.rating = dishData.rating;

    await dish.update(updateData, { transaction });

    // 更新菜品图片
    if (dishData.images !== undefined && Array.isArray(dishData.images)) {
      // 只有在明确传递了images数组时才进行图片更新
      
      // 获取当前的图片列表
      const currentImages = dish.images || [];
      const newImages = dishData.images;
      
      // 比较新旧图片，找出需要删除的图片
      const imagesToDelete = currentImages.filter(oldImage => 
        !newImages.includes(oldImage.url)
      );
      
      // 删除不再需要的图片文件
      for (const imageToDelete of imagesToDelete) {
        try {
          const oldImageUrl = imageToDelete.url;
          const oldFilename = oldImageUrl.split('/').pop();
          
          if (oldFilename && !oldImageUrl.includes('default')) {
            const { deleteFile } = require('../middlewares/upload');
            deleteFile(oldFilename, 'dish');
            logger.info(`已删除不再需要的菜品图片: ${oldFilename}`);
          }
        } catch (error) {
          logger.error('删除旧菜品图片失败:', error);
          // 不抛出错误，继续更新操作
        }
      }
      
      // 删除数据库中的旧图片记录
      await DishImage.destroy({
        where: { dish_id: dish.id },
        transaction,
      });

      // 添加新图片（包括保留的和新增的）
      if (newImages.length > 0) {
        for (let i = 0; i < newImages.length; i++) {
          await DishImage.create({
            dish_id: dish.id,
            url: newImages[i],
            sort: i,
          }, { transaction });
        }
      }
    }

    // 更新配料
    if (dishData.ingredients !== undefined && Array.isArray(dishData.ingredients)) {
      logger.info(`开始更新配料，收到 ${dishData.ingredients.length} 个配料`);
      
      // 删除旧配料
      await Ingredient.destroy({
        where: { dish_id: dish.id },
        transaction,
      });

      // 添加新配料
      let savedIngredientsCount = 0;
      for (let i = 0; i < dishData.ingredients.length; i++) {
        const ing = dishData.ingredients[i];
        if (ing && ing.name && ing.name.trim()) {  // 只添加有名称的配料
          try {
            await Ingredient.create({
              dish_id: dish.id,
              name: ing.name.trim(),
              amount: ing.amount || '',
              sort: ing.sort || i,
            }, { transaction });
            
            savedIngredientsCount++;
            logger.info(`成功创建配料 ${i + 1}: ${ing.name}`);
          } catch (error) {
            logger.error(`创建配料 ${i + 1} 失败:`, error);
          }
        }
      }
      
      logger.info(`配料更新完成，共保存 ${savedIngredientsCount} 个配料`);
    } else {
      logger.info('未提供配料数据，跳过更新');
    }

    // 更新营养成分
    if (dishData.nutrition !== undefined) {
      logger.info(`开始更新营养成分: ${JSON.stringify(dishData.nutrition)}`);
      
      // 删除旧营养成分
      await Nutrition.destroy({
        where: { dish_id: dish.id },
        transaction,
      });

      // 添加新营养成分
      try {
        const nutrition = dishData.nutrition;
        await Nutrition.create({
          dish_id: dish.id,
          calories: nutrition.calories || null,
          protein: nutrition.protein || null,
          carbs: nutrition.carbs || null,
          fat: nutrition.fat || null,
        }, { transaction });
        
        logger.info('营养成分更新成功');
      } catch (error) {
        logger.error('创建营养成分失败:', error);
      }
    } else {
      logger.info('未提供营养成分数据，跳过更新');
    }

    // 更新烹饪步骤
    if (dishData.cookingSteps !== undefined && Array.isArray(dishData.cookingSteps)) {
      logger.info(`开始更新烹饪步骤，收到 ${dishData.cookingSteps.length} 个步骤`);
      
      // 获取当前的烹饪步骤图片列表
      const currentStepImages = [];
      if (dish.cookingSteps && dish.cookingSteps.length > 0) {
        for (const oldStep of dish.cookingSteps) {
          if (oldStep.image) {
            currentStepImages.push(oldStep.image);
          }
        }
      }
      
      // 获取新的烹饪步骤图片列表
      const newStepImages: string[] = [];
      for (const step of dishData.cookingSteps) {
        if (step && step.image) {
          newStepImages.push(step.image);
        }
      }
      
      // 找出需要删除的步骤图片
      const stepImagesToDelete = currentStepImages.filter(oldImageUrl => 
        !newStepImages.includes(oldImageUrl)
      );
      
      // 删除不再需要的烹饪步骤图片文件
      for (const imageUrlToDelete of stepImagesToDelete) {
        try {
          const oldStepFilename = imageUrlToDelete.split('/').pop();
          
          if (oldStepFilename && !imageUrlToDelete.includes('default')) {
            const { deleteFile } = require('../middlewares/upload');
            deleteFile(oldStepFilename, 'dish');
            logger.info(`已删除不再需要的烹饪步骤图片: ${oldStepFilename}`);
          }
        } catch (error) {
          logger.error('删除旧烹饪步骤图片失败:', error);
          // 不抛出错误，继续更新操作
        }
      }
      
      // 删除数据库中的旧烹饪步骤记录
      await CookingStep.destroy({
        where: { dish_id: dish.id },
        transaction,
      });

      // 添加新烹饪步骤
      let savedStepsCount = 0;
      for (let i = 0; i < dishData.cookingSteps.length; i++) {
        const step = dishData.cookingSteps[i];
        logger.info(`处理步骤 ${i + 1}: ${JSON.stringify(step)}`);
        
        if (step && (step.description || step.title)) {  // 有描述或标题就保存
          try {
            const newStep = await CookingStep.create({
              dish_id: dish.id,
              step_number: i + 1,
              title: step.title || '',
              description: step.description || '',
              image: step.image || null,
            }, { transaction });
            
            savedStepsCount++;
            logger.info(`成功创建烹饪步骤 ${i + 1}，ID: ${newStep.id}`);
          } catch (error) {
            logger.error(`创建烹饪步骤 ${i + 1} 失败:`, error);
          }
        } else {
          logger.warn(`跳过空步骤 ${i + 1}`);
        }
      }
      
      logger.info(`烹饪步骤更新完成，共保存 ${savedStepsCount} 个步骤`);
    } else {
      logger.info('未提供烹饪步骤数据，跳过更新');
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 删除菜品
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 */
const deleteDish = async (userId: number, dishId: number, kitchenId: string): Promise<void> => {
  // 检查菜品是否存在
  const dish = await Dish.findOne({
    where: {
      id: dishId,
      kitchen_id: kitchenId,
    },
    include: [
      {
        model: DishImage,
        as: 'images',
      },
      {
        model: CookingStep,
        as: 'cookingSteps',
      },
    ],
  });

  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查用户是否有权限删除菜品
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限删除菜品');
  }

  // 删除菜品相关的图片文件
  try {
    // 删除主图片文件
    if (dish.image) {
      const mainImageUrl = dish.image;
      const mainImageFilename = mainImageUrl.split('/').pop();
      
      if (mainImageFilename && !mainImageUrl.includes('default')) {
        const { deleteFile } = require('../middlewares/upload');
        deleteFile(mainImageFilename, 'dish');
        logger.info(`已删除菜品主图片: ${mainImageFilename}`);
      }
    }

    // 删除多图文件
    if (dish.images && dish.images.length > 0) {
      for (const image of dish.images) {
        const imageUrl = image.url;
        const imageFilename = imageUrl.split('/').pop();
        
        if (imageFilename && !imageUrl.includes('default')) {
          const { deleteFile } = require('../middlewares/upload');
          deleteFile(imageFilename, 'dish');
          logger.info(`已删除菜品图片: ${imageFilename}`);
        }
      }
    }

    // 删除烹饪步骤图片文件
    if (dish.cookingSteps && dish.cookingSteps.length > 0) {
      for (const step of dish.cookingSteps) {
        if (step.image) {
          const stepImageUrl = step.image;
          const stepImageFilename = stepImageUrl.split('/').pop();
          
          if (stepImageFilename && !stepImageUrl.includes('default')) {
            const { deleteFile } = require('../middlewares/upload');
            deleteFile(stepImageFilename, 'dish');
            logger.info(`已删除烹饪步骤图片: ${stepImageFilename}`);
          }
        }
      }
    }
  } catch (error) {
    logger.error('删除菜品图片文件失败:', error);
    // 不抛出错误，继续删除操作
  }

  // 删除菜品
  await dish.destroy();

  // 更新厨房菜品数量
  const kitchen = await Kitchen.findByPk(kitchenId);
  if (kitchen && kitchen.dish_count > 0) {
    await kitchen.update({
      dish_count: kitchen.dish_count - 1,
    });
  }

  // 更新用户菜品数量
  const user = await User.findByPk(userId);
  if (user && user.dishes > 0) {
    await user.update({
      dishes: user.dishes - 1,
    });
  }
};

/**
 * 更新菜品状态
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 * @param status 状态
 */
const updateDishStatus = async (userId: number, dishId: number, kitchenId: string, status: string): Promise<void> => {
  // 检查菜品是否存在
  const dish = await Dish.findOne({
    where: {
      id: dishId,
      kitchen_id: kitchenId,
    },
  });

  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查用户是否有权限更新菜品状态
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限更新菜品状态');
  }

  // 检查状态是否有效
  if (!['on', 'off'].includes(status)) {
    throw new BusinessError('无效的状态值');
  }

  // 更新菜品状态
  await dish.update({ status });
};

/**
 * 更新菜品排序
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param dishes 菜品排序数据
 */
const updateDishSort = async (userId: number, kitchenId: string, dishes: any[]): Promise<void> => {
  // 检查用户是否有权限更新菜品排序
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限更新菜品排序');
  }

  // 更新菜品排序
  const transaction = await sequelize.transaction();

  try {
    for (let i = 0; i < dishes.length; i++) {
      await Dish.update(
        { sort: i },
        {
          where: {
            id: dishes[i].id,
            kitchen_id: kitchenId,
          },
          transaction,
        }
      );
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 点赞菜品
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @returns 点赞结果
 */
const likeDish = async (userId: number, dishId: number): Promise<any> => {
  // 检查菜品是否存在
  const dish = await Dish.findByPk(dishId, {
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'nick_name'],
      },
    ],
  });
  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查是否已经点赞
  const existingLike = await Like.findOne({
    where: {
      user_id: userId,
      dish_id: dishId,
    },
  });

  let action = '';

  if (existingLike) {
    // 取消点赞
    await existingLike.destroy();
    action = 'unlike';

    // 更新菜品创建者的获赞数量
    if (dish.created_by !== null) {
      const creator = await User.findByPk(dish.created_by);
      if (creator && creator.likes > 0) {
        await creator.update({
          likes: creator.likes - 1,
        });
      }
    }
  } else {
    // 添加点赞
    await Like.create({
      user_id: userId,
      dish_id: dishId,
    });
    action = 'like';

    // 更新菜品创建者的获赞数量
    if (dish.created_by !== null) {
      const creator = await User.findByPk(dish.created_by);
      if (creator) {
        await creator.update({
          likes: creator.likes + 1,
        });
      }
    }

    // 发送点赞消息通知给菜品创建者（不给自己发消息）
    if (dish.created_by && dish.created_by !== userId) {
      const liker = await User.findByPk(userId);
      if (liker) {
        // 导入消息服务
        const messageService = require('./messageService').default;
        await messageService.createMessage(
          dish.created_by,
          'like',
          '收到新点赞',
          `${liker.nick_name} 点赞了你的菜品「${dish.name}」`,
          dish.image
        );
      }
    }
  }

  // 获取最新点赞数量
  const likeCount = await Like.count({
    where: { dish_id: dishId },
  });

  return {
    action,
    likeCount,
  };
};

/**
 * 举报菜品
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param reason 举报原因
 */
const reportDish = async (userId: number, dishId: number, reason: string): Promise<void> => {
  // 检查菜品是否存在
  const dish = await Dish.findByPk(dishId);
  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查是否已经举报
  const existingReport = await Report.findOne({
    where: {
      user_id: userId,
      dish_id: dishId,
    },
  });

  if (existingReport) {
    throw new BusinessError('您已经举报过该菜品');
  }

  // 添加举报
  await Report.create({
    user_id: userId,
    dish_id: dishId,
    reason,
    status: 'pending',
  });
};

/**
 * 添加评论
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param content 评论内容
 * @param rating 评分
 * @returns 评论结果
 */
const addComment = async (userId: number, dishId: number, content: string, rating: number): Promise<any> => {
  // 检查评论功能是否开启
  const commentEnabled = await systemSettingService.isCommentEnabled();
  if (!commentEnabled) {
    throw new BusinessError('评论功能已关闭');
  }

  // 检查菜品是否存在
  const dish = await Dish.findByPk(dishId);
  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 创建评论
  const comment = await Comment.create({
    user_id: userId,
    dish_id: dishId,
    content,
    rating,
  });

  // 获取用户信息
  const user = await User.findByPk(userId);

  // 发送评论消息通知给菜品创建者（不给自己发消息）
  if (dish.created_by && dish.created_by !== userId) {
    const messageService = require('./messageService').default;
    await messageService.createMessage(
      dish.created_by,
      'comment',
      '收到新评论',
      `${user?.nick_name || '用户'} 评论了你的菜品「${dish.name}」：${content}`,
      dish.image
    );
  }

  return {
    id: comment.id,
    content: comment.content,
    rating: comment.rating,
    createdAt: comment.created_at,
    user: {
      id: user?.id,
      nickName: user?.nick_name,
      avatarUrl: user?.avatar_url,
    },
  };
};

/**
 * 添加菜品到厨房
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param targetKitchenId 目标厨房ID
 * @param targetCategoryId 目标分类ID
 * @returns 添加结果
 */
const addToKitchen = async (userId: number, dishId: number, targetKitchenId: string, targetCategoryId: number): Promise<any> => {
  // 检查菜品是否存在
  const dish = await Dish.findByPk(dishId, {
    include: [
      {
        model: DishImage,
        as: 'images',
      },
      {
        model: Ingredient,
        as: 'ingredients',
      },
      {
        model: Nutrition,
        as: 'nutrition',
      },
      {
        model: CookingStep,
        as: 'cookingSteps',
      },
    ],
  });

  if (!dish) {
    throw new BusinessError('菜品不存在');
  }

  // 检查用户是否有权限添加菜品到目标厨房
  const member = await KitchenMember.findOne({
    where: {
      user_id: userId,
      kitchen_id: targetKitchenId,
    },
  });

  if (!member || !member.canEdit()) {
    throw new BusinessError('没有权限添加菜品到目标厨房');
  }

  // 检查目标厨房是否存在
  const kitchen = await Kitchen.findByPk(targetKitchenId);
  if (!kitchen) {
    throw new BusinessError('目标厨房不存在');
  }

  // 检查目标分类是否存在
  const category = await Category.findOne({
    where: {
      id: targetCategoryId,
      kitchen_id: targetKitchenId,
    },
  });

  if (!category) {
    throw new BusinessError('目标分类不存在');
  }

  // 检查目标厨房菜品数量限制
  if (kitchen.dish_count >= kitchen.dish_limit) {
    throw new BusinessError(`目标厨房菜品数量已达上限（${kitchen.dish_limit}个），请先升级厨房`);
  }

  // 创建新菜品
  const newDish = await Dish.create({
    kitchen_id: targetKitchenId,
    category_id: targetCategoryId,
    name: dish.name,
    image: dish.image,
    price: dish.price,
    original_price: dish.original_price,
    description: dish.description,
    tags: dish.tags,
    status: 'on',
    created_by: userId,
  });

  // 复制其他相关数据
  if (dish.images && dish.images.length > 0) {
    for (const image of dish.images) {
      await DishImage.create({
        dish_id: newDish.id,
        url: image.url,
        sort: image.sort,
      });
    }
  }

  if (dish.ingredients && dish.ingredients.length > 0) {
    for (const ingredient of dish.ingredients) {
      await Ingredient.create({
        dish_id: newDish.id,
        name: ingredient.name,
        amount: ingredient.amount,
        sort: ingredient.sort,
      });
    }
  }

  if (dish.nutrition) {
    await Nutrition.create({
      dish_id: newDish.id,
      calories: dish.nutrition.calories,
      protein: dish.nutrition.protein,
      fat: dish.nutrition.fat,
      carbs: dish.nutrition.carbs,
      fiber: dish.nutrition.fiber,
      sugar: dish.nutrition.sugar,
      sodium: dish.nutrition.sodium,
    });
  }

  if (dish.cookingSteps && dish.cookingSteps.length > 0) {
    for (const step of dish.cookingSteps) {
      await CookingStep.create({
        dish_id: newDish.id,
        step_number: step.step_number,
        title: step.title,
        description: step.description,
        image: step.image,
      });
    }
  }

  // 更新厨房菜品数量
  await kitchen.update({
    dish_count: kitchen.dish_count + 1,
  });

  // 更新用户菜品数量
  const user = await User.findByPk(userId);
  if (user) {
    await user.update({
      dishes: user.dishes + 1,
    });
  }

  // 更新原菜品的销量（添加人数）
  await dish.update({
    sales: (dish.sales || 0) + 1,
  });

  return {
    id: newDish.id,
    name: newDish.name,
  };
};

export default {
  getCategories,
  getDishList,
  getDishDetail,
  searchDishes,
  getHotKeywords,
  addDish,
  updateDish,
  deleteDish,
  updateDishStatus,
  updateDishSort,
  likeDish,
  reportDish,
  addToKitchen,
  addComment,
};
