/**
 * URL管理器
 * 统一管理图片URL的生成和转换
 */
import config from '../config/config';

/**
 * 将完整URL转换为相对路径
 * @param fullUrl 完整URL
 * @returns 相对路径
 */
export const convertToRelativePath = (fullUrl: string): string => {
  if (!fullUrl) return '';
  
  // 如果已经是相对路径，直接返回
  if (fullUrl.startsWith('/uploads/')) {
    return fullUrl;
  }
  
  // 移除域名部分，提取相对路径
  const baseUrls = [
    config.server.baseUrl,
    config.server.corsOrigin,
    'https://dzcdd.zj1.natnps.cn',
    'http://dzcdd.zj1.natnps.cn'
  ];
  
  for (const baseUrl of baseUrls) {
    if (fullUrl.startsWith(baseUrl)) {
      return fullUrl.replace(baseUrl, '');
    }
  }
  
  // 如果都不匹配，尝试提取/uploads/开始的部分
  const uploadsIndex = fullUrl.indexOf('/uploads/');
  if (uploadsIndex !== -1) {
    return fullUrl.substring(uploadsIndex);
  }
  
  // 如果无法转换，返回原URL
  return fullUrl;
};

/**
 * 将相对路径转换为完整URL
 * @param relativePath 相对路径
 * @returns 完整URL
 */
export const convertToFullUrl = (relativePath: string): string => {
  if (!relativePath) return '';
  
  // 如果已经是完整URL，直接返回
  if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
    return relativePath;
  }
  
  // 确保路径以/开头
  const path = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;
  
  return `${config.server.baseUrl}${path}`;
};

/**
 * 生成文件的相对路径
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 相对路径
 */
export const getRelativePath = (filename: string, subDir: string = 'dish'): string => {
  return `/uploads/${subDir}/${filename}`;
};

/**
 * 生成文件的完整URL
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 完整URL
 */
export const getFullUrl = (filename: string, subDir: string = 'dish'): string => {
  const relativePath = getRelativePath(filename, subDir);
  return convertToFullUrl(relativePath);
};

/**
 * 获取默认图片的相对路径
 * @param type 图片类型
 * @returns 相对路径
 */
export const getDefaultImageRelativePath = (type: 'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'): string => {
  const imageMap = {
    'user-avatar': '/uploads/default/tou6.jpg',
    'kitchen-avatar': '/uploads/default/chu1.jpg',
    'kitchen-background': '/uploads/default/bj6.jpg',
    'dish': '/uploads/default/dish.jpg'
  };
  
  return imageMap[type];
};

/**
 * 获取默认图片的完整URL
 * @param type 图片类型
 * @returns 完整URL
 */
export const getDefaultImageUrl = (type: 'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'): string => {
  const relativePath = getDefaultImageRelativePath(type);
  return convertToFullUrl(relativePath);
};

/**
 * 获取随机用户头像的相对路径
 * @returns 相对路径
 */
export const getRandomUserAvatarRelativePath = (): string => {
  const avatarIndex = Math.floor(Math.random() * 6) + 1;
  return `/uploads/default/tou${avatarIndex}.jpg`;
};

/**
 * 获取随机用户头像的完整URL
 * @returns 完整URL
 */
export const getRandomUserAvatarUrl = (): string => {
  const relativePath = getRandomUserAvatarRelativePath();
  return convertToFullUrl(relativePath);
};

/**
 * 获取随机厨房头像的相对路径
 * @returns 相对路径
 */
export const getRandomKitchenAvatarRelativePath = (): string => {
  const avatarIndex = Math.floor(Math.random() * 6) + 1;
  return `/uploads/default/chu${avatarIndex}.jpg`;
};

/**
 * 获取随机厨房头像的完整URL
 * @returns 完整URL
 */
export const getRandomKitchenAvatarUrl = (): string => {
  const relativePath = getRandomKitchenAvatarRelativePath();
  return convertToFullUrl(relativePath);
};

/**
 * 获取随机厨房背景的相对路径
 * @returns 相对路径
 */
export const getRandomKitchenBackgroundRelativePath = (): string => {
  const backgroundIndex = Math.floor(Math.random() * 6) + 1;
  return `/uploads/default/bj${backgroundIndex}.jpg`;
};

/**
 * 获取随机厨房背景的完整URL
 * @returns 完整URL
 */
export const getRandomKitchenBackgroundUrl = (): string => {
  const relativePath = getRandomKitchenBackgroundRelativePath();
  return convertToFullUrl(relativePath);
};

/**
 * 处理图片URL，确保返回完整URL
 * @param imageUrl 图片URL（可能是相对路径或完整URL）
 * @param defaultType 默认图片类型
 * @returns 完整URL
 */
export const processImageUrl = (imageUrl: string | null, defaultType: 'user-avatar' | 'kitchen-avatar' | 'kitchen-background' | 'dish'): string => {
  if (!imageUrl || imageUrl.trim() === '') {
    return getDefaultImageUrl(defaultType);
  }
  
  return convertToFullUrl(imageUrl);
};

export default {
  convertToRelativePath,
  convertToFullUrl,
  getRelativePath,
  getFullUrl,
  getDefaultImageRelativePath,
  getDefaultImageUrl,
  getRandomUserAvatarRelativePath,
  getRandomUserAvatarUrl,
  getRandomKitchenAvatarRelativePath,
  getRandomKitchenAvatarUrl,
  getRandomKitchenBackgroundRelativePath,
  getRandomKitchenBackgroundUrl,
  processImageUrl,
}; 