{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES5", "allowJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "strict": false, "strictPropertyInitialization": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["ES5", "ES2015.Promise", "ES2015.Iterable"], "typeRoots": ["./typings"]}, "include": ["./**/*.ts"], "exclude": ["node_modules", "**/*.js", "**/*.js.map"]}