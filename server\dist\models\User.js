"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 用户模型
 * 存储用户基本信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 用户模型类
class User extends sequelize_1.Model {
    // 设置令牌
    setToken(token) {
        this.token = token;
    }
    // 转换为JSON时排除敏感字段
    toJSON() {
        const values = Object.assign({}, this.get());
        delete values.token;
        delete values.open_id;
        return values;
    }
}
// 初始化用户模型
User.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        comment: '用户ID',
    },
    open_id: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '微信OpenID',
    },
    nick_name: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '昵称',
    },
    avatar_url: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '头像URL',
    },
    gender: {
        type: sequelize_1.DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '性别(0:未知,1:男,2:女)',
    },
    coins: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1000, // 初始奖励1000大米
        comment: '大米数量',
    },
    likes: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '获赞数量',
    },
    dishes: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '添加的菜品数量',
    },
    kitchens: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '厨房数量',
    },
    token: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '用户令牌',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_open_id',
            fields: ['open_id'],
        },
    ],
});
exports.default = User;
//# sourceMappingURL=User.js.map