"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 支付相关路由
 * 处理微信支付API路由
 */
const express_1 = __importDefault(require("express"));
const paymentController_1 = __importDefault(require("../controllers/paymentController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 微信支付回调需要原始请求体的中间件
const rawBodyMiddleware = (req, res, next) => {
    if (req.path === '/notify' || req.path === '/refund-notify') {
        let data = '';
        req.setEncoding('utf8');
        req.on('data', (chunk) => {
            data += chunk;
        });
        req.on('end', () => {
            req.rawBody = data;
            try {
                req.body = JSON.parse(data);
            }
            catch (error) {
                req.body = {};
            }
            next();
        });
    }
    else {
        next();
    }
};
/**
 * 创建支付订单
 * POST /api/payment/create
 * 需要用户认证
 */
router.post('/create', auth_1.verifyToken, paymentController_1.default.createPaymentOrder);
/**
 * 查询支付订单状态
 * GET /api/payment/query/:outTradeNo
 * 需要用户认证
 */
router.get('/query/:outTradeNo', auth_1.verifyToken, paymentController_1.default.queryPaymentOrder);
/**
 * 关闭支付订单
 * POST /api/payment/close
 * 需要用户认证
 */
router.post('/close', auth_1.verifyToken, paymentController_1.default.closePaymentOrder);
/**
 * 微信支付回调通知
 * POST /api/payment/notify
 * 无需认证（微信服务器回调）
 */
router.post('/notify', rawBodyMiddleware, paymentController_1.default.paymentNotify);
/**
 * 申请退款
 * POST /api/payment/refund
 * 需要用户认证
 */
router.post('/refund', auth_1.verifyToken, paymentController_1.default.createRefund);
/**
 * 查询退款状态
 * GET /api/payment/refund/:outRefundNo
 * 需要用户认证
 */
router.get('/refund/:outRefundNo', auth_1.verifyToken, paymentController_1.default.queryRefund);
/**
 * 微信退款回调通知
 * POST /api/payment/refund-notify
 * 无需认证（微信服务器回调）
 */
router.post('/refund-notify', rawBodyMiddleware, paymentController_1.default.refundNotify);
/**
 * 手动确认支付（测试用）
 * POST /api/payment/confirm
 * 需要用户认证
 */
router.post('/confirm', auth_1.verifyToken, paymentController_1.default.confirmPayment);
exports.default = router;
//# sourceMappingURL=paymentRoutes.js.map