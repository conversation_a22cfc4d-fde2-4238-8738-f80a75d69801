"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const userService_1 = __importDefault(require("../services/userService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
const validator_1 = __importDefault(require("../utils/validator"));
/**
 * 用户登录
 * @route POST /api/user/login
 */
const login = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { code } = req.body;
        if (!code) {
            throw new error_1.BusinessError('缺少参数: code', response_1.ResponseCode.VALIDATION);
        }
        const result = yield userService_1.default.login(code);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户信息
 * @route GET /api/user/info
 */
const getUserInfo = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const userInfo = yield userService_1.default.getUserInfo(userId);
        (0, response_1.success)(res, userInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新用户信息
 * @route POST /api/user/update
 */
const updateUserInfo = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const userInfo = req.body;
        // 验证用户名长度
        if (userInfo.nick_name) {
            validator_1.default.validateStringLength(userInfo.nick_name, 2, 20, '用户名');
            // 过滤特殊字符
            userInfo.nick_name = validator_1.default.filterSpecialChars(userInfo.nick_name);
        }
        // 验证头像URL格式
        if (userInfo.avatar_url) {
            validator_1.default.validateUrl(userInfo.avatar_url, '头像URL');
        }
        // 验证性别
        if (userInfo.gender !== undefined) {
            validator_1.default.validateEnum(String(userInfo.gender), ['0', '1', '2'], '性别');
        }
        yield userService_1.default.updateUserInfo(userId, userInfo);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取搜索历史
 * @route GET /api/user/searchHistory
 */
const getSearchHistory = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const history = yield userService_1.default.getSearchHistory(userId);
        (0, response_1.success)(res, { history });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 清除搜索历史
 * @route POST /api/user/clearSearchHistory
 */
const clearSearchHistory = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        yield userService_1.default.clearSearchHistory(userId);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户背景设置
 * @route GET /api/user/backgroundSettings
 */
const getUserBackgroundSettings = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const settings = yield userService_1.default.getUserBackgroundSettings(userId);
        (0, response_1.success)(res, settings);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新用户背景设置
 * @route POST /api/user/updateBackgroundSettings
 */
const updateUserBackgroundSettings = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const settings = req.body;
        const result = yield userService_1.default.updateUserBackgroundSettings(userId, settings);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户大米信息
 * @route GET /api/user/coins
 */
const getUserCoins = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const coinsInfo = yield userService_1.default.getUserCoins(userId);
        (0, response_1.success)(res, coinsInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 充值大米
 * @route POST /api/user/recharge
 */
const rechargeCoins = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { amount } = req.body;
        // 验证充值金额
        if (!amount) {
            throw new error_1.BusinessError('缺少参数: amount', response_1.ResponseCode.VALIDATION);
        }
        // 验证充值金额范围
        const amountNum = Number(amount);
        if (isNaN(amountNum)) {
            throw new error_1.BusinessError('充值金额必须是数字', response_1.ResponseCode.VALIDATION);
        }
        // 验证充值金额范围（1-10000）
        validator_1.default.validateNumberRange(amountNum, 1, 10000, '充值金额');
        const result = yield userService_1.default.rechargeCoins(userId, amountNum);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取大米交易记录
 * @route GET /api/user/coinRecords
 */
const getCoinRecords = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { type = 'all' } = req.query;
        const records = yield userService_1.default.getCoinRecords(userId, type);
        (0, response_1.success)(res, { records });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取用户签到数据
 * @route GET /api/user/signInData
 */
const getSignInData = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const signInData = yield userService_1.default.getSignInData(userId);
        (0, response_1.success)(res, signInData);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 签到
 * @route POST /api/user/signIn
 */
const signIn = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const result = yield userService_1.default.signIn(userId);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 观看广告
 * @route POST /api/user/watchAd
 */
const watchAd = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const result = yield userService_1.default.watchAd(userId);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 补签
 * @route POST /api/user/compensateSignIn
 */
const compensateSignIn = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { date } = req.body;
        // 验证日期参数
        if (!date) {
            throw new error_1.BusinessError('缺少参数: date', response_1.ResponseCode.VALIDATION);
        }
        const result = yield userService_1.default.compensateSignIn(userId, date);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取会员状态
 * @route GET /api/user/membershipStatus
 */
const getMembershipStatus = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const status = yield userService_1.default.getMembershipStatus(userId);
        (0, response_1.success)(res, status);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 订阅会员
 * @route POST /api/user/subscribeMembership
 */
const subscribeMembership = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { memberType } = req.body;
        // 验证会员类型
        if (!memberType) {
            throw new error_1.BusinessError('缺少参数: memberType', response_1.ResponseCode.VALIDATION);
        }
        // 验证会员类型枚举值
        validator_1.default.validateEnum(memberType, ['monthly', 'yearly'], '会员类型');
        // 检查用户是否有足够的大米
        const result = yield userService_1.default.subscribeMembership(userId, memberType);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    login,
    getUserInfo,
    updateUserInfo,
    getSearchHistory,
    clearSearchHistory,
    getUserBackgroundSettings,
    updateUserBackgroundSettings,
    getUserCoins,
    rechargeCoins,
    getCoinRecords,
    getSignInData,
    signIn,
    watchAd,
    compensateSignIn,
    getMembershipStatus,
    subscribeMembership
};
//# sourceMappingURL=userController.js.map