"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 购物车模块路由
 * 处理购物车相关的API路由
 */
const express_1 = __importDefault(require("express"));
const cartController_1 = __importDefault(require("../controllers/cartController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取购物车列表
router.get('/list', auth_1.verifyToken, cartController_1.default.getCartList);
// 添加到购物车
router.post('/add', auth_1.verifyToken, cartController_1.default.addToCart);
// 更新购物车项数量
router.post('/updateCount', auth_1.verifyToken, cartController_1.default.updateCartItemCount);
// 清空购物车
router.post('/clear', auth_1.verifyToken, cartController_1.default.clearCart);
exports.default = router;
//# sourceMappingURL=cartRoutes.js.map