import { request } from '../utils/request'
import { SERVER_BASE_URL } from '../utils/constants'

// 反馈类型
export type FeedbackType = 'bug' | 'feature' | 'other'

// 反馈数据接口
export interface FeedbackData {
  type: FeedbackType
  description: string
  images?: string[]
  userId?: string
  deviceInfo?: any
}

// 提交反馈
export const submitFeedback = (data: FeedbackData) => {
  return request({
    url: '/api/feedback/submit',
    method: 'POST',
    data: {
      type: data.type,
      description: data.description,
      images: data.images || [],
      deviceInfo: data.deviceInfo || {}
    }
  })
}

// 获取反馈列表（管理员用）
export const getFeedbackList = (params: {
  page?: number
  pageSize?: number
  type?: FeedbackType
  status?: string
}) => {
  return request({
    url: '/api/feedback/list',
    method: 'POST',
    data: params
  })
}

// 更新反馈状态（管理员用）
export const updateFeedbackStatus = (feedbackId: string, status: string, reply?: string) => {
  return request({
    url: '/api/feedback/update-status',
    method: 'POST',
    data: {
      feedbackId,
      status,
      reply
    }
  })
}

// 上传反馈图片
export const uploadFeedbackImage = (filePath: string) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')
    
    wx.uploadFile({
      url: `${SERVER_BASE_URL}/api/feedback/upload-image`,
      filePath: filePath,
      name: 'image',
      header: {
        'auth': token
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (error) {
          reject(new Error('解析响应数据失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
} 