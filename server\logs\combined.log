
[2025-06-15 09:17:38] [信息] 微信支付服务初始化成功
[2025-06-15 09:17:38] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:17:38] [信息] 初始化模型关联关系...
[2025-06-15 09:17:38] [信息] 模型关联关系初始化完成
[2025-06-15 09:17:38] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:17:38] [信息] 环境: development
[2025-06-15 09:17:38] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:17:48] [信息] GET /api/payment/notify
[2025-06-15 09:18:09] [信息] GET /api/user/coins
[2025-06-15 09:18:09] [信息] GET /api/user/coins
[2025-06-15 09:18:13] [信息] GET /api/user/coins
[2025-06-15 09:18:13] [信息] GET /api/user/coins
[2025-06-15 09:18:31] [信息] POST /api/payment/create
[2025-06-15 09:18:31] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-15 09:18:31] [信息] 生成订单号: R1000117499503115705ws71b, 长度: 25
[2025-06-15 09:18:31] [信息] 微信支付统一下单请求参数:
[2025-06-15 09:18:31] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值10大米",
  "out_trade_no": "R1000117499503115705ws71b",
  "time_expire": "2025-06-15T01:48:31.664Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\",\"paymentOrderId\":18}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 10,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-15 09:18:32] [信息] 微信支付统一下单响应:
[2025-06-15 09:18:32] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749950312",
    "nonceStr": "usfqwd7k27",
    "package": "prepay_id=wx1509183211388017d014d9f751b0940001",
    "signType": "RSA",
    "paySign": "LmiaYmcuvp/Ua0mOoAn0CPVbbl+ckQNk1LH/BZ8CCP+HLA4ajYqn8VQSfoxbCkA4VVnMvZ89KmuFKfFkBIBKe4yvTiunUOGVliQoQcGJXc4WNfQEhAWDaMz8CWSNXSJRss55vshsQkcHFcjVnGgZKiZzLxpwC2JG7QTu4IvfZsOPit5YbwXk7KZo2+C16s6qz81heE8W94lLrOzR9HY6aCqrJmrzMrELzvyWT6iylxJkUrixCkdcHhNFIG7cTXpbfM8Zl74Mptev3vLDYmGZzK0X1ArKN2Mlkiq971cYzJmWQy503Li7kRiCwWIgLOTCgxquth9rpMWg63cwMo4Wdg=="
  }
}
[2025-06-15 09:18:32] [信息] 成功获取prepay_id:
[2025-06-15 09:18:32] [信息] 用户 10001 创建支付订单成功，订单号: R1000117499503115705ws71b, 金额: 0.1元, 大米: 10
[2025-06-15 09:18:51] [信息] GET /api/payment/query/R1000117499503115705ws71b
[2025-06-15 09:18:51] [信息] 查询微信支付订单:
[2025-06-15 09:18:51] [信息] 微信支付订单查询结果:
[2025-06-15 09:18:51] [信息] 查询订单状态: R1000117499503115705ws71b, 状态: undefined
[2025-06-15 09:18:53] [信息] GET /api/user/coinRecords?type=all
[2025-06-15 09:25:38] [信息] GET /api/payment/notify
[2025-06-15 09:25:38] [警告] 未找到路由: GET /api/payment/notify
[2025-06-15 09:25:38] [信息] GET /api/health
[2025-06-15 09:25:38] [警告] 未找到路由: GET /api/health
[2025-06-15 09:25:38] [信息] POST /api/payment/notify
[**********] [信息] 收到微信支付回调通知:
[2025-06-15 09:25:38] [错误] 解密支付通知数据失败:
[2025-06-15 09:25:38] [错误] 处理微信支付回调失败:
[2025-06-15 09:28:30] [信息] 微信支付服务初始化成功
[2025-06-15 09:28:30] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:28:30] [信息] 初始化模型关联关系...
[2025-06-15 09:28:30] [信息] 模型关联关系初始化完成
[2025-06-15 09:28:30] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:28:30] [信息] 环境: development
[2025-06-15 09:28:30] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:28:43] [信息] 微信支付服务初始化成功
[2025-06-15 09:28:43] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:28:43] [信息] 初始化模型关联关系...
[2025-06-15 09:28:43] [信息] 模型关联关系初始化完成
[2025-06-15 09:28:43] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:28:43] [信息] 环境: development
[2025-06-15 09:28:43] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:28:56] [信息] 微信支付服务初始化成功
[2025-06-15 09:28:56] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:28:56] [信息] 初始化模型关联关系...
[2025-06-15 09:28:57] [信息] 模型关联关系初始化完成
[2025-06-15 09:28:57] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:28:57] [信息] 环境: development
[2025-06-15 09:28:57] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:29:12] [信息] 微信支付服务初始化成功
[2025-06-15 09:29:13] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:29:13] [信息] 初始化模型关联关系...
[2025-06-15 09:29:13] [信息] 模型关联关系初始化完成
[2025-06-15 09:29:13] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:29:13] [信息] 环境: development
[2025-06-15 09:29:13] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:29:29] [信息] 微信支付服务初始化成功
[2025-06-15 09:29:29] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:29:29] [信息] 初始化模型关联关系...
[2025-06-15 09:29:29] [信息] 模型关联关系初始化完成
[2025-06-15 09:29:29] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:29:29] [信息] 环境: development
[2025-06-15 09:29:29] [信息] API服务就绪，包含管理员接口
[2025-06-15 09:29:45] [信息] 微信支付服务初始化成功
[2025-06-15 09:29:45] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-15 09:29:45] [信息] 初始化模型关联关系...
[2025-06-15 09:29:45] [信息] 模型关联关系初始化完成
[2025-06-15 09:29:45] [信息] 服务器启动成功，监听端口: 3000
[2025-06-15 09:29:45] [信息] 环境: development
[2025-06-15 09:29:45] [信息] API服务就绪，包含管理员接口
