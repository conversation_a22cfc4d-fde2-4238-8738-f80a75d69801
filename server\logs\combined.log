[2025-06-14 13:54:09] [信息] POST /api/payment/create
[2025-06-14 13:54:09] [信息] 微信支付统一下单请求参数:
[2025-06-14 13:54:09] [信息] 微信支付统一下单响应:
[2025-06-14 13:54:09] [错误] 微信支付统一下单失败:
[2025-06-14 13:54:09] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:00:09] [信息] 微信支付服务初始化成功
[2025-06-14 14:00:09] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:00:09] [信息] 初始化模型关联关系...
[2025-06-14 14:00:09] [信息] 模型关联关系初始化完成
[2025-06-14 14:00:09] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:00:09] [信息] 环境: development
[2025-06-14 14:00:09] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:00:26] [信息] 微信支付服务初始化成功
[2025-06-14 14:00:26] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:00:26] [信息] 初始化模型关联关系...
[2025-06-14 14:00:26] [信息] 模型关联关系初始化完成
[2025-06-14 14:00:26] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:00:26] [信息] 环境: development
[2025-06-14 14:00:26] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:01:30] [信息] POST /api/payment/create
[2025-06-14 14:01:30] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:01:30] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:01:30] [信息] 微信支付统一下单响应:
[2025-06-14 14:01:30] [错误] 微信支付统一下单返回错误状态:
[2025-06-14 14:01:30] [错误] 微信支付统一下单失败:
[2025-06-14 14:01:30] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:01:41] [信息] 微信支付服务初始化成功
[2025-06-14 14:01:41] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:01:41] [信息] 初始化模型关联关系...
[2025-06-14 14:01:41] [信息] 模型关联关系初始化完成
[2025-06-14 14:01:41] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:01:41] [信息] 环境: development
[2025-06-14 14:01:41] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:02:05] [信息] POST /api/payment/create
[2025-06-14 14:02:05] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:02:05] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:02:05] [信息] 微信支付统一下单响应:
[2025-06-14 14:02:05] [错误] 微信支付统一下单返回错误状态:
[2025-06-14 14:02:05] [错误] 微信支付统一下单失败:
[2025-06-14 14:02:05] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:02:34] [信息] 微信支付服务初始化成功
[2025-06-14 14:02:34] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:02:34] [信息] 初始化模型关联关系...
[2025-06-14 14:02:34] [信息] 模型关联关系初始化完成
[2025-06-14 14:02:34] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:02:34] [信息] 环境: development
[2025-06-14 14:02:34] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:02:50] [信息] 微信支付服务初始化成功
[2025-06-14 14:02:50] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:02:50] [信息] 初始化模型关联关系...
[2025-06-14 14:02:50] [信息] 模型关联关系初始化完成
[2025-06-14 14:02:50] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:02:50] [信息] 环境: development
[2025-06-14 14:02:50] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:03:20] [信息] 微信支付服务初始化成功
[2025-06-14 14:03:20] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:03:20] [信息] 初始化模型关联关系...
[2025-06-14 14:03:20] [信息] 模型关联关系初始化完成
[2025-06-14 14:03:20] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:03:20] [信息] 环境: development
[2025-06-14 14:03:20] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:03:27] [信息] POST /api/payment/create
[2025-06-14 14:03:27] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:03:27] [信息] 生成订单号: R10001174988100725992tnoi, 长度: 25
[2025-06-14 14:03:27] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:03:27] [信息] 微信支付统一下单响应:
[2025-06-14 14:03:27] [错误] 微信支付统一下单失败:
[2025-06-14 14:03:27] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:03:56] [信息] 微信支付服务初始化成功
[2025-06-14 14:03:56] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:03:56] [信息] 初始化模型关联关系...
[2025-06-14 14:03:57] [信息] 模型关联关系初始化完成
[2025-06-14 14:03:57] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:03:57] [信息] 环境: development
[2025-06-14 14:03:57] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:04:11] [信息] 微信支付服务初始化成功
[2025-06-14 14:04:11] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:04:11] [信息] 初始化模型关联关系...
[2025-06-14 14:04:11] [信息] 模型关联关系初始化完成
[2025-06-14 14:04:11] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:04:11] [信息] 环境: development
[2025-06-14 14:04:11] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:04:45] [信息] 微信支付服务初始化成功
[2025-06-14 14:04:45] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:04:45] [信息] 初始化模型关联关系...
[2025-06-14 14:04:45] [信息] 模型关联关系初始化完成
[2025-06-14 14:04:45] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:04:45] [信息] 环境: development
[2025-06-14 14:04:45] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:04:51] [信息] POST /api/payment/create
[2025-06-14 14:04:51] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:04:51] [信息] 生成订单号: R1000117498810916067osbq6, 长度: 25
[2025-06-14 14:04:51] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:04:51] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值3000大米",
  "out_trade_no": "R1000117498810916067osbq6",
  "time_expire": "2025-06-14T06:34:51.618Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\"}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 2800,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:04:52] [信息] 微信支付统一下单响应:
[2025-06-14 14:04:52] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749881092",
    "nonceStr": "g62azg0ny6b",
    "package": "prepay_id=wx14140452673315616d3cbd8b28d7010000",
    "signType": "RSA",
    "paySign": "FPTkK44Up8RFToOcQUKP4g27q614KfB8Fg+kWevCz/JheAhvOUvP8VL/rl5AT67J2Y8ZwRohDH7KU/xe/D/D+hSU9bS2hv6y5pW9vLM8VTSaipG9scrGKJy7xiAou8ai1uc5lCn6mgbDvD2gZjfoEfWU36UDMqEWZQcFzIvJ2utRfsvfqcG1G4Gye6vPm/qNMT1L3W5mtFK+K+Vtque9S+ifY2eMb9RI/wls/WyxpxvsK8cr4KugfBW/Boo5crJB1DKoOgaji2ZnaHQZBgeWEChYhsD/lpg0d1uxuPSr75dR74XKWTsXkBzuyl/5mc4At8WSvSfe2Ay23n+YF7HIcw=="
  }
}
[2025-06-14 14:04:52] [错误] 微信支付统一下单失败:
[2025-06-14 14:04:52] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:05:46] [信息] 微信支付服务初始化成功
[2025-06-14 14:05:46] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:05:46] [信息] 初始化模型关联关系...
[2025-06-14 14:05:46] [信息] 模型关联关系初始化完成
[2025-06-14 14:05:46] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:05:46] [信息] 环境: development
[2025-06-14 14:05:46] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:06:21] [信息] 微信支付服务初始化成功
[2025-06-14 14:06:21] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:06:21] [信息] 初始化模型关联关系...
[2025-06-14 14:06:21] [信息] 模型关联关系初始化完成
[2025-06-14 14:06:21] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:06:21] [信息] 环境: development
[2025-06-14 14:06:21] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:06:30] [信息] POST /api/payment/create
[2025-06-14 14:06:30] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:06:30] [信息] 生成订单号: R100011749881190863u70gz5, 长度: 25
[2025-06-14 14:06:30] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:06:30] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值3000大米",
  "out_trade_no": "R100011749881190863u70gz5",
  "time_expire": "2025-06-14T06:36:30.881Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\"}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 2800,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:06:31] [信息] 微信支付统一下单响应:
[2025-06-14 14:06:31] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749881191",
    "nonceStr": "caet3rhh6x",
    "package": "prepay_id=wx14140631987814958ced93ba5f65e30000",
    "signType": "RSA",
    "paySign": "YW1iixMzfYCz7hUjCr6Om+HA6xYjwZC+8xQ6FL8rqNWrpDfhH5i+tbJhq42gTpvB1Ty4HqMjgIuvfdvoIAzE2h2X6ftnqiszWyF55SgHUV77YAtPz4izMsY1LFGCEA2IkV4ZrMHL3OOqXfRKLhKWos+ny5sdNnotwC4A+l7C0DfzBDSmkO2T3zRiQgHkDix7bWfM0Yd4lqt7R6DaBEZ6yWVB3ORD/FDZde9wZeXelPVz3P0lSRhGo+6gGvjR/PINl8vnoxnff0Uxm2tP5ciLdCVyybh8xU/BfIT3V8wtIxV1L+ObMr/atf5c/Omjp5TtGllMJzVhIgDulhWgn0eYTQ=="
  }
}
[2025-06-14 14:06:31] [信息] 成功获取prepay_id:
[2025-06-14 14:06:31] [信息] 用户 10001 创建支付订单成功，订单号: R100011749881190863u70gz5, 金额: 28元
[2025-06-14 14:07:20] [信息] GET /api/user/coins
[2025-06-14 14:07:20] [信息] GET /api/user/coins
[2025-06-14 14:07:24] [信息] POST /api/payment/create
[2025-06-14 14:07:24] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:07:24] [信息] 生成订单号: R1000117498812447900e60gu, 长度: 25
[2025-06-14 14:07:24] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:07:24] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值3000大米",
  "out_trade_no": "R1000117498812447900e60gu",
  "time_expire": "2025-06-14T06:37:24.791Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\"}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 2800,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:07:25] [信息] 微信支付统一下单响应:
[2025-06-14 14:07:25] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749881245",
    "nonceStr": "7qp13liw0ch",
    "package": "prepay_id=wx141407258257461cec0c270a68229e0001",
    "signType": "RSA",
    "paySign": "UMnk7EK7mqtp6WV+qaveAXhw25Ax0kmzyvr29aHSb+OzR77xQFlhdFQ9joV8cFVlweslbK05FPJRL6zfAGDae14U9NCeuTba3waIBPakfR1Rhi27i6cBGPTMOHcft6th/eLkHZqXAfgOWMiJSURnzucNhvyOAyEC5tiqyvAA6mkT2gUmRlWrAxrvNt+r7l34TAATxc4PtAhaf5AAFAUgR9DBuGrjPdx4+zUnhqqumXn21A+zEGQrx80C58t58T49K14ew31SHtayknFIhaChbY474WYPWUvlIHopKKNkuRJzj+RLjg7KWirz+MSW7kJWFLQYrHzxDC7pS0iN4zTenQ=="
  }
}
[2025-06-14 14:07:25] [信息] 成功获取prepay_id:
[2025-06-14 14:07:25] [信息] 用户 10001 创建支付订单成功，订单号: R1000117498812447900e60gu, 金额: 28元
[2025-06-14 14:10:04] [信息] GET /api/user/coins
[2025-06-14 14:10:04] [信息] GET /api/user/coins
[2025-06-14 14:10:24] [信息] GET /api/user/coins
[2025-06-14 14:10:24] [信息] GET /api/user/coins
[2025-06-14 14:10:42] [信息] GET /api/user/coins
[2025-06-14 14:10:42] [信息] GET /api/user/coins
[2025-06-14 14:11:00] [信息] GET /api/user/coins
[2025-06-14 14:11:00] [信息] GET /api/user/coins
[2025-06-14 14:12:28] [信息] GET /api/user/coins
[2025-06-14 14:12:28] [信息] GET /api/user/coins
[2025-06-14 14:12:53] [信息] GET /api/user/coins
[2025-06-14 14:12:53] [信息] GET /api/user/coins
[2025-06-14 14:13:30] [信息] GET /api/user/coins
[2025-06-14 14:13:30] [信息] GET /api/user/coins
[2025-06-14 14:25:05] [信息] POST /api/payment/create
[2025-06-14 14:25:05] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:25:05] [信息] 生成订单号: R100011749882305892y6ni5x, 长度: 25
[2025-06-14 14:25:05] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:25:05] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值100大米",
  "out_trade_no": "R100011749882305892y6ni5x",
  "time_expire": "2025-06-14T06:55:05.893Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\"}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 100,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:25:06] [信息] 微信支付统一下单响应:
[2025-06-14 14:25:06] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749882306",
    "nonceStr": "bx3kxhbhe18",
    "package": "prepay_id=wx14142506868323e50fc899452361240001",
    "signType": "RSA",
    "paySign": "bH4wL3Fr83cCa2asRKQwhMHrSzSfSqqHMe8CzVsHryP/RtD5pLrrJ8qB3z638GcDtc3lIQ7wPJonnM/VqQWejC5+P411e6Io1um38IB8IeTD0JvDdP/osEt7pqZTR2P0y/GFwuOHzDUadUnJVDurLVC0XyGJgx+e2OWMIY35dbwab8KebE2bdCY08HqOcYCRirIFkiEIsD21UmwEhsxIO38YAAIow2olAGHNIhPCuMdYyp0xsl2A+J8eYAirZUuI7aQB8SK3OdjD+vcwdqlUbrbfeRc3mJv9cKVR/nSucB8/BjB5XAw2BNwArLvIrONQlHyD4bq7+xczMAzz92KJeA=="
  }
}
[2025-06-14 14:25:06] [信息] 成功获取prepay_id:
[2025-06-14 14:25:06] [信息] 用户 10001 创建支付订单成功，订单号: R100011749882305892y6ni5x, 金额: 1元
[2025-06-14 14:25:27] [信息] GET /api/kitchen/list
[2025-06-14 14:25:27] [信息] 获取用户(ID:10001)的厨房列表
[2025-06-14 14:25:27] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 14:25:27] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 14:25:27] [信息] 用户(ID:10001)创建的厨房数量: 1
[2025-06-14 14:25:28] [信息] 用户(ID:10001)加入的厨房成员记录数量: 0
[2025-06-14 14:25:28] [信息] 用户(ID:10001)有效加入的厨房数量: 0
[2025-06-14 14:25:28] [信息] 用户(ID:10001)去重后的厨房数量: 1
[2025-06-14 14:25:28] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 14:25:28] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 14:25:28] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 14:25:28] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 14:25:28] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 14:25:28] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 14:25:29] [信息] GET /api/user/info
[2025-06-14 14:25:29] [信息] GET /api/user/membershipStatus
[2025-06-14 14:25:29] [信息] GET /api/user/membershipStatus
[2025-06-14 14:25:30] [信息] GET /api/user/coins
[2025-06-14 14:25:30] [信息] GET /api/user/coins
[2025-06-14 14:25:36] [信息] POST /api/payment/create
[2025-06-14 14:25:36] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:25:36] [信息] 生成订单号: R100011749882336027gscx7i, 长度: 25
[2025-06-14 14:25:36] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:25:36] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值100大米",
  "out_trade_no": "R100011749882336027gscx7i",
  "time_expire": "2025-06-14T06:55:36.027Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\"}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 100,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:25:36] [信息] 微信支付统一下单响应:
[2025-06-14 14:25:36] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749882336",
    "nonceStr": "ivvdlushl2",
    "package": "prepay_id=wx14142537001299aa50492f2c41ee6a0001",
    "signType": "RSA",
    "paySign": "CbKVEC0brkdCjM6aeXfABhQPpiiuwsGuzayKMlZb+hWCG0QiFuF4IAWJvxPgOP6kprq+5xhjhTKOIvxdQ4sTe4QjW6ZioemUFIpzFr8uGGP5lDNJ/pCbEpBHCOdkuOQFOmdT4mrOyP7n3DqJ0nCVJ28WskQ0i/ooCNocNUujhaJynNHwoK5jzeYmFh/m1pOhyYSdgx3nojmQzpgfNgCr8au4Dft8gYqSntY5Yk0vwBQX4jgABOYpRW1+yqzjVY/m6qnPLvxnlaxJIfAMpzsmiXXL+hGSgxOn3bWCqwR/Ar0rXegWf6gP0cMIL9VMmLjvO8dcqz+haR18DlXAx1AzlA=="
  }
}
[2025-06-14 14:25:36] [信息] 成功获取prepay_id:
[2025-06-14 14:25:36] [信息] 用户 10001 创建支付订单成功，订单号: R100011749882336027gscx7i, 金额: 1元
[2025-06-14 14:25:43] [信息] GET /api/user/coins
[2025-06-14 14:25:45] [信息] GET /api/payment/query/R100011749882336027gscx7i
[2025-06-14 14:25:45] [信息] 查询微信支付订单:
[2025-06-14 14:25:45] [信息] GET /api/user/coinRecords?type=all
[2025-06-14 14:25:46] [信息] 微信支付订单查询结果:
[2025-06-14 14:25:46] [信息] 查询订单状态: R100011749882336027gscx7i, 状态: undefined
[2025-06-14 14:25:51] [信息] GET /api/user/membershipStatus
[2025-06-14 14:25:51] [信息] GET /api/user/coins
[2025-06-14 14:25:52] [信息] GET /api/user/coins
[2025-06-14 14:25:52] [信息] GET /api/user/coinRecords?type=all
[2025-06-14 14:26:52] [信息] GET /api/user/coins
[2025-06-14 14:30:33] [信息] 微信支付服务初始化成功
[2025-06-14 14:30:33] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:30:33] [信息] 初始化模型关联关系...
[2025-06-14 14:30:34] [信息] 模型关联关系初始化完成
[2025-06-14 14:30:34] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:30:34] [信息] 环境: development
[2025-06-14 14:30:34] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:30:49] [信息] 微信支付服务初始化成功
[2025-06-14 14:30:50] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:30:50] [信息] 初始化模型关联关系...
[2025-06-14 14:30:50] [信息] 模型关联关系初始化完成
[2025-06-14 14:30:50] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:30:50] [信息] 环境: development
[2025-06-14 14:30:50] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:31:05] [信息] 微信支付服务初始化成功
[2025-06-14 14:31:06] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:31:06] [信息] 初始化模型关联关系...
[2025-06-14 14:31:06] [信息] 模型关联关系初始化完成
[2025-06-14 14:31:06] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:31:06] [信息] 环境: development
[2025-06-14 14:31:06] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:33:20] [信息] GET /api/user/coins
[2025-06-14 14:33:21] [信息] GET /api/user/coins
[2025-06-14 14:33:34] [信息] GET /api/user/coins
[2025-06-14 14:33:34] [信息] GET /api/user/coins
[2025-06-14 14:34:48] [信息] 微信支付服务初始化成功
[2025-06-14 14:34:48] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:34:48] [信息] 初始化模型关联关系...
[2025-06-14 14:34:48] [信息] 模型关联关系初始化完成
[2025-06-14 14:34:48] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:34:48] [信息] 环境: development
[2025-06-14 14:34:48] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:35:19] [信息] 微信支付服务初始化成功
[2025-06-14 14:35:19] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:35:19] [信息] 初始化模型关联关系...
[2025-06-14 14:35:19] [信息] 模型关联关系初始化完成
[2025-06-14 14:35:19] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:35:19] [信息] 环境: development
[2025-06-14 14:35:19] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:35:38] [信息] 微信支付服务初始化成功
[2025-06-14 14:35:38] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:35:38] [信息] 初始化模型关联关系...
[2025-06-14 14:35:38] [信息] 模型关联关系初始化完成
[2025-06-14 14:35:38] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:35:38] [信息] 环境: development
[2025-06-14 14:35:38] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:36:03] [信息] 微信支付服务初始化成功
[2025-06-14 14:36:03] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:36:03] [信息] 初始化模型关联关系...
[2025-06-14 14:36:03] [信息] 模型关联关系初始化完成
[2025-06-14 14:36:03] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:36:03] [信息] 环境: development
[2025-06-14 14:36:03] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:36:23] [信息] 微信支付服务初始化成功
[2025-06-14 14:36:23] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:36:23] [信息] 初始化模型关联关系...
[2025-06-14 14:36:23] [信息] 模型关联关系初始化完成
[2025-06-14 14:36:23] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:36:23] [信息] 环境: development
[2025-06-14 14:36:23] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:36:38] [信息] 微信支付服务初始化成功
[2025-06-14 14:36:38] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:36:38] [信息] 初始化模型关联关系...
[2025-06-14 14:36:38] [信息] 模型关联关系初始化完成
[2025-06-14 14:36:38] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:36:38] [信息] 环境: development
[2025-06-14 14:36:38] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:37:12] [信息] 微信支付服务初始化成功
[2025-06-14 14:37:12] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:37:12] [信息] 初始化模型关联关系...
[2025-06-14 14:37:12] [信息] 模型关联关系初始化完成
[2025-06-14 14:37:12] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:37:12] [信息] 环境: development
[2025-06-14 14:37:12] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:37:26] [信息] 微信支付服务初始化成功
[2025-06-14 14:37:26] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:37:26] [信息] 初始化模型关联关系...
[2025-06-14 14:37:26] [信息] 模型关联关系初始化完成
[2025-06-14 14:37:26] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:37:26] [信息] 环境: development
[2025-06-14 14:37:26] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:37:41] [信息] 微信支付服务初始化成功
[2025-06-14 14:37:42] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:37:42] [信息] 初始化模型关联关系...
[2025-06-14 14:37:42] [信息] 模型关联关系初始化完成
[2025-06-14 14:37:42] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:37:42] [信息] 环境: development
[2025-06-14 14:37:42] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:39:56] [信息] GET /api/user/coins
[2025-06-14 14:39:56] [信息] GET /api/user/coins
[2025-06-14 14:40:17] [信息] 微信支付服务初始化成功
[2025-06-14 14:40:17] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:40:17] [信息] 初始化模型关联关系...
[2025-06-14 14:40:17] [信息] 模型关联关系初始化完成
[2025-06-14 14:40:17] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:40:17] [信息] 环境: development
[2025-06-14 14:40:17] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:40:35] [信息] POST /api/payment/create
[2025-06-14 14:40:35] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:40:35] [信息] 生成订单号: R100011749883235562bx9hx0, 长度: 25
[2025-06-14 14:40:35] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:40:38] [信息] GET /api/user/coins
[2025-06-14 14:40:38] [信息] GET /api/user/coins
[2025-06-14 14:40:43] [信息] POST /api/payment/create
[2025-06-14 14:40:43] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:40:43] [信息] 生成订单号: R100011749883243985l3lkog, 长度: 25
[2025-06-14 14:40:43] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:41:03] [信息] POST /api/payment/create
[2025-06-14 14:41:03] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:41:03] [信息] 生成订单号: R100011749883263303pofwz2, 长度: 25
[2025-06-14 14:41:03] [错误] 请求处理错误: POST /api/payment/create
[2025-06-14 14:44:14] [信息] 微信支付服务初始化成功
[2025-06-14 14:44:14] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:44:14] [信息] 初始化模型关联关系...
[2025-06-14 14:44:14] [信息] 模型关联关系初始化完成
[2025-06-14 14:44:14] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:44:14] [信息] 环境: development
[2025-06-14 14:44:14] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:46:49] [信息] 微信支付服务初始化成功
[2025-06-14 14:46:49] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:46:49] [信息] 初始化模型关联关系...
[2025-06-14 14:46:49] [信息] 模型关联关系初始化完成
[2025-06-14 14:46:49] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:46:49] [信息] 环境: development
[2025-06-14 14:46:49] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:46:57] [信息] POST /api/payment/create
[2025-06-14 14:46:57] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:46:57] [信息] 生成订单号: R100011749883617076hwnc3v, 长度: 25
[2025-06-14 14:46:57] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:46:57] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "测试充值10大米",
  "out_trade_no": "R100011749883617076hwnc3v",
  "time_expire": "2025-06-14T07:16:57.168Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\",\"paymentOrderId\":7}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 10,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:46:57] [信息] 微信支付统一下单响应:
[2025-06-14 14:46:57] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749883617",
    "nonceStr": "o7khe4xqvm",
    "package": "prepay_id=wx14144658187839dd4175a8f510d0730001",
    "signType": "RSA",
    "paySign": "bEycXGxCzc7txWUmnomrrm/YlZB1zVMydErotl6bRpP2/RQPM1ESqkSCCCXPRgsm2rGqrlTWKuvtg3+QkJX/VdvpnAu+73qglm+ETPmb+/menQHNXwz7bBISp9C3H9oHdnjPI53+DryjPDTl/R8cE7ndlf+WXzhhq8SGBC1xmpOgy9ofTHiFOjZRrVpt+vr5EIkXYB2v/jjxv7+D4zIeS1fZ601QJEe5/dDOqB1tahLuIN+9JD+iFeEb5XbMTJgYOeN2xjM6Ofm2TthMjYZXGlKXKdl4scha3LS78/+q1KfkXmXllSXeD+Dy+iRi9zKjHu4a+Hhbj5LSNjKY7PIXqA=="
  }
}
[2025-06-14 14:46:57] [信息] 成功获取prepay_id:
[2025-06-14 14:46:57] [信息] 用户 10001 创建支付订单成功，订单号: R100011749883617076hwnc3v, 金额: 0.1元, 大米: 10
[2025-06-14 14:46:57] [信息] POST /api/payment/confirm
[2025-06-14 14:46:57] [信息] 手动确认支付成功，用户: 10001, 订单号: R100011749883617076hwnc3v, 大米: 10
[2025-06-14 14:50:13] [信息] POST /api/payment/create
[2025-06-14 14:50:13] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 14:50:13] [信息] 生成订单号: R100011749883813684sjvnxw, 长度: 25
[2025-06-14 14:50:13] [信息] 微信支付统一下单请求参数:
[2025-06-14 14:50:13] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值10大米",
  "out_trade_no": "R100011749883813684sjvnxw",
  "time_expire": "2025-06-14T07:20:13.755Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\",\"paymentOrderId\":8}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 10,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 14:50:14] [信息] 微信支付统一下单响应:
[2025-06-14 14:50:14] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749883814",
    "nonceStr": "4pov3kqwj9x",
    "package": "prepay_id=wx141450147398295872ae48f93222380001",
    "signType": "RSA",
    "paySign": "Porzq8qai28peEd71EaOchY+CHGUC7k8q0qDuWjX+1+53Ktr66sK+QjFzTJsceqntyKOw8XlGKC+0P1PjG24hsBqRDLpu5Q1oTXt+iyqBlW887Ayuy5fXHu36VOcsYQosqn5ErUofIpIap3RbVPIxqSzKqXTIPVebh7nxzNe5vTN5Db3W6T3ICxmNWkyZLGdEM2+50vRRgjPp8UhMUP/pcU8vH25Uc9IM2xyK3V9TNsOrU+ERigAkE/37byb551gtAyiwRZv3VtVOOem6RL0DOoPNFenES4+axgaFE46tGA6jY3Y8qQEqYbZqIPcuibOkChMeNDGkzltTzIXsxdKsg=="
  }
}
[2025-06-14 14:50:14] [信息] 成功获取prepay_id:
[2025-06-14 14:50:14] [信息] 用户 10001 创建支付订单成功，订单号: R100011749883813684sjvnxw, 金额: 0.1元, 大米: 10
[2025-06-14 14:50:46] [信息] GET /api/payment/query/R100011749883813684sjvnxw
[2025-06-14 14:50:46] [信息] 查询微信支付订单:
[2025-06-14 14:50:46] [信息] GET /api/user/coinRecords?type=all
[2025-06-14 14:50:46] [信息] 微信支付订单查询结果:
[2025-06-14 14:50:46] [信息] 查询订单状态: R100011749883813684sjvnxw, 状态: undefined
[2025-06-14 14:51:34] [信息] GET /api/user/coins
[2025-06-14 14:51:42] [信息] GET /api/kitchen/list
[2025-06-14 14:51:42] [信息] 获取用户(ID:10001)的厨房列表
[2025-06-14 14:51:42] [信息] 用户(ID:10001)创建的厨房数量: 1
[2025-06-14 14:51:42] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] 用户(ID:10001)加入的厨房成员记录数量: 0
[2025-06-14 14:51:42] [信息] 用户(ID:10001)有效加入的厨房数量: 0
[2025-06-14 14:51:42] [信息] 用户(ID:10001)去重后的厨房数量: 1
[2025-06-14 14:51:42] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 14:51:42] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 14:51:43] [信息] GET /api/user/info
[2025-06-14 14:51:43] [信息] GET /api/user/membershipStatus
[2025-06-14 14:51:43] [信息] GET /api/user/membershipStatus
[2025-06-14 14:51:47] [信息] GET /api/user/signInData
[2025-06-14 14:51:47] [信息] GET /api/user/signInData
[2025-06-14 14:54:29] [信息] 微信支付服务初始化成功
[2025-06-14 14:54:29] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:54:29] [信息] 初始化模型关联关系...
[2025-06-14 14:54:29] [信息] 模型关联关系初始化完成
[2025-06-14 14:54:29] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:54:29] [信息] 环境: development
[2025-06-14 14:54:29] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:55:03] [信息] 微信支付服务初始化成功
[2025-06-14 14:55:03] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:55:03] [信息] 初始化模型关联关系...
[2025-06-14 14:55:03] [信息] 模型关联关系初始化完成
[2025-06-14 14:55:03] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:55:03] [信息] 环境: development
[2025-06-14 14:55:03] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:55:04] [信息] GET /api/admin/users?page=1&pageSize=10
[2025-06-14 14:55:04] [警告] 管理员令牌解析失败: jwt expired
[2025-06-14 14:55:04] [信息] GET /api/admin/users/stats
[2025-06-14 14:55:04] [警告] 管理员令牌解析失败: jwt expired
[2025-06-14 14:55:04] [信息] GET /api/admin/users?page=1&pageSize=10
[2025-06-14 14:55:04] [警告] 管理员令牌解析失败: jwt expired
[2025-06-14 14:55:04] [信息] GET /api/admin/users/stats
[2025-06-14 14:55:04] [警告] 管理员令牌解析失败: jwt expired
[2025-06-14 14:55:06] [信息] POST /api/admin/login
[2025-06-14 14:55:06] [信息] 管理员登录成功: superadmin
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/stats
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/charts
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/recent-orders
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/recent-orders
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/stats
[2025-06-14 14:55:06] [信息] GET /api/admin/dashboard/charts
[2025-06-14 14:55:16] [信息] 微信支付服务初始化成功
[2025-06-14 14:55:16] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:55:16] [信息] 初始化模型关联关系...
[2025-06-14 14:55:16] [信息] 模型关联关系初始化完成
[2025-06-14 14:55:16] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:55:16] [信息] 环境: development
[2025-06-14 14:55:16] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:55:18] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:18] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:18] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:18] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:22] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:22] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:22] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:22] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:23] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:23] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:23] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:23] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:24] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:24] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:24] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 14:55:24] [信息] GET /api/admin/comments/stats
[2025-06-14 14:55:55] [信息] 微信支付服务初始化成功
[2025-06-14 14:55:55] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:55:55] [信息] 初始化模型关联关系...
[2025-06-14 14:55:55] [信息] 模型关联关系初始化完成
[2025-06-14 14:55:55] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:55:55] [信息] 环境: development
[2025-06-14 14:55:55] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:58:06] [信息] 微信支付服务初始化成功
[2025-06-14 14:58:06] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:58:06] [信息] 初始化模型关联关系...
[2025-06-14 14:58:06] [信息] 模型关联关系初始化完成
[2025-06-14 14:58:06] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:58:06] [信息] 环境: development
[2025-06-14 14:58:06] [信息] API服务就绪，包含管理员接口
[2025-06-14 14:58:22] [信息] 微信支付服务初始化成功
[2025-06-14 14:58:22] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 14:58:22] [信息] 初始化模型关联关系...
[2025-06-14 14:58:22] [信息] 模型关联关系初始化完成
[2025-06-14 14:58:22] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 14:58:22] [信息] 环境: development
[2025-06-14 14:58:22] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:00:09] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 15:00:09] [信息] GET /api/admin/comments/stats
[2025-06-14 15:00:09] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 15:00:09] [信息] GET /api/admin/comments/stats
[2025-06-14 15:00:13] [信息] GET /api/admin/ads/configs?page=1&pageSize=10
[2025-06-14 15:00:13] [信息] GET /api/admin/ads/configs
[2025-06-14 15:00:13] [信息] GET /api/admin/tasks/ads/stats
[2025-06-14 15:00:13] [错误] 请求处理错误: GET /api/admin/ads/configs?page=1&pageSize=10
[2025-06-14 15:00:13] [错误] 请求处理错误: GET /api/admin/ads/configs
[2025-06-14 15:00:13] [信息] GET /api/admin/ads/configs?page=1&pageSize=10
[2025-06-14 15:00:13] [信息] GET /api/admin/ads/configs
[2025-06-14 15:00:13] [信息] GET /api/admin/tasks/ads/stats
[2025-06-14 15:00:13] [错误] 请求处理错误: GET /api/admin/ads/configs?page=1&pageSize=10
[2025-06-14 15:00:13] [错误] 请求处理错误: GET /api/admin/ads/configs
[2025-06-14 15:00:45] [信息] 微信支付服务初始化成功
[2025-06-14 15:00:45] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:00:45] [信息] 初始化模型关联关系...
[2025-06-14 15:00:45] [信息] 模型关联关系初始化完成
[2025-06-14 15:00:45] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:00:45] [信息] 环境: development
[2025-06-14 15:00:45] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:01:37] [信息] 微信支付服务初始化成功
[2025-06-14 15:01:37] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:01:37] [信息] 初始化模型关联关系...
[2025-06-14 15:01:37] [信息] 模型关联关系初始化完成
[2025-06-14 15:01:37] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:01:37] [信息] 环境: development
[2025-06-14 15:01:37] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:01:45] [信息] GET /api/user/signInData
[2025-06-14 15:01:45] [信息] GET /api/user/signInData
[2025-06-14 15:01:59] [信息] GET /api/user/signInData
[2025-06-14 15:02:13] [信息] GET /api/user/signInData
[2025-06-14 15:02:35] [信息] GET /api/user/signInData
[2025-06-14 15:02:35] [信息] GET /api/user/signInData
[2025-06-14 15:02:52] [信息] GET /api/user/signInData
[2025-06-14 15:02:52] [信息] GET /api/user/signInData
[2025-06-14 15:03:11] [信息] GET /api/user/signInData
[2025-06-14 15:03:11] [信息] GET /api/user/signInData
[2025-06-14 15:03:28] [信息] GET /api/user/signInData
[2025-06-14 15:03:28] [信息] GET /api/user/signInData
[2025-06-14 15:04:54] [信息] 微信支付服务初始化成功
[2025-06-14 15:04:54] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:04:54] [信息] 初始化模型关联关系...
[2025-06-14 15:04:54] [信息] 模型关联关系初始化完成
[2025-06-14 15:04:54] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:04:54] [信息] 环境: development
[2025-06-14 15:04:54] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:05:12] [信息] GET /api/user/signInData
[2025-06-14 15:05:12] [信息] GET /api/user/signInData
[2025-06-14 15:05:14] [信息] POST /api/user/signIn
[2025-06-14 15:08:13] [信息] GET /api/system/comment-enabled
[2025-06-14 15:08:27] [信息] GET /api/admin/comments/stats
[2025-06-14 15:08:27] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 15:08:27] [信息] GET /api/admin/comments?page=1&pageSize=10
[2025-06-14 15:08:27] [信息] GET /api/admin/comments/stats
[2025-06-14 15:08:31] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:08:31] [信息] GET /api/admin/roles
[2025-06-14 15:08:31] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:08:31] [信息] GET /api/admin/roles
[2025-06-14 15:08:32] [信息] GET /api/system/settings
[2025-06-14 15:08:34] [信息] POST /api/system/setting
[2025-06-14 15:08:34] [信息] 更新系统设置: comment_enabled = false
[2025-06-14 15:08:57] [信息] GET /api/user/membershipStatus
[2025-06-14 15:09:01] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:01] [信息] GET /api/discover/list?page=1&pageSize=10&sortType=random&searchValue=
[2025-06-14 15:09:01] [信息] 发现页面请求: userId=10001, page=1, pageSize=10, sortType=random, searchValue=
[2025-06-14 15:09:01] [信息] 查询到发现项数量: 0
[2025-06-14 15:09:01] [信息] 处理后的发现项数量: 0
[2025-06-14 15:09:01] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:09:01] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:01] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:09:04] [信息] GET /api/discover/list?page=1&pageSize=10&sortType=random&searchValue=
[2025-06-14 15:09:04] [信息] 发现页面请求: userId=10001, page=1, pageSize=10, sortType=random, searchValue=
[2025-06-14 15:09:04] [信息] 查询到发现项数量: 0
[2025-06-14 15:09:04] [信息] 处理后的发现项数量: 0
[2025-06-14 15:09:06] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:09:06] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:06] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:09:06] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:09:11] [信息] GET /api/user/membershipStatus
[2025-06-14 15:09:12] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:09:16] [信息] GET /api/admin/dishes?page=1&pageSize=20
[2025-06-14 15:09:16] [信息] GET /api/admin/dishes/stats
[2025-06-14 15:09:16] [信息] GET /api/admin/dish-categories
[2025-06-14 15:09:16] [信息] GET /api/admin/dish-categories
[2025-06-14 15:09:16] [信息] GET /api/admin/dishes?page=1&pageSize=20
[2025-06-14 15:09:16] [信息] GET /api/admin/dishes/stats
[2025-06-14 15:09:19] [信息] GET /api/admin/orders?page=1&pageSize=10
[2025-06-14 15:09:19] [信息] GET /api/admin/orders/stats
[2025-06-14 15:09:19] [信息] GET /api/admin/orders?page=1&pageSize=10
[2025-06-14 15:09:19] [信息] GET /api/admin/orders/stats
[2025-06-14 15:09:19] [信息] GET /api/admin/dishes?page=1&pageSize=20
[2025-06-14 15:09:19] [信息] GET /api/admin/dishes/stats
[2025-06-14 15:09:19] [信息] GET /api/admin/dish-categories
[2025-06-14 15:09:19] [信息] GET /api/admin/dish-categories
[2025-06-14 15:09:19] [信息] GET /api/admin/dishes?page=1&pageSize=20
[2025-06-14 15:09:19] [信息] GET /api/admin/dishes/stats
[2025-06-14 15:09:39] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:09:39] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:39] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:09:39] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:09:43] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:43] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:52] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:09:52] [信息] POST /api/upload/dishImage
[2025-06-14 15:10:15] [警告] 文件大小超过限制: 5MB
[2025-06-14 15:10:15] [错误] 请求处理错误: POST /api/upload/dishImage
[2025-06-14 15:10:21] [信息] GET /api/kitchen/list
[2025-06-14 15:10:21] [信息] 获取用户(ID:10001)的厨房列表
[2025-06-14 15:10:21] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:10:21] [信息] 用户(ID:10001)创建的厨房数量: 1
[2025-06-14 15:10:21] [信息] 用户(ID:10001)加入的厨房成员记录数量: 0
[2025-06-14 15:10:21] [信息] 用户(ID:10001)有效加入的厨房数量: 0
[2025-06-14 15:10:21] [信息] 用户(ID:10001)去重后的厨房数量: 1
[2025-06-14 15:10:21] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:21] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:10:21] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:21] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:10:22] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:10:22] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:10:22] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:10:24] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:24] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:32] [信息] POST /api/upload/dishImage
[2025-06-14 15:10:32] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:32] [信息] 图片压缩成功: C:\Users\<USER>\WeChatProjects\miniprogram-20\server\uploads\temp\temp_1749885032561_kyyskarry.jpg -> C:\Users\<USER>\WeChatProjects\miniprogram-20\server\uploads\dish\10001_ULY17P_DISHK_OES1M9.jpg
[2025-06-14 15:10:32] [信息] 文件上传并压缩成功: 10001_ULY17P_DISHK_OES1M9.jpg
[2025-06-14 15:10:44] [信息] POST /api/dish/add
[2025-06-14 15:10:46] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:10:46] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:10:46] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:10:46] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:10:47] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:10:47] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:10:54] [信息] GET /api/system/comment-enabled
[2025-06-14 15:10:55] [信息] GET /api/system/settings
[2025-06-14 15:10:55] [警告] 未提供管理员认证令牌
[2025-06-14 15:10:55] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:10:55] [信息] GET /api/admin/roles
[2025-06-14 15:10:55] [信息] GET /api/system/comment-enabled
[2025-06-14 15:10:55] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:10:55] [信息] GET /api/admin/roles
[2025-06-14 15:10:56] [信息] GET /api/system/settings
[2025-06-14 15:10:59] [信息] POST /api/system/setting
[2025-06-14 15:10:59] [信息] 更新系统设置: comment_enabled = false
[2025-06-14 15:11:02] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:02] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:02] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:02] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:11:02] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:02] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:04] [信息] POST /api/system/setting
[2025-06-14 15:11:04] [信息] 更新系统设置: comment_enabled = true
[2025-06-14 15:11:05] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:05] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:05] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:05] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:11:06] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:06] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:07] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:07] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:07] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:07] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:11:15] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:15] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:16] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:16] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:16] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:16] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:11:18] [信息] POST /api/system/setting
[2025-06-14 15:11:18] [信息] 更新系统设置: comment_enabled = false
[2025-06-14 15:11:18] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:18] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:19] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:19] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:20] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:20] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:11:20] [信息] POST /api/system/setting
[2025-06-14 15:11:20] [信息] 更新系统设置: comment_enabled = true
[2025-06-14 15:11:20] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:20] [信息] GET /api/dish/detail?id=1&kitchenId=ULY17P
[2025-06-14 15:11:22] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:11:22] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:11:22] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:11:22] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:12:39] [信息] GET /api/user/membershipStatus
[2025-06-14 15:12:39] [信息] GET /api/user/info
[2025-06-14 15:12:39] [信息] GET /api/user/membershipStatus
[2025-06-14 15:12:42] [信息] GET /api/order/list?status=all&orderType=kitchen&page=1&pageSize=10&kitchenId=ULY17P
[2025-06-14 15:12:42] [信息] GET /api/order/list?status=all&orderType=kitchen&page=1&pageSize=10&kitchenId=ULY17P
[2025-06-14 15:12:42] [信息] GET /api/order/list?status=all&orderType=kitchen&page=1&pageSize=10&kitchenId=ULY17P
[2025-06-14 15:12:43] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:12:43] [信息] GET /api/discover/list?page=1&pageSize=10&sortType=random&searchValue=
[2025-06-14 15:12:43] [信息] 发现页面请求: userId=10001, page=1, pageSize=10, sortType=random, searchValue=
[2025-06-14 15:12:43] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:12:43] [信息] 查询到发现项数量: 0
[2025-06-14 15:12:43] [信息] 处理后的发现项数量: 0
[2025-06-14 15:12:43] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:12:43] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:12:44] [信息] GET /api/message/messageCount
[2025-06-14 15:12:44] [信息] GET /api/message/tabContent?type=system&page=1&pageSize=10
[2025-06-14 15:12:45] [信息] GET /api/message/tabContent?type=like&page=1&pageSize=10
[2025-06-14 15:12:46] [信息] GET /api/message/tabContent?type=comment&page=1&pageSize=10
[2025-06-14 15:12:47] [信息] GET /api/message/tabContent?type=all&page=1&pageSize=10
[2025-06-14 15:12:47] [信息] GET /api/message/tabContent?type=system&page=1&pageSize=10
[2025-06-14 15:12:48] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:12:48] [信息] GET /api/user/membershipStatus
[2025-06-14 15:12:49] [信息] GET /api/discover/unreadCounts
[2025-06-14 15:12:57] [信息] GET /api/user/membershipStatus
[2025-06-14 15:12:59] [信息] GET /api/user/coins
[2025-06-14 15:12:59] [信息] GET /api/user/coins
[2025-06-14 15:13:02] [信息] GET /api/user/signInData
[2025-06-14 15:13:02] [信息] GET /api/user/signInData
[2025-06-14 15:13:14] [信息] 微信支付服务初始化成功
[2025-06-14 15:13:14] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:13:14] [信息] 初始化模型关联关系...
[2025-06-14 15:13:14] [信息] 模型关联关系初始化完成
[2025-06-14 15:13:14] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:13:14] [信息] 环境: development
[2025-06-14 15:13:14] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:13:16] [信息] GET /api/user/coins
[2025-06-14 15:13:17] [信息] GET /api/user/membershipStatus
[2025-06-14 15:13:18] [信息] GET /api/user/signInData
[2025-06-14 15:13:18] [信息] GET /api/user/signInData
[2025-06-14 15:13:19] [信息] GET /api/user/membershipStatus
[2025-06-14 15:13:38] [信息] 微信支付服务初始化成功
[2025-06-14 15:13:38] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:13:38] [信息] 初始化模型关联关系...
[2025-06-14 15:13:38] [信息] 模型关联关系初始化完成
[2025-06-14 15:13:38] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:13:38] [信息] 环境: development
[2025-06-14 15:13:38] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:13:51] [信息] 微信支付服务初始化成功
[2025-06-14 15:13:51] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:13:51] [信息] 初始化模型关联关系...
[2025-06-14 15:13:51] [信息] 模型关联关系初始化完成
[2025-06-14 15:13:51] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:13:51] [信息] 环境: development
[2025-06-14 15:13:51] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:14:10] [信息] 微信支付服务初始化成功
[2025-06-14 15:14:10] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:14:10] [信息] 初始化模型关联关系...
[2025-06-14 15:14:10] [信息] 模型关联关系初始化完成
[2025-06-14 15:14:10] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:14:10] [信息] 环境: development
[2025-06-14 15:14:10] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:19:01] [信息] 微信支付服务初始化成功
[2025-06-14 15:19:01] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:19:01] [信息] 初始化模型关联关系...
[2025-06-14 15:19:01] [信息] 模型关联关系初始化完成
[2025-06-14 15:19:01] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:19:01] [信息] 环境: development
[2025-06-14 15:19:01] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:19:24] [信息] 微信支付服务初始化成功
[2025-06-14 15:19:24] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:19:24] [信息] 初始化模型关联关系...
[2025-06-14 15:19:24] [信息] 模型关联关系初始化完成
[2025-06-14 15:19:24] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:19:24] [信息] 环境: development
[2025-06-14 15:19:24] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:19:41] [信息] 微信支付服务初始化成功
[2025-06-14 15:19:41] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:19:41] [信息] 初始化模型关联关系...
[2025-06-14 15:19:41] [信息] 模型关联关系初始化完成
[2025-06-14 15:19:41] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:19:41] [信息] 环境: development
[2025-06-14 15:19:41] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:20:14] [信息] 微信支付服务初始化成功
[2025-06-14 15:20:14] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:20:14] [信息] 初始化模型关联关系...
[2025-06-14 15:20:14] [信息] 模型关联关系初始化完成
[2025-06-14 15:20:14] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:20:14] [信息] 环境: development
[2025-06-14 15:20:14] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:20:36] [信息] 微信支付服务初始化成功
[2025-06-14 15:20:36] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:20:36] [信息] 初始化模型关联关系...
[2025-06-14 15:20:36] [信息] 模型关联关系初始化完成
[2025-06-14 15:20:36] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:20:36] [信息] 环境: development
[2025-06-14 15:20:36] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:20:53] [信息] 微信支付服务初始化成功
[2025-06-14 15:20:53] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:20:53] [信息] 初始化模型关联关系...
[2025-06-14 15:20:53] [信息] 模型关联关系初始化完成
[2025-06-14 15:20:53] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:20:53] [信息] 环境: development
[2025-06-14 15:20:53] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:21:34] [信息] 微信支付服务初始化成功
[2025-06-14 15:21:34] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:21:34] [信息] 初始化模型关联关系...
[2025-06-14 15:21:34] [信息] 模型关联关系初始化完成
[2025-06-14 15:21:34] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:21:34] [信息] 环境: development
[2025-06-14 15:21:34] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:22:08] [信息] 微信支付服务初始化成功
[2025-06-14 15:22:08] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:22:08] [信息] 初始化模型关联关系...
[2025-06-14 15:22:08] [信息] 模型关联关系初始化完成
[2025-06-14 15:22:08] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:22:08] [信息] 环境: development
[2025-06-14 15:22:08] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:22:25] [信息] 微信支付服务初始化成功
[2025-06-14 15:22:25] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:22:25] [信息] 初始化模型关联关系...
[2025-06-14 15:22:25] [信息] 模型关联关系初始化完成
[2025-06-14 15:22:25] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:22:25] [信息] 环境: development
[2025-06-14 15:22:25] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:22:46] [信息] 微信支付服务初始化成功
[2025-06-14 15:22:46] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:22:46] [信息] 初始化模型关联关系...
[2025-06-14 15:22:46] [信息] 模型关联关系初始化完成
[2025-06-14 15:22:46] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:22:46] [信息] 环境: development
[2025-06-14 15:22:46] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:23:43] [信息] 微信支付服务初始化成功
[2025-06-14 15:23:43] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:23:43] [信息] 初始化模型关联关系...
[2025-06-14 15:23:43] [信息] 模型关联关系初始化完成
[2025-06-14 15:23:43] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:23:43] [信息] 环境: development
[2025-06-14 15:23:43] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:23:46] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:23:46] [信息] GET /api/admin/roles
[2025-06-14 15:23:46] [信息] GET /api/admin/admins?page=1&pageSize=10
[2025-06-14 15:23:46] [信息] GET /api/admin/roles
[2025-06-14 15:23:55] [信息] 微信支付服务初始化成功
[2025-06-14 15:23:55] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:23:55] [信息] 初始化模型关联关系...
[2025-06-14 15:23:55] [信息] 模型关联关系初始化完成
[2025-06-14 15:23:55] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:23:55] [信息] 环境: development
[2025-06-14 15:23:55] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:24:10] [信息] 微信支付服务初始化成功
[2025-06-14 15:24:10] [信息] 数据库连接成功: restaurant_menu_db
[2025-06-14 15:24:10] [信息] 初始化模型关联关系...
[2025-06-14 15:24:10] [信息] 模型关联关系初始化完成
[2025-06-14 15:24:10] [信息] 服务器启动成功，监听端口: 3000
[2025-06-14 15:24:10] [信息] 环境: development
[2025-06-14 15:24:10] [信息] API服务就绪，包含管理员接口
[2025-06-14 15:26:10] [信息] GET /api/kitchen/baseInfo?kitchenId=ULY17P
[2025-06-14 15:26:10] [信息] GET /api/dish/categories?kitchenId=ULY17P
[2025-06-14 15:26:10] [信息] GET /api/cart/list?kitchenId=ULY17P
[2025-06-14 15:26:10] [信息] GET /api/dish/list?categoryId=&kitchenId=ULY17P
[2025-06-14 15:26:12] [信息] GET /api/user/membershipStatus
[2025-06-14 15:26:15] [信息] GET /api/user/membershipStatus
[2025-06-14 15:26:17] [信息] POST /api/user/subscribeMembership
[2025-06-14 15:26:19] [信息] GET /api/user/info
[2025-06-14 15:26:20] [信息] GET /api/user/membershipStatus
[2025-06-14 15:26:21] [信息] GET /api/user/coins
[2025-06-14 15:26:21] [信息] GET /api/user/coins
[2025-06-14 15:26:26] [信息] POST /api/payment/create
[2025-06-14 15:26:26] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 15:26:26] [信息] 生成订单号: R100011749885986556kwh1iw, 长度: 25
[2025-06-14 15:26:26] [信息] 微信支付统一下单请求参数:
[2025-06-14 15:26:26] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值3000大米",
  "out_trade_no": "R100011749885986556kwh1iw",
  "time_expire": "2025-06-14T07:56:26.568Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\",\"paymentOrderId\":9}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 2800,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 15:26:27] [信息] 微信支付统一下单响应:
[2025-06-14 15:26:27] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749885987",
    "nonceStr": "j0wxlzl0dud",
    "package": "prepay_id=wx1415262755692238cbc67fc6bc5f770001",
    "signType": "RSA",
    "paySign": "BmmZbiAGFUpwjkISfz8NrA274sVExoirVwZQaSrOgGTlIiyX2Rta6EWmU0jSAtT1BP1Yn1aLHNbqi5o0bRgTO9+QVC+Zuc85gDNZjTyrlOSlcpMWkdoAoQO0TEg1OjRnTJyoZ0FhTfy2rEX6TTcPV5EWOjcWmXdDyYfIxGGq6Y7/5Eaa4STlU05qvc5F2DhK2xVAPsap6TdWgaN5mdNjdpkAYcdrKowMOtooXATl8o64rwC3tt3UM2HZFcfgq8e73+iuq7WeQ7l09dvyNcIinRUD3eFhbbsCAnOa8Xk6sNy+btOa1Mx8hmx6VALjAuyFvYceOldX3KYSVFo0fRw4Iw=="
  }
}
[2025-06-14 15:26:27] [信息] 成功获取prepay_id:
[2025-06-14 15:26:27] [信息] 用户 10001 创建支付订单成功，订单号: R100011749885986556kwh1iw, 金额: 28元, 大米: 2800
[2025-06-14 15:26:57] [信息] POST /api/payment/create
[2025-06-14 15:26:58] [信息] 获取用户信息: userId=10001, openid=oBQ2m7Vs***
[2025-06-14 15:26:58] [信息] 生成订单号: R100011749886018005l30yca, 长度: 25
[2025-06-14 15:26:58] [信息] 微信支付统一下单请求参数:
[2025-06-14 15:26:58] [信息] {
  "appid": "wx41b8de9ea1e51474",
  "mchid": "1719611095",
  "description": "充值3000大米",
  "out_trade_no": "R100011749886018005l30yca",
  "time_expire": "2025-06-14T07:56:58.089Z",
  "attach": "{\"userId\":10001,\"type\":\"recharge\",\"paymentOrderId\":10}",
  "notify_url": "https://dzcdd.zj1.natnps.cn/api/payment/notify",
  "amount": {
    "total": 2800,
    "currency": "CNY"
  },
  "payer": {
    "openid": "oBQ2m7VsJlkXO6xc5ZEsflFcNPbg"
  }
}
[2025-06-14 15:26:58] [信息] 微信支付统一下单响应:
[2025-06-14 15:26:58] [信息] {
  "status": 200,
  "data": {
    "appId": "wx41b8de9ea1e51474",
    "timeStamp": "1749886018",
    "nonceStr": "m065vcwkqyk",
    "package": "prepay_id=wx14152659065574628082529f84c2640000",
    "signType": "RSA",
    "paySign": "zGwUu51TsM48saDXRwWd9jU0hf+rZC3LsKfV+RmNYnPZxGfH7UyQIVAvAdVFG1Rw3Cpp/aSsAQPrkPNgvEXwgMXPJajHmksednrGtmwzd6vOXl6RZz+etOQgVBLt7JwQk6YDwfhLl899CAFWtufCezzglWQjgqcuuPl4+pp4RWJ+OH3TeplIQZkiLCNYnORmB23pwG+vy/hyei6PvmzzT91oTdr9GlfPpw4E39mjHexvx0MQVgIjsk8MfVUGqcRA4wUH/ycPZQCg113FeSXbcw4xuyOQ3lfPfm6V5XZ+niLRhMkK295yK3dP2jutj2DRHoWkX/xUte5OXTHjf1dFqQ=="
  }
}
[2025-06-14 15:26:58] [信息] 成功获取prepay_id:
[2025-06-14 15:26:58] [信息] 用户 10001 创建支付订单成功，订单号: R100011749886018005l30yca, 金额: 28元, 大米: 2800
