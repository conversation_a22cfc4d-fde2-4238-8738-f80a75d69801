"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isKitchenMember = exports.isKitchenOwner = exports.optionalAuth = exports.verifyAdminToken = exports.verifyToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../config/config"));
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../utils/logger"));
/**
 * 验证JWT令牌
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const verifyToken = (req, res, next) => {
    // 从请求头获取token
    let token = req.headers.authorization || req.headers.auth;
    // 检查请求头中是否有token
    if (!token) {
        logger_1.default.warn('未提供认证令牌');
        return (0, response_1.unauthorized)(res, '请先登录');
    }
    try {
        // 去除Bearer前缀（如果有）
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }
        // 记录token长度（不记录具体内容）
        logger_1.default.debug(`接收到token(长度:${token.length})`);
        // 验证token
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
            req.user = decoded;
            logger_1.default.debug('用户认证成功', { userId: decoded.id });
            next();
        }
        catch (jwtError) {
            logger_1.default.warn(`令牌解析失败: ${jwtError instanceof Error ? jwtError.message : '未知错误'}`);
            return (0, response_1.unauthorized)(res, '登录已过期，请重新登录');
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        logger_1.default.warn(`令牌验证失败: ${errorMessage}`);
        return (0, response_1.unauthorized)(res, '登录验证失败');
    }
};
exports.verifyToken = verifyToken;
/**
 * 验证管理员JWT令牌
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const verifyAdminToken = (req, res, next) => {
    // 从请求头获取token
    let token = req.headers.authorization || req.headers.auth;
    // 检查请求头中是否有token
    if (!token) {
        logger_1.default.warn('未提供管理员认证令牌');
        return (0, response_1.unauthorized)(res, '请先登录管理员账户');
    }
    try {
        // 去除Bearer前缀（如果有）
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }
        // 验证token
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
            // 检查是否为管理员token
            if (!decoded.type || decoded.type !== 'admin') {
                logger_1.default.warn('尝试使用非管理员令牌访问管理员接口');
                return (0, response_1.forbidden)(res, '权限不足，需要管理员权限');
            }
            req.user = decoded;
            logger_1.default.debug('管理员认证成功', { adminId: decoded.id });
            next();
        }
        catch (jwtError) {
            logger_1.default.warn(`管理员令牌解析失败: ${jwtError instanceof Error ? jwtError.message : '未知错误'}`);
            return (0, response_1.unauthorized)(res, '管理员登录已过期，请重新登录');
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        logger_1.default.warn(`管理员令牌验证失败: ${errorMessage}`);
        return (0, response_1.unauthorized)(res, '管理员登录验证失败');
    }
};
exports.verifyAdminToken = verifyAdminToken;
/**
 * 可选的JWT验证
 * 如果提供了token则验证，未提供则继续
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const optionalAuth = (req, res, next) => {
    // 从请求头获取token
    let token = req.headers.authorization || req.headers.auth;
    if (!token) {
        logger_1.default.debug('未提供认证令牌，以匿名用户继续');
        return next();
    }
    try {
        // 去除Bearer前缀（如果有）
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }
        // 验证token
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
            req.user = decoded;
            logger_1.default.debug('用户认证成功', { userId: decoded.id });
            next();
        }
        catch (jwtError) {
            logger_1.default.warn(`可选令牌解析失败: ${jwtError instanceof Error ? jwtError.message : '未知错误'}`);
            // 认证失败但不阻止请求，以匿名用户继续
            next();
        }
    }
    catch (error) {
        logger_1.default.warn('令牌验证处理异常，以匿名用户继续', error);
        next();
    }
};
exports.optionalAuth = optionalAuth;
/**
 * 检查用户是否为厨房拥有者
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const isKitchenOwner = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { kitchenId } = req.params;
        const userId = req.user.id;
        // 这里需要从数据库检查用户是否为厨房拥有者
        // 由于模型尚未创建，这里先留空，后续实现
        // 示例代码：
        // const kitchen = await Kitchen.findByPk(kitchenId);
        // if (!kitchen || kitchen.ownerId !== userId) {
        //   return forbidden(res, '您不是该厨房的拥有者');
        // }
        next();
    }
    catch (error) {
        logger_1.default.error('检查厨房拥有者权限失败', error);
        return (0, response_1.unauthorized)(res);
    }
});
exports.isKitchenOwner = isKitchenOwner;
/**
 * 检查用户是否为厨房成员
 * @param req 请求对象
 * @param res 响应对象
 * @param next 下一个中间件
 */
const isKitchenMember = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { kitchenId } = req.params;
        const userId = req.user.id;
        // 这里需要从数据库检查用户是否为厨房成员
        // 由于模型尚未创建，这里先留空，后续实现
        // 示例代码：
        // const member = await KitchenMember.findOne({
        //   where: { kitchenId, userId }
        // });
        // if (!member) {
        //   return forbidden(res, '您不是该厨房的成员');
        // }
        next();
    }
    catch (error) {
        logger_1.default.error('检查厨房成员权限失败', error);
        return (0, response_1.unauthorized)(res);
    }
});
exports.isKitchenMember = isKitchenMember;
exports.default = {
    verifyToken: exports.verifyToken,
    verifyAdminToken: exports.verifyAdminToken,
    optionalAuth: exports.optionalAuth,
    isKitchenOwner: exports.isKitchenOwner,
    isKitchenMember: exports.isKitchenMember,
};
//# sourceMappingURL=auth.js.map