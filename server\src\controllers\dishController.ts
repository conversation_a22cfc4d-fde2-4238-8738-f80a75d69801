/**
 * 菜品控制器
 * 处理菜品相关的请求
 */
import { Request, Response, NextFunction } from 'express';
import dishService from '../services/dishService';
import logger from '../utils/logger';
import { success, error, ResponseCode } from '../utils/response';
import { BusinessError } from '../middlewares/error';
import validator from '../utils/validator';

/**
 * 获取菜品分类列表
 * @route GET /api/dish/categories
 */
const getCategories = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { kitchenId } = req.query;

    if (!kitchenId) {
      throw new BusinessError('缺少参数: kitchenId', ResponseCode.VALIDATION);
    }

    const categories = await dishService.getCategories(kitchenId as string);
    success(res, { categories });
  } catch (err) {
    next(err);
  }
};

/**
 * 获取菜品列表
 * @route GET /api/dish/list
 */
const getDishList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { kitchenId, categoryId } = req.query;
    const userId = req.user?.id;

    if (!kitchenId) {
      throw new BusinessError('缺少参数: kitchenId', ResponseCode.VALIDATION);
    }

    const dishes = await dishService.getDishList(kitchenId as string, categoryId as string, userId);
    success(res, { dishes });
  } catch (err) {
    next(err);
  }
};

/**
 * 获取菜品详情
 * @route GET /api/dish/detail
 */
const getDishDetail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id, kitchenId } = req.query;
    const userId = req.user?.id;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    const dish = await dishService.getDishDetail(parseInt(id as string), userId, kitchenId as string);
    success(res, dish);
  } catch (err) {
    next(err);
  }
};

/**
 * 搜索菜品
 * @route GET /api/dish/search
 */
const searchDishes = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { keyword, kitchenId } = req.query;
    const userId = req.user?.id;

    // 验证关键词
    if (!keyword) {
      throw new BusinessError('缺少参数: keyword', ResponseCode.VALIDATION);
    }

    // 验证关键词长度
    const keywordStr = keyword as string;
    validator.validateStringLength(keywordStr, 1, 50, '搜索关键词');

    // 过滤关键词中的特殊字符和SQL注入字符
    const safeKeyword = validator.filterSqlInjection(validator.filterSpecialChars(keywordStr));

    // 验证厨房ID（如果提供）
    if (kitchenId) {
      validator.validateId(kitchenId as string, '厨房ID');
    }

    const result = await dishService.searchDishes(safeKeyword, kitchenId as string, userId);
    success(res, result);
  } catch (err) {
    next(err);
  }
};

/**
 * 获取热门关键词
 * @route GET /api/dish/hotKeywords
 */
const getHotKeywords = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const keywords = await dishService.getHotKeywords();
    success(res, { keywords });
  } catch (err) {
    next(err);
  }
};

/**
 * 添加菜品
 * @route POST /api/dish/add
 */
const addDish = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const dishData = req.body;

    // 验证必要参数
    if (!dishData.kitchenId) {
      throw new BusinessError('缺少参数: kitchenId', ResponseCode.VALIDATION);
    }

    if (!dishData.categoryId) {
      throw new BusinessError('缺少参数: categoryId', ResponseCode.VALIDATION);
    }

    if (!dishData.name) {
      throw new BusinessError('缺少参数: name', ResponseCode.VALIDATION);
    }

    if (dishData.price === undefined) {
      throw new BusinessError('缺少参数: price', ResponseCode.VALIDATION);
    }

    // 验证厨房ID格式
    validator.validateId(dishData.kitchenId, '厨房ID');

    // 验证分类ID格式
    validator.validateId(dishData.categoryId, '分类ID');

    // 验证菜品名称长度
    validator.validateStringLength(dishData.name, 2, 50, '菜品名称');

    // 验证菜品价格范围
    validator.validateNumberRange(Number(dishData.price), 0, 99999, '菜品价格');

    // 验证原价范围（如果提供）
    if (dishData.originalPrice !== undefined) {
      validator.validateNumberRange(Number(dishData.originalPrice), 0, 99999, '菜品原价');
    }

    // 验证描述长度（如果提供）
    if (dishData.description) {
      validator.validateStringLength(dishData.description, 0, 500, '菜品描述');
      // 过滤XSS
      dishData.description = validator.sanitizeInput(dishData.description);
    }

    // 验证图片URL（如果提供）
    if (dishData.image) {
      validator.validateUrl(dishData.image, '菜品图片', true);
    }

    // 验证标签（如果提供）
    if (dishData.tags && Array.isArray(dishData.tags)) {
      validator.validateArray(dishData.tags, '菜品标签', 0, 10);
      // 验证每个标签的长度
      dishData.tags.forEach((tag: string, index: number) => {
        validator.validateStringLength(tag, 1, 20, `标签${index + 1}`);
        // 过滤特殊字符
        dishData.tags[index] = validator.filterSpecialChars(tag);
      });
    }

    const result = await dishService.addDish(userId, dishData);
    success(res, result);
  } catch (err) {
    next(err);
  }
};

/**
 * 更新菜品
 * @route POST /api/dish/update
 */
const updateDish = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const dishData = req.body;

    logger.info(`用户 ${userId} 尝试更新菜品，数据: ${JSON.stringify(dishData)}`);

    // 验证菜品ID
    if (!dishData.id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    try {
      validator.validateId(dishData.id, '菜品ID');
    } catch (error: any) {
      logger.error(`菜品ID验证失败: ${dishData.id}, 错误: ${error.message}`);
      throw error;
    }

    // 验证分类ID（如果提供）
    if (dishData.categoryId !== undefined) {
      try {
        validator.validateId(dishData.categoryId, '分类ID');
      } catch (error: any) {
        logger.error(`分类ID验证失败: ${dishData.categoryId}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证菜品名称（如果提供）
    if (dishData.name !== undefined) {
      try {
        validator.validateStringLength(dishData.name, 2, 50, '菜品名称');
      } catch (error: any) {
        logger.error(`菜品名称验证失败: ${dishData.name}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证菜品价格（如果提供）
    if (dishData.price !== undefined) {
      try {
        validator.validateNumberRange(Number(dishData.price), 0, 99999, '菜品价格');
      } catch (error: any) {
        logger.error(`菜品价格验证失败: ${dishData.price}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证原价（如果提供）
    if (dishData.originalPrice !== undefined) {
      try {
        validator.validateNumberRange(Number(dishData.originalPrice), 0, 99999, '菜品原价');
      } catch (error: any) {
        logger.error(`菜品原价验证失败: ${dishData.originalPrice}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证描述（如果提供）
    if (dishData.description !== undefined) {
      try {
        validator.validateStringLength(dishData.description, 0, 500, '菜品描述');
        // 过滤XSS
        dishData.description = validator.sanitizeInput(dishData.description);
      } catch (error: any) {
        logger.error(`菜品描述验证失败: ${dishData.description}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证图片URL（如果提供）
    if (dishData.image !== undefined) {
      try {
        validator.validateUrl(dishData.image, '菜品图片', true);
      } catch (error: any) {
        logger.error(`菜品图片URL验证失败: ${dishData.image}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证标签（如果提供）
    if (dishData.tags !== undefined) {
      if (!Array.isArray(dishData.tags)) {
        throw new BusinessError('菜品标签必须是数组', ResponseCode.VALIDATION);
      }

      try {
        validator.validateArray(dishData.tags, '菜品标签', 0, 10);

        // 验证每个标签的长度
        dishData.tags.forEach((tag: string, index: number) => {
          validator.validateStringLength(tag, 1, 20, `标签${index + 1}`);
          // 过滤特殊字符
          dishData.tags[index] = validator.filterSpecialChars(tag);
        });
      } catch (error: any) {
        logger.error(`菜品标签验证失败: ${JSON.stringify(dishData.tags)}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证状态（如果提供）
    if (dishData.status !== undefined) {
      try {
        validator.validateEnum(dishData.status, ['on', 'off'], '菜品状态');
      } catch (error: any) {
        logger.error(`菜品状态验证失败: ${dishData.status}, 错误: ${error.message}`);
        throw error;
      }
    }

    // 验证配料数组（如果提供）
    if (dishData.ingredients !== undefined) {
      if (!Array.isArray(dishData.ingredients)) {
        throw new BusinessError('配料必须是数组', ResponseCode.VALIDATION);
      }
      
      try {
        validator.validateArray(dishData.ingredients, '配料', 0, 20);
        
        // 验证每个配料的格式
        dishData.ingredients.forEach((ingredient: any, index: number) => {
          if (ingredient.name) {
            validator.validateStringLength(ingredient.name, 1, 50, `配料${index + 1}名称`);
          }
        });
      } catch (error: any) {
        logger.error(`配料验证失败: ${JSON.stringify(dishData.ingredients)}, 错误: ${error.message}`);
        throw error;
      }
    }

    logger.info('所有数据验证通过，调用服务更新菜品');

    await dishService.updateDish(userId, dishData);
    success(res, { success: true });
  } catch (err) {
    logger.error(`更新菜品失败: ${(err as Error).message}`);
    next(err);
  }
};

/**
 * 删除菜品
 * @route POST /api/dish/delete
 */
const deleteDish = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id, kitchenId } = req.body;

    if (!id || !kitchenId) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    await dishService.deleteDish(userId, parseInt(id), kitchenId);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 更新菜品状态
 * @route POST /api/dish/updateStatus
 */
const updateDishStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id, kitchenId, status } = req.body;

    if (!id || !kitchenId || !status) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    await dishService.updateDishStatus(userId, parseInt(id), kitchenId, status);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 更新菜品排序
 * @route POST /api/dish/updateSort
 */
const updateDishSort = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { kitchenId, dishes } = req.body;

    if (!kitchenId || !dishes || !Array.isArray(dishes)) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    await dishService.updateDishSort(userId, kitchenId, dishes);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 点赞菜品
 * @route POST /api/dish/like
 */
const likeDish = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    const result = await dishService.likeDish(userId, parseInt(id));
    success(res, result);
  } catch (err) {
    next(err);
  }
};

/**
 * 举报菜品
 * @route POST /api/dish/report
 */
const reportDish = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id, reason } = req.body;

    if (!id || !reason) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    await dishService.reportDish(userId, parseInt(id), reason);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 添加菜品到厨房
 * @route POST /api/dish/addToKitchen
 */
const addToKitchen = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { dishId, targetKitchenId, targetCategoryId } = req.body;

    if (!dishId || !targetKitchenId || !targetCategoryId) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    const result = await dishService.addToKitchen(userId, parseInt(dishId), targetKitchenId, parseInt(targetCategoryId));
    success(res, result);
  } catch (err) {
    next(err);
  }
};

/**
 * 添加评论
 * @route POST /api/dish/comment
 */
const addComment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { dishId, content, rating } = req.body;

    if (!dishId || !content) {
      throw new BusinessError('缺少必要参数', ResponseCode.VALIDATION);
    }

    // 验证评分范围
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      throw new BusinessError('评分必须在1-5之间', ResponseCode.VALIDATION);
    }

    const result = await dishService.addComment(userId, parseInt(dishId), content, rating || 5);
    success(res, result);
  } catch (err) {
    next(err);
  }
};

export default {
  getCategories,
  getDishList,
  getDishDetail,
  searchDishes,
  getHotKeywords,
  addDish,
  updateDish,
  deleteDish,
  updateDishStatus,
  updateDishSort,
  likeDish,
  reportDish,
  addToKitchen,
  addComment
};
