// 发现页面逻辑
import { getDiscoverList, addToDish, getUnreadCounts } from '../../api/discoverApi';
import { getDishCategories, getDishDetail } from '../../api/dishApi';
import { getDishList, addToCart } from '../../api/dishApi';
import { checkLogin } from '../../utils/util';
import { DEFAULT_IMAGES } from '../../utils/constants';

// 导入App选项接口
interface IAppOption {
  globalData: {
    unreadCounts: {
      likes: number;
      system: number;
      comment: number;
    }
  };
  addMessageCountChangeListener(listener: (counts: {
    likes: number;
    system: number;
    comment: number;
  }) => void): void;
  removeMessageCountChangeListener(listener: Function): void;
}

interface Dish {
  id: string;
  name: string;
  image: string;
  price: number;
  description: string;
  isAdded: boolean;
  addedCount: number;
  createTime: string;
  user: {
    userId: string;
    nickName: string;
    avatarUrl: string;
  };
}

interface Category {
  id: string;
  name: string;
  icon: string;
  sort: number;
}

// 搜索防抖定时器
let searchDebounceTimer: any = null;

Page({
  data: {
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
    textColorStyle: '--text-color: #333333;',
    messageCount: 0,  // 系统消息总未读数量
    likesCount: 0,    // 赞和添加未读数量
    commentCount: 0,  // 评价未读数量
    activeTab: 'likes', // 'likes', 'system', 'comment'
    dishes: [] as Dish[],
    refreshing: false,
    loading: false,   // 添加加载状态
    hasMore: true,
    page: 1,
    pageSize: 10,
    sortVisible: false,
    sortType: 'random', // 默认使用随机排序
    showCategoryDialog: false,
    currentDishId: '',
    categories: [] as Category[],
    selectedCategoryId: '',
    searchValue: '',
    // 数据缓存标记
    dataLoaded: false,
    lastLoadTime: 0,  // 上次加载时间
    cacheExpireTime: 5 * 60 * 1000, // 缓存5分钟
    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
    // 当前厨房ID
    currentKitchenId: '',
    // 搜索防抖定时器
    searchDebounceTimer: null as any
  },

  // 消息数量变化监听器
  messageCountChangeListener: null as ((counts: {
    likes: number;
    system: number;
    comment: number;
  }) => void) | null,

  onLoad() {
    this.loadCategories();
    this.loadDishes();
    // 先从本地存储中获取未读消息数量
    this.getUnreadCountsFromStorage();
    // 然后再从服务器获取最新的未读消息数量
    this.getUnreadCounts();
    // 加载背景设置
    this.loadBackgroundSettings();
    // 监听全局背景设置变化
    this.listenForBackgroundChanges();

    // 监听消息数量变化
    this.setupMessageCountListener();
  },

  onShow() {
    // 再次加载背景设置，确保从其他页面返回时背景设置是最新的
    this.loadBackgroundSettings();
    // 优先从本地存储获取消息数量，保证界面响应及时
    this.getUnreadCountsFromStorage();
    // 然后再从服务器获取最新的未读消息数量
    this.getUnreadCounts();

    // 检查厨房是否有变化，如果有变化则重新加载分类
    this.checkKitchenChanged();

    // 智能刷新：只在特定条件下刷新数据（但不在首次加载时执行）
    if (this.data.dataLoaded) {
      this.smartRefreshData();
    }
  },

  onUnload() {
    // 移除消息数量变化监听器
    this.removeMessageCountListener();
    
    // 清理搜索防抖定时器
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
      searchDebounceTimer = null;
    }
  },

  // 智能刷新数据
  smartRefreshData() {
    const currentTime = Date.now();
    const timeSinceLastLoad = currentTime - this.data.lastLoadTime;

    // 判断是否需要刷新数据
    const shouldRefresh =
      timeSinceLastLoad > this.data.cacheExpireTime ||   // 缓存已过期
      (this.data.sortType === 'random' && timeSinceLastLoad > 60000); // 随机排序且超过1分钟

    if (shouldRefresh) {
      console.log('智能刷新：需要刷新数据', {
        dataLoaded: this.data.dataLoaded,
        timeSinceLastLoad,
        sortType: this.data.sortType
      });
      this.loadDishes(true);
    } else {
      console.log('智能刷新：使用缓存数据', {
        timeSinceLastLoad,
        cacheExpireTime: this.data.cacheExpireTime
      });
    }
  },

  // 设置消息数量变化监听器
  setupMessageCountListener() {
    const app = getApp<IAppOption>();
    if (app) {
      // 创建监听器函数
      this.messageCountChangeListener = (counts) => {
        this.setData({
          messageCount: counts.system,
          likesCount: counts.likes,
          commentCount: counts.comment
        });
      };

      // 添加到app全局监听器
      app.addMessageCountChangeListener(this.messageCountChangeListener);
    }
  },

  // 移除消息数量变化监听器
  removeMessageCountListener() {
    const app = getApp<IAppOption>();
    if (app && this.messageCountChangeListener) {
      app.removeMessageCountChangeListener(this.messageCountChangeListener);
      this.messageCountChangeListener = null;
    }
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';

    this.setData({ navBgStyle });

    // 根据背景颜色计算合适的文字颜色
    this.updateTextColorByBackground(navBgStyle);
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景颜色值
    let bgColor = '#FFFFFF'; // 默认白色背景

    if (background) {
      // 尝试提取渐变的起始颜色
      const match = background.match(/linear-gradient\(to\s+\w+,\s+(#[A-Fa-f0-9]+),\s+/);
      if (match && match.length > 1) {
        bgColor = match[1];
      }
    }

    // 计算颜色亮度 (简化的亮度计算，仅用于本例)
    const r = parseInt(bgColor.substring(1, 3), 16);
    const g = parseInt(bgColor.substring(3, 5), 16);
    const b = parseInt(bgColor.substring(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF';
    const textColorStyle = `--text-color: ${textColor};`;

    this.setData({ textColorStyle });

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    });
  },

  // 监听全局背景设置变化
  listenForBackgroundChanges() {
    // 简化实现，直接在app.ts中添加更新通知函数
    // 这里采用轮询方式检查本地存储的变化
    setInterval(() => {
      const navBgStyle = wx.getStorageSync('navBgStyle');
      if (navBgStyle && navBgStyle !== this.data.navBgStyle) {
        this.setData({ navBgStyle });
        this.updateTextColorByBackground(navBgStyle);
      }
    }, 1000); // 每秒检查一次
  },

  // 当点击页面时，关闭排序下拉框
  onTap() {
    if (this.data.sortVisible) {
      this.setData({
        sortVisible: false
      });
    }
  },

  // 阻止冒泡
  preventBubble() {
    // 阻止事件冒泡
    return;
  },

  // 加载分类数据
  async loadCategories() {
    try {
      // 获取用户当前厨房ID - 修复获取逻辑
      let currentKitchenId = '';
      try {
        // 优先从last_selected_kitchen获取厨房ID
        currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';

        // 如果还是没有，尝试从userInfo获取
        if (!currentKitchenId) {
          const userInfo = wx.getStorageSync('userInfo');
          if (userInfo && userInfo.currentKitchenId) {
            currentKitchenId = userInfo.currentKitchenId;
          }
        }

        console.log('获取到的厨房ID:', currentKitchenId);
      } catch (error) {
        console.log('获取用户厨房信息失败', error);
      }

      // 如果没有厨房ID，清空数据并提示用户
      if (!currentKitchenId) {
        console.log('未找到厨房ID，清空分类数据');
        this.setData({
          categories: []
        });
        wx.showToast({
          title: '请先创建或选择厨房',
          icon: 'none'
        });
        return;
      }

      // 强制从API获取最新分类数据，确保包含dishCount字段
      console.log('调用getDishCategories API，传递厨房ID:', currentKitchenId);
      const res = await getDishCategories(currentKitchenId);

      if (res.error === 0 && res.body) {
        // 修复：正确处理后端返回的数据结构
        const categoriesData = res.body.categories || res.body;
        console.log('从API获取到的分类数据:', categoriesData);

        if (categoriesData && Array.isArray(categoriesData) && categoriesData.length > 0) {
          this.setData({
            categories: categoriesData
          });
          console.log('分类数据加载成功，数量:', categoriesData.length);

          // 更新本地缓存
          wx.setStorage({
            key: 'dishCategories',
            data: categoriesData
          });
        } else {
          console.log('未找到分类数据，清空分类数据');
          // 如果没有分类数据，清空分类数据并显示提示
          this.setData({
            categories: []
          });
          wx.showToast({
            title: '当前厨房还没有分类，请先添加分类',
            icon: 'none'
          });
        }
      } else {
        console.error('获取分类数据失败:', res.message);
        // API调用失败时清空分类数据
        this.setData({
          categories: []
        });
        wx.showToast({
          title: res.message || '获取分类失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('Failed to load categories', err);
      // 发生错误时清空分类数据
      this.setData({
        categories: []
      });
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      });
    }
  },

  // 加载发现页菜品
  async loadDishes(refresh = false) {
    // 如果正在加载，避免重复请求
    if (this.data.loading && !refresh) {
      return;
    }

    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        dishes: [],
        loading: true
      });
    } else {
      this.setData({
        loading: true
      });
    }

    try {
      const res = await getDiscoverList({
        page: this.data.page,
        pageSize: this.data.pageSize,
        sortType: this.data.sortType,
        searchValue: this.data.searchValue
      });

      if (res.error === 0) {
        const newDishes = refresh ? res.body.list : [...this.data.dishes, ...res.body.list];

        this.setData({
          dishes: newDishes,
          hasMore: res.body.list.length === this.data.pageSize,
          page: this.data.page + 1,
          dataLoaded: true,
          lastLoadTime: Date.now()
        });
      } else {
        // 根据错误码显示不同提示
        let errorMessage = res.message || '加载失败';
        if (res.error === 401) {
          errorMessage = '请先登录';
        } else if (res.error === 500) {
          errorMessage = '服务器错误，请稍后再试';
        }
        
        console.error('加载发现页菜品失败:', res);
        
        // 如果是刷新操作失败，保持原有数据
        if (!refresh && this.data.dishes.length > 0) {
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      }
    } catch (err: any) {
      console.error('Failed to load dishes', err);
      
      // 更详细的错误处理
      let errorMessage = '网络连接失败';
      if (err && err.message) {
        if (err.message.includes('timeout') || err.message.includes('超时')) {
          errorMessage = '请求超时，请检查网络';
        } else if (err.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络';
        }
      }
      
      // 如果是刷新操作失败，保持原有数据
      if (!refresh && this.data.dishes.length > 0) {
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    } finally {
      this.setData({
        refreshing: false,
        loading: false
      });
    }
  },

  // scroll-view下拉刷新
  onScrollRefresh() {
    this.setData({ refreshing: true });
    this.loadDishes(true);
  },

  // scroll-view滚动到底部加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadDishes();
    }
  },

  // 原生下拉刷新，改为使用scroll-view的刷新
  onPullDownRefresh() {
    wx.stopPullDownRefresh(); // 立即停止下拉刷新动画
  },

  // 点击菜品卡片，导航到菜品详情页
  navigateToDishDetail(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${id}&fromDiscover=1`
    });
  },

  // 点击添加按钮
  handleAddDish(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;

    // 检查厨房是否变更
    this.checkKitchenChanged();

    // 强制重新加载分类数据，确保数据是最新的
    if (this.data.categories.length === 0) {
      this.loadCategories().then(() => {
        this.showAddDialog(id);
      });
    } else {
      // 即使有分类数据，也检查是否需要更新
      const currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';
      if (currentKitchenId && currentKitchenId !== this.data.currentKitchenId) {
        // 厨房已变更，重新加载分类
      this.loadCategories().then(() => {
        this.showAddDialog(id);
      });
    } else {
      this.showAddDialog(id);
      }
    }
  },

  // 显示添加对话框
  showAddDialog(dishId: string) {
    this.setData({
      showCategoryDialog: true,
      currentDishId: dishId,
      selectedCategoryId: '' // 重置选中的分类ID
    });
  },

  // 选择分类
  selectCategory(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    this.setData({
      selectedCategoryId: id
    });
  },

  // 确认添加到分类
  async confirmAddToCategory() {
    if (!this.data.selectedCategoryId) {
      wx.showToast({
        title: '请先选择分类',
        icon: 'none'
      });
      return;
    }

    // 获取用户当前厨房ID - 修复获取逻辑，与loadCategories保持一致
    let currentKitchenId = '';
    try {
      // 优先从last_selected_kitchen获取厨房ID
      currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';

      // 如果还是没有，尝试从userInfo获取
      if (!currentKitchenId) {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo && userInfo.currentKitchenId) {
          currentKitchenId = userInfo.currentKitchenId;
        }
      }

      console.log('添加菜品时获取到的厨房ID:', currentKitchenId);
    } catch (error) {
      console.log('获取用户厨房信息失败', error);
    }

    if (!currentKitchenId) {
      wx.showToast({
        title: '请先创建或选择厨房',
        icon: 'none'
      });
      return;
    }

    // 添加到菜品
    try {
      const res = await addToDish(this.data.currentDishId, this.data.selectedCategoryId, currentKitchenId);
      if (res.error === 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });

        // 更新菜品状态 - 修复：将ID转换为字符串进行比较
        const dishes = this.data.dishes.map(dish => {
          if (dish.id.toString() === this.data.currentDishId.toString()) {
            return {
              ...dish,
              isAdded: true,
              addedCount: (dish.addedCount || 0) + 1  // 增加添加次数
            };
          }
          return dish;
        });

        this.setData({
          dishes,
          showCategoryDialog: false
        });

        // 不再刷新列表，只更新本地状态即可
      } else {
        wx.showToast({
          title: res.message || '添加失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('Failed to add dish', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  // 切换排序菜单显示状态
  toggleSortMenu() {
    this.setData({
      sortVisible: !this.data.sortVisible
    });
  },

  // 选择排序方式
  selectSort(e: WechatMiniprogram.TouchEvent) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      sortType: type,
      sortVisible: false,
      dataLoaded: false, // 重置缓存标记
      lastLoadTime: 0
    });
    this.loadDishes(true);
  },

  // 切换顶部标签
  switchTab(e: any) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 搜索输入实时响应
  onSearchInput(e: any) {
    const searchValue = e.detail.value.trim();
    this.setData({
      searchValue
    });
    
    // 清除之前的防抖定时器
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }
    
    // 设置新的防抖定时器，500ms后执行搜索
    searchDebounceTimer = setTimeout(() => {
      this.loadDishes(true);
    }, 500);
  },

  // 搜索菜品（回车确认）
  onSearch(e: any) {
    const searchValue = e.detail.value.trim();
    this.setData({
      searchValue
    });
    this.loadDishes(true);
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchValue: ''
    });
    this.loadDishes(true);
  },

  // 关闭分类选择对话框
  closeDialog() {
    this.setData({
      showCategoryDialog: false
    });
  },

  // 获取所有标签页的未读消息数量
  async getUnreadCounts() {
    try {
      const res = await getUnreadCounts();

      if (res.error === 0) {
        this.setData({
          messageCount: res.body.system, // 系统消息数量
          likesCount: res.body.likes,    // 赞和添加数量
          commentCount: res.body.comment // 评价数量
        });
      } else {
        console.error('获取未读消息数量失败', res.message);
      }
    } catch (err) {
      console.error('获取未读消息数量失败', err);
    }
  },

  // 导航到消息页面
  navigateToMessagePage(e: any) {
    const { tab } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/message/message?tab=${tab}`
    });
  },

  // 从本地存储获取未读消息数量
  getUnreadCountsFromStorage() {
    try {
      const unreadCounts = wx.getStorageSync('unreadMessageCounts');
      if (unreadCounts) {
        this.setData({
          messageCount: unreadCounts.system || 0,
          likesCount: unreadCounts.likes || 0,
          commentCount: unreadCounts.comment || 0
        });
      }
    } catch (error) {
      console.error('从本地存储获取未读消息数量失败', error);
    }
  },

  // 检查厨房变更
  checkKitchenChanged() {
    try {
      const currentKitchenId = wx.getStorageSync('last_selected_kitchen') || '';
      const storedKitchenId = this.data.currentKitchenId || '';

      if (currentKitchenId && currentKitchenId !== storedKitchenId) {
        console.log('发现页面检测到厨房变更，从', storedKitchenId, '切换到', currentKitchenId);
        
        // 强制重新加载分类数据
        this.setData({
          categories: [], // 先清空分类数据
          currentKitchenId: currentKitchenId
        });
        
        // 重新加载分类
        this.loadCategories();
      }
    } catch (error) {
      console.error('检查厨房变更失败', error);
    }
  }
})