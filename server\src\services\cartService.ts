/**
 * 购物车服务
 * 处理购物车相关的业务逻辑
 */
import { Op } from 'sequelize';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { CartItem, Dish, Kitchen, Category, Ingredient } from '../models';

/**
 * 获取购物车列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 购物车列表
 */
const getCartList = async (userId: number, kitchenId: string): Promise<any[]> => {
  const cartItems = await CartItem.findAll({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
    include: [
      {
        model: Dish,
        as: 'dish',
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name'],
          },
          {
            model: Ingredient,
            as: 'ingredients',
            attributes: ['id', 'name', 'amount'],
            order: [['sort', 'ASC']],
          },
        ],
      },
      {
        model: Kitchen,
        as: 'kitchen',
        attributes: ['id', 'name', 'avatar_url'],
      },
    ],
  });
  
  return cartItems.map(item => {
    // 从配料表生成标签，取前5个配料的名称
    const tags = item.dish?.ingredients ? item.dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];
    
    return {
      id: item.id,
      dishId: item.dish_id,
      kitchenId: item.kitchen_id,
      count: item.count,
      dish: item.dish ? {
        id: item.dish.id,
        name: item.dish.name,
        image: item.dish.image,
        price: item.dish.price,
        originalPrice: item.dish.original_price,
        description: item.dish.description,
        tags: tags, // 使用配料表生成的标签而不是原有的tags字段
        status: item.dish.status,
        categoryId: item.dish.category_id,
        categoryName: item.dish.category?.name,
      } : null,
      kitchen: item.kitchen ? {
        id: item.kitchen.id,
        name: item.kitchen.name,
        avatarUrl: item.kitchen.avatar_url,
      } : null,
    };
  });
};

/**
 * 添加到购物车
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 * @param count 数量
 * @returns 添加结果
 */
const addToCart = async (userId: number, dishId: number, kitchenId: string, count: number = 1): Promise<any> => {
  // 检查菜品是否存在
  const dish = await Dish.findOne({
    where: {
      id: dishId,
      kitchen_id: kitchenId,
      status: 'on', // 只能添加上架的菜品
    },
  });
  
  if (!dish) {
    throw new BusinessError('菜品不存在或已下架');
  }
  
  // 检查购物车中是否已存在该菜品
  let cartItem = await CartItem.findOne({
    where: {
      user_id: userId,
      dish_id: dishId,
    },
  });
  
  if (cartItem) {
    // 更新数量
    await cartItem.update({
      count: cartItem.count + count,
    });
  } else {
    // 创建新的购物车项
    cartItem = await CartItem.create({
      user_id: userId,
      kitchen_id: kitchenId,
      dish_id: dishId,
      count,
    });
  }
  
  return {
    id: cartItem.id,
    dishId: cartItem.dish_id,
    kitchenId: cartItem.kitchen_id,
    count: cartItem.count,
  };
};

/**
 * 更新购物车项数量
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param count 数量
 * @param kitchenId 厨房ID
 * @returns 更新结果
 */
const updateCartItemCount = async (userId: number, dishId: number, count: number, kitchenId: string): Promise<any> => {
  // 检查购物车项是否存在
  const cartItem = await CartItem.findOne({
    where: {
      user_id: userId,
      dish_id: dishId,
      kitchen_id: kitchenId
    },
  });
  
  if (!cartItem) {
    throw new BusinessError('购物车项不存在');
  }
  
  if (count <= 0) {
    // 删除购物车项
    await cartItem.destroy();
    return {
      deleted: true,
    };
  } else {
    // 更新数量
    await cartItem.update({
      count,
    });
    
    return {
      id: cartItem.id,
      dishId: cartItem.dish_id,
      kitchenId: cartItem.kitchen_id,
      count: cartItem.count,
    };
  }
};

/**
 * 清空购物车
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 */
const clearCart = async (userId: number, kitchenId: string): Promise<void> => {
  await CartItem.destroy({
    where: {
      user_id: userId,
      kitchen_id: kitchenId,
    },
  });
};

export default {
  getCartList,
  addToCart,
  updateCartItemCount,
  clearCart,
};
