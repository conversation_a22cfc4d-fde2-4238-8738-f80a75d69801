/**
 * 发现服务
 * 处理发现页相关的业务逻辑
 */
import { Op } from 'sequelize';
import sequelize from '../config/database';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { DiscoverItem, Dish, Kitchen, User, KitchenMember, Category, Ingredient } from '../models';
import { getPagination, buildPaginationResult } from '../utils/helpers';
import dishService from './dishService';

/**
 * 获取发现列表
 * @param userId 用户ID（可选）
 * @param page 页码
 * @param pageSize 每页大小
 * @param sortType 排序类型 'hot' | 'time' | 'random'
 * @param searchValue 搜索关键词
 * @returns 发现列表
 */
const getDiscoverList = async (userId?: number, page: number = 1, pageSize: number = 10, sortType: string = 'hot', searchValue: string = ''): Promise<any> => {
  try {
    // 分页参数
    const { limit, offset } = getPagination(page, pageSize);
    
    // 构建查询条件
    const whereConditions: any = {};
    const includeConditions: any = [];
    
    // 搜索条件
    if (searchValue && searchValue.trim()) {
      includeConditions.push({
        model: Dish,
        as: 'dish',
        where: {
          name: {
            [Op.like]: `%${searchValue.trim()}%`
          }
        },
        include: [
          {
            model: Kitchen,
            as: 'kitchen',
            attributes: ['id', 'name', 'avatar_url'],
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'nick_name', 'avatar_url'],
          },
          {
            model: Ingredient,
            as: 'ingredients',
            attributes: ['id', 'name', 'amount'],
            order: [['sort', 'ASC']],
          },
        ],
      });
    } else {
      includeConditions.push({
        model: Dish,
        as: 'dish',
        include: [
          {
            model: Kitchen,
            as: 'kitchen',
            attributes: ['id', 'name', 'avatar_url'],
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'nick_name', 'avatar_url'],
          },
          {
            model: Ingredient,
            as: 'ingredients',
            attributes: ['id', 'name', 'amount'],
            order: [['sort', 'ASC']],
          },
        ],
      });
    }
    
    // 排序条件
    let orderConditions: any = [];
    if (sortType === 'time') {
      // 按菜品更新时间排序
      orderConditions = [[{ model: Dish, as: 'dish' }, 'updated_at', 'DESC']];
    } else if (sortType === 'random') {
      // 随机排序
      orderConditions = [sequelize.fn('RAND')];
    } else {
      // 热门排序：按菜品销量排序
      orderConditions = [[{ model: Dish, as: 'dish' }, 'sales', 'DESC']];
    }
    
    // 查询发现项
    const { count, rows } = await DiscoverItem.findAndCountAll({
      where: whereConditions,
      include: includeConditions,
      order: orderConditions,
      limit,
      offset,
      distinct: true,
    });
    
    logger.info(`查询到发现项数量: ${rows.length}`);
    
    // 如果有用户ID，查询用户的添加记录
    let userAddedDishNames: Set<string> = new Set();
    if (userId) {
      try {
        // 查询用户在自己厨房中添加过的菜品名称
        const userDishes = await Dish.findAll({
          where: {
            created_by: userId,
          },
          attributes: ['name'],
        });
        
        // 创建用户已添加菜品名称的集合
        userAddedDishNames = new Set(userDishes.map(dish => dish.name));
      } catch (error) {
        logger.error('查询用户添加记录失败:', error);
      }
    }
    
    // 构建返回结果
    const items = rows.map(item => {
      const dish = item.dish;
      
      if (!dish) {
        logger.warn(`发现项 ${item.id} 关联的菜品不存在`);
        return null;
      }
      
      // 判断用户是否已添加该菜品（通过菜品名称判断，因为用户可能添加了同名菜品）
      const isAdded = userId ? userAddedDishNames.has(dish.name) : false;
      
      // 从配料表生成标签，取前5个配料的名称
      const tags = dish?.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];
      
      return {
        id: dish?.id,
        dishId: dish?.id,
        name: dish?.name,
        image: dish?.image,
        price: dish?.price,
        originalPrice: dish?.original_price,
        description: dish?.description,
        tags: tags, // 使用配料表生成的标签而不是原有的tags字段
        sales: dish?.sales,
        rating: dish?.rating,
        kitchen: dish?.kitchen ? {
          id: dish.kitchen.id,
          name: dish.kitchen.name,
          avatarUrl: dish.kitchen.avatar_url,
        } : null,
        user: dish?.creator ? {
          userId: dish.creator.id,
          nickName: dish.creator.nick_name,
          avatarUrl: dish.creator.avatar_url,
        } : null,
        creator: dish?.creator ? {
          id: dish.creator.id,
          nickName: dish.creator.nick_name,
          avatarUrl: dish.creator.avatar_url,
        } : null,
        tabType: item.tab_type,
        isAdded: isAdded,
        addedCount: dish?.sales || 0, // 使用菜品销量作为"*人添加"数据
        createTime: item.created_at,
      };
    }).filter(item => item !== null);
    
    logger.info(`处理后的发现项数量: ${items.length}`);
    
    return buildPaginationResult(items, count, page, pageSize);
  } catch (error: any) {
    logger.error(`发现服务处理失败: ${error.message}`);
    throw error;
  }
};

/**
 * 添加到分类
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param targetKitchenId 目标厨房ID
 * @param targetCategoryId 目标分类ID
 * @returns 添加结果
 */
const addToDish = async (userId: number, dishId: number, targetKitchenId: string, targetCategoryId: number): Promise<any> => {
  // 调用菜品服务的添加到厨房方法
  return await dishService.addToKitchen(userId, dishId, targetKitchenId, targetCategoryId);
};

/**
 * 获取发现页消息数量
 * @param userId 用户ID
 * @returns 消息数量
 */
const getMessageCount = async (userId: number): Promise<any> => {
  // 获取各标签页未读数量
  const recommendCount = await getUnreadCountByType(userId, 'recommend');
  const newestCount = await getUnreadCountByType(userId, 'newest');
  const popularCount = await getUnreadCountByType(userId, 'popular');
  
  // 获取总未读数量
  const totalCount = recommendCount + newestCount + popularCount;
  
  return {
    recommend: recommendCount,
    newest: newestCount,
    popular: popularCount,
    total: totalCount,
  };
};

/**
 * 获取指定类型的未读数量
 * @param userId 用户ID
 * @param type 标签类型
 * @returns 未读数量
 */
const getUnreadCountByType = async (userId: number, type: string): Promise<number> => {
  // 获取所有该类型的发现项
  const allItems = await DiscoverItem.findAll({
    where: { tab_type: type },
    attributes: ['id'],
  });
  
  // 获取用户已读的该类型发现项
  const readItems = await DiscoverItem.findAll({
    where: {
      user_id: userId,
      tab_type: type,
      is_read: true,
    },
    attributes: ['id'],
  });
  
  // 计算未读数量
  return allItems.length - readItems.length;
};

/**
 * 获取未读数量
 * @param userId 用户ID
 * @returns 未读数量
 */
const getUnreadCounts = async (userId: number): Promise<any> => {
  // 导入消息服务
  const messageService = require('./messageService').default;
  
  // 获取真实的消息数量
  const messageCounts = await messageService.getMessageCount(userId);
  
  return {
    likes: messageCounts.like + messageCounts.add, // 赞和添加合并
    system: messageCounts.system, // 系统消息
    comment: messageCounts.comment, // 评论消息
    total: messageCounts.total
  };
};

/**
 * 获取标签内容
 * @param userId 用户ID（可选）
 * @param type 标签类型
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 标签内容
 */
const getTabContent = async (userId?: number, type: string = 'recommend', page: number = 1, pageSize: number = 10): Promise<any> => {
  // 检查类型是否有效
  if (!['recommend', 'newest', 'popular'].includes(type)) {
    throw new BusinessError('无效的标签类型');
  }
  
  // 分页参数
  const { limit, offset } = getPagination(page, pageSize);
  
  // 查询发现项
  const { count, rows } = await DiscoverItem.findAndCountAll({
    where: { tab_type: type },
    include: [
      {
        model: Dish,
        as: 'dish',
        include: [
          {
            model: Kitchen,
            as: 'kitchen',
            attributes: ['id', 'name', 'avatar_url'],
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'nick_name', 'avatar_url'],
          },
          {
            model: Ingredient,
            as: 'ingredients',
            attributes: ['id', 'name', 'amount'],
            order: [['sort', 'ASC']],
          },
        ],
      },
    ],
    order: [['sort_value', 'DESC']],
    limit,
    offset,
    distinct: true,
  });
  
  // 注释：移除了会导致数据重复的已读状态更新逻辑
  // 如果需要已读状态功能，应该在用户明确操作时单独处理
  
  // 构建返回结果
  const items = rows.map(item => {
    const dish = item.dish;
    
    // 从配料表生成标签，取前5个配料的名称
    const tags = dish?.ingredients ? dish.ingredients.slice(0, 5).map((ingredient: any) => ingredient.name) : [];
    
    return {
      id: item.id,
      dishId: dish?.id,
      name: dish?.name,
      image: dish?.image,
      price: dish?.price,
      originalPrice: dish?.original_price,
      description: dish?.description,
      tags: tags, // 使用配料表生成的标签而不是原有的tags字段
      sales: dish?.sales,
      rating: dish?.rating,
      kitchen: dish?.kitchen ? {
        id: dish.kitchen.id,
        name: dish.kitchen.name,
        avatarUrl: dish.kitchen.avatar_url,
      } : null,
      user: dish?.creator ? {
        userId: dish.creator.id,
        nickName: dish.creator.nick_name,
        avatarUrl: dish.creator.avatar_url,
      } : null,
      creator: dish?.creator ? {
        id: dish.creator.id,
        nickName: dish.creator.nick_name,
        avatarUrl: dish.creator.avatar_url,
      } : null,
      tabType: item.tab_type,
      isAdded: false,
      addedCount: dish?.sales || 0,
      createTime: item.created_at,
    };
  });
  
  return buildPaginationResult(items, count, page, pageSize);
};

/**
 * 添加发现项
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 * @param tabType 标签类型
 * @param sortValue 排序值
 * @returns 添加结果
 */
const addDiscoverItem = async (dishId: number, kitchenId: string, tabType: string, sortValue: number): Promise<any> => {
  // 检查类型是否有效
  if (!['recommend', 'newest', 'popular'].includes(tabType)) {
    throw new BusinessError('无效的标签类型');
  }
  
  // 检查菜品是否存在
  const dish = await Dish.findOne({
    where: {
      id: dishId,
      kitchen_id: kitchenId,
    },
  });
  
  if (!dish) {
    throw new BusinessError('菜品不存在');
  }
  
  // 创建发现项
  const item = await DiscoverItem.create({
    dish_id: dishId,
    kitchen_id: kitchenId,
    tab_type: tabType,
    sort_value: sortValue,
  });
  
  return {
    id: item.id,
    dishId: item.dish_id,
    kitchenId: item.kitchen_id,
    tabType: item.tab_type,
    sortValue: item.sort_value,
  };
};

export default {
  getDiscoverList,
  addToDish,
  getMessageCount,
  getUnreadCounts,
  getTabContent,
  addDiscoverItem,
};
