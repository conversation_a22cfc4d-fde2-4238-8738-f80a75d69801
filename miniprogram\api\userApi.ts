import { request } from '../utils/request'
import { SERVER_BASE_URL } from '../utils/constants'

// 用户登录接口
export const login = (code: string) => {
  return request({
    url: '/api/user/login',
    method: 'POST',
    data: { code },
    showLoading: false
  })
}

// 获取用户信息接口
export const getUserInfo = () => {
  return request({
    url: '/api/user/info',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 更新用户信息接口
export const updateUserInfo = (userInfo: any) => {
  return request({
    url: '/api/user/update',
    method: 'POST',
    data: userInfo
  })
}

// 获取搜索历史
export const getSearchHistory = () => {
  return request({
    url: '/api/user/searchHistory',
    method: 'GET',
    data: {}
  })
}

// 清除搜索历史
export const clearSearchHistory = () => {
  return request({
    url: '/api/user/clearSearchHistory',
    method: 'POST',
    data: {}
  })
}

// 获取用户背景设置
export const getUserBackgroundSettings = () => {
  return request({
    url: '/api/user/backgroundSettings',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 更新用户背景设置
export const updateUserBackgroundSettings = (settings: {
  shopBg?: string,
  navBgStyle?: string,
  navBgIndex?: number
}) => {
  return request({
    url: '/api/user/updateBackgroundSettings',
    method: 'POST',
    data: settings,
    showLoading: false
  })
}

// 上传背景图片
export const uploadBackgroundImage = (filePath: string) => {
  return new Promise((resolve, reject) => {
    // 获取用户token
    const token = wx.getStorageSync('token') || '';

    console.log('上传背景图片:', filePath);

    // 使用安全的loading方法
    const { safeShowLoading, safeHideLoading } = require('../utils/request');
    safeShowLoading({
      title: '上传中...',
    });

    wx.uploadFile({
      url: `${SERVER_BASE_URL}/api/upload/backgroundImage`,
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}` // 使用Bearer Token认证
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          console.log('上传结果:', data);

          // 转换响应格式以匹配请求函数的格式
          if (data.error === 0 && data.body && data.body.imageUrl) {
            resolve({
              error: 0,
              body: {
                imageUrl: data.body.imageUrl
              },
              message: ''
            });
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
            reject({
              error: data.error || 500,
              body: null,
              message: data.message || '上传失败'
            });
          }
        } catch (e) {
          console.error('解析响应失败:', e);
          wx.showToast({
            title: '解析响应失败',
            icon: 'none'
          });
          reject({
            error: 500,
            body: null,
            message: '解析响应失败'
          });
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        wx.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
        reject({
          error: 500,
          body: null,
          message: err.errMsg || '网络错误'
        });
      },
      complete: () => {
        // 使用安全的hideLoading方法
        safeHideLoading();
      }
    });
  });
}

// 获取用户大米信息
export const getUserCoins = () => {
  // 直接请求API获取最新数据
  return request({
    url: '/api/user/coins',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 充值大米
export const rechargeCoins = (amount: number) => {
  return request({
    url: '/api/user/recharge',
    method: 'POST',
    data: { amount },
    showLoading: false
  })
}

// 获取大米交易记录
export const getCoinRecords = (type: string = 'all') => {
  return request({
    url: '/api/user/coinRecords',
    method: 'GET',
    data: { type },
    showLoading: false
  })
}

// 获取用户签到数据
export const getUserSignInData = () => {
  return request({
    url: '/api/user/signInData',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 签到接口
export const signIn = () => {
  return request({
    url: '/api/user/signIn',
    method: 'POST',
    data: {},
    showLoading: false
  })
}

// 观看广告接口
export const watchAd = () => {
  return request({
    url: '/api/user/watchAd',
    method: 'POST',
    data: {},
    showLoading: false
  })
}

// 补签接口
export const compensateSignIn = (date: string) => {
  return request({
    url: '/api/user/compensateSignIn',
    method: 'POST',
    data: { date },
    showLoading: false
  })
}

/**
 * 会员状态接口定义
 */
export interface MembershipStatus {
  isMember: boolean;      // 是否是会员
  expireDate: string;     // 到期时间
  memberType: string;     // 会员类型：monthly-月度会员，yearly-年度会员
  privileges?: {          // 会员特权
    customTheme: boolean; // 自定义主题
    tableManagement: boolean; // 桌号管理
  }
}

/**
 * 获取用户会员状态
 * @returns Promise<ApiResponse<MembershipStatus>> 包含会员状态的API响应
 */
export const getMembershipStatus = async () => {
  return request({
    url: '/api/user/membershipStatus',
    method: 'GET',
    data: {},
    showLoading: false
  });
}

/**
 * 订阅会员
 * @param memberType 会员类型：monthly-月度会员，yearly-年度会员
 * @returns Promise<ApiResponse<MembershipStatus>> 包含会员状态的API响应
 */
export const subscribeMembership = async (memberType: string) => {
  return request({
    url: '/api/user/subscribeMembership',
    method: 'POST',
    data: { memberType },
    showLoading: false
  });
}

// 上传用户头像
export const uploadUserAvatar = (filePath: string) => {
  return new Promise((resolve, reject) => {
    // 获取用户token
    const token = wx.getStorageSync('token') || '';

    console.log('上传用户头像:', filePath);

    // 使用安全的loading方法
    const { safeShowLoading, safeHideLoading } = require('../utils/request');
    safeShowLoading({
      title: '上传中...',
    });

    wx.uploadFile({
      url: `${SERVER_BASE_URL}/api/upload/userAvatar`,
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}` // 使用Bearer Token认证
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          console.log('上传用户头像结果:', data);

          // 转换响应格式以匹配请求函数的格式
          if (data.error === 0 && data.body && data.body.imageUrl) {
            resolve({
              error: 0,
              body: {
                imageUrl: data.body.imageUrl
              },
              message: ''
            });
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
            reject({
              error: data.error || 500,
              body: null,
              message: data.message || '上传失败'
            });
          }
        } catch (e) {
          console.error('解析响应失败:', e);
          wx.showToast({
            title: '解析响应失败',
            icon: 'none'
          });
          reject({
            error: 500,
            body: null,
            message: '解析响应失败'
          });
        }
      },
      fail: (err) => {
        console.error('上传用户头像失败:', err);
        wx.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
        reject({
          error: 500,
          body: null,
          message: err.errMsg || '网络错误'
        });
      },
      complete: () => {
        // 使用安全的hideLoading方法
        safeHideLoading();
      }
    });
  });
}

// 微信支付相关API

// 创建支付订单
export const createPaymentOrder = (amount: number, description: string) => {
  return request({
    url: '/api/payment/create',
    method: 'POST',
    data: { amount, description },
    showLoading: true
  })
}

// 查询支付订单状态
export const queryPaymentOrder = (outTradeNo: string) => {
  return request({
    url: `/api/payment/query/${outTradeNo}`,
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 关闭支付订单
export const closePaymentOrder = (outTradeNo: string) => {
  return request({
    url: '/api/payment/close',
    method: 'POST',
    data: { outTradeNo },
    showLoading: false
  })
}

// 申请退款
export const createRefund = (outTradeNo: string, refundAmount: number, reason?: string) => {
  return request({
    url: '/api/payment/refund',
    method: 'POST',
    data: { outTradeNo, refundAmount, reason },
    showLoading: true
  })
}

// 查询退款状态
export const queryRefund = (outRefundNo: string) => {
  return request({
    url: `/api/payment/refund/${outRefundNo}`,
    method: 'GET',
    data: {},
    showLoading: false
  })
}