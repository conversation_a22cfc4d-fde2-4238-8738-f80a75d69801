/**
 * 支付控制器
 * 处理微信支付相关请求
 */
import { Request, Response, NextFunction } from 'express';
import paymentService from '../services/paymentService';
import userService from '../services/userService';
import { success, ResponseCode } from '../utils/response';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { v4 as uuidv4 } from 'uuid';

/**
 * 退款通知数据接口
 */
interface RefundNotification {
  refund_status: string;
  out_trade_no: string;
  out_refund_no: string;
  refund_id: string;
  [key: string]: any;
}

/**
 * 创建支付订单
 * @route POST /api/payment/create
 */
const createPaymentOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { amount, description } = req.body;

    // 参数验证
    if (!amount || amount <= 0) {
      throw new BusinessError('支付金额必须大于0', ResponseCode.VALIDATION);
    }

    if (!description) {
      throw new BusinessError('订单描述不能为空', ResponseCode.VALIDATION);
    }

    // 获取用户信息
    const userInfo = await userService.getUserInfo(userId);
    logger.info(`获取用户信息: userId=${userId}, openid=${userInfo?.openid ? userInfo.openid.substring(0, 8) + '***' : '空'}`);

    if (!userInfo || !userInfo.openid) {
      logger.error(`用户支付验证失败: userId=${userId}, userInfo=${!!userInfo}, openid=${userInfo?.openid || '空'}`);
      throw new BusinessError('用户未绑定微信，无法支付', ResponseCode.VALIDATION);
    }

    // 生成商户订单号（确保不超过32个字符）
    const timestamp = Date.now().toString();
    const randomStr = Math.random().toString(36).substr(2, 6);
    const outTradeNo = `R${userId}${timestamp}${randomStr}`.substr(0, 32);

    logger.info(`生成订单号: ${outTradeNo}, 长度: ${outTradeNo.length}`);
    
    // 金额转换为分
    const totalFee = Math.round(amount * 100);

    // 调用微信支付统一下单
    const paymentParams = await paymentService.jsapiOrder({
      description: description,
      outTradeNo: outTradeNo,
      totalFee: totalFee,
      openid: userInfo.openid,
      attach: JSON.stringify({
        userId: userId,
        type: 'recharge'
      })
    });

    logger.info(`用户 ${userId} 创建支付订单成功，订单号: ${outTradeNo}, 金额: ${amount}元`);

    success(res, {
      amount: amount,
      ...paymentParams
    });
  } catch (err) {
    next(err);
  }
};

/**
 * 查询支付订单状态
 * @route GET /api/payment/query/:outTradeNo
 */
const queryPaymentOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { outTradeNo } = req.params;

    if (!outTradeNo) {
      throw new BusinessError('订单号不能为空', ResponseCode.VALIDATION);
    }

    // 查询订单状态
    const orderInfo = await paymentService.queryOrder(outTradeNo);

    logger.info(`查询订单状态: ${outTradeNo}, 状态: ${orderInfo.trade_state}`);

    success(res, orderInfo);
  } catch (err) {
    next(err);
  }
};

/**
 * 关闭支付订单
 * @route POST /api/payment/close
 */
const closePaymentOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { outTradeNo } = req.body;

    if (!outTradeNo) {
      throw new BusinessError('订单号不能为空', ResponseCode.VALIDATION);
    }

    // 关闭订单
    await paymentService.closeOrder(outTradeNo);

    logger.info(`关闭订单: ${outTradeNo}`);

    success(res, { message: '订单关闭成功' });
  } catch (err) {
    next(err);
  }
};

/**
 * 微信支付回调通知
 * @route POST /api/payment/notify
 */
const paymentNotify = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取请求头信息
    const signature = req.headers['wechatpay-signature'] as string;
    const timestamp = req.headers['wechatpay-timestamp'] as string;
    const nonce = req.headers['wechatpay-nonce'] as string;
    const serial = req.headers['wechatpay-serial'] as string;

    // 获取请求体
    const body = JSON.stringify(req.body);

    logger.info('收到微信支付回调通知:', {
      signature,
      timestamp,
      nonce,
      serial,
      body
    });

    // 验证签名
    const isValid = paymentService.verifyNotification(signature, timestamp, nonce, body, serial);
    if (!isValid) {
      logger.error('微信支付回调签名验证失败');
      res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
      return;
    }

    // 解密通知数据
    const { resource } = req.body;
    const paymentData = paymentService.decryptNotification(
      resource.ciphertext,
      resource.nonce,
      resource.associated_data
    );

    logger.info('解密后的支付数据:', paymentData);

    // 处理支付成功
    if (paymentData.trade_state === 'SUCCESS') {
      await handlePaymentSuccess(paymentData);
    }

    // 返回成功响应
    res.json({ code: 'SUCCESS', message: '成功' });
  } catch (error: any) {
    logger.error('处理微信支付回调失败:', error);
    res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
};

/**
 * 处理支付成功逻辑
 * @param paymentData 支付数据
 */
const handlePaymentSuccess = async (paymentData: any) => {
  try {
    const { out_trade_no, transaction_id } = paymentData;
    
    // 解析附加数据
    let attachData: any = {};
    try {
      attachData = JSON.parse(paymentData.attach || '{}');
    } catch (e) {
      logger.warn('解析attach数据失败:', e);
    }

    const { userId, type } = attachData;

    if (type === 'recharge' && userId) {
      // 充值大米
      const amount = Math.round(paymentData.total_fee / 100); // 分转元
      await userService.rechargeCoins(userId, amount);
      
      logger.info(`用户 ${userId} 充值成功，订单号: ${out_trade_no}, 微信订单号: ${transaction_id}, 金额: ${amount}元`);
    }
  } catch (error: any) {
    logger.error('处理支付成功逻辑失败:', error);
    throw error;
  }
};

/**
 * 申请退款
 * @route POST /api/payment/refund
 */
const createRefund = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { outTradeNo, refundAmount, reason } = req.body;

    if (!outTradeNo) {
      throw new BusinessError('订单号不能为空', ResponseCode.VALIDATION);
    }

    if (!refundAmount || refundAmount <= 0) {
      throw new BusinessError('退款金额必须大于0', ResponseCode.VALIDATION);
    }

    // 查询原订单
    const orderInfo = await paymentService.queryOrder(outTradeNo);
    if (orderInfo.trade_state !== 'SUCCESS') {
      throw new BusinessError('订单未支付成功，无法退款', ResponseCode.VALIDATION);
    }

    // 生成退款单号（确保不超过32个字符）
    const refundTimestamp = Date.now().toString();
    const outRefundNo = `RF${refundTimestamp}${Math.random().toString(36).substr(2, 4)}`.substr(0, 32);
    
    // 金额转换为分
    const refundFee = Math.round(refundAmount * 100);
    const totalFee = orderInfo.total_fee;

    if (refundFee > totalFee) {
      throw new BusinessError('退款金额不能大于订单金额', ResponseCode.VALIDATION);
    }

    // 申请退款
    const refundResult = await paymentService.refund(
      outTradeNo,
      outRefundNo,
      totalFee,
      refundFee,
      reason
    );

    logger.info(`申请退款成功，订单号: ${outTradeNo}, 退款单号: ${outRefundNo}, 退款金额: ${refundAmount}元`);

    success(res, {
      outRefundNo: outRefundNo,
      refundAmount: refundAmount,
      ...refundResult
    });
  } catch (err) {
    next(err);
  }
};

/**
 * 查询退款状态
 * @route GET /api/payment/refund/:outRefundNo
 */
const queryRefund = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { outRefundNo } = req.params;

    if (!outRefundNo) {
      throw new BusinessError('退款单号不能为空', ResponseCode.VALIDATION);
    }

    // 查询退款状态
    const refundInfo = await paymentService.queryRefund(outRefundNo);

    logger.info(`查询退款状态: ${outRefundNo}, 状态: ${refundInfo.status}`);

    success(res, refundInfo);
  } catch (err) {
    next(err);
  }
};

/**
 * 微信退款回调通知
 * @route POST /api/payment/refund-notify
 */
const refundNotify = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取请求头信息
    const signature = req.headers['wechatpay-signature'] as string;
    const timestamp = req.headers['wechatpay-timestamp'] as string;
    const nonce = req.headers['wechatpay-nonce'] as string;
    const serial = req.headers['wechatpay-serial'] as string;

    // 获取请求体
    const body = JSON.stringify(req.body);

    logger.info('收到微信退款回调通知:', {
      signature,
      timestamp,
      nonce,
      serial,
      body
    });

    // 验证签名
    const isValid = paymentService.verifyNotification(signature, timestamp, nonce, body, serial);
    if (!isValid) {
      logger.error('微信退款回调签名验证失败');
      res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
      return;
    }

    // 解密通知数据
    const { resource } = req.body;
    const refundData = paymentService.decryptNotification(
      resource.ciphertext,
      resource.nonce,
      resource.associated_data
    ) as unknown as RefundNotification;

    logger.info('解密后的退款数据:', refundData);

    // 处理退款成功
    if (refundData.refund_status === 'SUCCESS') {
      await handleRefundSuccess(refundData);
    }

    // 返回成功响应
    res.json({ code: 'SUCCESS', message: '成功' });
  } catch (error: any) {
    logger.error('处理微信退款回调失败:', error);
    res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
};

/**
 * 处理退款成功逻辑
 * @param refundData 退款数据
 */
const handleRefundSuccess = async (refundData: RefundNotification) => {
  try {
    const { out_trade_no, out_refund_no, refund_id } = refundData;
    
    logger.info(`退款成功，订单号: ${out_trade_no}, 退款单号: ${out_refund_no}, 微信退款单号: ${refund_id}`);
    
    // 这里可以添加退款成功后的业务逻辑
    // 比如更新订单状态、扣减用户大米等
  } catch (error: any) {
    logger.error('处理退款成功逻辑失败:', error);
    throw error;
  }
};

export default {
  createPaymentOrder,
  queryPaymentOrder,
  closePaymentOrder,
  paymentNotify,
  createRefund,
  queryRefund,
  refundNotify
}; 