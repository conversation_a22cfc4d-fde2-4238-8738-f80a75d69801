"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 厨房种子数据脚本
 * 创建测试厨房数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
// 测试厨房数据
const testKitchens = [
    {
        id: 'KIT001',
        name: '测试厨房1',
        owner_id: 10001,
        avatar_url: 'https://example.com/kitchen1.jpg',
        notice: '欢迎光临测试厨房1',
        level: 1,
        category_limit: 10,
        dish_limit: 50,
        member_limit: 10,
        kitchen_limit: 2,
        dish_count: 0,
        member_count: 1,
        order_count: 0,
        total_sales: 0,
    },
    {
        id: 'KIT002',
        name: '测试厨房2',
        owner_id: 10002,
        avatar_url: 'https://example.com/kitchen2.jpg',
        notice: '欢迎光临测试厨房2',
        level: 1,
        category_limit: 10,
        dish_limit: 50,
        member_limit: 10,
        kitchen_limit: 2,
        dish_count: 0,
        member_count: 2,
        order_count: 0,
        total_sales: 0,
    },
    {
        id: 'KIT003',
        name: '高级厨房',
        owner_id: 10003,
        avatar_url: 'https://example.com/kitchen3.jpg',
        notice: '这是一个高级厨房，提供各种美食',
        level: 3,
        category_limit: 20,
        dish_limit: 100,
        member_limit: 20,
        kitchen_limit: 5,
        dish_count: 0,
        member_count: 3,
        order_count: 0,
        total_sales: 0,
    },
];
// 测试厨房成员数据
const testKitchenMembers = [
    // 厨房1的成员
    {
        kitchen_id: 'KIT001',
        user_id: 10001,
        role: 'owner',
        join_time: new Date(),
    },
    // 厨房2的成员
    {
        kitchen_id: 'KIT002',
        user_id: 10002,
        role: 'owner',
        join_time: new Date(),
    },
    {
        kitchen_id: 'KIT002',
        user_id: 10001,
        role: 'member',
        join_time: new Date(),
    },
    // 厨房3的成员
    {
        kitchen_id: 'KIT003',
        user_id: 10003,
        role: 'owner',
        join_time: new Date(),
    },
    {
        kitchen_id: 'KIT003',
        user_id: 10001,
        role: 'admin',
        join_time: new Date(),
    },
    {
        kitchen_id: 'KIT003',
        user_id: 10002,
        role: 'editor',
        join_time: new Date(),
    },
];
/**
 * 创建测试厨房数据
 */
function seedKitchens() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试厨房数据...');
        try {
            // 清空现有数据
            yield models_1.Kitchen.destroy({ where: {} });
            yield models_1.KitchenMember.destroy({ where: {} });
            logger_1.default.info('已清空现有厨房相关数据');
            // 创建测试厨房
            for (const kitchenData of testKitchens) {
                const kitchen = yield models_1.Kitchen.create(kitchenData);
                logger_1.default.info(`创建厨房: ${kitchen.name} (ID: ${kitchen.id})`);
            }
            // 创建测试厨房成员
            for (const memberData of testKitchenMembers) {
                yield models_1.KitchenMember.create(memberData);
                logger_1.default.info(`创建厨房成员: 用户 ${memberData.user_id} 加入厨房 ${memberData.kitchen_id} 作为 ${memberData.role}`);
            }
            logger_1.default.info(`共创建 ${testKitchens.length} 个测试厨房和 ${testKitchenMembers.length} 个测试厨房成员`);
        }
        catch (error) {
            logger_1.default.error('创建测试厨房数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试厨房
if (require.main === module) {
    seedKitchens()
        .then(() => {
        logger_1.default.info('创建测试厨房数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试厨房数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedKitchens;
//# sourceMappingURL=seedKitchens.js.map