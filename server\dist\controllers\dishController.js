"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dishService_1 = __importDefault(require("../services/dishService"));
const logger_1 = __importDefault(require("../utils/logger"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
const validator_1 = __importDefault(require("../utils/validator"));
/**
 * 获取菜品分类列表
 * @route GET /api/dish/categories
 */
const getCategories = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { kitchenId } = req.query;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const categories = yield dishService_1.default.getCategories(kitchenId);
        (0, response_1.success)(res, { categories });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取菜品列表
 * @route GET /api/dish/list
 */
const getDishList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { kitchenId, categoryId } = req.query;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const dishes = yield dishService_1.default.getDishList(kitchenId, categoryId, userId);
        (0, response_1.success)(res, { dishes });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取菜品详情
 * @route GET /api/dish/detail
 */
const getDishDetail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id, kitchenId } = req.query;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        const dish = yield dishService_1.default.getDishDetail(parseInt(id), userId, kitchenId);
        (0, response_1.success)(res, dish);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 搜索菜品
 * @route GET /api/dish/search
 */
const searchDishes = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { keyword, kitchenId } = req.query;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        // 验证关键词
        if (!keyword) {
            throw new error_1.BusinessError('缺少参数: keyword', response_1.ResponseCode.VALIDATION);
        }
        // 验证关键词长度
        const keywordStr = keyword;
        validator_1.default.validateStringLength(keywordStr, 1, 50, '搜索关键词');
        // 过滤关键词中的特殊字符和SQL注入字符
        const safeKeyword = validator_1.default.filterSqlInjection(validator_1.default.filterSpecialChars(keywordStr));
        // 验证厨房ID（如果提供）
        if (kitchenId) {
            validator_1.default.validateId(kitchenId, '厨房ID');
        }
        const result = yield dishService_1.default.searchDishes(safeKeyword, kitchenId, userId);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取热门关键词
 * @route GET /api/dish/hotKeywords
 */
const getHotKeywords = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const keywords = yield dishService_1.default.getHotKeywords();
        (0, response_1.success)(res, { keywords });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加菜品
 * @route POST /api/dish/add
 */
const addDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const dishData = req.body;
        // 验证必要参数
        if (!dishData.kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        if (!dishData.categoryId) {
            throw new error_1.BusinessError('缺少参数: categoryId', response_1.ResponseCode.VALIDATION);
        }
        if (!dishData.name) {
            throw new error_1.BusinessError('缺少参数: name', response_1.ResponseCode.VALIDATION);
        }
        if (dishData.price === undefined) {
            throw new error_1.BusinessError('缺少参数: price', response_1.ResponseCode.VALIDATION);
        }
        // 验证厨房ID格式
        validator_1.default.validateId(dishData.kitchenId, '厨房ID');
        // 验证分类ID格式
        validator_1.default.validateId(dishData.categoryId, '分类ID');
        // 验证菜品名称长度
        validator_1.default.validateStringLength(dishData.name, 2, 50, '菜品名称');
        // 验证菜品价格范围
        validator_1.default.validateNumberRange(Number(dishData.price), 0, 99999, '菜品价格');
        // 验证原价范围（如果提供）
        if (dishData.originalPrice !== undefined) {
            validator_1.default.validateNumberRange(Number(dishData.originalPrice), 0, 99999, '菜品原价');
        }
        // 验证描述长度（如果提供）
        if (dishData.description) {
            validator_1.default.validateStringLength(dishData.description, 0, 500, '菜品描述');
            // 过滤XSS
            dishData.description = validator_1.default.sanitizeInput(dishData.description);
        }
        // 验证图片URL（如果提供）
        if (dishData.image) {
            validator_1.default.validateUrl(dishData.image, '菜品图片', true);
        }
        // 验证标签（如果提供）
        if (dishData.tags && Array.isArray(dishData.tags)) {
            validator_1.default.validateArray(dishData.tags, '菜品标签', 0, 10);
            // 验证每个标签的长度
            dishData.tags.forEach((tag, index) => {
                validator_1.default.validateStringLength(tag, 1, 20, `标签${index + 1}`);
                // 过滤特殊字符
                dishData.tags[index] = validator_1.default.filterSpecialChars(tag);
            });
        }
        const result = yield dishService_1.default.addDish(userId, dishData);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新菜品
 * @route POST /api/dish/update
 */
const updateDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const dishData = req.body;
        logger_1.default.info(`用户 ${userId} 尝试更新菜品，数据: ${JSON.stringify(dishData)}`);
        // 验证菜品ID
        if (!dishData.id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        try {
            validator_1.default.validateId(dishData.id, '菜品ID');
        }
        catch (error) {
            logger_1.default.error(`菜品ID验证失败: ${dishData.id}, 错误: ${error.message}`);
            throw error;
        }
        // 验证分类ID（如果提供）
        if (dishData.categoryId !== undefined) {
            try {
                validator_1.default.validateId(dishData.categoryId, '分类ID');
            }
            catch (error) {
                logger_1.default.error(`分类ID验证失败: ${dishData.categoryId}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证菜品名称（如果提供）
        if (dishData.name !== undefined) {
            try {
                validator_1.default.validateStringLength(dishData.name, 2, 50, '菜品名称');
            }
            catch (error) {
                logger_1.default.error(`菜品名称验证失败: ${dishData.name}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证菜品价格（如果提供）
        if (dishData.price !== undefined) {
            try {
                validator_1.default.validateNumberRange(Number(dishData.price), 0, 99999, '菜品价格');
            }
            catch (error) {
                logger_1.default.error(`菜品价格验证失败: ${dishData.price}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证原价（如果提供）
        if (dishData.originalPrice !== undefined) {
            try {
                validator_1.default.validateNumberRange(Number(dishData.originalPrice), 0, 99999, '菜品原价');
            }
            catch (error) {
                logger_1.default.error(`菜品原价验证失败: ${dishData.originalPrice}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证描述（如果提供）
        if (dishData.description !== undefined) {
            try {
                validator_1.default.validateStringLength(dishData.description, 0, 500, '菜品描述');
                // 过滤XSS
                dishData.description = validator_1.default.sanitizeInput(dishData.description);
            }
            catch (error) {
                logger_1.default.error(`菜品描述验证失败: ${dishData.description}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证图片URL（如果提供）
        if (dishData.image !== undefined) {
            try {
                validator_1.default.validateUrl(dishData.image, '菜品图片', true);
            }
            catch (error) {
                logger_1.default.error(`菜品图片URL验证失败: ${dishData.image}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证标签（如果提供）
        if (dishData.tags !== undefined) {
            if (!Array.isArray(dishData.tags)) {
                throw new error_1.BusinessError('菜品标签必须是数组', response_1.ResponseCode.VALIDATION);
            }
            try {
                validator_1.default.validateArray(dishData.tags, '菜品标签', 0, 10);
                // 验证每个标签的长度
                dishData.tags.forEach((tag, index) => {
                    validator_1.default.validateStringLength(tag, 1, 20, `标签${index + 1}`);
                    // 过滤特殊字符
                    dishData.tags[index] = validator_1.default.filterSpecialChars(tag);
                });
            }
            catch (error) {
                logger_1.default.error(`菜品标签验证失败: ${JSON.stringify(dishData.tags)}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证状态（如果提供）
        if (dishData.status !== undefined) {
            try {
                validator_1.default.validateEnum(dishData.status, ['on', 'off'], '菜品状态');
            }
            catch (error) {
                logger_1.default.error(`菜品状态验证失败: ${dishData.status}, 错误: ${error.message}`);
                throw error;
            }
        }
        // 验证配料数组（如果提供）
        if (dishData.ingredients !== undefined) {
            if (!Array.isArray(dishData.ingredients)) {
                throw new error_1.BusinessError('配料必须是数组', response_1.ResponseCode.VALIDATION);
            }
            try {
                validator_1.default.validateArray(dishData.ingredients, '配料', 0, 20);
                // 验证每个配料的格式
                dishData.ingredients.forEach((ingredient, index) => {
                    if (ingredient.name) {
                        validator_1.default.validateStringLength(ingredient.name, 1, 50, `配料${index + 1}名称`);
                    }
                });
            }
            catch (error) {
                logger_1.default.error(`配料验证失败: ${JSON.stringify(dishData.ingredients)}, 错误: ${error.message}`);
                throw error;
            }
        }
        logger_1.default.info('所有数据验证通过，调用服务更新菜品');
        yield dishService_1.default.updateDish(userId, dishData);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        logger_1.default.error(`更新菜品失败: ${err.message}`);
        next(err);
    }
});
/**
 * 删除菜品
 * @route POST /api/dish/delete
 */
const deleteDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId } = req.body;
        if (!id || !kitchenId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield dishService_1.default.deleteDish(userId, parseInt(id), kitchenId);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新菜品状态
 * @route POST /api/dish/updateStatus
 */
const updateDishStatus = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, kitchenId, status } = req.body;
        if (!id || !kitchenId || !status) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield dishService_1.default.updateDishStatus(userId, parseInt(id), kitchenId, status);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新菜品排序
 * @route POST /api/dish/updateSort
 */
const updateDishSort = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, dishes } = req.body;
        if (!kitchenId || !dishes || !Array.isArray(dishes)) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield dishService_1.default.updateDishSort(userId, kitchenId, dishes);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 点赞菜品
 * @route POST /api/dish/like
 */
const likeDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        const result = yield dishService_1.default.likeDish(userId, parseInt(id));
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 举报菜品
 * @route POST /api/dish/report
 */
const reportDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id, reason } = req.body;
        if (!id || !reason) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        yield dishService_1.default.reportDish(userId, parseInt(id), reason);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加菜品到厨房
 * @route POST /api/dish/addToKitchen
 */
const addToKitchen = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { dishId, targetKitchenId, targetCategoryId } = req.body;
        if (!dishId || !targetKitchenId || !targetCategoryId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield dishService_1.default.addToKitchen(userId, parseInt(dishId), targetKitchenId, parseInt(targetCategoryId));
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加评论
 * @route POST /api/dish/comment
 */
const addComment = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { dishId, content, rating } = req.body;
        if (!dishId || !content) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        // 验证评分范围
        if (rating !== undefined && (rating < 1 || rating > 5)) {
            throw new error_1.BusinessError('评分必须在1-5之间', response_1.ResponseCode.VALIDATION);
        }
        const result = yield dishService_1.default.addComment(userId, parseInt(dishId), content, rating || 5);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getCategories,
    getDishList,
    getDishDetail,
    searchDishes,
    getHotKeywords,
    addDish,
    updateDish,
    deleteDish,
    updateDishStatus,
    updateDishSort,
    likeDish,
    reportDish,
    addToKitchen,
    addComment
};
//# sourceMappingURL=dishController.js.map