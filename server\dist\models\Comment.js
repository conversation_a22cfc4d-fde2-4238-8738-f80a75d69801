"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 评论模型
 * 存储用户对菜品的评论信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 评论模型类
class Comment extends sequelize_1.Model {
}
// 初始化评论模型
Comment.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '评论ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        comment: '评论内容',
    },
    rating: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 5.0,
        comment: '评分(1-5)',
        validate: {
            min: 1,
            max: 5,
        },
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Comment',
    tableName: 'comments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_rating',
            fields: ['rating'],
        },
        {
            name: 'idx_created_at',
            fields: ['created_at'],
        },
    ],
});
exports.default = Comment;
//# sourceMappingURL=Comment.js.map