"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const paymentService_1 = __importDefault(require("../services/paymentService"));
const userService_1 = __importDefault(require("../services/userService"));
const PaymentOrder_1 = __importDefault(require("../models/PaymentOrder"));
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
/**
 * 创建支付订单
 * @route POST /api/payment/create
 */
const createPaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { amount, description } = req.body;
        // 参数验证
        if (!amount || amount <= 0) {
            throw new error_1.BusinessError('支付金额必须大于0', response_1.ResponseCode.VALIDATION);
        }
        if (!description) {
            throw new error_1.BusinessError('订单描述不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 获取用户信息
        const userInfo = yield userService_1.default.getUserInfo(userId);
        logger_1.default.info(`获取用户信息: userId=${userId}, openid=${(userInfo === null || userInfo === void 0 ? void 0 : userInfo.openid) ? userInfo.openid.substring(0, 8) + '***' : '空'}`);
        if (!userInfo || !userInfo.openid) {
            logger_1.default.error(`用户支付验证失败: userId=${userId}, userInfo=${!!userInfo}, openid=${(userInfo === null || userInfo === void 0 ? void 0 : userInfo.openid) || '空'}`);
            throw new error_1.BusinessError('用户未绑定微信，无法支付', response_1.ResponseCode.VALIDATION);
        }
        // 生成商户订单号（确保不超过32个字符）
        const timestamp = Date.now().toString();
        const randomStr = Math.random().toString(36).substr(2, 6);
        const outTradeNo = `R${userId}${timestamp}${randomStr}`.substr(0, 32);
        logger_1.default.info(`生成订单号: ${outTradeNo}, 长度: ${outTradeNo.length}`);
        // 金额转换为分
        const totalFee = Math.round(amount * 100);
        // 计算对应的大米数量
        const coins = Math.round(amount * 100); // 1元=100大米
        // 先创建支付订单记录
        const paymentOrder = yield PaymentOrder_1.default.create({
            order_no: outTradeNo,
            user_id: userId,
            amount: amount,
            coins_amount: coins,
            status: 'pending',
            payment_method: 'wechat'
        });
        // 调用微信支付统一下单
        const paymentParams = yield paymentService_1.default.jsapiOrder({
            description: description,
            outTradeNo: outTradeNo,
            totalFee: totalFee,
            openid: userInfo.openid,
            attach: JSON.stringify({
                userId: userId,
                type: 'recharge',
                paymentOrderId: paymentOrder.id
            })
        });
        // 更新支付订单的prepay_id
        yield paymentOrder.update({
            prepay_id: paymentParams.prepayId
        });
        logger_1.default.info(`用户 ${userId} 创建支付订单成功，订单号: ${outTradeNo}, 金额: ${amount}元, 大米: ${coins}`);
        (0, response_1.success)(res, Object.assign({ amount: amount, coins: coins, outTradeNo: outTradeNo, paymentOrderId: paymentOrder.id }, paymentParams));
    }
    catch (err) {
        next(err);
    }
});
/**
 * 查询支付订单状态
 * @route GET /api/payment/query/:outTradeNo
 */
const queryPaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo } = req.params;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查询订单状态
        const orderInfo = yield paymentService_1.default.queryOrder(outTradeNo);
        logger_1.default.info(`查询订单状态: ${outTradeNo}, 状态: ${orderInfo.trade_state}`);
        (0, response_1.success)(res, orderInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 关闭支付订单
 * @route POST /api/payment/close
 */
const closePaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo } = req.body;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 关闭订单
        yield paymentService_1.default.closeOrder(outTradeNo);
        logger_1.default.info(`关闭订单: ${outTradeNo}`);
        (0, response_1.success)(res, { message: '订单关闭成功' });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 微信支付回调通知
 * @route POST /api/payment/notify
 */
const paymentNotify = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        logger_1.default.info('=== 微信支付回调开始 ===');
        // 获取请求头信息
        const signature = req.headers['wechatpay-signature'];
        const timestamp = req.headers['wechatpay-timestamp'];
        const nonce = req.headers['wechatpay-nonce'];
        const serial = req.headers['wechatpay-serial'];
        // 获取请求体
        const body = JSON.stringify(req.body);
        logger_1.default.info('微信支付回调请求信息:', {
            headers: {
                signature: signature ? '***已设置***' : '未设置',
                timestamp,
                nonce,
                serial
            },
            bodyLength: body.length,
            bodyPreview: body.substring(0, 200) + (body.length > 200 ? '...' : '')
        });
        // 检查必要的头信息
        if (!signature || !timestamp || !nonce || !serial) {
            logger_1.default.error('微信支付回调缺少必要的头信息', {
                hasSignature: !!signature,
                hasTimestamp: !!timestamp,
                hasNonce: !!nonce,
                hasSerial: !!serial
            });
            res.status(400).json({ code: 'FAIL', message: '缺少必要的头信息' });
            return;
        }
        // 验证签名
        logger_1.default.info('开始验证微信支付回调签名...');
        let isValid = false;
        try {
            isValid = paymentService_1.default.verifyNotification(signature, timestamp, nonce, body, serial);
            logger_1.default.info(`签名验证结果: ${isValid ? '成功' : '失败'}`);
        }
        catch (verifyError) {
            logger_1.default.error('签名验证过程中出错:', verifyError);
            res.status(400).json({ code: 'FAIL', message: '签名验证异常' });
            return;
        }
        if (!isValid) {
            logger_1.default.error('微信支付回调签名验证失败');
            res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
            return;
        }
        // 解密通知数据
        logger_1.default.info('开始解密微信支付回调数据...');
        let paymentData;
        try {
            const { resource } = req.body;
            if (!resource) {
                logger_1.default.error('微信支付回调缺少resource字段');
                res.status(400).json({ code: 'FAIL', message: '缺少resource字段' });
                return;
            }
            paymentData = paymentService_1.default.decryptNotification(resource.ciphertext, resource.nonce, resource.associated_data);
            logger_1.default.info('解密成功，支付数据:', {
                out_trade_no: paymentData.out_trade_no,
                transaction_id: paymentData.transaction_id,
                trade_state: paymentData.trade_state,
                total_fee: paymentData.total_fee
            });
        }
        catch (decryptError) {
            logger_1.default.error('解密微信支付回调数据失败:', decryptError);
            res.status(400).json({ code: 'FAIL', message: '解密失败' });
            return;
        }
        // 处理支付成功
        if (paymentData.trade_state === 'SUCCESS') {
            logger_1.default.info('支付状态为SUCCESS，开始处理支付成功逻辑...');
            yield handlePaymentSuccess(paymentData);
            logger_1.default.info('支付成功处理完成');
        }
        else {
            logger_1.default.warn(`支付状态不是SUCCESS: ${paymentData.trade_state}`);
        }
        // 返回成功响应
        logger_1.default.info('=== 微信支付回调处理完成 ===');
        res.json({ code: 'SUCCESS', message: '成功' });
    }
    catch (error) {
        logger_1.default.error('处理微信支付回调失败:', {
            message: error.message,
            stack: error.stack
        });
        res.status(500).json({ code: 'FAIL', message: '处理失败' });
    }
});
/**
 * 处理支付成功逻辑
 * @param paymentData 支付数据
 */
const handlePaymentSuccess = (paymentData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { out_trade_no, transaction_id } = paymentData;
        // 查找支付订单
        const paymentOrder = yield PaymentOrder_1.default.findOne({
            where: { order_no: out_trade_no }
        });
        if (!paymentOrder) {
            logger_1.default.error(`支付订单不存在: ${out_trade_no}`);
            return;
        }
        if (paymentOrder.status === 'paid') {
            logger_1.default.warn(`支付订单已处理: ${out_trade_no}`);
            return;
        }
        // 标记订单为已支付
        yield paymentOrder.markAsPaid(transaction_id);
        // 充值大米
        yield userService_1.default.rechargeCoins(paymentOrder.user_id, paymentOrder.coins_amount);
        logger_1.default.info(`用户 ${paymentOrder.user_id} 充值成功，订单号: ${out_trade_no}, 微信订单号: ${transaction_id}, 金额: ${paymentOrder.amount}元, 大米: ${paymentOrder.coins_amount}`);
    }
    catch (error) {
        logger_1.default.error('处理支付成功逻辑失败:', error);
        throw error;
    }
});
/**
 * 申请退款
 * @route POST /api/payment/refund
 */
const createRefund = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo, refundAmount, reason } = req.body;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        if (!refundAmount || refundAmount <= 0) {
            throw new error_1.BusinessError('退款金额必须大于0', response_1.ResponseCode.VALIDATION);
        }
        // 查询原订单
        const orderInfo = yield paymentService_1.default.queryOrder(outTradeNo);
        if (orderInfo.trade_state !== 'SUCCESS') {
            throw new error_1.BusinessError('订单未支付成功，无法退款', response_1.ResponseCode.VALIDATION);
        }
        // 生成退款单号（确保不超过32个字符）
        const refundTimestamp = Date.now().toString();
        const outRefundNo = `RF${refundTimestamp}${Math.random().toString(36).substr(2, 4)}`.substr(0, 32);
        // 金额转换为分
        const refundFee = Math.round(refundAmount * 100);
        const totalFee = orderInfo.total_fee;
        if (refundFee > totalFee) {
            throw new error_1.BusinessError('退款金额不能大于订单金额', response_1.ResponseCode.VALIDATION);
        }
        // 申请退款
        const refundResult = yield paymentService_1.default.refund(outTradeNo, outRefundNo, totalFee, refundFee, reason);
        logger_1.default.info(`申请退款成功，订单号: ${outTradeNo}, 退款单号: ${outRefundNo}, 退款金额: ${refundAmount}元`);
        (0, response_1.success)(res, Object.assign({ outRefundNo: outRefundNo, refundAmount: refundAmount }, refundResult));
    }
    catch (err) {
        next(err);
    }
});
/**
 * 查询退款状态
 * @route GET /api/payment/refund/:outRefundNo
 */
const queryRefund = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outRefundNo } = req.params;
        if (!outRefundNo) {
            throw new error_1.BusinessError('退款单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查询退款状态
        const refundInfo = yield paymentService_1.default.queryRefund(outRefundNo);
        logger_1.default.info(`查询退款状态: ${outRefundNo}, 状态: ${refundInfo.status}`);
        (0, response_1.success)(res, refundInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 微信退款回调通知
 * @route POST /api/payment/refund-notify
 */
const refundNotify = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 获取请求头信息
        const signature = req.headers['wechatpay-signature'];
        const timestamp = req.headers['wechatpay-timestamp'];
        const nonce = req.headers['wechatpay-nonce'];
        const serial = req.headers['wechatpay-serial'];
        // 获取请求体
        const body = JSON.stringify(req.body);
        logger_1.default.info('收到微信退款回调通知:', {
            signature,
            timestamp,
            nonce,
            serial,
            body
        });
        // 验证签名
        const isValid = paymentService_1.default.verifyNotification(signature, timestamp, nonce, body, serial);
        if (!isValid) {
            logger_1.default.error('微信退款回调签名验证失败');
            res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
            return;
        }
        // 解密通知数据
        const { resource } = req.body;
        const refundData = paymentService_1.default.decryptNotification(resource.ciphertext, resource.nonce, resource.associated_data);
        logger_1.default.info('解密后的退款数据:', refundData);
        // 处理退款成功
        if (refundData.refund_status === 'SUCCESS') {
            yield handleRefundSuccess(refundData);
        }
        // 返回成功响应
        res.json({ code: 'SUCCESS', message: '成功' });
    }
    catch (error) {
        logger_1.default.error('处理微信退款回调失败:', error);
        res.status(500).json({ code: 'FAIL', message: '处理失败' });
    }
});
/**
 * 处理退款成功逻辑
 * @param refundData 退款数据
 */
const handleRefundSuccess = (refundData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { out_trade_no, out_refund_no, refund_id } = refundData;
        logger_1.default.info(`退款成功，订单号: ${out_trade_no}, 退款单号: ${out_refund_no}, 微信退款单号: ${refund_id}`);
        // 这里可以添加退款成功后的业务逻辑
        // 比如更新订单状态、扣减用户大米等
    }
    catch (error) {
        logger_1.default.error('处理退款成功逻辑失败:', error);
        throw error;
    }
});
/**
 * 手动确认支付（测试用）
 * @route POST /api/payment/confirm
 */
const confirmPayment = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo } = req.body;
        const userId = req.user.id;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查找支付订单
        const paymentOrder = yield PaymentOrder_1.default.findOne({
            where: {
                order_no: outTradeNo,
                user_id: userId
            }
        });
        if (!paymentOrder) {
            throw new error_1.BusinessError('支付订单不存在', response_1.ResponseCode.NOT_FOUND);
        }
        if (paymentOrder.status === 'paid') {
            throw new error_1.BusinessError('订单已支付', response_1.ResponseCode.VALIDATION);
        }
        // 模拟支付成功
        const mockTransactionId = `TEST_${Date.now()}`;
        yield paymentOrder.markAsPaid(mockTransactionId);
        // 充值大米
        yield userService_1.default.rechargeCoins(paymentOrder.user_id, paymentOrder.coins_amount);
        logger_1.default.info(`手动确认支付成功，用户: ${paymentOrder.user_id}, 订单号: ${outTradeNo}, 大米: ${paymentOrder.coins_amount}`);
        (0, response_1.success)(res, {
            message: '支付确认成功',
            coins: paymentOrder.coins_amount
        });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    createPaymentOrder,
    queryPaymentOrder,
    closePaymentOrder,
    paymentNotify,
    createRefund,
    queryRefund,
    refundNotify,
    confirmPayment
};
//# sourceMappingURL=paymentController.js.map