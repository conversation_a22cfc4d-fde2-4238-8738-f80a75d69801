"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const paymentService_1 = __importDefault(require("../services/paymentService"));
const userService_1 = __importDefault(require("../services/userService"));
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
/**
 * 创建支付订单
 * @route POST /api/payment/create
 */
const createPaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { amount, description } = req.body;
        // 参数验证
        if (!amount || amount <= 0) {
            throw new error_1.BusinessError('支付金额必须大于0', response_1.ResponseCode.VALIDATION);
        }
        if (!description) {
            throw new error_1.BusinessError('订单描述不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 获取用户信息
        const userInfo = yield userService_1.default.getUserInfo(userId);
        logger_1.default.info(`获取用户信息: userId=${userId}, openid=${(userInfo === null || userInfo === void 0 ? void 0 : userInfo.openid) ? userInfo.openid.substring(0, 8) + '***' : '空'}`);
        if (!userInfo || !userInfo.openid) {
            logger_1.default.error(`用户支付验证失败: userId=${userId}, userInfo=${!!userInfo}, openid=${(userInfo === null || userInfo === void 0 ? void 0 : userInfo.openid) || '空'}`);
            throw new error_1.BusinessError('用户未绑定微信，无法支付', response_1.ResponseCode.VALIDATION);
        }
        // 生成商户订单号（确保不超过32个字符）
        const timestamp = Date.now().toString();
        const randomStr = Math.random().toString(36).substr(2, 6);
        const outTradeNo = `R${userId}${timestamp}${randomStr}`.substr(0, 32);
        logger_1.default.info(`生成订单号: ${outTradeNo}, 长度: ${outTradeNo.length}`);
        // 金额转换为分
        const totalFee = Math.round(amount * 100);
        // 调用微信支付统一下单
        const paymentParams = yield paymentService_1.default.jsapiOrder({
            description: description,
            outTradeNo: outTradeNo,
            totalFee: totalFee,
            openid: userInfo.openid,
            attach: JSON.stringify({
                userId: userId,
                type: 'recharge'
            })
        });
        logger_1.default.info(`用户 ${userId} 创建支付订单成功，订单号: ${outTradeNo}, 金额: ${amount}元`);
        (0, response_1.success)(res, Object.assign({ amount: amount }, paymentParams));
    }
    catch (err) {
        next(err);
    }
});
/**
 * 查询支付订单状态
 * @route GET /api/payment/query/:outTradeNo
 */
const queryPaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo } = req.params;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查询订单状态
        const orderInfo = yield paymentService_1.default.queryOrder(outTradeNo);
        logger_1.default.info(`查询订单状态: ${outTradeNo}, 状态: ${orderInfo.trade_state}`);
        (0, response_1.success)(res, orderInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 关闭支付订单
 * @route POST /api/payment/close
 */
const closePaymentOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo } = req.body;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 关闭订单
        yield paymentService_1.default.closeOrder(outTradeNo);
        logger_1.default.info(`关闭订单: ${outTradeNo}`);
        (0, response_1.success)(res, { message: '订单关闭成功' });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 微信支付回调通知
 * @route POST /api/payment/notify
 */
const paymentNotify = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 获取请求头信息
        const signature = req.headers['wechatpay-signature'];
        const timestamp = req.headers['wechatpay-timestamp'];
        const nonce = req.headers['wechatpay-nonce'];
        const serial = req.headers['wechatpay-serial'];
        // 获取请求体
        const body = JSON.stringify(req.body);
        logger_1.default.info('收到微信支付回调通知:', {
            signature,
            timestamp,
            nonce,
            serial,
            body
        });
        // 验证签名
        const isValid = paymentService_1.default.verifyNotification(signature, timestamp, nonce, body, serial);
        if (!isValid) {
            logger_1.default.error('微信支付回调签名验证失败');
            res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
            return;
        }
        // 解密通知数据
        const { resource } = req.body;
        const paymentData = paymentService_1.default.decryptNotification(resource.ciphertext, resource.nonce, resource.associated_data);
        logger_1.default.info('解密后的支付数据:', paymentData);
        // 处理支付成功
        if (paymentData.trade_state === 'SUCCESS') {
            yield handlePaymentSuccess(paymentData);
        }
        // 返回成功响应
        res.json({ code: 'SUCCESS', message: '成功' });
    }
    catch (error) {
        logger_1.default.error('处理微信支付回调失败:', error);
        res.status(500).json({ code: 'FAIL', message: '处理失败' });
    }
});
/**
 * 处理支付成功逻辑
 * @param paymentData 支付数据
 */
const handlePaymentSuccess = (paymentData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { out_trade_no, transaction_id } = paymentData;
        // 解析附加数据
        let attachData = {};
        try {
            attachData = JSON.parse(paymentData.attach || '{}');
        }
        catch (e) {
            logger_1.default.warn('解析attach数据失败:', e);
        }
        const { userId, type } = attachData;
        if (type === 'recharge' && userId) {
            // 充值大米
            const amount = Math.round(paymentData.total_fee / 100); // 分转元
            yield userService_1.default.rechargeCoins(userId, amount);
            logger_1.default.info(`用户 ${userId} 充值成功，订单号: ${out_trade_no}, 微信订单号: ${transaction_id}, 金额: ${amount}元`);
        }
    }
    catch (error) {
        logger_1.default.error('处理支付成功逻辑失败:', error);
        throw error;
    }
});
/**
 * 申请退款
 * @route POST /api/payment/refund
 */
const createRefund = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outTradeNo, refundAmount, reason } = req.body;
        if (!outTradeNo) {
            throw new error_1.BusinessError('订单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        if (!refundAmount || refundAmount <= 0) {
            throw new error_1.BusinessError('退款金额必须大于0', response_1.ResponseCode.VALIDATION);
        }
        // 查询原订单
        const orderInfo = yield paymentService_1.default.queryOrder(outTradeNo);
        if (orderInfo.trade_state !== 'SUCCESS') {
            throw new error_1.BusinessError('订单未支付成功，无法退款', response_1.ResponseCode.VALIDATION);
        }
        // 生成退款单号（确保不超过32个字符）
        const refundTimestamp = Date.now().toString();
        const outRefundNo = `RF${refundTimestamp}${Math.random().toString(36).substr(2, 4)}`.substr(0, 32);
        // 金额转换为分
        const refundFee = Math.round(refundAmount * 100);
        const totalFee = orderInfo.total_fee;
        if (refundFee > totalFee) {
            throw new error_1.BusinessError('退款金额不能大于订单金额', response_1.ResponseCode.VALIDATION);
        }
        // 申请退款
        const refundResult = yield paymentService_1.default.refund(outTradeNo, outRefundNo, totalFee, refundFee, reason);
        logger_1.default.info(`申请退款成功，订单号: ${outTradeNo}, 退款单号: ${outRefundNo}, 退款金额: ${refundAmount}元`);
        (0, response_1.success)(res, Object.assign({ outRefundNo: outRefundNo, refundAmount: refundAmount }, refundResult));
    }
    catch (err) {
        next(err);
    }
});
/**
 * 查询退款状态
 * @route GET /api/payment/refund/:outRefundNo
 */
const queryRefund = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { outRefundNo } = req.params;
        if (!outRefundNo) {
            throw new error_1.BusinessError('退款单号不能为空', response_1.ResponseCode.VALIDATION);
        }
        // 查询退款状态
        const refundInfo = yield paymentService_1.default.queryRefund(outRefundNo);
        logger_1.default.info(`查询退款状态: ${outRefundNo}, 状态: ${refundInfo.status}`);
        (0, response_1.success)(res, refundInfo);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 微信退款回调通知
 * @route POST /api/payment/refund-notify
 */
const refundNotify = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 获取请求头信息
        const signature = req.headers['wechatpay-signature'];
        const timestamp = req.headers['wechatpay-timestamp'];
        const nonce = req.headers['wechatpay-nonce'];
        const serial = req.headers['wechatpay-serial'];
        // 获取请求体
        const body = JSON.stringify(req.body);
        logger_1.default.info('收到微信退款回调通知:', {
            signature,
            timestamp,
            nonce,
            serial,
            body
        });
        // 验证签名
        const isValid = paymentService_1.default.verifyNotification(signature, timestamp, nonce, body, serial);
        if (!isValid) {
            logger_1.default.error('微信退款回调签名验证失败');
            res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
            return;
        }
        // 解密通知数据
        const { resource } = req.body;
        const refundData = paymentService_1.default.decryptNotification(resource.ciphertext, resource.nonce, resource.associated_data);
        logger_1.default.info('解密后的退款数据:', refundData);
        // 处理退款成功
        if (refundData.refund_status === 'SUCCESS') {
            yield handleRefundSuccess(refundData);
        }
        // 返回成功响应
        res.json({ code: 'SUCCESS', message: '成功' });
    }
    catch (error) {
        logger_1.default.error('处理微信退款回调失败:', error);
        res.status(500).json({ code: 'FAIL', message: '处理失败' });
    }
});
/**
 * 处理退款成功逻辑
 * @param refundData 退款数据
 */
const handleRefundSuccess = (refundData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { out_trade_no, out_refund_no, refund_id } = refundData;
        logger_1.default.info(`退款成功，订单号: ${out_trade_no}, 退款单号: ${out_refund_no}, 微信退款单号: ${refund_id}`);
        // 这里可以添加退款成功后的业务逻辑
        // 比如更新订单状态、扣减用户大米等
    }
    catch (error) {
        logger_1.default.error('处理退款成功逻辑失败:', error);
        throw error;
    }
});
exports.default = {
    createPaymentOrder,
    queryPaymentOrder,
    closePaymentOrder,
    paymentNotify,
    createRefund,
    queryRefund,
    refundNotify
};
//# sourceMappingURL=paymentController.js.map