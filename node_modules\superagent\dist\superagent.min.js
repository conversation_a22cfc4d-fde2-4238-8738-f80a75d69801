!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).superagent=t()}}((function(){var t={exports:{}};function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((r=o[n])===e||r.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(r){o=0;for(var n=(r=r.slice(0)).length;o<n;++o)r[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length},t=t.exports;var r;r=a,a.default=a,a.stable=p,a.stableStringify=p;var o=[],n=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function a(t,e,r,a){var u;void 0===a&&(a=i()),function t(e,r,o,n,i,a,u){var p;if(a+=1,"object"==typeof e&&null!==e){for(p=0;p<n.length;p++)if(n[p]===e)return void s("[Circular]",e,r,i);if(void 0!==u.depthLimit&&a>u.depthLimit)return void s("[...]",e,r,i);if(void 0!==u.edgesLimit&&o+1>u.edgesLimit)return void s("[...]",e,r,i);if(n.push(e),Array.isArray(e))for(p=0;p<e.length;p++)t(e[p],p,p,n,e,a,u);else{var c=Object.keys(e);for(p=0;p<c.length;p++){var l=c[p];t(e[l],l,p,n,e,a,u)}}n.pop()}}(t,"",0,[],void 0,0,a);try{u=0===n.length?JSON.stringify(t,e,r):JSON.stringify(t,c(e),r)}catch(l){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var p=o.pop();4===p.length?Object.defineProperty(p[0],p[1],p[3]):p[0][p[1]]=p[2]}}return u}function s(t,e,r,i){var a=Object.getOwnPropertyDescriptor(i,r);void 0!==a.get?a.configurable?(Object.defineProperty(i,r,{value:t}),o.push([i,r,e,a])):n.push([e,r,t]):(i[r]=t,o.push([i,r,e]))}function u(t,e){return t<e?-1:t>e?1:0}function p(t,e,r,a){void 0===a&&(a=i());var p,l=function t(e,r,n,i,a,p,c){var l;if(p+=1,"object"==typeof e&&null!==e){for(l=0;l<i.length;l++)if(i[l]===e)return void s("[Circular]",e,r,a);try{if("function"==typeof e.toJSON)return}catch(d){return}if(void 0!==c.depthLimit&&p>c.depthLimit)return void s("[...]",e,r,a);if(void 0!==c.edgesLimit&&n+1>c.edgesLimit)return void s("[...]",e,r,a);if(i.push(e),Array.isArray(e))for(l=0;l<e.length;l++)t(e[l],l,l,i,e,p,c);else{var f={},y=Object.keys(e).sort(u);for(l=0;l<y.length;l++){var h=y[l];t(e[h],h,l,i,e,p,c),f[h]=e[h]}if(void 0===a)return f;o.push([a,r,e]),a[r]=f}i.pop()}}(t,"",0,[],void 0,0,a)||t;try{p=0===n.length?JSON.stringify(l,e,r):JSON.stringify(l,c(e),r)}catch(y){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var f=o.pop();4===f.length?Object.defineProperty(f[0],f[1],f[3]):f[0][f[1]]=f[2]}}return p}function c(t){return t=void 0!==t?t:function(t,e){return e},function(e,r){if(n.length>0)for(var o=0;o<n.length;o++){var i=n[o];if(i[1]===e&&i[0]===r){r=i[2],n.splice(o,1);break}}return t.call(this,e,r)}}var l="undefined"!=typeof Symbol&&Symbol,f=Array.prototype.slice,y=Object.prototype.toString,h=Function.prototype.bind||function(t){var e=this;if("function"!=typeof e||"[object Function]"!==y.call(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var r,o=f.call(arguments,1),n=Math.max(0,e.length-o.length),i=[],a=0;a<n;a++)i.push("$"+a);if(r=Function("binder","return function ("+i.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var n=e.apply(this,o.concat(f.call(arguments)));return Object(n)===n?n:this}return e.apply(t,o.concat(f.call(arguments)))})),e.prototype){var s=function(){};s.prototype=e.prototype,r.prototype=new s,s.prototype=null}return r},d=h.call(Function.call,Object.prototype.hasOwnProperty),m=SyntaxError,b=Function,g=TypeError,v=function(t){try{return b('"use strict"; return ('+t+").constructor;")()}catch(e){}},w=Object.getOwnPropertyDescriptor;if(w)try{w({},"")}catch(rr){w=null}var _,S=function(){throw new g},E=w?function(){try{return S}catch(t){try{return w(arguments,"callee").get}catch(e){return S}}}():S,A="function"==typeof l&&"function"==typeof Symbol&&"symbol"==typeof l("foo")&&"symbol"==typeof Symbol("bar")&&function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(t,e);if(42!==n.value||!0!==n.enumerable)return!1}return!0}(),O=Object.getPrototypeOf||function(t){return t.__proto__},T={},j="undefined"==typeof Uint8Array?void 0:O(Uint8Array),P={"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":A?O([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":b,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":A?O(O([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&A?O((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&A?O((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":A?O(""[Symbol.iterator]()):void 0,"%Symbol%":A?Symbol:void 0,"%SyntaxError%":m,"%ThrowTypeError%":E,"%TypedArray%":j,"%TypeError%":g,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet},x={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},k=h.call(Function.call,Array.prototype.concat),R=h.call(Function.apply,Array.prototype.splice),C=h.call(Function.call,String.prototype.replace),I=h.call(Function.call,String.prototype.slice),F=h.call(Function.call,RegExp.prototype.exec),N=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,D=/\\(\\)?/g,U=function(t,e){var r,o=t;if(d(x,o)&&(o="%"+(r=x[o])[0]+"%"),d(P,o)){var n=P[o];if(n===T&&(n=function t(e){var r;if("%AsyncFunction%"===e)r=v("async function () {}");else if("%GeneratorFunction%"===e)r=v("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=v("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&(r=O(n.prototype))}return P[e]=r,r}(o)),void 0===n&&!e)throw new g("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new m("intrinsic "+t+" does not exist!")},M=function(t,e){if("string"!=typeof t||0===t.length)throw new g("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new g('"allowMissing" argument must be a boolean');if(null===F(/^%?[^%]*%?$/,t))throw new m("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(t){var e=I(t,0,1),r=I(t,-1);if("%"===e&&"%"!==r)throw new m("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new m("invalid intrinsic syntax, expected opening `%`");var o=[];return C(t,N,(function(t,e,r,n){o[o.length]=r?C(n,D,"$1"):e||t})),o}(t),o=r.length>0?r[0]:"",n=U("%"+o+"%",e),i=n.name,a=n.value,s=!1,u=n.alias;u&&(o=u[0],R(r,k([0,1],u)));for(var p=1,c=!0;p<r.length;p+=1){var l=r[p],f=I(l,0,1),y=I(l,-1);if(('"'===f||"'"===f||"`"===f||'"'===y||"'"===y||"`"===y)&&f!==y)throw new m("property names with quotes must have matching quotes");if("constructor"!==l&&c||(s=!0),d(P,i="%"+(o+="."+l)+"%"))a=P[i];else if(null!=a){if(!(l in a)){if(!e)throw new g("base intrinsic for "+t+" exists, but the property is not available.");return}if(w&&p+1>=r.length){var h=w(a,l);a=(c=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:a[l]}else c=d(a,l),a=a[l];c&&!s&&(P[i]=a)}}return a},L=M("%Function.prototype.apply%"),q=M("%Function.prototype.call%"),B=M("%Reflect.apply%",!0)||h.call(q,L),W=M("%Object.getOwnPropertyDescriptor%",!0),$=M("%Object.defineProperty%",!0),H=M("%Math.max%");if($)try{$({},"a",{value:1})}catch(rr){$=null}_=function(t){var e=B(h,q,arguments);return W&&$&&W(e,"length").configurable&&$(e,"length",{value:1+H(0,t.length-(arguments.length-1))}),e};var z=function(){return B(h,L,arguments)};$?$(_,"apply",{value:z}):_.apply=z;var G=_(M("String.prototype.indexOf")),J=function(t,e){var r=M(t,!!e);return"function"==typeof r&&G(t,".prototype.")>-1?_(r):r},V={},Q="function"==typeof Map&&Map.prototype,X=Object.getOwnPropertyDescriptor&&Q?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,K=Q&&X&&"function"==typeof X.get?X.get:null,Y=Q&&Map.prototype.forEach,Z="function"==typeof Set&&Set.prototype,tt=Object.getOwnPropertyDescriptor&&Z?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,et=Z&&tt&&"function"==typeof tt.get?tt.get:null,rt=Z&&Set.prototype.forEach,ot="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,nt="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,it="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,at=Boolean.prototype.valueOf,st=Object.prototype.toString,ut=Function.prototype.toString,pt=String.prototype.match,ct=String.prototype.slice,lt=String.prototype.replace,ft=String.prototype.toUpperCase,yt=String.prototype.toLowerCase,ht=RegExp.prototype.test,dt=Array.prototype.concat,mt=Array.prototype.join,bt=Array.prototype.slice,gt=Math.floor,vt="function"==typeof BigInt?BigInt.prototype.valueOf:null,wt=Object.getOwnPropertySymbols,_t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,St="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Et="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,At=Object.prototype.propertyIsEnumerable,Ot=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function Tt(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||ht.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-gt(-t):gt(t);if(o!==t){var n=String(o),i=ct.call(e,n.length+1);return lt.call(n,r,"$&_")+"."+lt.call(lt.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return lt.call(e,r,"$&_")}var jt=V.custom,Pt=It(jt)?jt:null;function xt(t,e,r){var o="double"===(r.quoteStyle||e)?'"':"'";return o+t+o}function kt(t){return lt.call(String(t),/"/g,"&quot;")}function Rt(t){return!("[object Array]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}function Ct(t){return!("[object RegExp]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}function It(t){if(St)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!_t)return!1;try{return _t.call(t),!0}catch(rr){}return!1}var Ft=Object.prototype.hasOwnProperty||function(t){return t in this};function Nt(t,e){return Ft.call(t,e)}function Dt(t){return st.call(t)}function Ut(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function Mt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+ft.call(e.toString(16))}function Lt(t){return"Object("+t+")"}function qt(t){return t+" { ? }"}function Bt(t,e,r,o){return t+" ("+e+") {"+(o?Wt(r,o):mt.call(r,", "))+"}"}function Wt(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+mt.call(t,","+r)+"\n"+e.prev}function $t(t,e){var r=Rt(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=Nt(t,n)?e(t[n],t):""}var i,a="function"==typeof wt?wt(t):[];if(St){i={};for(var s=0;s<a.length;s++)i["$"+a[s]]=a[s]}for(var u in t)Nt(t,u)&&(r&&String(Number(u))===u&&u<t.length||St&&i["$"+u]instanceof Symbol||(ht.call(/[^\w$]/,u)?o.push(e(u,t)+": "+e(t[u],t)):o.push(u+": "+e(t[u],t))));if("function"==typeof wt)for(var p=0;p<a.length;p++)At.call(t,a[p])&&o.push("["+e(a[p])+"]: "+e(t[a[p]],t));return o}var Ht=M("%TypeError%"),zt=M("%WeakMap%",!0),Gt=M("%Map%",!0),Jt=J("WeakMap.prototype.get",!0),Vt=J("WeakMap.prototype.set",!0),Qt=J("WeakMap.prototype.has",!0),Xt=J("Map.prototype.get",!0),Kt=J("Map.prototype.set",!0),Yt=J("Map.prototype.has",!0),Zt=function(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r,r},te=function(){var t,e,r,o={assert:function(t){if(!o.has(t))throw new Ht("Side channel does not contain "+function t(e,r,o,n){var i=r||{};if(Nt(i,"quoteStyle")&&"single"!==i.quoteStyle&&"double"!==i.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Nt(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!Nt(i,"customInspect")||i.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Nt(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Nt(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=i.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return function t(e,r){if(e.length>r.maxStringLength){var o=e.length-r.maxStringLength,n="... "+o+" more character"+(o>1?"s":"");return t(ct.call(e,0,r.maxStringLength),r)+n}return xt(lt.call(lt.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Mt),"single",r)}(e,i);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var u=String(e);return s?Tt(e,u):u}if("bigint"==typeof e){var p=String(e)+"n";return s?Tt(e,p):p}var c=void 0===i.depth?5:i.depth;if(void 0===o&&(o=0),o>=c&&c>0&&"object"==typeof e)return Rt(e)?"[Array]":"[Object]";var l,f=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=mt.call(Array(t.indent+1)," ")}return{base:r,prev:mt.call(Array(e+1),r)}}(i,o);if(void 0===n)n=[];else if(Ut(n,e)>=0)return"[Circular]";function y(e,r,a){if(r&&(n=bt.call(n)).push(r),a){var s={depth:i.depth};return Nt(i,"quoteStyle")&&(s.quoteStyle=i.quoteStyle),t(e,s,o+1,n)}return t(e,i,o+1,n)}if("function"==typeof e&&!Ct(e)){var h=function(t){if(t.name)return t.name;var e=pt.call(ut.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(e),d=$t(e,y);return"[Function"+(h?": "+h:" (anonymous)")+"]"+(d.length>0?" { "+mt.call(d,", ")+" }":"")}if(It(e)){var m=St?lt.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):_t.call(e);return"object"!=typeof e||St?m:Lt(m)}if((l=e)&&"object"==typeof l&&("undefined"!=typeof HTMLElement&&l instanceof HTMLElement||"string"==typeof l.nodeName&&"function"==typeof l.getAttribute)){for(var b="<"+yt.call(String(e.nodeName)),g=e.attributes||[],v=0;v<g.length;v++)b+=" "+g[v].name+"="+xt(kt(g[v].value),"double",i);return b+=">",e.childNodes&&e.childNodes.length&&(b+="..."),b+"</"+yt.call(String(e.nodeName))+">"}if(Rt(e)){if(0===e.length)return"[]";var w=$t(e,y);return f&&!function(t){for(var e=0;e<t.length;e++)if(Ut(t[e],"\n")>=0)return!1;return!0}(w)?"["+Wt(w,f)+"]":"[ "+mt.call(w,", ")+" ]"}if(function(t){return!("[object Error]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}(e)){var _=$t(e,y);return"cause"in Error.prototype||!("cause"in e)||At.call(e,"cause")?0===_.length?"["+String(e)+"]":"{ ["+String(e)+"] "+mt.call(_,", ")+" }":"{ ["+String(e)+"] "+mt.call(dt.call("[cause]: "+y(e.cause),_),", ")+" }"}if("object"==typeof e&&a){if(Pt&&"function"==typeof e[Pt]&&V)return V(e,{depth:c-o});if("symbol"!==a&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!K||!t||"object"!=typeof t)return!1;try{K.call(t);try{et.call(t)}catch(b){return!0}return t instanceof Map}catch(rr){}return!1}(e)){var S=[];return Y.call(e,(function(t,r){S.push(y(r,e,!0)+" => "+y(t,e))})),Bt("Map",K.call(e),S,f)}if(function(t){if(!et||!t||"object"!=typeof t)return!1;try{et.call(t);try{K.call(t)}catch(e){return!0}return t instanceof Set}catch(rr){}return!1}(e)){var E=[];return rt.call(e,(function(t){E.push(y(t,e))})),Bt("Set",et.call(e),E,f)}if(function(t){if(!ot||!t||"object"!=typeof t)return!1;try{ot.call(t,ot);try{nt.call(t,nt)}catch(b){return!0}return t instanceof WeakMap}catch(rr){}return!1}(e))return qt("WeakMap");if(function(t){if(!nt||!t||"object"!=typeof t)return!1;try{nt.call(t,nt);try{ot.call(t,ot)}catch(b){return!0}return t instanceof WeakSet}catch(rr){}return!1}(e))return qt("WeakSet");if(function(t){if(!it||!t||"object"!=typeof t)return!1;try{return it.call(t),!0}catch(rr){}return!1}(e))return qt("WeakRef");if(function(t){return!("[object Number]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}(e))return Lt(y(Number(e)));if(function(t){if(!t||"object"!=typeof t||!vt)return!1;try{return vt.call(t),!0}catch(rr){}return!1}(e))return Lt(y(vt.call(e)));if(function(t){return!("[object Boolean]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}(e))return Lt(at.call(e));if(function(t){return!("[object String]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}(e))return Lt(y(String(e)));if(!function(t){return!("[object Date]"!==Dt(t)||Et&&"object"==typeof t&&Et in t)}(e)&&!Ct(e)){var A=$t(e,y),O=Ot?Ot(e)===Object.prototype:e instanceof Object||e.constructor===Object,T=e instanceof Object?"":"null prototype",j=!O&&Et&&Object(e)===e&&Et in e?ct.call(Dt(e),8,-1):T?"Object":"",P=(O||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(j||T?"["+mt.call(dt.call([],j||[],T||[]),": ")+"] ":"");return 0===A.length?P+"{}":f?P+"{"+Wt(A,f)+"}":P+"{ "+mt.call(A,", ")+" }"}return String(e)}(t))},get:function(o){if(zt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Jt(t,o)}else if(Gt){if(e)return Xt(e,o)}else if(r)return function(t,e){var r=Zt(t,e);return r&&r.value}(r,o)},has:function(o){if(zt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Qt(t,o)}else if(Gt){if(e)return Yt(e,o)}else if(r)return function(t,e){return!!Zt(t,e)}(r,o);return!1},set:function(o,n){zt&&o&&("object"==typeof o||"function"==typeof o)?(t||(t=new zt),Vt(t,o,n)):Gt?(e||(e=new Gt),Kt(e,o,n)):(r||(r={key:{},next:null}),function(t,e,r){var o=Zt(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(r,o,n))}};return o},ee=String.prototype.replace,re=/%20/g,oe={default:"RFC3986",formatters:{RFC1738:function(t){return ee.call(t,re,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:"RFC3986"},ne=Object.prototype.hasOwnProperty,ie=Array.isArray,ae=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),se={combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],i=n.obj[n.prop],a=Object.keys(i),s=0;s<a.length;++s){var u=a[s],p=i[u];"object"==typeof p&&null!==p&&-1===r.indexOf(p)&&(e.push({obj:i,prop:u}),r.push(p))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(ie(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(rr){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var i=t;if("symbol"==typeof t?i=Symbol.prototype.toString.call(t):"string"!=typeof t&&(i=String(t)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var a="",s=0;s<i.length;++s){var u=i.charCodeAt(s);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||n===oe.RFC1738&&(40===u||41===u)?a+=i.charAt(s):u<128?a+=ae[u]:u<2048?a+=ae[192|u>>6]+ae[128|63&u]:u<55296||u>=57344?a+=ae[224|u>>12]+ae[128|u>>6&63]+ae[128|63&u]:(s+=1,u=65536+((1023&u)<<10|1023&i.charCodeAt(s)),a+=ae[240|u>>18]+ae[128|u>>12&63]+ae[128|u>>6&63]+ae[128|63&u])}return a},isBuffer:function(t){return!(!t||"object"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(ie(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r){if(ie(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!ne.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return ie(e)&&!ie(r)&&(n=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r}(e,o)),ie(e)&&ie(r)?(r.forEach((function(r,n){if(ne.call(e,n)){var i=e[n];i&&"object"==typeof i&&r&&"object"==typeof r?e[n]=t(i,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var i=r[n];return ne.call(e,n)?e[n]=t(e[n],i,o):e[n]=i,e}),n)}},ue=Object.prototype.hasOwnProperty,pe={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},ce=Array.isArray,le=String.prototype.split,fe=Array.prototype.push,ye=function(t,e){fe.apply(t,ce(e)?e:[e])},he=Date.prototype.toISOString,de=oe.default,me={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:se.encode,encodeValuesOnly:!1,format:de,formatter:oe.formatters[de],indices:!1,serializeDate:function(t){return he.call(t)},skipNulls:!1,strictNullHandling:!1},be={},ge=function t(e,r,o,n,i,a,s,u,p,c,l,f,y,h,d,m){for(var b,g=e,v=m,w=0,_=!1;void 0!==(v=v.get(be))&&!_;){var S=v.get(e);if(w+=1,void 0!==S){if(S===w)throw new RangeError("Cyclic object value");_=!0}void 0===v.get(be)&&(w=0)}if("function"==typeof u?g=u(r,g):g instanceof Date?g=l(g):"comma"===o&&ce(g)&&(g=se.maybeMap(g,(function(t){return t instanceof Date?l(t):t}))),null===g){if(i)return s&&!h?s(r,me.encoder,d,"key",f):r;g=""}if("string"==typeof(b=g)||"number"==typeof b||"boolean"==typeof b||"symbol"==typeof b||"bigint"==typeof b||se.isBuffer(g)){if(s){var E=h?r:s(r,me.encoder,d,"key",f);if("comma"===o&&h){for(var A=le.call(String(g),","),O="",T=0;T<A.length;++T)O+=(0===T?"":",")+y(s(A[T],me.encoder,d,"value",f));return[y(E)+(n&&ce(g)&&1===A.length?"[]":"")+"="+O]}return[y(E)+"="+y(s(g,me.encoder,d,"value",f))]}return[y(r)+"="+y(String(g))]}var j,P=[];if(void 0===g)return P;if("comma"===o&&ce(g))j=[{value:g.length>0?g.join(",")||null:void 0}];else if(ce(u))j=u;else{var x=Object.keys(g);j=p?x.sort(p):x}for(var k=n&&ce(g)&&1===g.length?r+"[]":r,R=0;R<j.length;++R){var C=j[R],I="object"==typeof C&&void 0!==C.value?C.value:g[C];if(!a||null!==I){var F=ce(g)?"function"==typeof o?o(k,C):k:k+(c?"."+C:"["+C+"]");m.set(e,w);var N=te();N.set(be,m),ye(P,t(I,F,o,n,i,a,s,u,p,c,l,f,y,h,d,N))}}return P},ve=(Object.prototype.hasOwnProperty,Array.isArray,{stringify:function(t,e){var r,o=t,n=function(t){if(!t)return me;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||me.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=oe.default;if(void 0!==t.format){if(!ue.call(oe.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o=oe.formatters[r],n=me.filter;return("function"==typeof t.filter||ce(t.filter))&&(n=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:me.addQueryPrefix,allowDots:void 0===t.allowDots?me.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:me.charsetSentinel,delimiter:void 0===t.delimiter?me.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:me.encode,encoder:"function"==typeof t.encoder?t.encoder:me.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:me.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:me.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:me.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:me.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):ce(n.filter)&&(r=n.filter);var i,a=[];if("object"!=typeof o||null===o)return"";i=e&&e.arrayFormat in pe?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var s=pe[i];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var u="comma"===s&&e&&e.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var p=te(),c=0;c<r.length;++c){var l=r[c];n.skipNulls&&null===o[l]||ye(a,ge(o[l],l,s,u,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,p))}var f=a.join(n.delimiter),y=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),f.length>0?y+f:""}}),we={type:t=>t.split(/ *; */).shift(),params:t=>{const e={};for(const r of t.split(/ *; */)){const t=r.split(/ *= */),o=t.shift(),n=t.shift();o&&n&&(e[o]=n)}return e},parseLinks:t=>{const e={};for(const r of t.split(/ *, */)){const t=r.split(/ *; */),o=t[0].slice(1,-1);e[t[1].split(/ *= */)[1].slice(1,-1)]=o}return e},isObject:t=>null!==t&&"object"==typeof t};we.hasOwn=Object.hasOwn||function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(t),e)},we.mixin=(t,e)=>{for(const r in e)we.hasOwn(e,r)&&(t[r]=e[r])};var _e,Se,Ee,Ae=_e={};function Oe(){throw new Error("setTimeout has not been defined")}function Te(){throw new Error("clearTimeout has not been defined")}function je(t){if(Se===setTimeout)return setTimeout(t,0);if((Se===Oe||!Se)&&setTimeout)return Se=setTimeout,setTimeout(t,0);try{return Se(t,0)}catch(rr){try{return Se.call(null,t,0)}catch(rr){return Se.call(this,t,0)}}}!function(){try{Se="function"==typeof setTimeout?setTimeout:Oe}catch(rr){Se=Oe}try{Ee="function"==typeof clearTimeout?clearTimeout:Te}catch(rr){Ee=Te}}();var Pe,xe=[],ke=!1,Re=-1;function Ce(){ke&&Pe&&(ke=!1,Pe.length?xe=Pe.concat(xe):Re=-1,xe.length&&Ie())}function Ie(){if(!ke){var t=je(Ce);ke=!0;for(var e=xe.length;e;){for(Pe=xe,xe=[];++Re<e;)Pe&&Pe[Re].run();Re=-1,e=xe.length}Pe=null,ke=!1,function(t){if(Ee===clearTimeout)return clearTimeout(t);if((Ee===Te||!Ee)&&clearTimeout)return Ee=clearTimeout,clearTimeout(t);try{Ee(t)}catch(rr){try{return Ee.call(null,t)}catch(rr){return Ee.call(this,t)}}}(t)}}function Fe(t,e){this.fun=t,this.array=e}function Ne(){}Ae.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];xe.push(new Fe(t,e)),1!==xe.length||ke||je(Ie)},Fe.prototype.run=function(){this.fun.apply(null,this.array)},Ae.title="browser",Ae.browser=!0,Ae.env={},Ae.argv=[],Ae.version="",Ae.versions={},Ae.on=Ne,Ae.addListener=Ne,Ae.once=Ne,Ae.off=Ne,Ae.removeListener=Ne,Ae.removeAllListeners=Ne,Ae.emit=Ne,Ae.prependListener=Ne,Ae.prependOnceListener=Ne,Ae.listeners=function(t){return[]},Ae.binding=function(t){throw new Error("process.binding is not supported")},Ae.cwd=function(){return"/"},Ae.chdir=function(t){throw new Error("process.chdir is not supported")},Ae.umask=function(){return 0};var De={};(function(t){(function(){const{isObject:e,hasOwn:r}=we;function o(){}De=o,o.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},o.prototype.parse=function(t){return this._parser=t,this},o.prototype.responseType=function(t){return this._responseType=t,this},o.prototype.serialize=function(t){return this._serializer=t,this},o.prototype.timeout=function(t){if(!t||"object"!=typeof t)return this._timeout=t,this._responseTimeout=0,this._uploadTimeout=0,this;for(const e in t)if(r(t,e))switch(e){case"deadline":this._timeout=t.deadline;break;case"response":this._responseTimeout=t.response;break;case"upload":this._uploadTimeout=t.upload;break;default:console.warn("Unknown timeout option",e)}return this},o.prototype.retry=function(t,e){return 0!==arguments.length&&!0!==t||(t=1),t<=0&&(t=0),this._maxRetries=t,this._retries=0,this._retryCallback=e,this};const n=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),i=new Set([408,413,429,500,502,503,504,521,522,524]);o.prototype._shouldRetry=function(t,e){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(t,e);if(!0===r)return!0;if(!1===r)return!1}catch(r){console.error(r)}if(e&&e.status&&i.has(e.status))return!0;if(t){if(t.code&&n.has(t.code))return!0;if(t.timeout&&"ECONNABORTED"===t.code)return!0;if(t.crossDomain)return!0}return!1},o.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},o.prototype.then=function(t,e){if(!this._fullfilledPromise){const t=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((e,r)=>{t.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void r(this.timedoutError);const t=new Error("Aborted");t.code="ABORTED",t.status=this.status,t.method=this.method,t.url=this.url,r(t)}),t.end((t,o)=>{t?r(t):e(o)})})}return this._fullfilledPromise.then(t,e)},o.prototype.catch=function(t){return this.then(void 0,t)},o.prototype.use=function(t){return t(this),this},o.prototype.ok=function(t){if("function"!=typeof t)throw new Error("Callback required");return this._okCallback=t,this},o.prototype._isResponseOK=function(t){return!!t&&(this._okCallback?this._okCallback(t):t.status>=200&&t.status<300)},o.prototype.get=function(t){return this._header[t.toLowerCase()]},o.prototype.getHeader=o.prototype.get,o.prototype.set=function(t,o){if(e(t)){for(const e in t)r(t,e)&&this.set(e,t[e]);return this}return this._header[t.toLowerCase()]=o,this.header[t]=o,this},o.prototype.unset=function(t){return delete this._header[t.toLowerCase()],delete this.header[t],this},o.prototype.field=function(t,o,n){if(null==t)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(e(t)){for(const e in t)r(t,e)&&this.field(e,t[e]);return this}if(Array.isArray(o)){for(const e in o)r(o,e)&&this.field(t,o[e]);return this}if(null==o)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof o&&(o=String(o)),n?this._getFormData().append(t,o,n):this._getFormData().append(t,o),this},o.prototype.abort=function(){if(this._aborted)return this;if(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req){if(V.gte(t.version,"v13.0.0")&&V.lt(t.version,"v14.0.0"))throw new Error("Superagent does not work in v13 properly with abort() due to Node.js core changes");V.gte(t.version,"v14.0.0")&&(this.req.destroyed=!0),this.req.abort()}return this.clearTimeout(),this.emit("abort"),this},o.prototype._auth=function(t,e,r,o){switch(r.type){case"basic":this.set("Authorization","Basic "+o(`${t}:${e}`));break;case"auto":this.username=t,this.password=e;break;case"bearer":this.set("Authorization","Bearer "+t)}return this},o.prototype.withCredentials=function(t){return void 0===t&&(t=!0),this._withCredentials=t,this},o.prototype.redirects=function(t){return this._maxRedirects=t,this},o.prototype.maxResponseSize=function(t){if("number"!=typeof t)throw new TypeError("Invalid argument");return this._maxResponseSize=t,this},o.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},o.prototype.send=function(t){const o=e(t);let n=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(o&&!this._data)Array.isArray(t)?this._data=[]:this._isHost(t)||(this._data={});else if(t&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(o&&e(this._data))for(const e in t)r(t,e)&&(this._data[e]=t[e]);else"string"==typeof t?(n||this.type("form"),(n=this._header["content-type"])&&(n=n.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===n?this._data?`${this._data}&${t}`:t:(this._data||"")+t):this._data=t;return!o||this._isHost(t)||n||this.type("json"),this},o.prototype.sortQuery=function(t){return this._sort=void 0===t||t,this},o.prototype._finalizeQueryString=function(){const t=this._query.join("&");if(t&&(this.url+=(this.url.includes("?")?"&":"?")+t),this._query.length=0,this._sort){const t=this.url.indexOf("?");if(t>=0){const e=this.url.slice(t+1).split("&");"function"==typeof this._sort?e.sort(this._sort):e.sort(),this.url=this.url.slice(0,t)+"?"+e.join("&")}}},o.prototype._appendQueryString=()=>{console.warn("Unsupported")},o.prototype._timeoutError=function(t,e,r){if(this._aborted)return;const o=new Error(t+e+"ms exceeded");o.timeout=e,o.code="ECONNABORTED",o.errno=r,this.timedout=!0,this.timedoutError=o,this.abort(),this.callback(o)},o.prototype._setTimeouts=function(){const t=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{t._timeoutError("Timeout of ",t._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{t._timeoutError("Response timeout of ",t._responseTimeout,"ETIMEDOUT")},this._responseTimeout))}}).call(this)}).call(this,_e);var Ue;function Me(){}Ue=Me,Me.prototype.get=function(t){return this.header[t.toLowerCase()]},Me.prototype._setHeaderProperties=function(t){const e=t["content-type"]||"";this.type=we.type(e);const r=we.params(e);for(const n in r)Object.prototype.hasOwnProperty.call(r,n)&&(this[n]=r[n]);this.links={};try{t.link&&(this.links=we.parseLinks(t.link))}catch(o){}},Me.prototype._setStatusProperties=function(t){const e=Math.trunc(t/100);this.statusCode=t,this.status=this.statusCode,this.statusType=e,this.info=1===e,this.ok=2===e,this.redirect=3===e,this.clientError=4===e,this.serverError=5===e,this.error=(4===e||5===e)&&this.toError(),this.created=201===t,this.accepted=202===t,this.noContent=204===t,this.badRequest=400===t,this.unauthorized=401===t,this.notAcceptable=406===t,this.forbidden=403===t,this.notFound=404===t,this.unprocessableEntity=422===t};var Le={};function qe(){this._defaults=[]}for(const or of["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"])qe.prototype[or]=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return this._defaults.push({fn:or,args:e}),this};qe.prototype._setDefaults=function(t){for(const e of this._defaults)t[e.fn](...e.args)},Le=qe;var Be={};let We;"undefined"!=typeof window?We=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),We=this):We=self;const{isObject:$e,mixin:He,hasOwn:ze}=we;function Ge(){}const Je=Be=Be=function(t,e){return"function"==typeof e?new Be.Request("GET",t).end(e):1===arguments.length?new Be.Request("GET",t):new Be.Request(t,e)};Be.Request=tr,Je.getXHR=()=>{if(We.XMLHttpRequest)return new We.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const Ve="".trim?t=>t.trim():t=>t.replace(/(^\s*|\s*$)/g,"");function Qe(t){if(!$e(t))return t;const e=[];for(const r in t)ze(t,r)&&Xe(e,r,t[r]);return e.join("&")}function Xe(t,e,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))for(const o of r)Xe(t,e,o);else if($e(r))for(const o in r)ze(r,o)&&Xe(t,`${e}[${o}]`,r[o]);else t.push(encodeURI(e)+"="+encodeURIComponent(r));else t.push(encodeURI(e))}function Ke(t){const e={},r=t.split("&");let o,n;for(let i=0,a=r.length;i<a;++i)-1===(n=(o=r[i]).indexOf("="))?e[decodeURIComponent(o)]="":e[decodeURIComponent(o.slice(0,n))]=decodeURIComponent(o.slice(n+1));return e}function Ye(t){return/[/+]json($|[^-\w])/i.test(t)}function Ze(t){this.req=t,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:e}=this.xhr;1223===e&&(e=204),this._setStatusProperties(e),this.headers=function(t){const e=t.split(/\r?\n/),r={};let o,n,i,a;for(let s=0,u=e.length;s<u;++s)-1!==(o=(n=e[s]).indexOf(":"))&&(i=n.slice(0,o).toLowerCase(),a=Ve(n.slice(o+1)),r[i]=a);return r}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&t._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function tr(t,e){const r=this;this._query=this._query||[],this.method=t,this.url=e,this.header={},this._header={},this.on("end",()=>{let t,e=null,o=null;try{o=new Ze(r)}catch(n){return(e=new Error("Parser is unable to parse the response")).parse=!0,e.original=n,r.xhr?(e.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,e.status=r.xhr.status?r.xhr.status:null,e.statusCode=e.status):(e.rawResponse=null,e.status=null),r.callback(e)}r.emit("response",o);try{r._isResponseOK(o)||(t=new Error(o.statusText||o.text||"Unsuccessful HTTP response"))}catch(n){t=n}t?(t.original=e,t.response=o,t.status=t.status||o.status,r.callback(t,o)):r.callback(null,o)})}Je.serializeObject=Qe,Je.parseString=Ke,Je.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},Je.serialize={"application/x-www-form-urlencoded":ve.stringify,"application/json":r},Je.parse={"application/x-www-form-urlencoded":Ke,"application/json":JSON.parse},He(Ze.prototype,Ue.prototype),Ze.prototype._parseBody=function(t){let e=Je.parse[this.type];return this.req._parser?this.req._parser(this,t):(!e&&Ye(this.type)&&(e=Je.parse["application/json"]),e&&t&&(t.length>0||t instanceof Object)?e(t):null)},Ze.prototype.toError=function(){const{req:t}=this,{method:e}=t,{url:r}=t,o=`cannot ${e} ${r} (${this.status})`,n=new Error(o);return n.status=this.status,n.method=e,n.url=r,n},Je.Response=Ze,t(tr.prototype),He(tr.prototype,De.prototype),tr.prototype.type=function(t){return this.set("Content-Type",Je.types[t]||t),this},tr.prototype.accept=function(t){return this.set("Accept",Je.types[t]||t),this},tr.prototype.auth=function(t,e,r){1===arguments.length&&(e=""),"object"==typeof e&&null!==e&&(r=e,e=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});const o=r.encoder?r.encoder:t=>{if("function"==typeof btoa)return btoa(t);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(t,e,r,o)},tr.prototype.query=function(t){return"string"!=typeof t&&(t=Qe(t)),t&&this._query.push(t),this},tr.prototype.attach=function(t,e,r){if(e){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(t,e,r||e.name)}return this},tr.prototype._getFormData=function(){return this._formData||(this._formData=new We.FormData),this._formData},tr.prototype.callback=function(t,e){if(this._shouldRetry(t,e))return this._retry();const r=this._callback;this.clearTimeout(),t&&(this._maxRetries&&(t.retries=this._retries-1),this.emit("error",t)),r(t,e)},tr.prototype.crossDomainError=function(){const t=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");t.crossDomain=!0,t.status=this.status,t.method=this.method,t.url=this.url,this.callback(t)},tr.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},tr.prototype.ca=tr.prototype.agent,tr.prototype.buffer=tr.prototype.ca,tr.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},tr.prototype.pipe=tr.prototype.write,tr.prototype._isHost=function(t){return t&&"object"==typeof t&&!Array.isArray(t)&&"[object Object]"!==Object.prototype.toString.call(t)},tr.prototype.end=function(t){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=t||Ge,this._finalizeQueryString(),this._end()},tr.prototype._setUploadTimeout=function(){const t=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{t._timeoutError("Upload timeout of ",t._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},tr.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const t=this;this.xhr=Je.getXHR();const{xhr:e}=this;let r=this._formData||this._data;this._setTimeouts(),e.addEventListener("readystatechange",()=>{const{readyState:r}=e;if(r>=2&&t._responseTimeoutTimer&&clearTimeout(t._responseTimeoutTimer),4!==r)return;let o;try{o=e.status}catch(n){o=0}if(!o){if(t.timedout||t._aborted)return;return t.crossDomainError()}t.emit("end")});const o=(e,r)=>{r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(t._uploadTimeoutTimer)),r.direction=e,t.emit("progress",r)};if(this.hasListeners("progress"))try{e.addEventListener("progress",o.bind(null,"download")),e.upload&&e.upload.addEventListener("progress",o.bind(null,"upload"))}catch(n){}e.upload&&this._setUploadTimeout();try{this.username&&this.password?e.open(this.method,this.url,!0,this.username,this.password):e.open(this.method,this.url,!0)}catch(n){return this.callback(n)}if(this._withCredentials&&(e.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){const t=this._header["content-type"];let e=this._serializer||Je.serialize[t?t.split(";")[0]:""];!e&&Ye(t)&&(e=Je.serialize["application/json"]),e&&(r=e(r))}for(const i in this.header)null!==this.header[i]&&ze(this.header,i)&&e.setRequestHeader(i,this.header[i]);this._responseType&&(e.responseType=this._responseType),this.emit("request",this),e.send(void 0===r?null:r)},Je.agent=()=>new Le;for(const or of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])Le.prototype[or.toLowerCase()]=function(t,e){const r=new Je.Request(or,t);return this._setDefaults(r),e&&r.end(e),r};function er(t,e,r){const o=Je("DELETE",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o}return Le.prototype.del=Le.prototype.delete,Je.get=(t,e,r)=>{const o=Je("GET",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},Je.head=(t,e,r)=>{const o=Je("HEAD",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},Je.options=(t,e,r)=>{const o=Je("OPTIONS",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Je.del=er,Je.delete=er,Je.patch=(t,e,r)=>{const o=Je("PATCH",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Je.post=(t,e,r)=>{const o=Je("POST",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Je.put=(t,e,r)=>{const o=Je("PUT",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Be}));