/**
 * 系统设置服务
 * 处理系统配置相关的业务逻辑
 */
import { SystemSetting } from '../models';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { ResponseCode } from '../utils/response';

class SystemSettingService {
  /**
   * 获取设置值
   * @param key 设置键名
   * @returns 设置值
   */
  async getSetting(key: string): Promise<any> {
    try {
      const setting = await SystemSetting.findOne({
        where: { key }
      });

      if (!setting) {
        return null;
      }

      // 根据类型转换值
      switch (setting.type) {
        case 'boolean':
          return setting.value === 'true';
        case 'number':
          return Number(setting.value);
        case 'json':
          try {
            return JSON.parse(setting.value);
          } catch {
            return setting.value;
          }
        default:
          return setting.value;
      }
    } catch (error) {
      logger.error(`获取系统设置失败: ${error}`);
      throw new BusinessError('获取系统设置失败', ResponseCode.SERVER_ERROR);
    }
  }

  /**
   * 设置配置值
   * @param key 设置键名
   * @param value 设置值
   * @param type 值类型
   * @param description 描述
   */
  async setSetting(key: string, value: any, type: 'boolean' | 'string' | 'number' | 'json' = 'string', description?: string): Promise<void> {
    try {
      let stringValue: string;

      // 根据类型转换值
      switch (type) {
        case 'boolean':
          stringValue = String(Boolean(value));
          break;
        case 'number':
          stringValue = String(Number(value));
          break;
        case 'json':
          stringValue = JSON.stringify(value);
          break;
        default:
          stringValue = String(value);
      }

      await SystemSetting.upsert({
        key,
        value: stringValue,
        type,
        description: description || ''
      });

      logger.info(`更新系统设置: ${key} = ${stringValue}`);
    } catch (error) {
      logger.error(`设置系统配置失败: ${error}`);
      throw new BusinessError('设置系统配置失败', ResponseCode.SERVER_ERROR);
    }
  }

  /**
   * 获取所有设置
   * @returns 所有设置列表
   */
  async getAllSettings(): Promise<any[]> {
    try {
      const settings = await SystemSetting.findAll({
        order: [['key', 'ASC']]
      });

      return settings.map(setting => ({
        key: setting.key,
        value: this.parseValue(setting.value, setting.type),
        type: setting.type,
        description: setting.description,
        rawValue: setting.value
      }));
    } catch (error) {
      logger.error(`获取所有系统设置失败: ${error}`);
      throw new BusinessError('获取系统设置失败', ResponseCode.SERVER_ERROR);
    }
  }

  /**
   * 删除设置
   * @param key 设置键名
   */
  async deleteSetting(key: string): Promise<void> {
    try {
      await SystemSetting.destroy({
        where: { key }
      });

      logger.info(`删除系统设置: ${key}`);
    } catch (error) {
      logger.error(`删除系统设置失败: ${error}`);
      throw new BusinessError('删除系统设置失败', ResponseCode.SERVER_ERROR);
    }
  }

  /**
   * 检查评论功能是否开启
   * @returns 是否开启评论功能
   */
  async isCommentEnabled(): Promise<boolean> {
    const enabled = await this.getSetting('comment_enabled');
    return enabled !== null ? enabled : true; // 默认开启
  }

  /**
   * 获取新用户默认大米数量
   * @returns 默认大米数量
   */
  async getDefaultCoins(): Promise<number> {
    const coins = await this.getSetting('default_coins');
    return coins !== null ? coins : 1000; // 默认1000大米
  }

  /**
   * 获取每日签到奖励大米数量
   * @returns 签到奖励大米数量
   */
  async getDailySignInCoins(): Promise<number> {
    const coins = await this.getSetting('daily_signin_coins');
    return coins !== null ? coins : 20; // 默认20大米
  }

  /**
   * 解析设置值
   * @param value 原始值
   * @param type 类型
   * @returns 解析后的值
   */
  private parseValue(value: string, type: string): any {
    switch (type) {
      case 'boolean':
        return value === 'true';
      case 'number':
        return Number(value);
      case 'json':
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      default:
        return value;
    }
  }
}

export default new SystemSettingService(); 