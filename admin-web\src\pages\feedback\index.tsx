import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Select, Input, Modal, message, Tag, Space, Pagination, Image } from 'antd';
import { EyeOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { getFeedbackList, updateFeedbackStatus, FeedbackItem } from '@/services/feedbackService';
import './index.less';

const { Option } = Select;
const { TextArea } = Input;

// 反馈类型映射
const FEEDBACK_TYPE_MAP = {
  bug: { text: '问题反馈', color: 'red' },
  feature: { text: '功能建议', color: 'blue' },
  other: { text: '其他', color: 'default' }
};

// 状态映射
const STATUS_MAP = {
  pending: { text: '待处理', color: 'orange' },
  processing: { text: '处理中', color: 'blue' },
  completed: { text: '已完成', color: 'green' },
  rejected: { text: '已拒绝', color: 'red' }
};

const FeedbackManagement: React.FC = () => {
  const [feedbackList, setFeedbackList] = useState<FeedbackItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filterType, setFilterType] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [detailVisible, setDetailVisible] = useState(false);
  const [replyVisible, setReplyVisible] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [newStatus, setNewStatus] = useState<string>('');

  // 获取反馈列表
  const fetchFeedbackList = async () => {
    setLoading(true);
    try {
      const params = {
        page: currentPage,
        pageSize: pageSize,
        ...(filterType && { type: filterType }),
        ...(filterStatus && { status: filterStatus })
      };

      const result = await getFeedbackList(params);
      
      setFeedbackList(result.list);
      setTotal(result.total);
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      message.error('获取反馈列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新反馈状态
  const handleUpdateFeedbackStatus = async (feedbackId: string, status: string, reply?: string) => {
    try {
      await updateFeedbackStatus({
        feedbackId,
        status,
        reply
      });
      message.success('状态更新成功');
      fetchFeedbackList();
      setReplyVisible(false);
      setReplyContent('');
      setNewStatus('');
    } catch (error) {
      console.error('状态更新失败:', error);
      message.error('状态更新失败');
    }
  };

  // 查看详情
  const handleViewDetail = (record: FeedbackItem) => {
    setSelectedFeedback(record);
    setDetailVisible(true);
  };

  // 处理反馈
  const handleReply = (record: FeedbackItem) => {
    setSelectedFeedback(record);
    setNewStatus(record.status);
    setReplyContent(record.reply || '');
    setReplyVisible(true);
  };

  // 提交回复
  const handleSubmitReply = () => {
    if (!selectedFeedback) return;
    
    handleUpdateFeedbackStatus(
      selectedFeedback.id,
      newStatus,
      replyContent.trim() || undefined
    );
  };

  // 表格列定义
  const columns: ColumnsType<FeedbackItem> = [
    {
      title: '反馈ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={FEEDBACK_TYPE_MAP[type as keyof typeof FEEDBACK_TYPE_MAP]?.color}>
          {FEEDBACK_TYPE_MAP[type as keyof typeof FEEDBACK_TYPE_MAP]?.text}
        </Tag>
      ),
    },
    {
      title: '用户',
      dataIndex: 'userNickname',
      key: 'userNickname',
      width: 120,
      ellipsis: true,
    },
    {
      title: '问题描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <div style={{ maxWidth: 200 }}>{text}</div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={STATUS_MAP[status as keyof typeof STATUS_MAP]?.color}>
          {STATUS_MAP[status as keyof typeof STATUS_MAP]?.text}
        </Tag>
      ),
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      width: 160,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleReply(record)}
          >
            处理
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchFeedbackList();
  }, [currentPage, pageSize, filterType, filterStatus]);

  return (
    <div className="feedback-management">
      <Card>
        <div className="filter-bar">
          <Space size="middle">
            <span>类型：</span>
            <Select
              style={{ width: 120 }}
              placeholder="全部类型"
              allowClear
              value={filterType || undefined}
              onChange={setFilterType}
            >
              <Option value="bug">问题反馈</Option>
              <Option value="feature">功能建议</Option>
              <Option value="other">其他</Option>
            </Select>
            
            <span>状态：</span>
            <Select
              style={{ width: 120 }}
              placeholder="全部状态"
              allowClear
              value={filterStatus || undefined}
              onChange={setFilterStatus}
            >
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="completed">已完成</Option>
              <Option value="rejected">已拒绝</Option>
            </Select>
            
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={fetchFeedbackList}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={feedbackList}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
        />

        <div className="pagination-wrapper">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `共 ${total} 条记录`}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            }}
          />
        </div>
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="反馈详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={600}
      >
        {selectedFeedback && (
          <div className="feedback-detail">
            <div className="detail-item">
              <span className="label">反馈ID：</span>
              <span>{selectedFeedback.id}</span>
            </div>
            <div className="detail-item">
              <span className="label">类型：</span>
              <Tag color={FEEDBACK_TYPE_MAP[selectedFeedback.type]?.color}>
                {FEEDBACK_TYPE_MAP[selectedFeedback.type]?.text}
              </Tag>
            </div>
            <div className="detail-item">
              <span className="label">用户：</span>
              <span>{selectedFeedback.userNickname} ({selectedFeedback.userId})</span>
            </div>
            <div className="detail-item">
              <span className="label">状态：</span>
              <Tag color={STATUS_MAP[selectedFeedback.status]?.color}>
                {STATUS_MAP[selectedFeedback.status]?.text}
              </Tag>
            </div>
            <div className="detail-item">
              <span className="label">提交时间：</span>
              <span>{new Date(selectedFeedback.submitTime).toLocaleString()}</span>
            </div>
            <div className="detail-item">
              <span className="label">问题描述：</span>
              <div className="description">{selectedFeedback.description}</div>
            </div>
            {selectedFeedback.images && selectedFeedback.images.length > 0 && (
              <div className="detail-item">
                <span className="label">相关截图：</span>
                <div className="images">
                  <Image.PreviewGroup>
                    {selectedFeedback.images.map((image, index) => (
                      <Image
                        key={index}
                        width={100}
                        height={100}
                        src={image}
                        style={{ marginRight: 8, marginBottom: 8 }}
                      />
                    ))}
                  </Image.PreviewGroup>
                </div>
              </div>
            )}
            {selectedFeedback.reply && (
              <div className="detail-item">
                <span className="label">管理员回复：</span>
                <div className="reply">{selectedFeedback.reply}</div>
                {selectedFeedback.replyTime && (
                  <div className="reply-time">
                    回复时间：{new Date(selectedFeedback.replyTime).toLocaleString()}
                  </div>
                )}
              </div>
            )}
            {selectedFeedback.deviceInfo && (
              <div className="detail-item">
                <span className="label">设备信息：</span>
                <div className="device-info">
                  <pre>{JSON.stringify(selectedFeedback.deviceInfo, null, 2)}</pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 处理反馈弹窗 */}
      <Modal
        title="处理反馈"
        open={replyVisible}
        onCancel={() => setReplyVisible(false)}
        onOk={handleSubmitReply}
        okText="提交"
        cancelText="取消"
      >
        {selectedFeedback && (
          <div className="reply-form">
            <div className="form-item">
              <span className="label">反馈内容：</span>
              <div className="content">{selectedFeedback.description}</div>
            </div>
            <div className="form-item">
              <span className="label">处理状态：</span>
              <Select
                style={{ width: '100%' }}
                value={newStatus}
                onChange={setNewStatus}
              >
                <Option value="pending">待处理</Option>
                <Option value="processing">处理中</Option>
                <Option value="completed">已完成</Option>
                <Option value="rejected">已拒绝</Option>
              </Select>
            </div>
            <div className="form-item">
              <span className="label">回复内容：</span>
              <TextArea
                rows={4}
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="请输入回复内容（可选）"
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FeedbackManagement; 