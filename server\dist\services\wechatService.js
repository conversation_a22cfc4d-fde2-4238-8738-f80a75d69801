"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 微信服务
 * 处理微信相关的API调用
 */
const axios_1 = __importDefault(require("axios"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
/**
 * 通过code获取微信用户的openid和session_key
 * @param code 微信登录code
 * @returns 微信用户信息
 */
const getWechatUserInfo = (code) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        logger_1.default.info(`开始调用微信API获取用户信息，code: ${code.substring(0, 8)}***`);
        const url = 'https://api.weixin.qq.com/sns/jscode2session';
        const params = {
            appid: config_1.default.wechat.appId,
            secret: config_1.default.wechat.appSecret,
            js_code: code,
            grant_type: 'authorization_code'
        };
        logger_1.default.info(`微信API请求参数: appid=${params.appid}, grant_type=${params.grant_type}`);
        const response = yield axios_1.default.get(url, {
            params,
            timeout: 10000, // 10秒超时
        });
        logger_1.default.info(`微信API响应状态: ${response.status}`);
        logger_1.default.info(`微信API响应数据: ${JSON.stringify(response.data)}`);
        const { openid, session_key, unionid, errcode, errmsg } = response.data;
        // 检查微信API是否返回错误
        if (errcode) {
            logger_1.default.error(`微信API返回错误: errcode=${errcode}, errmsg=${errmsg}`);
            // 根据错误码返回具体错误信息
            switch (errcode) {
                case 40013:
                    throw new error_1.BusinessError('微信AppID无效，请联系管理员');
                case 40029:
                    throw new error_1.BusinessError('登录code无效，请重新登录');
                case 45011:
                    throw new error_1.BusinessError('微信API调用频率限制，请稍后再试');
                case 40125:
                    throw new error_1.BusinessError('微信AppSecret无效，请联系管理员');
                default:
                    throw new error_1.BusinessError(`微信登录失败: ${errmsg || '未知错误'}`);
            }
        }
        // 检查是否成功获取openid
        if (!openid) {
            logger_1.default.error('微信API未返回openid');
            throw new error_1.BusinessError('微信登录失败，未获取到用户标识');
        }
        logger_1.default.info(`微信登录成功，openid: ${openid.substring(0, 8)}***`);
        return {
            openId: openid,
            sessionKey: session_key,
            unionId: unionid
        };
    }
    catch (error) {
        if (error instanceof error_1.BusinessError) {
            // 业务错误直接抛出
            throw error;
        }
        // 处理网络错误
        if (axios_1.default.isAxiosError(error)) {
            logger_1.default.error(`微信API请求失败: ${error.message}`, error);
            if (error.code === 'ECONNABORTED') {
                throw new error_1.BusinessError('微信登录超时，请检查网络后重试');
            }
            else if (error.response) {
                throw new error_1.BusinessError(`微信登录失败，状态码: ${error.response.status}`);
            }
            else {
                throw new error_1.BusinessError('微信登录失败，网络连接异常');
            }
        }
        // 其他未知错误
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        logger_1.default.error(`微信登录未知错误: ${errorMessage}`, error);
        throw new error_1.BusinessError('微信登录失败，请稍后重试');
    }
});
/**
 * 验证微信配置是否正确
 * @returns 配置是否有效
 */
const validateWechatConfig = () => {
    const { appId, appSecret } = config_1.default.wechat;
    if (!appId || !appSecret) {
        logger_1.default.error('微信配置不完整: appId或appSecret为空');
        return false;
    }
    if (appId.length < 10 || appSecret.length < 10) {
        logger_1.default.error('微信配置格式错误: appId或appSecret长度不足');
        return false;
    }
    logger_1.default.info('微信配置验证通过');
    return true;
};
exports.default = {
    getWechatUserInfo,
    validateWechatConfig
};
//# sourceMappingURL=wechatService.js.map