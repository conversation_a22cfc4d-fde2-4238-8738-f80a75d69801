"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 举报模型
 * 存储用户对菜品的举报信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 举报模型类
class Report extends sequelize_1.Model {
}
// 初始化举报模型
Report.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '举报ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    reason: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        comment: '举报原因',
    },
    status: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'pending',
        comment: '状态(pending:待处理,processed:已处理,rejected:已驳回)',
        validate: {
            isIn: [['pending', 'processed', 'rejected']],
        },
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Report',
    tableName: 'reports',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_status',
            fields: ['status'],
        },
    ],
});
exports.default = Report;
//# sourceMappingURL=Report.js.map