<!-- 我的大米页面 -->
<view class="coins-container" style="{{textColorStyle}}">
  <!-- 自定义导航栏 -->
  <custom-navbar 
    title="我的大米" 
    showBack="{{true}}" 
    navBgStyle="{{navBgStyle}}" 
    useShadow="{{true}}">
  </custom-navbar>
  
  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 大米信息卡片 -->
    <view class="coins-card">
      <!-- 标题 -->
      <view class="coins-title">大米余额</view>
      
      <!-- 余额数字 -->
      <view class="coins-balance">{{userCoins.balance}}</view>
      
      <!-- 统计信息 -->
      <view class="coins-stats">
        <view class="stats-item">
          <view class="stats-label">累计获得</view>
          <view class="stats-value">{{userCoins.total}}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">已经使用</view>
          <view class="stats-value">{{userCoins.used}}</view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn recharge-btn" bindtap="showRechargeModal">充值</view>
        <view class="action-btn detail-btn" bindtap="showDetailModal">明细</view>
      </view>
    </view>
    
    <!-- 赚取大米提示 -->
    <view class="earn-coins-section">
      <!-- 标题 -->
      <view class="section-title">赚取大米</view>
      
      <!-- 赚取大米卡片 -->
      <view class="earn-method-card" bindtap="navigateToTask" data-task="daily">
        <view class="earn-icon">
          <image src="/static/images/icons/check1.png" mode="aspectFit"></image>
        </view>
        <view class="earn-content">
          <view class="earn-title">完成任务</view>
          <view class="earn-desc">每日签到、浏览菜谱等任务获得大米奖励</view>
        </view>
        <view class="earn-arrow">></view>
      </view>
      
      <view class="earn-method-card" bindtap="navigateToInvite" data-task="invite">
        <view class="earn-icon">
          <image src="/static/images/icons/yao.png" mode="aspectFit"></image>
        </view>
        <view class="earn-content">
          <view class="earn-title">邀请好友</view>
          <view class="earn-desc">邀请好友注册，双方各得100大米</view>
        </view>
        <view class="earn-arrow">></view>
      </view>
    </view>
    
    <!-- 大米用途说明 -->
    <view class="usage-section">
      <!-- 标题 -->
      <view class="section-title">大米用途</view>
      
      <!-- 用途卡片列表 -->
      <view class="usage-cards">
        <view class="usage-card" bindtap="navigateToUsage" data-usage="vip">
          <view class="usage-icon">
            <image src="/static/images/icons/crown.png" mode="aspectFit"></image>
          </view>
          <view class="usage-title">兑换VIP会员</view>
          <view class="usage-desc">会员专享权益和功能</view>
        </view>
        
        <view class="usage-card" bindtap="navigateToUsage" data-usage="lottery">
          <view class="usage-icon">
            <image src="/static/images/icons/liwu.png" mode="aspectFit"></image>
          </view>
          <view class="usage-title">参与抽奖</view>
          <view class="usage-desc">参与每周幸运抽奖活动</view>
        </view>
        
        <view class="usage-card" bindtap="navigateToUsage" data-usage="upgrade">
          <view class="usage-icon">
            <image src="/static/images/icons/kitchen.png" mode="aspectFit"></image>
          </view>
          <view class="usage-title">升级店铺</view>
          <view class="usage-desc">提升店铺额度和容量</view>
        </view>
        
        <view class="usage-card" bindtap="navigateToOrder" data-usage="discount">
          <view class="usage-icon">
            <image src="/static/images/icons/fail.png" mode="aspectFit"></image>
          </view>
          <view class="usage-title">点餐下单</view>
          <view class="usage-desc">抵扣部分订单金额</view>
        </view>
      </view>
    </view>
    
    <!-- 大米使用说明 -->
    <view class="instruction-section">
      <view class="instruction-title">大米使用说明</view>
      <view class="instruction-list">
        <view class="instruction-item">1. 大米可用于兑换平台内各种权益和服务</view>
        <view class="instruction-item">2. 大米可通过完成任务、充值等方式获得</view>
        <view class="instruction-item">3. 大米长期有效，不会自动过期或清零</view>
        <view class="instruction-item">4. 平台保留对大米规则进行调整的权利</view>
      </view>
    </view>
  </view>
  
  <!-- 充值弹窗 -->
  <modal-dialog 
    visible="{{showRecharge}}" 
    title="大米充值" 
    showCancel="{{true}}" 
    cancelText="取消" 
    confirmText="确认充值"
    themeColor="#FF6B35"
    bind:close="closeRechargeModal"
    bind:cancel="closeRechargeModal"
    bind:confirm="confirmRecharge"
  >
    <view class="recharge-form">
      <!-- 大米兑换率说明 -->
      <view class="exchange-rate-tip">100大米 = 1元</view>
      
      <!-- 充值选项 -->
      <view class="recharge-options">
        <view class="recharge-option {{selectedAmount === 1000 ? 'selected' : ''}}" 
              bindtap="selectRechargeAmount" data-amount="1000">
          <view class="option-amount">1000</view>
          <view class="option-price">¥10</view>
        </view>
        
        <view class="recharge-option {{selectedAmount === 3000 ? 'selected' : ''}}" 
              bindtap="selectRechargeAmount" data-amount="3000">
          <view class="option-amount">3000</view>
          <view class="option-price">¥28</view>
          <view class="option-tag">优惠</view>
        </view>
      </view>

      <view class="recharge-options">
        <view class="recharge-option {{selectedAmount === 5000 ? 'selected' : ''}}" 
              bindtap="selectRechargeAmount" data-amount="5000">
          <view class="option-amount">5000</view>
          <view class="option-price">¥45</view>
          <view class="option-tag">优惠</view>
        </view>
        
        <view class="recharge-option {{selectedAmount === 10000 ? 'selected' : ''}}" 
              bindtap="selectRechargeAmount" data-amount="10000">
          <view class="option-amount">10000</view>
          <view class="option-price">¥88</view>
          <view class="option-tag">超值</view>
        </view>
      </view>
      
      <!-- 支付方式 -->
      <view class="payment-methods">
        <view class="payment-title">支付方式</view>
        <view class="payment-method {{paymentMethod === 'wechat' ? 'selected' : ''}}" 
              bindtap="selectPaymentMethod" data-method="wechat">
          <view class="method-icon">💰</view>
          <view class="method-name">微信支付</view>
          <view class="method-check">✓</view>
        </view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 明细弹窗 -->
  <modal-dialog 
    visible="{{showDetail}}" 
    title="大米明细" 
    showCancel="{{false}}" 
    confirmText="关闭"
    themeColor="#FF6B35"
    bind:close="closeDetailModal"
    bind:confirm="closeDetailModal"
  >
    <view class="detail-content">
      <view class="detail-tabs">
        <view class="tab {{currentTab === 'all' ? 'active' : ''}}" 
              bindtap="switchTab" data-tab="all">全部</view>
        <view class="tab {{currentTab === 'in' ? 'active' : ''}}" 
              bindtap="switchTab" data-tab="in">收入</view>
        <view class="tab {{currentTab === 'out' ? 'active' : ''}}" 
              bindtap="switchTab" data-tab="out">支出</view>
      </view>
      
      <scroll-view class="detail-list" scroll-y="true">
        <block wx:if="{{coinRecords.length > 0}}">
          <view class="detail-item" wx:for="{{coinRecords}}" wx:key="id">
            <view class="detail-left">
              <view class="detail-title">{{item.title}}</view>
              <view class="detail-time">{{item.time}}</view>
            </view>
            <view class="detail-amount {{item.type === 'in' ? 'income' : 'expense'}}">
              {{item.type === 'in' ? '+' : '-'}}{{item.amount}}
            </view>
          </view>
        </block>
        <view wx:else class="empty-records">
          <view class="empty-icon">📊</view>
          <view class="empty-text">暂无交易记录</view>
        </view>
      </scroll-view>
    </view>
  </modal-dialog>
  
  <!-- 支付确认对话框 -->
  <confirm-dialog
    visible="{{showPayConfirm}}"
    title="确认支付"
    content="确定支付 ¥{{selectedAmount/100}} 购买 {{selectedAmount}} 大米吗？"
    cancelText="取消"
    confirmText="确认支付"
    confirmColor="#FF6B35"
    bind:cancel="cancelPayment"
    bind:confirm="confirmPayment"
  />
  
  <!-- 厨房会员组件 -->
  <kitchen-member id="kitchen-member"></kitchen-member>
</view> 