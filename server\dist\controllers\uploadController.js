"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
const upload_1 = require("../middlewares/upload");
const urlManager_1 = require("../utils/urlManager");
/**
 * 上传菜品图片
 * @route POST /api/upload/dishImage
 */
const uploadDishImage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            throw new error_1.BusinessError('未上传文件', response_1.ResponseCode.VALIDATION);
        }
        const relativePath = (0, upload_1.getFileUrl)(req.file.filename, 'dish');
        const fullUrl = (0, urlManager_1.convertToFullUrl)(relativePath);
        (0, response_1.success)(res, { imageUrl: fullUrl });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 上传背景图片
 * @route POST /api/upload/backgroundImage
 */
const uploadBackgroundImage = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            throw new error_1.BusinessError('未上传文件', response_1.ResponseCode.VALIDATION);
        }
        const relativePath = (0, upload_1.getFileUrl)(req.file.filename, 'background');
        const fullUrl = (0, urlManager_1.convertToFullUrl)(relativePath);
        (0, response_1.success)(res, { imageUrl: fullUrl });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 上传分类图标
 * @route POST /api/upload/categoryIcon
 */
const uploadCategoryIcon = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            throw new error_1.BusinessError('未上传文件', response_1.ResponseCode.VALIDATION);
        }
        const relativePath = (0, upload_1.getFileUrl)(req.file.filename, 'category');
        const fullUrl = (0, urlManager_1.convertToFullUrl)(relativePath);
        (0, response_1.success)(res, { imageUrl: fullUrl });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 上传厨房头像
 * @route POST /api/upload/kitchenAvatar
 */
const uploadKitchenAvatar = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            throw new error_1.BusinessError('未上传文件', response_1.ResponseCode.VALIDATION);
        }
        const relativePath = (0, upload_1.getFileUrl)(req.file.filename, 'kitchen');
        const fullUrl = (0, urlManager_1.convertToFullUrl)(relativePath);
        (0, response_1.success)(res, { imageUrl: fullUrl });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 上传用户头像
 * @route POST /api/upload/userAvatar
 */
const uploadUserAvatar = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            throw new error_1.BusinessError('未上传文件', response_1.ResponseCode.VALIDATION);
        }
        const relativePath = (0, upload_1.getFileUrl)(req.file.filename, 'avatar');
        const fullUrl = (0, urlManager_1.convertToFullUrl)(relativePath);
        (0, response_1.success)(res, { imageUrl: fullUrl });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    uploadDishImage,
    uploadBackgroundImage,
    uploadCategoryIcon,
    uploadKitchenAvatar,
    uploadUserAvatar
};
//# sourceMappingURL=uploadController.js.map