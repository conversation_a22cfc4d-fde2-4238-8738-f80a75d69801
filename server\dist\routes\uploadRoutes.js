"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 上传模块路由
 * 处理文件上传相关的API路由
 */
const express_1 = __importDefault(require("express"));
const uploadController_1 = __importDefault(require("../controllers/uploadController"));
const auth_1 = require("../middlewares/auth");
const upload_1 = require("../middlewares/upload");
const router = express_1.default.Router();
// 上传菜品图片
router.post('/dishImage', auth_1.verifyToken, (0, upload_1.uploadSingleFile)('file'), uploadController_1.default.uploadDishImage);
// 上传背景图片
router.post('/backgroundImage', auth_1.verifyToken, (0, upload_1.uploadSingleFile)('file'), uploadController_1.default.uploadBackgroundImage);
// 上传分类图标
router.post('/categoryIcon', auth_1.verifyToken, (0, upload_1.uploadSingleFile)('file'), uploadController_1.default.uploadCategoryIcon);
// 上传厨房头像
router.post('/kitchenAvatar', auth_1.verifyToken, (0, upload_1.uploadSingleFile)('file'), uploadController_1.default.uploadKitchenAvatar);
// 上传用户头像
router.post('/userAvatar', auth_1.verifyToken, (0, upload_1.uploadSingleFile)('file'), uploadController_1.default.uploadUserAvatar);
exports.default = router;
//# sourceMappingURL=uploadRoutes.js.map