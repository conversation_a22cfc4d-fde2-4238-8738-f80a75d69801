# 服务器配置
PORT=3000
NODE_ENV=development
CORS_ORIGIN=https://dzcdd.zj1.natnps.cn
BASE_URL=https://dzcdd.zj1.natnps.cn
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=restaurant_menu_db
DB_USER=root
DB_PASSWORD=123123

# ==================== 微信支付配置 ====================
# 微信支付AppID（通常与小程序AppID相同）
WECHAT_PAY_APPID=wx41b8de9ea1e51474
# 微信支付商户号（在微信支付商户平台获取）
WECHAT_PAY_MCHID=1719611095
# 微信支付商户号V3（通常与MCHID相同）
WECHAT_PAY_MCHID_V3=1719611095
# 微信支付API密钥（旧版本，如果不使用可以不填）
WECHAT_PAY_API_KEY=
# 微信支付APIv3密钥（在商户平台设置，用于签名验证）
WECHAT_PAY_API_V3_KEY=z8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2
# 商户证书序列号（下载证书时获取）
WECHAT_PAY_SERIAL_NO=19E5CF9FAD0789EAA2CFD178D7101AB83F7E2D02
# 支付成功回调通知地址（微信会调用此地址通知支付结果）
WECHAT_PAY_NOTIFY_URL=https://dzcdd.zj1.natnps.cn/api/payment/notify




# JWT配置
JWT_SECRET=restaurant_menu_app_secret_key
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880 # 5MB

# 日志配置
LOG_LEVEL=info
LOG_DIR=logs

WECHAT_APPID=wx41b8de9ea1e51474
WECHAT_APP_SECRET=dd1e5ca6a7a7df81954cda4813dd2e2a

# 测试配置
TEST_BASE_URL=https://dzcdd.zj1.natnps.cn
