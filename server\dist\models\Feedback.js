"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 反馈模型
 * 存储用户提交的问题反馈和功能建议
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 反馈模型类
class Feedback extends sequelize_1.Model {
}
// 初始化反馈模型
Feedback.init({
    id: {
        type: sequelize_1.DataTypes.STRING(50),
        primaryKey: true,
        defaultValue: () => {
            // 生成反馈ID: FB + 时间戳 + 随机数
            const timestamp = Date.now().toString();
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `FB${timestamp}${random}`;
        },
    },
    user_id: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '提交反馈的用户ID',
    },
    type: {
        type: sequelize_1.DataTypes.ENUM('bug', 'feature', 'other'),
        allowNull: false,
        comment: '反馈类型：bug-问题反馈，feature-功能建议，other-其他',
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        comment: '问题描述',
    },
    images: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: '图片URL列表，JSON格式存储',
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('pending', 'processing', 'completed', 'rejected'),
        allowNull: false,
        defaultValue: 'pending',
        comment: '处理状态：pending-待处理，processing-处理中，completed-已完成，rejected-已拒绝',
    },
    reply: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: '管理员回复',
    },
    device_info: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: '设备信息，JSON格式存储',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
    reply_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '回复时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Feedback',
    tableName: 'feedbacks',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '用户反馈表',
    indexes: [
        {
            fields: ['user_id'],
            name: 'idx_feedback_user_id',
        },
        {
            fields: ['type'],
            name: 'idx_feedback_type',
        },
        {
            fields: ['status'],
            name: 'idx_feedback_status',
        },
        {
            fields: ['created_at'],
            name: 'idx_feedback_created_at',
        },
    ],
});
exports.default = Feedback;
//# sourceMappingURL=Feedback.js.map