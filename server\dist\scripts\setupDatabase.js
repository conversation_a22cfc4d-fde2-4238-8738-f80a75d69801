"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 数据库设置主脚本
 * 按顺序执行所有初始化、迁移和种子数据脚本
 */
const logger_1 = __importDefault(require("../utils/logger"));
const initDatabase_1 = __importDefault(require("./initDatabase"));
const createTables_1 = __importDefault(require("./migrations/createTables"));
const seedUsers_1 = __importDefault(require("./seeds/seedUsers"));
const seedKitchens_1 = __importDefault(require("./seeds/seedKitchens"));
const seedCategories_1 = __importDefault(require("./seeds/seedCategories"));
const seedTables_1 = __importDefault(require("./seeds/seedTables"));
const seedDishes_1 = __importDefault(require("./seeds/seedDishes"));
const seedOrders_1 = __importDefault(require("./seeds/seedOrders"));
const seedSystemSettings_1 = __importDefault(require("./seeds/seedSystemSettings"));
/**
 * 解析命令行参数
 * @returns 命令行参数对象
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        init: false,
        migrate: false,
        seed: false,
        all: false,
    };
    if (args.length === 0) {
        options.all = true;
    }
    else {
        for (const arg of args) {
            switch (arg) {
                case '--init':
                    options.init = true;
                    break;
                case '--migrate':
                    options.migrate = true;
                    break;
                case '--seed':
                    options.seed = true;
                    break;
                case '--all':
                    options.all = true;
                    break;
                default:
                    logger_1.default.warn(`未知参数: ${arg}`);
            }
        }
    }
    return options;
}
/**
 * 设置数据库
 */
function setupDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        const options = parseArgs();
        logger_1.default.info('开始设置数据库...');
        logger_1.default.info(`选项: ${JSON.stringify(options)}`);
        try {
            // 初始化数据库
            if (options.init || options.all) {
                logger_1.default.info('=== 初始化数据库 ===');
                yield (0, initDatabase_1.default)();
            }
            // 创建表
            if (options.migrate || options.all) {
                logger_1.default.info('=== 创建数据库表 ===');
                yield (0, createTables_1.default)();
            }
            // 添加种子数据
            if (options.seed || options.all) {
                logger_1.default.info('=== 添加种子数据 ===');
                logger_1.default.info('--- 创建用户数据 ---');
                yield (0, seedUsers_1.default)();
                logger_1.default.info('--- 创建厨房数据 ---');
                yield (0, seedKitchens_1.default)();
                logger_1.default.info('--- 创建分类数据 ---');
                yield (0, seedCategories_1.default)();
                logger_1.default.info('--- 创建桌号数据 ---');
                yield (0, seedTables_1.default)();
                logger_1.default.info('--- 创建菜品数据 ---');
                yield (0, seedDishes_1.default)();
                logger_1.default.info('--- 创建订单数据 ---');
                yield (0, seedOrders_1.default)();
                logger_1.default.info('--- 创建系统设置数据 ---');
                yield (0, seedSystemSettings_1.default)();
            }
            logger_1.default.info('数据库设置完成');
        }
        catch (error) {
            logger_1.default.error('数据库设置失败:');
            if (error instanceof Error) {
                logger_1.default.error(`错误消息: ${error.message}`);
                logger_1.default.error(`错误堆栈: ${error.stack}`);
            }
            else {
                logger_1.default.error(`未知错误: ${error}`);
            }
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行设置数据库
if (require.main === module) {
    setupDatabase()
        .then(() => {
        logger_1.default.info('数据库设置脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('数据库设置脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = setupDatabase;
//# sourceMappingURL=setupDatabase.js.map