// 导入API
import { getDishCategories, getDishList, addToCart as addDishToCart, getCartList, clearCart as clearCartApi, updateCartItemCount, updateDishStatus, deleteDish, updateCategorySort, updateDishSort } from '../../api/dishApi'
import { getCurrentKitchenBaseInfo } from '../../api/kitchenApi'
import { checkLogin } from '../../utils/util'
import { DEFAULT_IMAGES } from '../../utils/constants'

// 定义菜品和购物车数据接口
interface DishItem {
  id: string;
  categoryId: string;
  name: string;
  image: string;
  price: number;
  originalPrice: number;
  description: string;
  tags: string[];
  sales: number;
  rating: number;
  categoryName: string;
  isFirstInCategory?: boolean;
  isLastInCategory?: boolean;
  isLastEnabledInCategory?: boolean; // 是否是分类中最后一个上架菜品
  categoryCount?: number;
  categoryEnabledCount?: number; // 分类内上架菜品数量
  count?: number; // 菜品数量
  disabled?: boolean;
  isDeleted?: boolean;
  status?: string; // 菜品状态 on-上架 off-下架
  sort?: number; // 菜品排序
}

interface CartItem extends DishItem {
  count: number;
}

interface Cart {
  cartList: CartItem[];
  totalPrice: number;
  totalCount: number;
}

// 厨房ID本地存储键名
const LAST_SELECTED_KITCHEN_KEY = 'last_selected_kitchen'

// 页面配置
Page({
  // 页面数据
  data: {
    // 用户信息
    isLoggedIn: false,
    userInfo: {} as Record<string, any>,

    // 餐厅信息
    restaurantInfo: {
      name: '',
      logo: '',
      notice: ''
    },

    // 当前选中的厨房ID
    currentKitchenId: '',

    // 分享厨房相关
    sharedKitchenId: '', // 从分享链接获取的厨房ID
    isSharedKitchen: false, // 标识是否为分享的厨房

    // 标签页状态
    activeTab: 'order',

    // 分类数据
    categories: [] as Array<{id: string, name: string, icon: string, sort: number, dishCount?: number}>,
    activeCategoryId: '',

    // 菜品数据
    dishList: [] as DishItem[],
    scrollIntoView: '',
    dishListLoading: false, // 菜品列表加载状态

    // 分类是否有菜品的映射
    hasDishInCategory: {} as Record<string, boolean>,

    // 搜索相关
    isSearching: false,
    isSearchExiting: false,
    searchKeyword: '',
    searchResults: [] as DishItem[],

    // 购物车数据
    cart: {
      cartList: [] as CartItem[],
      totalPrice: 0,
      totalCount: 0
    } as Cart,

    // 显示购物车清单
    showCartList: false,

    // 购物栏退出动画状态
    isExiting: false,

    // 购物车徽章动画状态
    cartBadgeAnimated: false,

    // 上一次购物车数量
    prevCartCount: 0,

    // 状态栏高度
    statusBarHeight: 0,

    // 分类管理
    showCategoryManager: false,

    // 新增方法: 切换菜品排序模式
    sortModeCategory: '',

    // 新增方法: 当前拖动的菜品ID
    currentDragDishId: '',

    // 新增方法: 拖动菜品开始的Y坐标
    dishStartY: 0,

    // 新增方法: 当前拖动的菜品索引
    currentDishIndex: -1,

    // 新增方法: 移动菜品到的目标索引
    moveToDishIndex: -1,

    // 新增: 下单模式标志
    isOrderMode: false,

    // 背景设置
    shopBg: '',

    // 文本颜色样式
    textColorStyle: '--text-color: #FFFFFF;',

    // 二维码分享
    showQrCodeShare: false,

    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
  },

  // 生命周期函数--监听页面加载
  async onLoad(options) {
    // 处理从小程序码跳转的参数
    let isSharedKitchen = false;
    
    if (options) {
      // 处理直接传递的kitchenId参数（URL参数）
      if (options.kitchenId) {
        isSharedKitchen = true;
        this.setData({
          currentKitchenId: options.kitchenId,
          sharedKitchenId: options.kitchenId,
          isSharedKitchen: true
        });
        // 隐藏TabBar
        wx.hideTabBar({});
        // 不保存分享的厨房ID到本地存储，避免影响用户自己的厨房选择
      }

      // 处理从小程序码的scene参数（微信扫码）
      if (options.scene) {
        try {
          // scene参数需要使用decodeURIComponent解码
          const sceneDecoded = decodeURIComponent(options.scene);

          // 解析scene中的参数
          let kitchenId = null;

          // 新格式：k=kitchenId
          if (sceneDecoded.startsWith('k=')) {
            kitchenId = sceneDecoded.substring(2); // 去掉 'k=' 前缀
          } else {
            // 兼容旧格式：kitchenId=xxx&from=qrcode
            const params = new URLSearchParams(sceneDecoded);
            kitchenId = params.get('kitchenId');
          }

          if (kitchenId) {
            isSharedKitchen = true;
            this.setData({
              currentKitchenId: kitchenId,
              sharedKitchenId: kitchenId,
              isSharedKitchen: true
            });
            // 隐藏TabBar
            wx.hideTabBar({});
            // 不保存分享的厨房ID到本地存储，避免影响用户自己的厨房选择

            // 显示欢迎提示
            setTimeout(() => {
              wx.showToast({
                title: '欢迎来到餐厅！',
                icon: 'success',
                duration: 2000
              });
            }, 1000);
          }
        } catch (error) {
          console.error('解析scene参数失败:', error);
        }
      }
    }

    // 立即设置默认背景，避免加载闪烁
    // 但是在分享厨房模式下，先不设置背景，等获取到真实厨房背景后再设置
    if (!isSharedKitchen) {
      const app = getApp<IAppOption>();
      let initialBg = '';
      
      if (app.globalData.backgroundSettings) {
        initialBg = app.globalData.backgroundSettings.shopBg || '';
      }
      
      // 如果没有全局背景，检查是否有当前厨房背景
      if (!initialBg) {
        initialBg = wx.getStorageSync('current_kitchen_bg') || wx.getStorageSync('shopBg') || DEFAULT_IMAGES.KITCHEN_BACKGROUND;
      }
      
      this.setData({
        shopBg: initialBg
      });
    }
    // 分享厨房模式下，不设置shopBg，保持初始的空值，等待获取真实厨房背景

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 首次检查登录状态
    await this.checkLoginStatus();

    // 获取或设置厨房ID
    await this.getCurrentKitchenId();

    if (this.data.currentKitchenId) {
      // 并发获取厨房信息和分类数据，优化加载速度
      await Promise.all([
        this.fetchRestaurantInfo(),
        this.fetchCategories()
      ]);

      // 先获取分类，再获取菜品
      if (this.data.categories.length > 0) {
        await this.fetchDishList();
      }
    }

    // 加载背景设置，但不重复设置已设置的背景
    this.loadBackgroundSettings();

    // 获取购物车数据
    await this.fetchCartList();
    
    console.log('页面初始化完成');
  },

  // 厨房切换事件处理
  onKitchenSwitched(data: { kitchenId: string; kitchenInfo: any }) {
    if (!this.data.isSharedKitchen && data.kitchenId !== this.data.currentKitchenId) {
      this.setData({ currentKitchenId: data.kitchenId });
      // 重新加载餐厅数据
      this.fetchRestaurantInfo();
      this.fetchCategories().then(() => {
        Promise.all([
          this.fetchDishList(),
          this.fetchCartList()
        ]).catch(error => {
          console.error('获取数据失败', error);
        });
      });
    }
  },

  // 生命周期函数--监听页面显示
  async onShow() {
    // 检查登录状态并同步数据 - 改为异步等待
    await this.checkLoginStatus();
    
    // 每次显示页面时检查厨房背景是否有变化
    this.checkBackgroundUpdate();

    // 检查厨房是否有变化（简单方法：检查本地存储）
    if (!this.data.isSharedKitchen) {
      const storedKitchenId = wx.getStorageSync(LAST_SELECTED_KITCHEN_KEY);
      if (storedKitchenId && storedKitchenId !== this.data.currentKitchenId) {
        this.setData({ currentKitchenId: storedKitchenId });
        // 重新加载餐厅数据
        this.fetchRestaurantInfo();
        await this.fetchCategories();
        Promise.all([
          this.fetchDishList(),
          this.fetchCartList()
        ]).catch(error => {
          console.error('获取数据失败', error);
        });
        return; // 已经更新，不需要执行后续逻辑
      }
    }
    
    // 如果是分享厨房模式，但没有厨房ID，尝试获取
    if (this.data.isSharedKitchen && !this.data.currentKitchenId) {
      await this.getCurrentKitchenId();
    }
    
    // 如果有厨房ID，重新获取厨房信息和菜品
    if (this.data.currentKitchenId) {
      // 刷新厨房信息
      this.fetchRestaurantInfo();
      
      // 先获取分类，再获取菜品列表和购物车
      await this.fetchCategories();
      
      // 同时获取菜品和购物车
      Promise.all([
        this.fetchDishList(),
        this.fetchCartList()
      ]).catch(error => {
        console.error('获取数据失败', error);
      });
    } else if (!this.data.isSharedKitchen) {
      // 非分享模式下，如果没有厨房ID，尝试获取
      await this.getCurrentKitchenId();
      
      // 如果获取到厨房ID，继续加载数据
      if (this.data.currentKitchenId) {
        this.fetchRestaurantInfo();
        await this.fetchCategories();
        Promise.all([
          this.fetchDishList(),
          this.fetchCartList()
        ]).catch(error => {
          console.error('获取数据失败', error);
        });
      }
    }
  },

  // 获取当前选中的厨房ID
  async getCurrentKitchenId() {
    try {
      // 如果是分享厨房模式，不需要获取用户厨房列表
      if (this.data.isSharedKitchen) {
        return;
      }

      // 检查是否登录，未登录时不获取厨房列表
      if (!this.data.isLoggedIn) {
        return;
      }

      // 先检查本地存储的厨房ID
      const kitchenId = wx.getStorageSync(LAST_SELECTED_KITCHEN_KEY);

      if (kitchenId) {
        // 验证厨房ID是否仍然有效
        const { getKitchenList } = require('../../api/kitchenApi');
        const result = await getKitchenList();

        if (result.error === 0 && result.body && result.body.length > 0) {
          // 检查本地存储的厨房ID是否在用户的厨房列表中
          const validKitchen = result.body.find((kitchen: any) => kitchen.id === kitchenId);

          if (validKitchen) {
            this.setData({ currentKitchenId: kitchenId });
            return;
          } else {
            // 本地厨房ID无效，使用第一个厨房
            const firstKitchenId = result.body[0].id;
            this.setData({ currentKitchenId: firstKitchenId });
            wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, firstKitchenId);
            return;
          }
        }
      }

      // 如果本地没有存储厨房ID或验证失败，重新获取厨房列表
      const { getKitchenList } = require('../../api/kitchenApi');
      const result = await getKitchenList();

      if (result.error === 0 && result.body && result.body.length > 0) {
        // 使用第一个厨房的ID
        const firstKitchenId = result.body[0].id;
        this.setData({ currentKitchenId: firstKitchenId });
        // 保存到本地存储
        wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, firstKitchenId);
      } else {
        // 清除无效的本地存储
        wx.removeStorageSync(LAST_SELECTED_KITCHEN_KEY);
      }
    } catch (error) {
      console.error('获取当前厨房ID失败', error);
      // 发生错误时也清除可能无效的本地存储
      wx.removeStorageSync(LAST_SELECTED_KITCHEN_KEY);
    }
  },

  // 检查当前厨房是否变更
  async checkKitchenChanged() {
    try {
      // 如果是分享厨房模式，不进行厨房变更检查
      if (this.data.isSharedKitchen) {
        return;
      }

      // 如果用户未登录，不进行厨房变更检查
      if (!this.data.isLoggedIn) {
        return;
      }

      let kitchenId = wx.getStorageSync(LAST_SELECTED_KITCHEN_KEY);

      // 如果本地存储没有厨房ID
      if (!kitchenId) {
        // 尝试获取厨房列表并使用第一个厨房
        const { getKitchenList } = require('../../api/kitchenApi');
        const result = await getKitchenList();

        if (result.error === 0 && result.body && result.body.length > 0) {
          // 使用第一个厨房的ID
          kitchenId = result.body[0].id;
          // 保存到本地存储
          wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, kitchenId);
        }
      }

      // 如果厨房ID变更，重新加载数据
      if (kitchenId && kitchenId !== this.data.currentKitchenId) {
        // 清除之前厨房的背景图缓存
        wx.removeStorageSync('current_kitchen_bg');

        // 先清空所有相关数据，避免数据残留
        this.setData({ 
          currentKitchenId: kitchenId,
          categories: [],
          activeCategoryId: '',
          dishList: [],
          cart: {
            cartList: [],
            totalPrice: 0,
            totalCount: 0
          },
          // 清空搜索相关状态
          isSearching: false,
          searchKeyword: '',
          searchResults: [],
          // 清空排序模式
          sortModeCategory: '',
          // 重置当前标签页为点餐模式
          currentTab: 'order'
        });

        // 强制刷新页面数据
        // 使用Promise.all同时加载所有数据
        Promise.all([
          this.fetchCategories(),
          this.fetchRestaurantInfo(),
          this.fetchCartList() // 也重新加载购物车数据
        ]).then(() => {
          // 分类加载完后再加载菜品列表
          return this.fetchDishList();
        }).then(() => {
          // 显示切换成功提示
          wx.showToast({
            title: '已切换厨房',
            icon: 'success',
            duration: 1500
          });
        }).catch(error => {
          console.error('厨房切换数据加载失败', error);
          wx.showToast({
            title: '数据加载失败',
            icon: 'none',
            duration: 2000
          });
        });
      }
    } catch (error) {
      console.error('检查厨房变更失败', error);
    }
  },

  // 生命周期函数--页面卸载
  onUnload() {
    // 移除主题变更监听器
    const app = getApp<IAppOption>();
    app.removeThemeChangeListener(this.onThemeChanged.bind(this));
  },

  // 主题变更处理函数
  onThemeChanged(settings: { shopBg: string; navBgStyle: string; navBgIndex: number }) {
    // 只更新导航栏背景样式，不影响文字颜色
    this.setData({
      navBgStyle: settings.navBgStyle
    });

    // 文字颜色继续使用餐厅背景图来决定
    const shopBg = settings.shopBg || this.data.shopBg;
    if (shopBg) {
      // 如果有背景图，使用背景图来计算文字颜色
      this.watchShopBg(shopBg);
    }
  },

  // 检查登录状态 - 改为异步函数
  async checkLoginStatus() {
    return new Promise<void>((resolve) => {
      const isLoggedIn = checkLogin()
      if (isLoggedIn) {
        const userInfo = wx.getStorageSync('userInfo') || {}
        this.setData({
          isLoggedIn,
          userInfo
        }, () => {
          resolve();
        })
      } else {
        // 未登录的情况，保留分享厨房信息，只清除用户相关信息
        const updateData: any = {
          isLoggedIn: false,
          userInfo: {},
          cart: {
            cartList: [],
            totalPrice: 0,
            totalCount: 0
          }
        }

        // 如果不是分享厨房模式，才清除厨房相关信息
        if (!this.data.isSharedKitchen) {
          updateData.currentKitchenId = ''
          updateData.restaurantInfo = {
            name: '',
            logo: '',
            notice: ''
          }
          updateData.categories = []
          updateData.dishList = []

          // 清除本地存储的厨房相关信息
          try {
            wx.removeStorageSync('last_selected_kitchen')
            wx.removeStorageSync('shopBg')
            wx.removeStorageSync('current_kitchen_bg')
            wx.removeStorageSync('navBgStyle')
            wx.removeStorageSync('navBgIndex')
          } catch (error) {
            console.error('清除本地存储失败', error)
          }

          // 恢复默认背景图
          const defaultBg = DEFAULT_IMAGES.KITCHEN_BACKGROUND;
          updateData.shopBg = defaultBg;

          // 计算默认背景的文字颜色
          this.watchShopBg(defaultBg);
        }

        this.setData(updateData, () => {
          resolve();
        });
      }
    });
  },

  // 获取菜品分类
  async fetchCategories() {
    try {
      const { currentKitchenId } = this.data;

      // 如果没有厨房ID，先尝试获取
      if (!currentKitchenId) {
        await this.getCurrentKitchenId();
      }

      // 再次检查，如果仍然没有厨房ID，则清空数据并返回
      if (!this.data.currentKitchenId) {
        this.setData({
          categories: [],
          activeCategoryId: ''
        });
        return;
      }

      // 传递当前厨房ID
      const result = await getDishCategories(this.data.currentKitchenId);

      if (result.error === 0) {
        // 检查返回数据结构
        let categories = result.body;

        // 兼容两种返回数据结构：直接数组或包含categories字段的对象
        if (result.body && result.body.categories) {
          categories = result.body.categories;
        }

        if (Array.isArray(categories) && categories.length > 0) {
          // 为每个分类添加hasDishes属性，默认为false
          categories = categories.map(cat => ({
            ...cat,
            hasDishes: false
          }));

          this.setData({
            categories: categories,
            activeCategoryId: categories[0].id
          });
        } else {
          // 当厨房没有分类时，清空分类数据
          this.setData({
            categories: [],
            activeCategoryId: ''
          });
        }
      } else {
        console.error('获取分类列表失败', result.message);
        // API调用失败时也清空分类数据
        this.setData({
          categories: [],
          activeCategoryId: ''
        });
      }
    } catch (error) {
      console.error('获取分类失败', error);
      // 发生错误时清空分类数据
      this.setData({
        categories: [],
        activeCategoryId: ''
      });
    }
  },

  // 获取菜品列表
  async fetchDishList() {
    try {
      const { currentKitchenId } = this.data;

      // 如果没有厨房ID，先尝试获取
      if (!currentKitchenId) {
        await this.getCurrentKitchenId();
      }

      // 再次检查，如果仍然没有厨房ID，则清空数据并返回
      if (!this.data.currentKitchenId) {
        this.setData({ 
          dishList: [],
          dishListLoading: false 
        });
        return;
      }

      // 设置加载状态
      this.setData({ dishListLoading: true });

      // 不传递分类ID，获取所有菜品
      const result = await getDishList('', this.data.currentKitchenId);

      if (result.error === 0) {
        // 检查响应中是否存在dishes数组
        const dishesData = result.body && result.body.dishes ? result.body.dishes : result.body;
        
        if (Array.isArray(dishesData) && dishesData.length > 0) {
        // 处理菜品数据，标记每个分类的第一个和最后一个菜品
        const dishList = this.processDishListData(dishesData);

        // 根据分类顺序对菜品列表重新排序
        if (this.data.categories && this.data.categories.length > 0) {
          // 创建分类ID到排序索引的映射
          const categoryOrderMap: {[key: string]: number} = {};
          this.data.categories.forEach((cat, index) => {
            categoryOrderMap[cat.id] = index;
          });

          // 先按分类排序，再按上下架状态排序，再按菜品排序
          dishList.sort((a: DishItem, b: DishItem) => {
            // 首先按照分类顺序排序
            const categoryOrderA = categoryOrderMap[a.categoryId] !== undefined ? categoryOrderMap[a.categoryId] : 999;
            const categoryOrderB = categoryOrderMap[b.categoryId] !== undefined ? categoryOrderMap[b.categoryId] : 999;

            if (categoryOrderA !== categoryOrderB) {
              return categoryOrderA - categoryOrderB;
            }

            // 同一分类内，先按上下架状态排序（上架在前，下架在后）
            if (a.disabled !== b.disabled) {
              return a.disabled ? 1 : -1;
            }

            // 最后按照菜品排序字段排序
            return (a.sort || 0) - (b.sort || 0);
          });
        }

        this.setData({ dishList });

        // 菜品列表更新后，立即合并购物车数据
        this.updateDishListWithCart();

        // 预加载菜品图片
        this.preloadDishImages(dishList);
        } else {
          // 当厨房没有菜品时，清空菜品数据
          this.setData({ dishList: [] });
        }
      } else {
        console.error('获取菜品列表失败', result.message);
        // API调用失败时也清空菜品数据
        this.setData({ dishList: [] });
      }
    } catch (error) {
      console.error('获取菜品列表失败', error);
      // 发生错误时清空菜品数据
      this.setData({ dishList: [] });
    } finally {
      // 清除加载状态
      this.setData({ dishListLoading: false });
    }
  },

  // 预加载菜品图片
  preloadDishImages(dishList: DishItem[]) {
    // 恢复预加载功能，但采用智能策略
    try {
      // 直接使用同步导入，避免异步导入可能的问题
      const { preloadDishImages } = require('../../utils/imagePreloader');
      
      // 提取首屏菜品图片URL
      const imageUrls = dishList
        .slice(0, 8) // 只预加载前8个菜品的图片
        .map(dish => dish.image)
        .filter(image => image && image.trim() !== '' && !image.includes(this.data.defaultImages.DISH));
      
      if (imageUrls.length > 0) {
        // 使用setTimeout避免阻塞主线程
        setTimeout(() => {
          preloadDishImages(imageUrls);
        }, 100);
      }
    } catch (error) {
      console.error('预加载菜品图片失败:', error);
      // 即使预加载失败，也不影响正常显示
    }
  },

  // 获取购物车列表
  async fetchCartList() {
    try {
      const { currentKitchenId } = this.data;

      // 如果没有厨房ID，先尝试获取
      if (!currentKitchenId) {
        await this.getCurrentKitchenId();
      }

      // 检查是否有当前厨房ID
      if (!this.data.currentKitchenId) {
        // 如果没有厨房ID，设置一个空的购物车数据结构
        this.setData({
          cart: { cartList: [], totalPrice: 0, totalCount: 0 }
        });
        return;
      }

      // 传递厨房ID参数
      const result = await getCartList(this.data.currentKitchenId);

      if (result.error === 0) {
        // 处理购物车数据格式
        let cartData = result.body;

        // 如果返回的是数组(cartItems)，转换为标准购物车格式
        if (cartData && Array.isArray(cartData.cartItems)) {
          const cartItems = cartData.cartItems;
          const totalPrice = cartItems.reduce((total: number, item: any) => total + (item.dish && item.dish.price ? item.dish.price : 0) * item.count, 0);
          const totalCount = cartItems.reduce((total: number, item: any) => total + item.count, 0);

          cartData = {
            cartList: cartItems.map((item: any) => ({
              id: item.dishId || (item.dish && item.dish.id ? item.dish.id : ''),
              name: item.dish && item.dish.name ? item.dish.name : '',
              image: item.dish && item.dish.image ? item.dish.image : '',
              price: item.dish && item.dish.price ? item.dish.price : 0,
              count: item.count
            })),
            totalPrice,
            totalCount
          };
        } else if (Array.isArray(cartData)) {
          // 如果直接返回了数组，也进行格式转换
          const cartItems = cartData;
          const totalPrice = cartItems.reduce((total: number, item: any) => total + (item.dish && item.dish.price ? item.dish.price : 0) * item.count, 0);
          const totalCount = cartItems.reduce((total: number, item: any) => total + item.count, 0);

          cartData = {
            cartList: cartItems.map((item: any) => ({
              id: item.dishId || (item.dish && item.dish.id ? item.dish.id : ''),
              name: item.dish && item.dish.name ? item.dish.name : '',
              image: item.dish && item.dish.image ? item.dish.image : '',
              price: item.dish && item.dish.price ? item.dish.price : 0,
              count: item.count
            })),
            totalPrice,
            totalCount
          };
        }

        this.setData({
          cart: cartData
        });

        // 更新菜品列表中的数量
        this.updateDishListWithCart();
      }
    } catch (error) {
      console.error('获取购物车失败', error);
    }
  },

  // 将购物车数据合并到菜品列表
  updateDishListWithCart() {
    const { dishList } = this.data
    const { cart } = this.data

    // 确保购物车和菜品列表都存在
    if (!cart || !cart.cartList || !Array.isArray(cart.cartList) || !Array.isArray(dishList)) {
      return;
    }

    // 创建一个购物车菜品ID到数量的映射
    const cartMap: Record<string, number> = {}
    cart.cartList.forEach((item: CartItem) => {
      cartMap[item.id] = item.count
    })

    // 更新菜品列表中的数量
    const updatedDishList = dishList.map(dish => {
      return {
        ...dish,
        count: cartMap[dish.id] || 0
      }
    })

    this.setData({ dishList: updatedDishList })
  },

  // 处理菜品数据
  processDishListData(dishList: any[]): DishItem[] {
    // 确保dishList是数组
    if (!Array.isArray(dishList)) {
      return [];
    }

    // 处理菜品的状态，设置disabled属性
    dishList = dishList.map(dish => ({
      ...dish,
      // 后端返回status为'off'表示下架，转换为前端的disabled=true
      disabled: dish.status === 'off'
    }));

    // 记录每个分类的菜品总数（不考虑上下架状态，只考虑是否删除）
    const categoryCounts: Record<string, number> = {};

    // 记录每个分类的上架菜品数量
    const categoryEnabledCounts: Record<string, number> = {};

    // 初始化分类菜品映射
    const hasDishInCategory: Record<string, boolean> = {};

    // 为所有分类初始化默认值
    if (this.data.categories && this.data.categories.length > 0) {
      this.data.categories.forEach(category => {
        hasDishInCategory[category.id] = false;
      });
    }

    // 计算每个分类下非删除状态的菜品总数和上架菜品数量
    dishList.forEach(dish => {
      if (!dish.isDeleted) {
        // 计算所有菜品总数
        if (categoryCounts[dish.categoryId]) {
          categoryCounts[dish.categoryId]++;
        } else {
          categoryCounts[dish.categoryId] = 1;
        }

        // 单独计算上架菜品数量
        if (!dish.disabled) {
          if (categoryEnabledCounts[dish.categoryId]) {
            categoryEnabledCounts[dish.categoryId]++;
          } else {
            categoryEnabledCounts[dish.categoryId] = 1;
          }

          // 如果有上架菜品，则该分类有菜品可显示
          hasDishInCategory[dish.categoryId] = true;
        }
      }
    });

    // 更新hasDishInCategory到data中
    this.setData({
      hasDishInCategory
    });

    // 记录每个分类的第一个和最后一个菜品索引
    const categoryFirstIndex: Record<string, number> = {};
    const categoryLastIndex: Record<string, number> = {};

    // 记录每个分类的首个上架菜品和末尾上架菜品索引
    const categoryFirstEnabledIndex: Record<string, number> = {};
    const categoryLastEnabledIndex: Record<string, number> = {};

    // 记录分类内菜品计数
    let currentCategoryCount: Record<string, { total: number, enabled: number }> = {};

    // 遍历菜品列表，标记每个分类的首尾菜品
    const nonDeletedDishes = dishList.filter(dish => !dish.isDeleted);

    // 先按分类排序，再按上下架状态排序（上架在前，下架在后）
    nonDeletedDishes.sort((a: any, b: any) => {
      // 首先按分类ID排序
      const aCat = String(a.categoryId);
      const bCat = String(b.categoryId);
      if (aCat !== bCat) {
        return aCat.localeCompare(bCat);
      }

      // 然后按上下架状态排序（上架在前，下架在后）
      if (a.disabled !== b.disabled) {
        return a.disabled ? 1 : -1;
      }

      // 最后按照原有排序字段排序
      return (a.sort || 0) - (b.sort || 0);
    });

    nonDeletedDishes.forEach((dish, index) => {
      // 如果是该分类的第一个菜品（无论上下架）
      if (categoryFirstIndex[dish.categoryId] === undefined) {
        categoryFirstIndex[dish.categoryId] = index;
        currentCategoryCount[dish.categoryId] = { total: 0, enabled: 0 };
      }

      // 增加分类内菜品计数
      currentCategoryCount[dish.categoryId].total++;

      // 如果是上架菜品
      if (!dish.disabled) {
        // 如果是该分类的第一个上架菜品
        if (categoryFirstEnabledIndex[dish.categoryId] === undefined) {
          categoryFirstEnabledIndex[dish.categoryId] = index;
        }

        // 增加上架菜品计数
        currentCategoryCount[dish.categoryId].enabled++;

        // 如果是该分类的最后一个上架菜品
        if (currentCategoryCount[dish.categoryId].enabled === categoryEnabledCounts[dish.categoryId]) {
          categoryLastEnabledIndex[dish.categoryId] = index;
        }
      }

      // 如果是该分类的最后一个菜品（无论上下架）
      if (currentCategoryCount[dish.categoryId].total === categoryCounts[dish.categoryId]) {
        categoryLastIndex[dish.categoryId] = index;
      }
    });

    // 标记每个菜品的状态
    return dishList.map((dish) => {
      // 找出该菜品在非删除菜品数组中的索引
      const dishIndex = nonDeletedDishes.findIndex(d => d.id === dish.id);

      // 判断是否是分类中的第一个或最后一个菜品
      const isFirstInCategory = !dish.isDeleted && dishIndex === categoryFirstIndex[dish.categoryId];
      const isLastInCategory = !dish.isDeleted && dishIndex === categoryLastIndex[dish.categoryId];

      // 判断是否是分类中的最后一个上架菜品（用于点餐模式显示菜品数量）
      const isLastEnabledInCategory = !dish.isDeleted && !dish.disabled && dishIndex === categoryLastEnabledIndex[dish.categoryId];

      return {
        ...dish,
        isFirstInCategory,
        isLastInCategory,
        isLastEnabledInCategory, // 新增字段
        categoryCount: categoryCounts[dish.categoryId] || 0,
        categoryEnabledCount: categoryEnabledCounts[dish.categoryId] || 0
      };
    }) as DishItem[];
  },

  // 切换标签
  switchTab(e: any) {
    const { tab } = e.currentTarget.dataset
    const previousTab = this.data.activeTab;

    // 在下单模式下，只能切换到点餐状态
    if (this.data.isOrderMode && tab !== 'order') {
      return;
    }

    this.setData({
      activeTab: tab,
      // 如果从编辑模式切换到点餐模式，需要重置一些状态
      showCartList: previousTab === 'edit' && tab === 'order' ? false : this.data.showCartList,
      // 退出菜品排序模式
      sortModeCategory: previousTab === 'edit' && tab === 'order' ? '' : this.data.sortModeCategory,
    })
  },

  // 选择分类
  selectCategory(e: any) {
    const { id } = e.currentTarget.dataset
    this.setData({
      activeCategoryId: id,
      scrollIntoView: `category-${id}`
    })
  },

  // 添加到购物车
  async addToCart(e: any) {
    const { dish } = e.currentTarget.dataset as { dish: DishItem }
    const { currentKitchenId } = this.data;

    // 检查菜品是否已下架
    if (dish.disabled) {
      wx.showToast({
        title: '该菜品已下架',
        icon: 'none'
      });
      return;
    }

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      // 在下单模式或分享厨房模式下直接跳转到登录页面
      if (this.data.isOrderMode || this.data.isSharedKitchen) {
        wx.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 普通模式下只显示提示
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    try {
      // 正式环境调用API
      const result = await addDishToCart(dish.id, 1, currentKitchenId)
      if (result.error === 0) {
        // 重新获取购物车数据
        this.fetchCartList()
      }
    } catch (error) {
      console.error('添加到购物车失败', error)
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      })
    }
  },

  // 登录点击
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login',
    })
  },

  // 店铺管理
  onStore() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
    wx.showToast({
        title: '请先登录',
      icon: 'none'
      })
      return
    }

    // 导航到店铺管理页面
    wx.navigateTo({
      url: '/pages/kitchen-manage/kitchen-manage'
    })
  },

  // 分享点击
  onShare() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 显示厨房二维码分享弹窗
    this.setData({
      showQrCodeShare: true
    });
  },

  // 添加食谱点击
  onAddRecipe() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/add-dish/add-dish'
    })
  },

  // 搜索点击
  onSearch() {
    // 先显示搜索框
    this.setData({
      isSearching: true
    });
  },

  // 搜索输入
  onSearchInput(e: any) {
    const keyword = e.detail.value;

    this.setData({
      searchKeyword: keyword
    });

    // 实时搜索
    if (keyword) {
      this.performSearch(keyword);
    } else {
      // 如果关键词为空，清除搜索结果
      this.setData({
        searchResults: []
      });
    }
  },

  // 搜索确认
  onSearchConfirm(e: any) {
    const keyword = e.detail.value;
    if (keyword) {
      this.performSearch(keyword);
    }
  },

  // 执行搜索
  performSearch(keyword: string) {
    // 执行搜索逻辑
    const { dishList } = this.data;

    // 先清空搜索结果，触发新的动画效果
    this.setData({
      searchResults: []
    });

    // 延迟一小段时间再显示搜索结果，让清空动画有时间执行
    setTimeout(() => {
      // 转为小写进行不区分大小写的搜索
      const lowerKeyword = keyword.toLowerCase();

      const searchResults = dishList.filter(dish => {
        // 对菜品名称、描述和标签进行不区分大小写的搜索
        const nameMatch = dish.name && dish.name.toLowerCase().includes(lowerKeyword);
        const descMatch = dish.description && dish.description.toLowerCase().includes(lowerKeyword);

        // 确保tags是数组
        const tagsMatch = Array.isArray(dish.tags) && dish.tags.some(tag =>
          tag && tag.toLowerCase().includes(lowerKeyword)
        );

        return nameMatch || descMatch || tagsMatch;
      });

      this.setData({
        searchResults
      });

      // 如果有结果，滚动到第一个结果所在的分类
      if (searchResults.length > 0) {
        this.setData({
          activeCategoryId: searchResults[0].categoryId,
          scrollIntoView: `category-${searchResults[0].categoryId}`
        });
      }
    }, 50);
  },

  // 取消搜索
  cancelSearch() {
    // 先执行退出动画
    this.setData({
      isSearchExiting: true
    });

    // 延迟重置搜索状态
    setTimeout(() => {
      this.setData({
        isSearching: false,
        isSearchExiting: false,
        searchKeyword: '',
        searchResults: []
      });
    }, 300);
  },

  // 阻止搜索框点击事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止点击搜索框时触发蒙层的点击事件
    return false;
  },

  // 增加数量
  async increaseQuantity(e: any) {
    const { dish } = e.currentTarget.dataset as { dish: DishItem }
    const { currentKitchenId } = this.data;

    // 检查菜品是否已下架
    if (dish.disabled) {
      wx.showToast({
        title: '该菜品已下架',
        icon: 'none'
      });
      return;
    }

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      // 在下单模式或分享厨房模式下直接跳转到登录页面
      if (this.data.isOrderMode || this.data.isSharedKitchen) {
        wx.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 普通模式下只显示提示
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    try {
      // 正式环境调用API
      const result = await addDishToCart(dish.id, 1, currentKitchenId)
      if (result.error === 0) {
        // 重新获取购物车数据
        this.fetchCartList()
      }
    } catch (error) {
      console.error('添加到购物车失败', error)
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      })
    }
  },

  // 减少数量
  async decreaseQuantity(e: any) {
    const { dish } = e.currentTarget.dataset as { dish: DishItem }
    const { currentKitchenId } = this.data;

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      // 在下单模式或分享厨房模式下直接跳转到登录页面
      if (this.data.isOrderMode || this.data.isSharedKitchen) {
        wx.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 普通模式下只显示提示
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    try {
      // 正式环境调用API
      const currentCount = dish.count || 0;
      if (currentCount <= 0) return;

      const result = await updateCartItemCount(dish.id, currentCount - 1, currentKitchenId)
      if (result.error === 0) {
        // 重新获取购物车数据
        this.fetchCartList()
      } else {
        wx.showToast({
          title: result.message || '操作失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('从购物车减少失败', error)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  // 提交订单点击
  onSubmitOrder() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      // 跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    if (this.data.cart.totalCount <= 0) {
      wx.showToast({
        title: '购物车为空，请先选择菜品',
        icon: 'none'
      })
      return
    }

    // 关闭购物车清单
    this.setData({
      showCartList: false
    })

    // 在分享厨房模式下，需要临时保存分享厨房ID到本地存储
    // 这样下单页面就能获取到正确的厨房信息
    if (this.data.isSharedKitchen && this.data.currentKitchenId) {
      // 保存原来的厨房ID
      const originalKitchenId = wx.getStorageSync('last_selected_kitchen') || '';
      wx.setStorageSync('original_kitchen_id', originalKitchenId);
      // 临时设置分享厨房ID
      wx.setStorageSync('last_selected_kitchen', this.data.currentKitchenId);
      wx.setStorageSync('is_shared_kitchen_order', true);
    }

    // 跳转到提交订单页面
    wx.navigateTo({
      url: '/pages/submit-order/submit-order'
    })
  },

  // 页面相关事件处理函数--监听用户下拉动作
  async onPullDownRefresh() {
    try {
      // 刷新所有数据，包括用户信息
      await Promise.all([
        this.fetchRestaurantInfo(),
        this.fetchCategories(),
        this.fetchDishList(),
        this.fetchCartList()
      ])

      // 更新菜品列表中的购物车数据
      this.updateDishListWithCart()
    } catch (error) {
      console.error('下拉刷新失败', error)
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh()
    }
  },

  // 分享信息
  onShareAppMessage(options) {
    const { currentKitchenId } = this.data;

    // 根据分享类型提供不同的分享内容
    if (options && options.from === 'button') {
      // 来自"邀请下单"按钮的分享（分享下单模式）
      return {
        title: '请点餐 - 菜单已分享给您',
        path: `/pages/restaurant/restaurant?orderMode=true&kitchenId=${currentKitchenId}`
      }
    } else {
      // 来自右上角菜单的分享（分享主页）
      return {
        title: '电子菜单助手 - 美食随心点',
        path: `/pages/restaurant/restaurant?kitchenId=${currentKitchenId}`
      }
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { currentKitchenId } = this.data;

    return {
      title: '电子菜单助手 - 美食随心点',
      query: `kitchenId=${currentKitchenId}`
    }
  },

  // 切换购物车清单显示
  toggleCartList() {
    // 只有购物车中有菜品时才能切换显示购物车清单
    if (this.data.cart.totalCount > 0) {
      this.setData({
        showCartList: !this.data.showCartList
      })
    } else {
      // 购物车为空时给出提示
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      })
    }
  },

  // 清空购物车
  async clearCart() {
    try {
      const { currentKitchenId } = this.data;

      if (!currentKitchenId) {
        return;
      }

      // 提示用户确认
      const res = await wx.showModal({
        title: '清空购物车',
        content: '确定要清空购物车吗？',
        confirmText: '确定',
        cancelText: '取消'
      });

      if (res.confirm) {
        // 导入clearCart函数
        const { clearCart } = require('../../api/dishApi');
        const result = await clearCart(currentKitchenId);

        if (result.error === 0) {
          // 更新菜品列表中的数量，将所有菜品数量设为0
          const updatedDishList = this.data.dishList.map(dish => {
            return {
              ...dish,
              count: 0
            };
          });

          this.setData({
            cart: { cartList: [], totalPrice: 0, totalCount: 0 },
            showCartList: false,
            dishList: updatedDishList
          });

          wx.showToast({
            title: '购物车已清空',
            icon: 'success'
          });
        }
      }
    } catch (error) {
      console.error('清空购物车失败', error);
    }
  },

  // 获取状态栏高度
  getStatusBarHeight() {
    const systemInfo = wx.getSystemInfoSync();

    const statusBarHeight = systemInfo.statusBarHeight;

    // 通过API获取菜单按钮（胶囊按钮）的位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算胶囊按钮的垂直中心位置（相对于状态栏底部）
    const capsuleCenter = menuButtonInfo.top + menuButtonInfo.height / 2;

    // 标题栏顶部应该是状态栏高度
    // 我们需要在状态栏高度的基础上加上一个值，使标题与胶囊按钮垂直居中对齐
    const titleTop = statusBarHeight + (capsuleCenter - statusBarHeight - 16); // 16为标题高度的一半

    this.setData({
      statusBarHeight: titleTop
    });

    return {
      statusBarHeight: statusBarHeight,
      titleTop: titleTop
    };
  },

  // 分类管理点击
  onManageCategory() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 使用setTimeout确保在下一个事件循环中显示弹窗，避免滚动条引起的布局问题
    setTimeout(() => {
      this.setData({
        showCategoryManager: true
      });
    }, 50);
  },

  // 关闭分类管理弹窗
  onCloseCategoryManager() {
    // 使用setTimeout确保在下一个事件循环中关闭弹窗，避免滚动条引起的布局问题
    setTimeout(() => {
      this.setData({
        showCategoryManager: false
      });
    }, 50);
  },

  // 添加分类
  async onAddCategory(e: {detail: {category: {name: string, icon: string, sort: number}}}) {
    const { category } = e.detail;
    const { currentKitchenId } = this.data;

    try {
      // 真实环境下调用API
      const { addCategory } = require('../../api/dishApi');

      const result = await addCategory(category.name, category.icon, currentKitchenId);

      if (result.error === 0) {
        // API调用成功后重新加载分类列表
        await this.fetchCategories();

        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        // 根据错误代码显示不同提示
        let errorMessage = result.message || '添加失败，请重试';

        // 显示错误信息
        wx.showModal({
          title: '添加失败',
          content: errorMessage,
          showCancel: false
        });

        // 如果错误是由于分类上限或分类名称已存在，自动刷新列表
        if (errorMessage.includes('上限') || errorMessage.includes('已存在')) {
          await this.fetchCategories();
        }
      }
    } catch (error) {
      console.error('添加分类失败：', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  // 更新分类
  async onUpdateCategory(e: {detail: {index: number, category: {id: string, name: string, icon: string, sort: number}}}) {
    const { index, category } = e.detail;
    const { categories, currentKitchenId } = this.data;

    try {
      // 真实环境下调用API
      const { updateCategory } = require('../../api/dishApi');

      const result = await updateCategory(category.id, category.name, category.icon, category.sort, currentKitchenId);

      if (result.error === 0) {
        // API调用成功后重新加载分类列表和菜品列表
        await this.fetchCategories();
        await this.fetchDishList();

        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      } else {
        // 显示错误信息
        wx.showModal({
          title: '更新失败',
          content: result.message || '未知错误',
          showCancel: false
        });
      }
    } catch (error) {
      console.error('更新分类失败：', error);
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      });
    }
  },

  // 删除分类
  async onDeleteCategory(e: {detail: {index: number, category: {id: string, name: string, icon: string}}}) {
    const { index, category } = e.detail;
    const { categories, dishList, currentKitchenId } = this.data;

    try {
      // 真实环境下调用API
      const { deleteCategory } = require('../../api/dishApi');

      const result = await deleteCategory(category.id, currentKitchenId);

      if (result.error === 0) {
        // API调用成功后重新加载分类列表和菜品列表
        await this.fetchCategories();
        await this.fetchDishList();

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        // 显示错误信息
        wx.showModal({
          title: '删除失败',
          content: result.message || '未知错误',
          showCancel: false
        });
      }
    } catch (error) {
      console.error('删除分类失败：', error);
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  // 修改菜品
  onModifyDish(e: any) {
    const dish = e.currentTarget.dataset.dish;
    // 将菜品数据转换为查询字符串
    wx.navigateTo({
      url: `/pages/add-dish/add-dish?isEdit=true&dishId=${dish.id}`
    });
  },

  // 切换菜品上下架状态
  onToggleDishStatus(e: any) {
    const dish = e.currentTarget.dataset.dish;
    const dishList = [...this.data.dishList];
    const { currentKitchenId } = this.data;

    // 查找菜品索引
    const index = dishList.findIndex(item => item.id === dish.id);

    if (index !== -1) {
      // 切换上下架状态
      const newStatus = !dishList[index].disabled;
      dishList[index].disabled = newStatus;

      this.setData({
        dishList
      });

      // 调用API更新服务器数据
      updateDishStatus(dish.id, newStatus, currentKitchenId)
        .then(res => {
          if (res.error === 0) {
            wx.showToast({
              title: newStatus ? '菜品已下架' : '菜品已上架',
              icon: 'success'
            });
          } else {
            // 恢复之前的状态
            dishList[index].disabled = !newStatus;
            this.setData({
              dishList
            });
            wx.showToast({
              title: res.message || '操作失败，请重试',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('更新菜品状态失败', err);
          // 恢复之前的状态
          dishList[index].disabled = !newStatus;
          this.setData({
            dishList
          });
          wx.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          });
        });
    }
  },

  // 删除菜品
  onDeleteDish(e: any) {
    const dish = e.currentTarget.dataset.dish;
    const { currentKitchenId } = this.data;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${dish.name}"吗？`,
      confirmColor: '#FF6B35',
      success: (res) => {
        if (res.confirm) {
          // 调用删除API
          deleteDish(dish.id, currentKitchenId)
            .then(res => {
              if (res.error === 0) {
                // API调用成功，更新本地数据
                this.updateLocalAfterDelete(dish);
              } else {
                // API调用失败
                wx.showToast({
                  title: res.message || '删除失败，请重试',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              console.error('删除菜品失败', err);
              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 删除后更新本地数据
  updateLocalAfterDelete(dish: DishItem) {
    let dishList = [...this.data.dishList];

    // 记录原始分类信息，用于后续比较
    const originalCategories = new Set(dishList.map(item => item.categoryId));
    const categoryDishCounts: Record<string, number> = {};

    // 统计每个分类的菜品数量
    dishList.forEach(item => {
      if (!item.isDeleted) {
        categoryDishCounts[item.categoryId] = (categoryDishCounts[item.categoryId] || 0) + 1;
      }
    });

    // 找到要删除的菜品索引
    const deleteIndex = dishList.findIndex(item => item.id === dish.id);

    if (deleteIndex === -1) return;

    // 获取该菜品的分类ID
    const categoryId = dish.categoryId;

    // 该分类下的所有菜品
    const sameCategoryDishes = dishList.filter(item =>
      item.categoryId === categoryId && !item.isDeleted
    );

    // 标记或删除菜品
    if (sameCategoryDishes.length === 1) {
      // 这里不做真正的删除，而是标记为已删除状态，保留分类标题
      dishList[deleteIndex].isDeleted = true;
      dishList[deleteIndex].disabled = true;
    } else {
      // 从列表中移除菜品
      dishList = dishList.filter(item => item.id !== dish.id);
    }

    // 更新分类计数
    categoryDishCounts[categoryId]--;

    // 重新处理菜品数据，确保分类标记正确
    dishList = this.processDishListData(dishList);

    // 分析菜谱变化
    let menuChanges: string[] = [];
    if (categoryDishCounts[categoryId] === 0) {
      menuChanges.push(`"${dish.categoryName}"分类下已无菜品`);
    }

    // 更新数据
    this.setData({
      dishList
    });

    // 更新成功提示，包含菜谱变化信息
    if (menuChanges.length > 0) {
      wx.showModal({
        title: '删除成功',
        content: `${dish.name}已删除。\n\n${menuChanges.join('\n')}`,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
    } else {
      wx.showToast({
        title: '删除成功',
        icon: 'success',
        mask: true
      });
    }
  },

  // 分类排序
  async onSortCategories(e: {detail: {categories: Array<{id: string, name: string, icon: string, sort: number}>}}) {
    const { categories } = e.detail;
    const { currentKitchenId } = this.data;

    try {
      // 真实环境下调用API
      const { updateCategorySort } = require('../../api/dishApi');

      // 构建排序数据
      const sortList = categories.map((item, index) => ({
        id: item.id,
        sort: index
      }));

      const result = await updateCategorySort(sortList, currentKitchenId);

      if (result.error === 0) {
        // 更新本地数据
        this.setData({
          categories
        });

        wx.showToast({
          title: '排序已更新',
          icon: 'success'
        });
      } else {
        // 显示错误信息
        wx.showModal({
          title: '更新排序失败',
          content: result.message || '未知错误',
          showCancel: false
        });
      }
    } catch (error) {
      console.error('更新分类排序失败：', error);
      wx.showToast({
        title: '更新排序失败，请重试',
        icon: 'none'
      });
    }
  },

  // 新增方法: 切换菜品排序模式
  toggleSortMode(e: any) {
    // 如果已经在排序模式，则退出排序模式并保存所有分类的排序
    if (this.data.sortModeCategory) {
      // 获取所有分类ID
      const categoryIds = Array.from(new Set(this.data.dishList.map(dish => dish.categoryId)));
      
      // 保存所有分类的排序
      categoryIds.forEach(categoryId => {
        if (categoryId) {
          this.saveDishSort(categoryId);
        }
      });
      
      this.setData({
        sortModeCategory: ''
      });
      
      wx.showToast({
        title: '排序已保存',
        icon: 'success'
      });
      
      return;
    }

    // 进入全局排序模式
    this.setData({
      sortModeCategory: 'global'  // 使用'global'表示全局排序模式
    });
  },

  // 菜品拖动开始
  onDishTouchStart(e: any) {
    const { index, id, categoryId } = e.currentTarget.dataset;
    const startY = e.touches[0].clientY;

    this.setData({
      currentDragDishId: id,
      dishStartY: startY,
      currentDishIndex: index
    });
  },

  // 菜品拖动中
  onDishTouchMove(e: any) {
    const { currentDishIndex, currentDragDishId } = this.data;
    if (!currentDragDishId) return;

    const currentY = e.touches[0].clientY;
    const deltaY = currentY - this.data.dishStartY;

    // 获取菜品项的高度(约200rpx)
    const itemHeight = 200;

    // 计算应该移动到哪个索引
    const moveOffset = Math.round(deltaY / itemHeight);

    // 获取所有可见的菜品（未删除的）
    const visibleDishes = this.data.dishList.filter(dish => !dish.isDeleted);

    // 找出当前拖动菜品在全部可见菜品中的索引
    const currentVisibleIndex = visibleDishes.findIndex(dish => dish.id === currentDragDishId);

    // 计算目标索引（在所有可见菜品中）
    let moveToVisibleIndex = currentVisibleIndex + moveOffset;

    // 限制索引范围
    moveToVisibleIndex = Math.max(0, Math.min(visibleDishes.length - 1, moveToVisibleIndex));

    // 获取实际列表中的索引
    const moveToDishIndex = this.data.dishList.findIndex(dish => dish.id === visibleDishes[moveToVisibleIndex].id);

    // 如果位置有变化，更新UI
    if (moveToDishIndex !== this.data.moveToDishIndex) {
      this.setData({
        moveToDishIndex
      });
    }
  },

  // 菜品拖动结束
  onDishTouchEnd(e: any) {
    const { currentDishIndex, moveToDishIndex, currentDragDishId } = this.data;

    // 如果没有拖动或目标位置相同，不做处理
    if (!currentDragDishId || currentDishIndex === moveToDishIndex || moveToDishIndex === -1) {
      this.setData({
        currentDragDishId: '',
        dishStartY: 0,
        currentDishIndex: -1,
        moveToDishIndex: -1
      });
      return;
    }

    // 获取所有菜品的副本
    const dishList = [...this.data.dishList];
    
    // 获取当前拖动的菜品
    const movedItem = dishList[currentDishIndex];
    
    // 获取目标位置的菜品
    const targetItem = dishList[moveToDishIndex];
    
    // 如果拖动到了不同分类，需要更新菜品的分类ID
    if (movedItem.categoryId !== targetItem.categoryId) {
      // 将拖动菜品的分类ID更新为目标菜品的分类ID
      movedItem.categoryId = targetItem.categoryId;
      movedItem.categoryName = targetItem.categoryName;
    }

    // 执行排序，将当前菜品移动到目标位置
    dishList.splice(currentDishIndex, 1);
    dishList.splice(moveToDishIndex, 0, movedItem);

    // 更新列表
    this.setData({
      dishList: this.processDishListData(dishList),
      currentDragDishId: '',
      dishStartY: 0,
      currentDishIndex: -1,
      moveToDishIndex: -1
    });
  },

  // 保存菜品排序
  saveDishSort(categoryId: string) {
    const { currentKitchenId } = this.data;
    // 获取当前分类下的所有菜品
    const categoryDishes = this.data.dishList.filter(dish =>
      dish.categoryId === categoryId && !dish.isDeleted
    );

    // 构建排序数据
    const sortList = categoryDishes.map((dish, index) => ({
      id: dish.id,
      sort: index,
      categoryId: dish.categoryId // 添加分类ID，支持跨分类拖动
    }));

    try {
      // 调用真实API - 这里需要确保后端API支持更新菜品分类
      updateDishSort(categoryId, sortList, currentKitchenId)
        .then(res => {
          if (res.error !== 0) {
            console.error(`保存分类 ${categoryId} 排序失败: ${res.message}`);
          }
        })
        .catch(err => {
          console.error(`保存分类 ${categoryId} 排序失败`, err);
        });
    } catch (error) {
      console.error(`保存分类 ${categoryId} 排序失败`, error);
    }
  },

  // 获取餐厅信息
  async fetchRestaurantInfo() {
    try {
      const { currentKitchenId } = this.data;

      // 如果没有厨房ID，先尝试获取
      if (!currentKitchenId) {
        await this.getCurrentKitchenId();
      }

      // 再次检查，如果仍然没有厨房ID，则直接返回
      if (!this.data.currentKitchenId) {
        return;
      }

      const result = await getCurrentKitchenBaseInfo(this.data.currentKitchenId);

      if (result.error === 0) {
        const kitchenInfo = result.body;
        this.setData({
          restaurantInfo: {
            name: kitchenInfo.name || '',
            logo: kitchenInfo.avatarUrl || '', // 移除默认头像兜底
            notice: kitchenInfo.notice || ''
          }
        });

        // 更新厨房背景
        this.updateKitchenBackground(kitchenInfo.backgroundUrl);

        // 预加载餐厅图片
        this.preloadRestaurantImages(kitchenInfo);
      }
    } catch (error) {
      console.error('获取餐厅信息失败', error);
    }
  },

  // 预加载餐厅图片
  preloadRestaurantImages(kitchenInfo: any) {
    // 图片预加载功能已移除
  },

  // 统一更新厨房背景图
  updateKitchenBackground(backgroundUrl?: string) {
    // 检查背景图是否已经是当前要设置的背景图，避免重复设置
    if (backgroundUrl && this.data.shopBg === backgroundUrl) {
      return; // 背景图相同，无需重复设置
    }
    
    if (backgroundUrl) {
      // 如果厨房有背景图，使用厨房背景图
      this.setData({
        shopBg: backgroundUrl
      });
      // 更新本地存储为当前厨房的背景图
      if (!this.data.isSharedKitchen) {
        wx.setStorageSync('current_kitchen_bg', backgroundUrl);
      }
      // 使用背景图计算文字颜色
      this.watchShopBg(backgroundUrl);

      // 清除smart-image缓存确保显示最新背景图
      setTimeout(() => {
        const bgImageComponents = this.selectAllComponents('.bg-image');
        if (bgImageComponents && bgImageComponents.length > 0) {
          bgImageComponents.forEach((component: any) => {
            if (component && component.resetAndReload) {
              component.resetAndReload();
            }
          });
        }
      }, 100);
    } else {
      // 如果厨房没有背景图
      if (this.data.isSharedKitchen) {
        // 分享厨房模式下，如果没有背景图，使用默认背景
        const defaultBg = DEFAULT_IMAGES.KITCHEN_BACKGROUND;
        this.setData({ shopBg: defaultBg });
        this.watchShopBg(defaultBg);
      } else {
        // 普通模式下，清除当前厨房背景缓存
        wx.removeStorageSync('current_kitchen_bg');

        // 尝试使用用户的全局背景设置，如果没有则使用默认背景
        const globalShopBg = wx.getStorageSync('shopBg') || DEFAULT_IMAGES.KITCHEN_BACKGROUND;
        
        // 检查是否需要更新背景图
        if (this.data.shopBg !== globalShopBg) {
          this.setData({ shopBg: globalShopBg });
          this.watchShopBg(globalShopBg);
        }
      }
    }
  },

  // 加载背景设置（仅加载导航栏样式，背景图由updateKitchenBackground统一管理）
  loadBackgroundSettings() {
    // 从本地存储中获取导航栏背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';
    const navBgIndex = wx.getStorageSync('navBgIndex') !== undefined ? wx.getStorageSync('navBgIndex') : 0;

    // 只设置导航栏样式，不设置背景图（背景图由updateKitchenBackground统一管理）
    this.setData({
      navBgStyle,
      navBgIndex
    });

    // 注意：不再在这里设置背景图，避免与updateKitchenBackground冲突
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景色中的颜色值
    let bgColor = '#FF6B35'; // 默认颜色

    if (background.includes('linear-gradient')) {
      // 从渐变中提取第一个颜色值
      const matches = background.match(/#[a-fA-F0-9]{6}/);
      if (matches && matches.length > 0) {
        bgColor = matches[0];
      }
    } else if (background.startsWith('#')) {
      bgColor = background;
    }

    // 计算背景亮度
    const r = parseInt(bgColor.substring(1, 3), 16);
    const g = parseInt(bgColor.substring(3, 5), 16);
    const b = parseInt(bgColor.substring(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF';
    const textColorStyle = `--text-color: ${textColor};`;

    this.setData({ textColorStyle });

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    });
  },

  // 监听背景图片变化
  watchShopBg(shopBg: string) {
    // 根据新的背景图片计算文字颜色
    if (shopBg) {
      // 使用getImageInfo获取图片信息
      wx.getImageInfo({
        src: shopBg,
        success: (res) => {
          // 创建canvas上下文
          const ctx = wx.createCanvasContext('bgAnalysisCanvas');

          // 在canvas上绘制图片
          ctx.drawImage(res.path, 0, 0, 100, 100);
          ctx.draw(false, () => {
            // 绘制完成后获取图片数据
            setTimeout(() => {
              wx.canvasGetImageData({
                canvasId: 'bgAnalysisCanvas',
                x: 0,
                y: 0,
                width: 100,
                height: 100,
                success: (res) => {
                  // 使用优化的颜色分析算法
                  const colorAnalysis = this.analyzeImageColors(res.data, res.width, res.height);

                  // 根据分析结果设置文字颜色
                  const textColor = colorAnalysis.recommendedTextColor;
                  const textColorStyle = `--text-color: ${textColor};`;

                  this.setData({ textColorStyle });

                  // 设置状态栏样式
                  wx.setNavigationBarColor({
                    frontColor: colorAnalysis.statusBarColor,
                    backgroundColor: colorAnalysis.dominantColor,
                    animation: {
                      duration: 300,
                      timingFunc: 'easeInOut'
                    }
                  });
                }
              });
            }, 100); // 给canvas绘制一点时间
          });
        }
      });
    }
  },

  // 优化的图片颜色分析算法
  analyzeImageColors(imageData: Uint8ClampedArray, width: number, height: number) {
    const pixelCount = width * height;

    // 1. 计算多种亮度指标
    let totalLuminance = 0;
    let totalBrightness = 0;

    // 2. 颜色分布统计
    const colorBuckets = {
      dark: 0,    // 0-85
      medium: 0,  // 86-170
      light: 0    // 171-255
    };

    // 3. 采样关键区域（顶部区域权重更高，因为状态栏在顶部）
    const topRegionWeight = 2.0;
    const middleRegionWeight = 1.0;
    const bottomRegionWeight = 0.5;

    let weightedLuminance = 0;
    let totalWeight = 0;

    for (let i = 0; i < pixelCount; i++) {
      const r = imageData[i * 4];
      const g = imageData[i * 4 + 1];
      const b = imageData[i * 4 + 2];
      const a = imageData[i * 4 + 3] / 255; // 透明度

      // 计算相对亮度（更准确的感知亮度）
      const luminance = this.calculateRelativeLuminance(r, g, b) * a;

      // 计算传统亮度
      const brightness = (r * 0.299 + g * 0.587 + b * 0.114) * a;

      totalLuminance += luminance;
      totalBrightness += brightness;

      // 根据位置计算权重
      const y = Math.floor(i / width);
      let weight = middleRegionWeight;

      if (y < height * 0.3) {
        weight = topRegionWeight; // 顶部30%
      } else if (y > height * 0.7) {
        weight = bottomRegionWeight; // 底部30%
      }

      weightedLuminance += luminance * weight;
      totalWeight += weight;

      // 颜色分布统计
      if (brightness < 85) {
        colorBuckets.dark++;
      } else if (brightness < 170) {
        colorBuckets.medium++;
      } else {
        colorBuckets.light++;
      }
    }

    // 4. 计算各种指标
    const avgLuminance = totalLuminance / pixelCount;
    const avgBrightness = totalBrightness / pixelCount;
    const weightedAvgLuminance = weightedLuminance / totalWeight;

    // 5. 颜色分布分析
    const darkRatio = colorBuckets.dark / pixelCount;
    const lightRatio = colorBuckets.light / pixelCount;
    const mediumRatio = colorBuckets.medium / pixelCount;

    // 6. 智能阈值计算
    let threshold = 0.5; // 默认阈值

    // 如果图片主要是深色或浅色，调整阈值
    if (darkRatio > 0.6) {
      threshold = 0.3; // 深色图片，更容易选择白色文字
    } else if (lightRatio > 0.6) {
      threshold = 0.7; // 浅色图片，更容易选择黑色文字
    } else {
      threshold = 0.5; // 混合色彩，使用标准阈值
    }

    // 7. 综合判断文字颜色
    const useWeightedLuminance = weightedAvgLuminance;
    const shouldUseDarkText = useWeightedLuminance > threshold;

    // 8. 计算对比度并进行微调
    const whiteContrast = this.calculateContrast(useWeightedLuminance, 1.0);
    const blackContrast = this.calculateContrast(useWeightedLuminance, 0.0);

    // 确保对比度足够（WCAG标准建议至少4.5:1）
    let finalTextColor = shouldUseDarkText ? '#333333' : '#FFFFFF';
    let finalStatusBarColor = shouldUseDarkText ? '#000000' : '#ffffff';

    // 如果对比度不够，强制使用对比度更高的颜色
    if (Math.max(whiteContrast, blackContrast) < 3.0) {
      if (whiteContrast > blackContrast) {
        finalTextColor = '#FFFFFF';
        finalStatusBarColor = '#ffffff';
      } else {
        finalTextColor = '#333333';
        finalStatusBarColor = '#000000';
      }
    }

    // 9. 计算主色调
    const dominantColor = this.calculateDominantColor(imageData, pixelCount);

    return {
      recommendedTextColor: finalTextColor,
      statusBarColor: finalStatusBarColor,
      dominantColor: dominantColor,
      avgLuminance: avgLuminance,
      avgBrightness: avgBrightness,
      weightedLuminance: useWeightedLuminance,
      colorDistribution: { darkRatio, mediumRatio, lightRatio },
      contrast: { white: whiteContrast, black: blackContrast }
    };
  },

  // 计算相对亮度（更准确的感知亮度）
  calculateRelativeLuminance(r: number, g: number, b: number): number {
    // 将RGB值转换为0-1范围
    const rs = r / 255;
    const gs = g / 255;
    const bs = b / 255;

    // 应用gamma校正
    const rLinear = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);
    const gLinear = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);
    const bLinear = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);

    // 计算相对亮度
    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  },

  // 计算对比度
  calculateContrast(luminance1: number, luminance2: number): number {
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  },

  // 计算主色调
  calculateDominantColor(imageData: Uint8ClampedArray, pixelCount: number): string {
    // 简化的主色调计算，取平均RGB值
    let totalR = 0, totalG = 0, totalB = 0;

    for (let i = 0; i < pixelCount; i++) {
      totalR += imageData[i * 4];
      totalG += imageData[i * 4 + 1];
      totalB += imageData[i * 4 + 2];
    }

    const avgR = Math.round(totalR / pixelCount);
    const avgG = Math.round(totalG / pixelCount);
    const avgB = Math.round(totalB / pixelCount);

    // 转换为十六进制
    const toHex = (n: number) => {
      const hex = n.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(avgR)}${toHex(avgG)}${toHex(avgB)}`;
  },

  // 检查背景是否更新（仅检查全局背景设置，厨房背景由updateKitchenBackground管理）
  checkBackgroundUpdate() {
    // 如果是分享厨房模式，不进行背景更新检查，避免被全局设置干扰
    if (this.data.isSharedKitchen) {
      return;
    }
    
    // 获取当前存储的厨房背景
    const currentKitchenBg = wx.getStorageSync('current_kitchen_bg') || '';
    
    // 检查当前用户是否已登录
    const isLoggedIn = checkLogin();
    
    // 如果未登录，使用默认背景
    if (!isLoggedIn) {
      // 未登录状态，使用默认背景
      const defaultBg = DEFAULT_IMAGES.KITCHEN_BACKGROUND;
      if (this.data.shopBg !== defaultBg) {
        this.setData({ shopBg: defaultBg });
        this.watchShopBg(defaultBg);
      }
      return;
    }
    
    // 判断背景是否需要更新
    if (currentKitchenBg && this.data.shopBg !== currentKitchenBg) {
      // 使用存储的当前厨房背景
      this.setData({ shopBg: currentKitchenBg });
      this.watchShopBg(currentKitchenBg);
    } else if (!currentKitchenBg) {
      // 如果没有当前厨房背景，则使用全局背景或默认背景
      const globalShopBg = wx.getStorageSync('shopBg') || DEFAULT_IMAGES.KITCHEN_BACKGROUND;
      if (this.data.shopBg !== globalShopBg) {
        this.setData({ shopBg: globalShopBg });
        this.watchShopBg(globalShopBg);
      }
    }
  },

  // 跳转到菜品详情页
  navigateToDishDetail(e: any) {
    // 阻止冒泡，避免与添加购物车等操作冲突
    if (e.target &&
        (e.target.dataset.action === 'add' ||
         e.target.dataset.action === 'increase' ||
         e.target.dataset.action === 'decrease')) {
      return;
    }

    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${id}`
    });
  },

  // 返回首页
  async onBackToHome() {
    // 如果是分享厨房模式，需要退出分享模式
    if (this.data.isSharedKitchen) {
      try {
        // 清除分享厨房状态，重置所有相关数据
        this.setData({
          isSharedKitchen: false,
          sharedKitchenId: '',
          currentKitchenId: '',
          isOrderMode: false,
          // 重置餐厅信息
          restaurantInfo: {
            name: '',
            logo: '',
            notice: ''
          },
          // 重置分类和菜品
          categories: [],
          dishList: [],
          activeCategoryId: '',
          // 重置购物车
          cart: {
            cartList: [],
            totalPrice: 0,
            totalCount: 0
          },
          // 重置搜索
          searchKeyword: '',
          searchResults: [],
          showSearchResults: false
        });

        // 显示TabBar
        wx.showTabBar({});

        // 重新获取用户自己的厨房ID
        await this.getCurrentKitchenId();

        // 确保有厨房ID后再加载数据
        if (this.data.currentKitchenId) {
          // 重新加载数据
          await Promise.all([
            this.fetchRestaurantInfo(),
            this.fetchCategories()
          ]);

          // 分类加载完成后再加载菜品
          await this.fetchDishList();

          // 加载购物车
          await this.fetchCartList();

          // 更新菜品列表
          this.updateDishListWithCart();

          // 加载背景设置
          this.loadBackgroundSettings();

          wx.showToast({
            title: '已返回您的厨房',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.switchTab({
            url: '/pages/restaurant/restaurant'
          });
        }
      } catch (error) {
        console.error('退出分享厨房模式失败:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }

      return;
    }

    // 导航到首页
    if (this.data.isOrderMode) {
      // 在下单模式中，只需退出下单模式，保留在当前页面
      this.setData({
        isOrderMode: false
      });

      // 显示TabBar
      wx.showTabBar({});
    } else {
      // 不在下单模式中，则导航到首页
      wx.switchTab({
        url: '/pages/restaurant/restaurant'
      });
    }
  },

  // 分享点击（下单模式）
  onShareQrCode() {
    // 显示厨房二维码分享弹窗
    this.setData({
      showQrCodeShare: true
    })
  },

  // 关闭二维码分享弹窗
  onCloseQrCodeShare() {
    this.setData({
      showQrCodeShare: false
    })
  },
})