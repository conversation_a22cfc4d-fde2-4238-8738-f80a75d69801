请求接口: /api/payment/create
request.ts:42 请求参数: {amount: 0.1, description: "充值10大米"}
request.ts:16 显示loading，当前计数: 1
request.ts:95 请求耗时: 1097ms - /api/payment/create
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}
request.ts:26 隐藏loading，当前计数: 0
my-coins.ts:355 支付参数: {amount: 0.1, coins: 10, outTradeNo: "R1000117499503115705ws71b", paymentOrderId: 18, appId: "wx41b8de9ea1e51474", …}
my-coins.ts:382 微信支付成功: {errMsg: "requestPayment:ok"}
request.ts:41 请求接口: /api/payment/query/R1000117499503115705ws71b
request.ts:42 请求参数: {}
request.ts:95 请求耗时: 706ms - /api/payment/query/R1000117499503115705ws71b
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}
my-coins.ts:428 支付状态异常: {error: 0, body: {…}, message: ""}
verifyPaymentResult @ my-coins.ts:428
async function (async)
verifyPaymentResult @ my-coins.ts:422
(anonymous) @ my-coins.ts:388
listOnTimeout @ node:internal/timers:557
processTimers @ node:internal/timers:500
request.ts:41 请求接口: /api/user/coinRecords
request.ts:42 请求参数: {type: "all"}
request.ts:95 请求耗时: 159ms - /api/user/coinRecords
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}