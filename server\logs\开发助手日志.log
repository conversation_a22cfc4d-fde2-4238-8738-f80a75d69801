请求接口: /api/payment/create
request.ts:42 请求参数: {amount: 0.1, description: "充值10大米"}
request.ts:16 显示loading，当前计数: 1
request.ts:95 请求耗时: 1059ms - /api/payment/create
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}
request.ts:26 隐藏loading，当前计数: 0
my-coins.ts:355 支付参数: {amount: 0.1, coins: 10, outTradeNo: "R100011749949011491ceyjia", paymentOrderId: 13, appId: "wx41b8de9ea1e51474", …}
my-coins.ts:382 微信支付成功: {errMsg: "requestPayment:ok"}
request.ts:41 请求接口: /api/payment/query/R100011749949011491ceyjia
request.ts:42 请求参数: {}
request.ts:41 请求接口: /api/user/coinRecords
request.ts:42 请求参数: {type: "all"}
request.ts:95 请求耗时: 325ms - /api/user/coinRecords
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}
request.ts:95 请求耗时: 786ms - /api/payment/query/R100011749949011491ceyjia
request.ts:98 返回数据: {error: 0, body: {…}, message: ""}
my-coins.ts:428 支付状态异常: {error: 0, body: {…}, message: ""}
verifyPaymentResult @ my-coins.ts:428
async function (async)
verifyPaymentResult @ my-coins.ts:422
(anonymous) @ my-coins.ts:388
listOnTimeout @ node:internal/timers:557
processTimers @ node:internal/timers:500