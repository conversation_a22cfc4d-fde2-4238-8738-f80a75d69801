请求接口: /api/payment/create
VM714 request.js:49 请求参数: {amount: 28, description: "充值3000大米"}
VM714 request.js:22 显示loading，当前计数: 1
VM718 app.js:222 POST https://dzcdd.zj1.natnps.cn/api/payment/create 500 (Internal Server Error)(env: Windows,mp,1.06.2412050; lib: 3.8.8)
wx.request @ VM718 app.js:222
(anonymous) @ VM714 request.js:82
request @ VM714 request.js:47
createPaymentOrder @ userApi.ts:348
confirmPayment @ my-coins.ts:267
onConfirm @ confirm-dialog.ts:124
runNextTicks @ node:internal/process/task_queues:61
processTimers @ node:internal/timers:497
async function (async)
onConfirm @ confirm-dialog.ts:123
VM714 request.js:104 请求耗时: 638ms - /api/payment/create
VM714 request.js:107 返回数据: {error: 1000, body: {…}, message: "获取预支付ID失败"}
VM714 request.js:33 隐藏loading，当前计数: 0
请注意 showLoading 与 hideLoading 必须配对使用
safeHideLoading @ VM714 request.js:36
complete @ VM714 request.js:187
listOnTimeout @ node:internal/timers:557
processTimers @ node:internal/timers:500
请注意 showLoading 与 hideLoading 必须配对使用
confirmPayment @ my-coins.ts:282
async function (async)
confirmPayment @ my-coins.ts:267
onConfirm @ confirm-dialog.ts:124
runNextTicks @ node:internal/process/task_queues:61
processTimers @ node:internal/timers:497
async function (async)
onConfirm @ confirm-dialog.ts:123
my-coins.ts:283 支付失败: Error: 获取预支付ID失败
    at li.confirmPayment (my-coins.ts:268)(env: Windows,mp,1.06.2412050; lib: 3.8.8)