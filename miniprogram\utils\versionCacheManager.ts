/**
 * 版本控制缓存管理器
 * 统一管理图片版本信息和缓存策略
 */

interface VersionCacheItem {
  version: string;
  cacheTime: number;
  status: 'loading' | 'loaded' | 'error';
  etag?: string;
  lastModified?: string;
}

// 缓存策略配置
export const CACHE_STRATEGIES = {
  // 有版本号的图片永久缓存（应用生命周期内）
  VERSIONED: -1,
  // 静态图标缓存24小时
  STATIC_ICONS: 24 * 60 * 60 * 1000,
  // 菜品图片缓存12小时
  DISH_IMAGES: 12 * 60 * 60 * 1000,
  // 用户头像缓存6小时
  USER_AVATARS: 6 * 60 * 60 * 1000,
  // 背景图片缓存1小时
  BACKGROUNDS: 60 * 60 * 1000,
  // 默认缓存2小时
  DEFAULT: 2 * 60 * 60 * 1000
};

export class VersionCacheManager {
  private static cache = new Map<string, VersionCacheItem>();

  /**
   * 解析图片版本信息
   */
  static parseImageVersion(src: string): { cleanUrl: string, version: string, hasVersion: boolean } {
    if (!src) return { cleanUrl: '', version: '', hasVersion: false };
    
    // 检查URL中的版本参数
    const versionMatches = [
      src.match(/[?&]v=([^&]+)/),           // ?v=xxx 或 &v=xxx
      src.match(/[?&]version=([^&]+)/),     // ?version=xxx
      src.match(/[?&]t=([^&]+)/),           // ?t=xxx (时间戳)
      src.match(/[?&]_v=([^&]+)/)           // ?_v=xxx
    ];
    
    for (const match of versionMatches) {
      if (match) {
        return {
          cleanUrl: src.replace(match[0], ''),
          version: match[1],
          hasVersion: true
        };
      }
    }
    
    return { cleanUrl: src, version: '', hasVersion: false };
  }

  /**
   * 获取图片类型对应的缓存时间
   */
  static getCacheTime(src: string, hasVersion: boolean): number {
    // 有版本号的图片永久缓存
    if (hasVersion) {
      return CACHE_STRATEGIES.VERSIONED;
    }
    
    // 根据URL判断图片类型
    if (src.includes('/static/images/icons/')) {
      return CACHE_STRATEGIES.STATIC_ICONS;
    }
    
    if (src.includes('/uploads/dish/') || src.includes('dish')) {
      return CACHE_STRATEGIES.DISH_IMAGES;
    }
    
    if (src.includes('/uploads/avatar/') || src.includes('avatar')) {
      return CACHE_STRATEGIES.USER_AVATARS;
    }
    
    if (src.includes('/uploads/background/') || src.includes('background')) {
      return CACHE_STRATEGIES.BACKGROUNDS;
    }
    
    return CACHE_STRATEGIES.DEFAULT;
  }

  /**
   * 检查缓存是否有效
   */
  static isCacheValid(src: string, version: string, hasVersion: boolean): boolean {
    const cacheInfo = this.cache.get(src);
    
    if (!cacheInfo || cacheInfo.status !== 'loaded') {
      return false;
    }
    
    // 有版本号的图片，版本一致则永久有效
    if (hasVersion) {
      return cacheInfo.version === version;
    }
    
    // 无版本号的图片，检查缓存时间
    const cacheTime = this.getCacheTime(src, hasVersion);
    if (cacheTime === CACHE_STRATEGIES.VERSIONED) {
      return true; // 永久缓存
    }
    
    const now = Date.now();
    return (now - cacheInfo.cacheTime) < cacheTime;
  }

  /**
   * 设置缓存
   */
  static setCache(src: string, version: string, status: 'loading' | 'loaded' | 'error', options?: {
    etag?: string;
    lastModified?: string;
  }): void {
    this.cache.set(src, {
      version,
      cacheTime: Date.now(),
      status,
      etag: options?.etag,
      lastModified: options?.lastModified
    });
  }

  /**
   * 获取缓存
   */
  static getCache(src: string): VersionCacheItem | undefined {
    return this.cache.get(src);
  }

  /**
   * 删除缓存
   */
  static deleteCache(src: string): boolean {
    return this.cache.delete(src);
  }

  /**
   * 清理过期缓存
   */
  static cleanExpiredCache(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    for (const [url, info] of entries) {
      const { version, hasVersion } = this.parseImageVersion(url);
      const cacheTime = this.getCacheTime(url, hasVersion);
      
      // 永久缓存的不清理
      if (cacheTime === CACHE_STRATEGIES.VERSIONED) {
        continue;
      }
      
      // 检查是否过期
      if ((now - info.cacheTime) > cacheTime) {
        this.cache.delete(url);
        console.log('清理过期缓存:', url);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheStats(): {
    total: number;
    loaded: number;
    loading: number;
    error: number;
    versioned: number;
  } {
    const entries = Array.from(this.cache.values());
    const stats = {
      total: entries.length,
      loaded: 0,
      loading: 0,
      error: 0,
      versioned: 0
    };

    entries.forEach(item => {
      stats[item.status]++;
      if (item.version) {
        stats.versioned++;
      }
    });

    return stats;
  }
} 