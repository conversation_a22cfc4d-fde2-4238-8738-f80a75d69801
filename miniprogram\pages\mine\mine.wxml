<!-- 我的页面 -->
<view class="mine-container" style="{{textColorStyle}}">
  <!-- 顶部背景区域 -->
  <view class="user-info-area">
    <view class="bg-image" style="{{navBgStyle ? 'background: ' + navBgStyle : ''}}"></view>
    
    <!-- 顶部标题区域 -->
    <view class="top-title" style="top: {{statusBarHeight}}px;">
      <text class="page-title">个人中心</text>
    </view>
    
    <!-- 用户信息 -->
    <view class="user-info">
      <block wx:if="{{isLoggedIn}}">
        <view class="restaurant-avatar-area">
          <smart-image
            custom-class="avatar"
            src="{{userInfo.avatarUrl}}"
            mode="aspectFill"
            width="120rpx"
            height="120rpx"
            border-radius="60"
          />
        </view>
        <view class="user-detail">
          <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
          <view class="user-id-row">
            <text class="user-id">用户ID：{{userInfo.userId}}</text>
            <view class="copy-btn" bindtap="copyUserId">
              <image class="copy-icon" src="/static/images/icons/copy.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="restaurant-avatar-area">
          <smart-image
            custom-class="avatar"
            src="{{defaultImages.USER_AVATAR}}"
            mode="aspectFill"
            width="120rpx"
            height="120rpx"
            border-radius="60"
          />
        </view>
        <view class="user-detail">
          <view class="login-btn-container">
            <button class="login-btn" bind:tap="onLogin">立即登录</button>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 切换主题按钮 -->
    <view class="theme-btn" bind:tap="showBackgroundSettings">
      <image class="theme-icon" src="/static/images/icons/theme-switch.png" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 内容区域 -->
  <view class="function-area">
    <!-- 用户数据统计 -->
    <view class="user-stats">
      <view class="stats-content">
        <view class="stats-left">
          <view class="stats-item">
            <text class="stats-value coins-value">{{userInfo.coins || 0}}</text>
            <text class="stats-label">大米</text>
          </view>
          <view class="stats-item">
            <text class="stats-value">{{userInfo.likes || 0}}</text>
            <text class="stats-label">获赞</text>
          </view>
          <view class="stats-item">
            <text class="stats-value">{{userInfo.dishes || 0}}</text>
            <text class="stats-label">添加</text>
          </view>
          <view class="stats-item">
            <text class="stats-value">{{userInfo.kitchens || 0}}</text>
            <text class="stats-label">厨房</text>
          </view>
        </view>
        <view class="stats-right">
          <button class="edit-profile-btn" bind:tap="showEditProfileModal">编辑资料</button>
        </view>
      </view>
    </view>
    
    <!-- 会员按钮 -->
    <view class="member-button-container" bindtap="showMemberModal">
      <view class="member-button {{isMember ? 'is-member' : ''}}">
        <view class="member-icon">
          <image wx:if="{{isMember}}" src="/static/images/icons/crown.png" mode="aspectFit"></image>
          <image wx:else src="/static/images/icons/medal.png" mode="aspectFit"></image>
        </view>
        <view class="member-content">
          <view class="member-title">{{isMember ? memberInfo.name + '会员' : '开通会员'}}</view>
          <view class="member-subtitle">{{isMember ? memberInfo.expireDate + '到期' : '享多项专属特权'}}</view>
        </view>
        <view class="member-badge">VIP</view>
        <view class="member-arrow">></view>
      </view>
    </view>
    
    <!-- 快捷功能按钮 -->
    <view class="quick-functions">
      <view class="function-item" bind:tap="navigateToFunction" data-function="kitchen">
        <view class="function-icon">
          <image src="/static/images/icons/kitchen.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">厨房管理</text>
      </view>
      <view class="function-item" bind:tap="navigateToFunction" data-function="coins">
        <view class="function-icon">
          <image src="/static/images/icons/coins.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">我的大米</text>
      </view>
      <view class="function-item" bind:tap="navigateToFunction" data-function="tasks">
        <view class="function-icon">
          <image src="/static/images/icons/task.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">任务大厅</text>
      </view>
      <view class="function-item" bind:tap="navigateToFunction" data-function="service">
        <view class="function-icon">
          <image src="/static/images/icons/chat.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">联系客服</text>
      </view>
      <view class="function-item" bind:tap="navigateToFunction" data-function="more">
        <view class="function-icon">
          <image src="/static/images/icons/settings.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">更多功能</text>
      </view>
    </view>
    
    <!-- 功能列表标题 -->
    <view class="section-title">其他功能</view>
    
    <!-- 功能列表 -->
    <view class="feature-list card-shadow">
      <!-- 注释掉消息绑定按钮 -->
      <!-- <view class="feature-item" bind:tap="navigateToFeature" data-feature="message">
        <image class="feature-icon" src="/static/images/icons/phone.png" mode="aspectFit"></image>
        <text class="feature-name">消息绑定</text>
        <text class="feature-arrow">></text>
      </view> -->
      
      <!-- 注释掉关于我们按钮 -->
      <!-- <view class="feature-item" bind:tap="navigateToFeature" data-feature="about">
        <image class="feature-icon" src="/static/images/icons/info.png" mode="aspectFit"></image>
        <text class="feature-name">关于我们</text>
        <text class="feature-arrow">></text>
      </view> -->
      
      <view class="feature-item" bind:tap="showFeedbackModal">
        <image class="feature-icon" src="/static/images/icons/feedback.png" mode="aspectFit"></image>
        <text class="feature-name">问题反馈</text>
        <text class="feature-arrow">></text>
      </view>
      <view class="feature-item" bind:tap="clearCache">
        <image class="feature-icon" src="/static/images/icons/trash.png" mode="aspectFit"></image>
        <text class="feature-name">清除缓存</text>
        <text class="feature-arrow">></text>
      </view>
      <view class="feature-item" wx:if="{{isLoggedIn}}" bind:tap="showLogoutConfirm">
        <image class="feature-icon" src="/static/images/icons/logout.png" mode="aspectFit"></image>
        <text class="feature-name">退出登录</text>
        <text class="feature-arrow">></text>
      </view>
    </view>
  </view>
  
  <!-- 编辑资料弹窗 - 使用通用模态对话框组件 -->
  <modal-dialog 
    visible="{{showEditProfile}}" 
    title="编辑资料" 
    showCancel="{{true}}" 
    cancelText="取消" 
    confirmText="保存"
    themeColor="#FF6B35"
    bind:close="closeEditProfileModal"
    bind:cancel="closeEditProfileModal"
    bind:confirm="saveProfile"
  >
    <!-- 编辑表单 -->
    <view class="profile-form">
      <!-- 头像编辑 -->
      <view class="avatar-editor fade-in-up" style="animation-delay: 0.1s;">
        <smart-image
          custom-class="edit-avatar"
          src="{{editUserInfo.avatarUrl}}"
          mode="aspectFill"
          width="160rpx"
          height="160rpx"
          border-radius="80"
        />
        <view class="avatar-upload-btn" bindtap="chooseAvatar">
          <image class="upload-icon" src="/static/images/icons/camera.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 昵称编辑 -->
      <view class="form-item fade-in-up" style="animation-delay: 0.2s;">
        <text class="form-label">昵称</text>
        <input class="form-input" placeholder="请输入昵称" value="{{editUserInfo.nickName}}" bindinput="onNicknameInput" />
      </view>
      
      <!-- 性别选择 -->
      <view class="form-item fade-in-up" style="animation-delay: 0.3s;">
        <text class="form-label">性别</text>
        <view class="gender-selector">
          <view class="gender-option {{editUserInfo.gender === 1 ? 'selected' : ''}}" bindtap="selectGender" data-gender="1">
            <image class="gender-icon" src="/static/images/icons/male.png" mode="aspectFit"></image>
            <text class="gender-text">男</text>
          </view>
          <view class="gender-option {{editUserInfo.gender === 2 ? 'selected' : ''}}" bindtap="selectGender" data-gender="2">
            <image class="gender-icon" src="/static/images/icons/female.png" mode="aspectFit"></image>
            <text class="gender-text">女</text>
          </view>
          <view class="gender-option {{editUserInfo.gender === 0 ? 'selected' : ''}}" bindtap="selectGender" data-gender="0">
            <image class="gender-icon" src="/static/images/icons/neutral.png" mode="aspectFit"></image>
            <text class="gender-text">保密</text>
          </view>
        </view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 背景设置弹窗 -->
  <background-settings
    visible="{{showBackgroundSettings}}"
    currentShopBg="{{shopBg}}"
    currentNavBg="{{navBgStyle}}"
    currentNavBgIndex="{{navBgIndex}}"
    isVip="{{isMember}}"
    kitchenId="{{currentKitchenId}}"
    bind:close="closeBackgroundSettings"
    bind:save="saveBackgroundSettings"
  />
  
  <!-- 退出登录确认弹窗 -->
  <confirm-dialog
    visible="{{showLogoutConfirm}}"
    title="退出登录"
    content="确定要退出当前账号吗？"
    cancelText="取消"
    confirmText="退出"
    confirmColor="#FF6B35"
    bind:cancel="cancelLogout"
    bind:confirm="confirmLogout"
  />
  
  <!-- 清除缓存确认弹窗 -->
  <confirm-dialog
    visible="{{showClearCacheConfirm}}"
    title="清除缓存"
    content="确定要清除本地缓存数据吗？\n清除后部分设置将会恢复默认。"
    cancelText="取消"
    confirmText="清除"
    confirmColor="#FF6B35"
    bind:cancel="cancelClearCache"
    bind:confirm="confirmClearCache"
  />
  
  <!-- 会员组件 -->
  <kitchen-member
    visible="{{showMemberModal}}"
    kitchenId="{{userInfo.userId}}"
    userCoins="{{userInfo.coins}}"
    bind:close="closeMemberModal"
    bind:success="onMembershipSuccess"
  />

  <!-- 问题反馈弹窗 -->
  <modal-dialog 
    visible="{{showFeedbackModal}}" 
    title="问题反馈" 
    showCancel="{{true}}" 
    cancelText="取消" 
    confirmText="提交反馈"
    themeColor="#FF6B35"
    bind:close="closeFeedbackModal"
    bind:cancel="closeFeedbackModal"
    bind:confirm="submitFeedback"
  >
    <!-- 反馈表单 -->
    <view class="feedback-form">
      <!-- 反馈类型选择 -->
      <view class="form-item fade-in-up" style="animation-delay: 0.1s;">
        <text class="form-label">反馈类型</text>
        <view class="feedback-type-selector">
          <view class="type-option {{feedbackData.type === 'bug' ? 'selected' : ''}}" bindtap="selectFeedbackType" data-type="bug">
            <text class="type-text">问题反馈</text>
          </view>
          <view class="type-option {{feedbackData.type === 'feature' ? 'selected' : ''}}" bindtap="selectFeedbackType" data-type="feature">
            <text class="type-text">功能建议</text>
          </view>
          <view class="type-option {{feedbackData.type === 'other' ? 'selected' : ''}}" bindtap="selectFeedbackType" data-type="other">
            <text class="type-text">其他</text>
          </view>
        </view>
      </view>
      
      <!-- 问题描述 -->
      <view class="form-item fade-in-up" style="animation-delay: 0.2s;">
        <text class="form-label">问题描述 <text class="required">*</text></text>
        <textarea 
          class="form-textarea" 
          placeholder="请详细描述您遇到的问题或建议..." 
          value="{{feedbackData.description}}" 
          bindinput="onDescriptionInput"
          maxlength="200"
          show-confirm-bar="{{false}}"
        />
        <text class="char-count">{{feedbackData.description.length}}/200</text>
      </view>
      
      <!-- 图片上传 -->
      <view class="form-item fade-in-up" style="animation-delay: 0.3s;">
        <text class="form-label">相关截图</text>
        <view class="image-upload-area">
          <view class="uploaded-images">
            <view class="image-item" wx:for="{{feedbackData.images}}" wx:key="index">
              <smart-image
                custom-class="uploaded-image"
                src="{{item}}"
                mode="aspectFill"
                width="160rpx"
                height="160rpx"
                border-radius="8"
                bindtap="previewImage"
                data-index="{{index}}"
              />
              <view class="delete-image-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-btn" wx:if="{{feedbackData.images.length < 3}}" bindtap="chooseImages">
              <image class="upload-icon" src="/static/images/icons/camera.png" mode="aspectFit"></image>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
          <text class="upload-tip">最多可上传3张图片</text>
        </view>
      </view>
    </view>
  </modal-dialog>

  <!-- 联系客服弹窗 -->
  <modal-dialog 
    visible="{{showServiceModal}}" 
    title="联系客服" 
    showCancel="{{false}}" 
    confirmText="我知道了"
    themeColor="#FF6B35"
    bind:close="closeServiceModal"
    bind:confirm="closeServiceModal"
  >
    <!-- 客服信息 -->
    <view class="service-content">
      <view class="service-tip">
        <text class="tip-text">扫描下方微信二维码添加客服</text>
        <text class="tip-subtitle">工作时间：9:00-18:00（周一至周五）</text>
      </view>
      
      <!-- 微信二维码 -->
      <view class="qrcode-container">
        <smart-image
          custom-class="service-qrcode"
          src="{{defaultImages.WECHAT_QR}}"
          mode="aspectFit"
          width="400rpx"
          height="400rpx"
          border-radius="12"
          bindlongpress="onQrcodeLongPress"
          data-src="{{defaultImages.WECHAT_QR}}"
        />
        <text class="qrcode-tip">长按二维码保存到相册</text>
      </view>
    </view>
  </modal-dialog>
</view> 