/**
 * 微信支付服务
 * 基于微信支付 API v3
 */
import fs from 'fs';
import path from 'path';
import config from '../config/config';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';

// 使用require导入微信支付模块
const WxPay = require('wechatpay-node-v3');

/**
 * 统一下单请求参数
 */
export interface UnifiedOrderParams {
  description: string;
  outTradeNo: string;
  totalFee: number;
  openid: string;
  attach?: string;
  timeExpire?: string;
}

/**
 * 支付回调通知数据
 */
export interface PaymentNotification {
  transaction_id: string;
  out_trade_no: string;
  trade_state: string;
  trade_state_desc: string;
  bank_type: string;
  total_fee: number;
  cash_fee: number;
  success_time: string;
  payer: {
    openid: string;
  };
}

/**
 * 查询订单结果
 */
export interface QueryOrderResult {
  transaction_id: string;
  out_trade_no: string;
  trade_state: string;
  trade_state_desc: string;
  bank_type: string;
  total_fee: number;
  cash_fee: number;
  success_time?: string;
  payer: {
    openid: string;
  };
}

class PaymentService {
  private payment: any;
  private readonly appId: string;
  private readonly mchId: string;
  private readonly apiV3Key: string;
  private readonly serialNo: string;
  private readonly privateKey: string;
  private readonly publicKey: string;
  private readonly notifyUrl: string;

  constructor() {
    this.appId = config.wechatPay.appId;
    this.mchId = config.wechatPay.mchIdV3 || config.wechatPay.mchId;
    this.apiV3Key = config.wechatPay.apiV3Key;
    this.serialNo = config.wechatPay.serialNo;
    this.notifyUrl = config.wechatPay.notifyUrl;

    // 读取商户私钥和证书
    try {
      this.privateKey = fs.readFileSync(config.wechatPay.privateKeyPath, 'utf8');
      this.publicKey = fs.readFileSync(config.wechatPay.certificatePath, 'utf8');
    } catch (error: any) {
      logger.error('读取微信支付证书失败:', error);
      throw new BusinessError('微信支付配置错误，证书文件不存在');
    }

    // 初始化微信支付实例
    this.payment = new WxPay({
      appid: this.appId,
      mchid: this.mchId,
      publicKey: this.publicKey,
      privateKey: this.privateKey,
      key: this.apiV3Key,
    });

    logger.info('微信支付服务初始化成功');
  }

  /**
   * 小程序统一下单
   * @param params 下单参数
   * @returns 支付参数
   */
  async jsapiOrder(params: UnifiedOrderParams) {
    try {
      const orderParams = {
        appid: this.appId,
        mchid: this.mchId,
        description: params.description,
        out_trade_no: params.outTradeNo,
        time_expire: params.timeExpire || this.getExpireTime(),
        attach: params.attach || '',
        notify_url: this.notifyUrl,
        amount: {
          total: params.totalFee,
          currency: 'CNY'
        },
        payer: {
          openid: params.openid
        }
      };

      logger.info('微信支付统一下单请求参数:');
      logger.info(JSON.stringify(orderParams, null, 2));

      // 调用统一下单接口
      const result = await this.payment.transactions_jsapi(orderParams);

      logger.info('微信支付统一下单响应:');
      logger.info(JSON.stringify(result, null, 2));

      // 检查响应是否包含错误
      if (result.status && result.status !== 200) {
        logger.error('微信支付统一下单返回错误状态:');
        logger.error(JSON.stringify(result, null, 2));
        throw new BusinessError(`微信支付下单失败: ${result.error || '未知错误'}`);
      }

      // 检查是否成功获取到支付参数
      if (!result.data || !result.data.package) {
        logger.error('微信支付响应数据格式错误:', result);
        throw new BusinessError('获取预支付ID失败');
      }

      // 从package中提取prepay_id
      const packageStr = result.data.package;
      const prepayIdMatch = packageStr.match(/prepay_id=(.+)/);
      if (!prepayIdMatch) {
        logger.error('无法从package中提取prepay_id:', packageStr);
        throw new BusinessError('获取预支付ID失败');
      }
      const prepayId = prepayIdMatch[1];

      logger.info('成功获取prepay_id:', prepayId);

      return {
        ...result.data,
        prepayId: prepayId,
        outTradeNo: params.outTradeNo
      };
    } catch (error: any) {
      logger.error('微信支付统一下单失败:', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`微信支付下单失败: ${error.message}`);
    }
  }

  /**
   * 生成小程序支付参数
   * @param prepayId 预支付ID
   * @returns 支付参数
   */
  private generatePayParams(prepayId: string) {
    const timeStamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = this.generateNonceStr();
    const packageStr = `prepay_id=${prepayId}`;
    const signType = 'RSA';

    // 生成签名
    const signMessage = `${this.appId}\n${timeStamp}\n${nonceStr}\n${packageStr}\n`;
    const paySign = this.payment.getSignature('POST', nonceStr, timeStamp, '/v3/pay/transactions/jsapi', signMessage);

    return {
      timeStamp,
      nonceStr,
      package: packageStr,
      signType,
      paySign
    };
  }

  /**
   * 查询订单
   * @param outTradeNo 商户订单号
   * @returns 订单信息
   */
  async queryOrder(outTradeNo: string): Promise<QueryOrderResult> {
    try {
      logger.info('查询微信支付订单:', outTradeNo);

      const result = await this.payment.query({
        out_trade_no: outTradeNo,
        mchid: this.mchId
      });

      logger.info('微信支付订单查询结果:', JSON.stringify(result, null, 2));

      return {
        transaction_id: result.transaction_id,
        out_trade_no: result.out_trade_no,
        trade_state: result.trade_state,
        trade_state_desc: result.trade_state_desc,
        bank_type: result.bank_type || '',
        total_fee: result.amount?.total || 0,
        cash_fee: result.amount?.payer_total || result.amount?.total || 0,
        success_time: result.success_time,
        payer: {
          openid: result.payer?.openid || ''
        }
      };
    } catch (error: any) {
      logger.error('查询微信支付订单失败:', error);
      throw new BusinessError(`查询订单失败: ${error.message}`);
    }
  }

  /**
   * 关闭订单
   * @param outTradeNo 商户订单号
   */
  async closeOrder(outTradeNo: string) {
    try {
      logger.info('关闭微信支付订单:', outTradeNo);

      await this.payment.close({
        out_trade_no: outTradeNo,
        mchid: this.mchId
      });

      logger.info('微信支付订单关闭成功:', outTradeNo);
    } catch (error: any) {
      logger.error('关闭微信支付订单失败:', error);
      throw new BusinessError(`关闭订单失败: ${error.message}`);
    }
  }

  /**
   * 验证支付回调通知
   * @param signature 签名
   * @param timestamp 时间戳
   * @param nonce 随机字符串
   * @param body 请求体
   * @param serial 证书序列号
   * @returns 验证结果
   */
  verifyNotification(signature: string, timestamp: string, nonce: string, body: string, serial: string): boolean {
    try {
      return this.payment.verifySign({
        timestamp,
        nonce,
        serial,
        signature,
        body
      });
    } catch (error: any) {
      logger.error('验证支付通知签名失败:', error);
      return false;
    }
  }

  /**
   * 解密支付通知数据
   * @param encryptedData 加密数据
   * @param nonce 随机字符串
   * @param associatedData 关联数据
   * @returns 解密后的数据
   */
  decryptNotification(encryptedData: string, nonce: string, associatedData: string): PaymentNotification {
    try {
      const decryptedData = this.payment.decipher_gcm(encryptedData, nonce, associatedData);
      return JSON.parse(decryptedData);
    } catch (error: any) {
      logger.error('解密支付通知数据失败:', error);
      throw new BusinessError('解密通知数据失败');
    }
  }

  /**
   * 生成随机字符串
   * @param length 长度
   * @returns 随机字符串
   */
  private generateNonceStr(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 获取订单过期时间
   * @returns ISO 8601格式的时间字符串
   */
  private getExpireTime(): string {
    const expireTime = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期
    return expireTime.toISOString();
  }

  /**
   * 申请退款
   * @param outTradeNo 商户订单号
   * @param outRefundNo 商户退款单号
   * @param totalFee 订单总金额
   * @param refundFee 退款金额
   * @param reason 退款原因
   * @returns 退款结果
   */
  async refund(outTradeNo: string, outRefundNo: string, totalFee: number, refundFee: number, reason?: string) {
    try {
      const refundParams = {
        out_trade_no: outTradeNo,
        out_refund_no: outRefundNo,
        reason: reason || '用户申请退款',
        notify_url: this.notifyUrl.replace('/notify', '/refund-notify'),
        amount: {
          refund: refundFee,
          total: totalFee,
          currency: 'CNY'
        }
      };

      logger.info('微信支付申请退款请求参数:', JSON.stringify(refundParams, null, 2));

      const result = await this.payment.refunds(refundParams);

      logger.info('微信支付申请退款响应:', JSON.stringify(result, null, 2));

      return result;
    } catch (error: any) {
      logger.error('微信支付申请退款失败:', error);
      throw new BusinessError(`申请退款失败: ${error.message}`);
    }
  }

  /**
   * 查询退款
   * @param outRefundNo 商户退款单号
   * @returns 退款信息
   */
  async queryRefund(outRefundNo: string) {
    try {
      logger.info('查询微信支付退款:', outRefundNo);

      const result = await this.payment.find_refunds({
        out_refund_no: outRefundNo
      });

      logger.info('微信支付退款查询结果:', JSON.stringify(result, null, 2));

      return result;
    } catch (error: any) {
      logger.error('查询微信支付退款失败:', error);
      throw new BusinessError(`查询退款失败: ${error.message}`);
    }
  }
}

// 创建单例
const paymentService = new PaymentService();

export default paymentService; 