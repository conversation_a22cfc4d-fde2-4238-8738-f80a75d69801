import { request } from '../utils/request'

// 获取评论功能开关状态
export const getCommentEnabled = () => {
  return request({
    url: '/api/system/comment-enabled',
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 获取系统设置
export const getSystemSetting = (key: string) => {
  return request({
    url: `/api/system/setting/${key}`,
    method: 'GET',
    data: {},
    showLoading: false
  })
}

// 更新系统设置
export const updateSystemSetting = (key: string, value: any, type: string = 'string', description?: string) => {
  return request({
    url: '/api/system/setting',
    method: 'POST',
    data: { key, value, type, description },
    showLoading: false
  })
} 