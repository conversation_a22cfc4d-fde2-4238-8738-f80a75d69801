"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processImageUrl = exports.getRandomKitchenBackgroundUrl = exports.getRandomKitchenBackgroundRelativePath = exports.getRandomKitchenAvatarUrl = exports.getRandomKitchenAvatarRelativePath = exports.getRandomUserAvatarUrl = exports.getRandomUserAvatarRelativePath = exports.getDefaultImageUrl = exports.getDefaultImageRelativePath = exports.getFullUrl = exports.getRelativePath = exports.convertToFullUrl = exports.convertToRelativePath = void 0;
/**
 * URL管理器
 * 统一管理图片URL的生成和转换
 */
const config_1 = __importDefault(require("../config/config"));
/**
 * 将完整URL转换为相对路径
 * @param fullUrl 完整URL
 * @returns 相对路径
 */
const convertToRelativePath = (fullUrl) => {
    if (!fullUrl)
        return '';
    // 如果已经是相对路径，直接返回
    if (fullUrl.startsWith('/uploads/')) {
        return fullUrl;
    }
    // 移除域名部分，提取相对路径
    const baseUrls = [
        config_1.default.server.baseUrl,
        config_1.default.server.corsOrigin,
        'https://dzcdd.zj1.natnps.cn',
        'http://dzcdd.zj1.natnps.cn'
    ];
    for (const baseUrl of baseUrls) {
        if (fullUrl.startsWith(baseUrl)) {
            return fullUrl.replace(baseUrl, '');
        }
    }
    // 如果都不匹配，尝试提取/uploads/开始的部分
    const uploadsIndex = fullUrl.indexOf('/uploads/');
    if (uploadsIndex !== -1) {
        return fullUrl.substring(uploadsIndex);
    }
    // 如果无法转换，返回原URL
    return fullUrl;
};
exports.convertToRelativePath = convertToRelativePath;
/**
 * 将相对路径转换为完整URL
 * @param relativePath 相对路径
 * @returns 完整URL
 */
const convertToFullUrl = (relativePath) => {
    if (!relativePath)
        return '';
    // 如果已经是完整URL，直接返回
    if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
        return relativePath;
    }
    // 确保路径以/开头
    const path = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;
    return `${config_1.default.server.baseUrl}${path}`;
};
exports.convertToFullUrl = convertToFullUrl;
/**
 * 生成文件的相对路径
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 相对路径
 */
const getRelativePath = (filename, subDir = 'dish') => {
    return `/uploads/${subDir}/${filename}`;
};
exports.getRelativePath = getRelativePath;
/**
 * 生成文件的完整URL
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 完整URL
 */
const getFullUrl = (filename, subDir = 'dish') => {
    const relativePath = (0, exports.getRelativePath)(filename, subDir);
    return (0, exports.convertToFullUrl)(relativePath);
};
exports.getFullUrl = getFullUrl;
/**
 * 获取默认图片的相对路径
 * @param type 图片类型
 * @returns 相对路径
 */
const getDefaultImageRelativePath = (type) => {
    const imageMap = {
        'user-avatar': '/uploads/default/tou6.jpg',
        'kitchen-avatar': '/uploads/default/chu1.jpg',
        'kitchen-background': '/uploads/default/bj6.jpg',
        'dish': '/uploads/default/dish.jpg'
    };
    return imageMap[type];
};
exports.getDefaultImageRelativePath = getDefaultImageRelativePath;
/**
 * 获取默认图片的完整URL
 * @param type 图片类型
 * @returns 完整URL
 */
const getDefaultImageUrl = (type) => {
    const relativePath = (0, exports.getDefaultImageRelativePath)(type);
    return (0, exports.convertToFullUrl)(relativePath);
};
exports.getDefaultImageUrl = getDefaultImageUrl;
/**
 * 获取随机用户头像的相对路径
 * @returns 相对路径
 */
const getRandomUserAvatarRelativePath = () => {
    const avatarIndex = Math.floor(Math.random() * 6) + 1;
    return `/uploads/default/tou${avatarIndex}.jpg`;
};
exports.getRandomUserAvatarRelativePath = getRandomUserAvatarRelativePath;
/**
 * 获取随机用户头像的完整URL
 * @returns 完整URL
 */
const getRandomUserAvatarUrl = () => {
    const relativePath = (0, exports.getRandomUserAvatarRelativePath)();
    return (0, exports.convertToFullUrl)(relativePath);
};
exports.getRandomUserAvatarUrl = getRandomUserAvatarUrl;
/**
 * 获取随机厨房头像的相对路径
 * @returns 相对路径
 */
const getRandomKitchenAvatarRelativePath = () => {
    const avatarIndex = Math.floor(Math.random() * 6) + 1;
    return `/uploads/default/chu${avatarIndex}.jpg`;
};
exports.getRandomKitchenAvatarRelativePath = getRandomKitchenAvatarRelativePath;
/**
 * 获取随机厨房头像的完整URL
 * @returns 完整URL
 */
const getRandomKitchenAvatarUrl = () => {
    const relativePath = (0, exports.getRandomKitchenAvatarRelativePath)();
    return (0, exports.convertToFullUrl)(relativePath);
};
exports.getRandomKitchenAvatarUrl = getRandomKitchenAvatarUrl;
/**
 * 获取随机厨房背景的相对路径
 * @returns 相对路径
 */
const getRandomKitchenBackgroundRelativePath = () => {
    const backgroundIndex = Math.floor(Math.random() * 6) + 1;
    return `/uploads/default/bj${backgroundIndex}.jpg`;
};
exports.getRandomKitchenBackgroundRelativePath = getRandomKitchenBackgroundRelativePath;
/**
 * 获取随机厨房背景的完整URL
 * @returns 完整URL
 */
const getRandomKitchenBackgroundUrl = () => {
    const relativePath = (0, exports.getRandomKitchenBackgroundRelativePath)();
    return (0, exports.convertToFullUrl)(relativePath);
};
exports.getRandomKitchenBackgroundUrl = getRandomKitchenBackgroundUrl;
/**
 * 处理图片URL，确保返回完整URL
 * @param imageUrl 图片URL（可能是相对路径或完整URL）
 * @param defaultType 默认图片类型
 * @returns 完整URL
 */
const processImageUrl = (imageUrl, defaultType) => {
    if (!imageUrl || imageUrl.trim() === '') {
        return (0, exports.getDefaultImageUrl)(defaultType);
    }
    return (0, exports.convertToFullUrl)(imageUrl);
};
exports.processImageUrl = processImageUrl;
exports.default = {
    convertToRelativePath: exports.convertToRelativePath,
    convertToFullUrl: exports.convertToFullUrl,
    getRelativePath: exports.getRelativePath,
    getFullUrl: exports.getFullUrl,
    getDefaultImageRelativePath: exports.getDefaultImageRelativePath,
    getDefaultImageUrl: exports.getDefaultImageUrl,
    getRandomUserAvatarRelativePath: exports.getRandomUserAvatarRelativePath,
    getRandomUserAvatarUrl: exports.getRandomUserAvatarUrl,
    getRandomKitchenAvatarRelativePath: exports.getRandomKitchenAvatarRelativePath,
    getRandomKitchenAvatarUrl: exports.getRandomKitchenAvatarUrl,
    getRandomKitchenBackgroundRelativePath: exports.getRandomKitchenBackgroundRelativePath,
    getRandomKitchenBackgroundUrl: exports.getRandomKitchenBackgroundUrl,
    processImageUrl: exports.processImageUrl,
};
//# sourceMappingURL=urlManager.js.map