"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 桌号管理模块路由
 * 处理桌号相关的API路由
 */
const express_1 = __importDefault(require("express"));
const tableController_1 = __importDefault(require("../controllers/tableController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取桌号列表 - 支持匿名访问（分享厨房模式）
router.get('/list', auth_1.optionalAuth, tableController_1.default.getTableList);
// 添加桌号
router.post('/add', auth_1.verifyToken, tableController_1.default.addTable);
// 更新桌号
router.post('/update', auth_1.verifyToken, tableController_1.default.updateTable);
// 删除桌号
router.post('/delete', auth_1.verifyToken, tableController_1.default.deleteTable);
// 桌号排序
router.post('/sort', auth_1.verifyToken, tableController_1.default.sortTables);
exports.default = router;
//# sourceMappingURL=tableRoutes.js.map