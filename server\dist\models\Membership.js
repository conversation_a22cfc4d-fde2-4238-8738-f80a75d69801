"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 会员模型
 * 存储用户会员信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 会员模型类
class Membership extends sequelize_1.Model {
    // 检查会员是否有效
    isValid() {
        return this.is_member && this.expire_date !== null && new Date() < this.expire_date;
    }
    // 获取会员特权
    getPrivileges() {
        if (!this.isValid()) {
            return {
                customTheme: false,
                tableManagement: false
            };
        }
        return {
            customTheme: true,
            tableManagement: true
        };
    }
}
// 初始化会员模型
Membership.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '会员ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    is_member: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否是会员',
    },
    member_type: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: true,
        comment: '会员类型(monthly:月度会员,yearly:年度会员)',
    },
    expire_date: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '到期时间',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Membership',
    tableName: 'memberships',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_expire_date',
            fields: ['expire_date'],
        },
    ],
});
exports.default = Membership;
//# sourceMappingURL=Membership.js.map