<!-- 发现页面 -->
<view class="discover-page" bindtap="onTap" style="{{textColorStyle}}">
  <!-- 固定顶部区域 -->
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
    <custom-navbar title="发现" navBgStyle="{{navBgStyle}}"></custom-navbar>

    <!-- 顶部标签页(修改为图标+文字按钮) -->
    <view class="tab-header">
      <view class="tab-button {{activeTab === 'likes' ? 'active' : ''}}" bindtap="navigateToMessagePage" data-tab="likes">
        <image class="tab-icon" src="/static/images/icons/good.png" mode="aspectFit"></image>
        <text class="tab-text">赞和添加</text>
        <view class="badge" wx:if="{{likesCount > 0}}">{{likesCount}}</view>
      </view>
      <view class="tab-button {{activeTab === 'system' ? 'active' : ''}}" bindtap="navigateToMessagePage" data-tab="system">
        <image class="tab-icon" src="/static/images/icons/email.png" mode="aspectFit"></image>
        <text class="tab-text">系统消息</text>
        <view class="badge" wx:if="{{messageCount > 0}}">{{messageCount}}</view>
      </view>
      <view class="tab-button {{activeTab === 'comment' ? 'active' : ''}}" bindtap="navigateToMessagePage" data-tab="comment">
        <image class="tab-icon" src="/static/images/icons/fail.png" mode="aspectFit"></image>
        <text class="tab-text">用餐评价</text>
        <view class="badge" wx:if="{{commentCount > 0}}">{{commentCount}}</view>
      </view>
    </view>

    <!-- 搜索栏和排序按钮 -->
    <view class="search-sort-container">
      <view class="search-container">
        <icon type="search" size="18" color="#999"></icon>
        <input
          class="search-input"
          placeholder="搜索菜品"
          value="{{searchValue}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
          confirm-type="search"
        ></input>
        <text class="cancel-text" wx:if="{{searchValue}}" bindtap="clearSearch">取消</text>
      </view>

      <!-- 排序按钮 -->
      <view class="sort-button" catchtap="toggleSortMenu">
        <text>{{sortType === 'random' ? '随机排列' : sortType === 'hot' ? '按热门排序' : '按时间排序'}}</text>
        <view class="sort-arrow {{sortVisible ? 'open' : ''}}"></view>

        <!-- 排序菜单 -->
        <view class="sort-menu {{sortVisible ? 'visible' : ''}}" catchtap="preventBubble">
          <view class="sort-item {{sortType === 'random' ? 'active' : ''}}" bindtap="selectSort" data-type="random">
            <text class="sort-text">随机</text>
            <text class="sort-desc">随机排列</text>
          </view>
          <view class="sort-item {{sortType === 'hot' ? 'active' : ''}}" bindtap="selectSort" data-type="hot">
            <text class="sort-text">热门</text>
            <text class="sort-desc">按添加人数</text>
          </view>
          <view class="sort-item {{sortType === 'time' ? 'active' : ''}}" bindtap="selectSort" data-type="time">
            <text class="sort-text">最新</text>
            <text class="sort-desc">按更新时间</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 遮罩层，当排序菜单打开时显示 -->
  <view class="mask" wx:if="{{sortVisible}}" bindtap="onTap"></view>

  <!-- 可滚动区域 -->
  <scroll-view
    class="scrollable-content"
    scroll-y="true"
    refresher-enabled="true"
    refresher-triggered="{{refreshing}}"
    refresher-threshold="80"
    bindrefresherrefresh="onScrollRefresh"
    bindscrolltolower="onLoadMore"
  >
    <!-- 下拉刷新动画由scroll-view内置管理，无需单独添加 -->

    <!-- 菜品列表 -->
    <view class="dish-grid">
      <!-- 菜品卡片 -->
      <view class="dish-card slide-in" wx:for="{{dishes}}" wx:key="id" bindtap="navigateToDishDetail" data-id="{{item.id}}">
        <!-- 菜品图片 -->
        <smart-image
          custom-class="dish-image"
          src="{{item.image || defaultImages.DISH}}"
          mode="aspectFill"
          width="100%"
          height="240rpx"
          border-radius="8"
          lazy="{{true}}"
          lazy-delay="{{300}}"
        />

        <!-- 菜品信息 -->
        <view class="dish-info">
          <view class="dish-name-container">
            <text class="dish-name">{{item.name}}</text>

            <!-- 添加按钮或已添加标记 -->
            <view
              class="add-button {{item.isAdded ? 'added' : ''}}"
              catchtap="{{!item.isAdded ? 'handleAddDish' : ''}}"
              data-id="{{item.id}}"
            >
              <image wx:if="{{!item.isAdded}}" class="add-icon" src="/static/images/icons/plus.png" mode="aspectFit"></image>
              <image wx:else class="add-icon" src="/static/images/icons/check1.png" mode="aspectFit"></image>
            </view>
          </view>

          <!-- 用户信息 -->
          <view class="user-info">
            <view class="avatar-area">
              <smart-image
                custom-class="user-avatar"
                src="{{item.user.avatarUrl || defaultImages.USER_AVATAR}}"
                mode="aspectFill"
                width="32rpx"
                height="32rpx"
                border-radius="16"
              />
            </view>
            <text class="user-name">{{item.user.nickName}}</text>
            <text class="added-count">{{item.addedCount}}人添加</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && dishes.length > 0}}">
      没有更多数据了
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{dishes.length === 0 && !loading}}">
      <image class="empty-icon" src="/static/images/empty-state.png" mode="aspectFit"></image>
      <text class="empty-text">暂无数据</text>
    </view>
  </scroll-view>

  <!-- 分类选择对话框 -->
  <modal-dialog
    visible="{{showCategoryDialog}}"
    title="选择分类"
    showCancel="{{true}}"
    bind:close="closeDialog"
    bind:cancel="closeDialog"
    bind:confirm="confirmAddToCategory"
  >
    <view class="category-list">
      <view wx:if="{{categories.length === 0}}" class="empty-categories">
        <text>暂无分类数据，请先创建分类</text>
      </view>
      <view
        class="category-item {{selectedCategoryId === item.id ? 'selected' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        bindtap="selectCategory"
        data-id="{{item.id}}"
      >
        <view class="category-content">
          <view class="category-left">
            <image class="category-icon" src="{{item.icon || '/static/images/icons/class/food.png'}}" mode="aspectFit"></image>
            <text class="category-name">{{item.name}}</text>
          </view>
          <text class="category-count">{{item.dishCount || 0}}个菜品</text>
        </view>
      </view>
    </view>
  </modal-dialog>
</view>