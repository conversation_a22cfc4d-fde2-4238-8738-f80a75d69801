"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 交易记录模型
 * 存储用户大米交易记录
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 交易记录模型类
class Transaction extends sequelize_1.Model {
}
// 初始化交易记录模型
Transaction.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '交易ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    amount: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '数量',
    },
    title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        comment: '交易标题',
    },
    type: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '类型(in:收入,out:支出)',
        validate: {
            isIn: [['in', 'out']],
        },
    },
    time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '交易时间',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Transaction',
    tableName: 'transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_time',
            fields: ['time'],
        },
        {
            name: 'idx_type',
            fields: ['type'],
        },
    ],
});
exports.default = Transaction;
//# sourceMappingURL=Transaction.js.map