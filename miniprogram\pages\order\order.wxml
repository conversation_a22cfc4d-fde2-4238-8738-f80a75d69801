<view class="container">
  <!-- 使用自定义导航栏组件 -->
  <custom-navbar
    title="订单"
    showBack="{{false}}"
    navBgStyle="{{navBgStyle}}"
    useShadow="{{true}}"
  />

  <!-- 订单类型 Tab -->
  <view class="order-tabs" style="top: {{statusBarHeight + navHeight}}px">
    <view class="tab {{activeTab === 'kitchen' ? 'active' : ''}}" bindtap="switchTab" data-tab="kitchen">
      <text>厨房订单</text>
      <view class="tab-indicator" wx:if="{{activeTab === 'kitchen'}}"></view>
    </view>
    <view class="tab {{activeTab === 'mine' ? 'active' : ''}}" bindtap="switchTab" data-tab="mine">
      <text>我的订单</text>
      <view class="tab-indicator" wx:if="{{activeTab === 'mine'}}"></view>
    </view>
  </view>

  <!-- 订单内容区域 -->
  <view class="order-content">
    <!-- 订单状态筛选 -->
    <scroll-view scroll-x class="status-tabs" style="top: {{statusBarHeight + navHeight + 50}}px" show-scrollbar="{{false}}">
      <view class="status-tabs-inner">
        <view class="status-tab {{status === 'all' ? 'active' : ''}}" bindtap="switchStatus" data-status="all">全部</view>
        <view class="status-tab {{status === 'pending' ? 'active' : ''}}" bindtap="switchStatus" data-status="pending">待接单</view>
        <view class="status-tab {{status === 'accepted' ? 'active' : ''}}" bindtap="switchStatus" data-status="accepted">已接单</view>
        <view class="status-tab {{status === 'cooking' ? 'active' : ''}}" bindtap="switchStatus" data-status="cooking">烹饪中</view>
        <view class="status-tab {{status === 'completed' ? 'active' : ''}}" bindtap="switchStatus" data-status="completed">已完成</view>
        <view class="status-tab {{status === 'cancelled' ? 'active' : ''}}" bindtap="switchStatus" data-status="cancelled">已取消</view>
      </view>
    </scroll-view>

    <!-- 列表区域 -->
    <scroll-view
      class="order-list-scroll"
      scroll-y="{{true}}"
      refresher-enabled="{{true}}"
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onPullDownRefresh"
      bindscrolltolower="onLoadMore"
      show-scrollbar="{{false}}"
      enhanced="{{true}}"
      bounces="{{false}}">

      <!-- 订单列表容器 -->
      <view class="order-list-container {{smoothUpdating ? 'smooth-updating' : ''}}">
        <block wx:if="{{orderList.length > 0}}">
          <!-- 厨房订单卡片样式 -->
          <view wx:if="{{activeTab === 'kitchen'}}" class="order-card slide-in {{smoothUpdating ? 'updating' : ''}}" wx:for="{{orderList}}" wx:key="orderId" bindtap="goToOrderDetail" data-order-id="{{item.orderId}}">
            <!-- 订单头部：用户信息 -->
            <view class="kitchen-order-header">
              <view class="avatar-area">
              <smart-image
                custom-class="shop-avatar"
                src="{{item.userInfo.avatarUrl || defaultImages.USER_AVATAR}}"
                mode="aspectFill"
                width="60rpx"
                height="60rpx"
                border-radius="30"
              />
              </view>
              <view class="shop-info">
                <view class="shop-name">{{item.userInfo.nickName || '用户'}}</view>
                <view class="order-brief">菜品×{{item.itemCount}} {{item.totalPrice}}大米</view>
              </view>
              <view class="order-status-tag {{item.status}}">
                {{item.status === 'pending' ? '待接单' :
                  item.status === 'accepted' ? '已接单' :
                  item.status === 'cooking' ? '烹饪中' :
                  item.status === 'completed' ? '已完成' : '已取消'}}
              </view>
            </view>

            <!-- 订单菜品展示 -->
            <view class="divider"></view>
            <scroll-view scroll-x class="dishes-scroll" show-scrollbar="{{false}}">
              <view class="dishes-container">
                <!-- 显示真实的菜品数据 -->
                <view class="dish-item" wx:for="{{item.dishes || []}}" wx:key="dishId" wx:for-item="dish">
                  <smart-image
                    custom-class="dish-image"
                    src="{{dish.image || defaultImages.DISH}}"
                    mode="aspectFill"
                    width="150rpx"
                    height="150rpx"
                    border-radius="8"
                    lazy="{{true}}"
                    lazy-delay="{{200}}"
                  />
                  <view class="dish-name">{{dish.name}}</view>
                </view>
                <!-- 如果没有菜品数据时显示提示 -->
                <block wx:if="{{!item.dishes || item.dishes.length === 0}}">
                  <view class="empty-dish-hint">暂无菜品信息</view>
                </block>
              </view>
            </scroll-view>

            <!-- 订单底部 -->
            <view class="divider"></view>
            <view class="order-footer">
              <view class="order-time">{{item.createTime}}</view>
              <view class="order-actions">
                <!-- 待接单状态的厨房订单 -->
                <block wx:if="{{item.status === 'pending'}}">
                  <button class="btn-cancel" catchtap="onRejectOrder" data-order-id="{{item.orderId}}">拒绝</button>
                  <button class="btn-accept" catchtap="onAcceptOrder" data-order-id="{{item.orderId}}">接单</button>
                </block>

                <!-- 已接单状态的厨房订单 -->
                <block wx:if="{{item.status === 'accepted'}}">
                  <button class="btn-cancel" catchtap="onCancelAcceptedOrder" data-order-id="{{item.orderId}}">取消</button>
                  <button class="btn-accept" catchtap="onStartCooking" data-order-id="{{item.orderId}}">烹饪</button>
                </block>

                <!-- 烹饪中状态的厨房订单 -->
                <block wx:if="{{item.status === 'cooking'}}">
                  <button class="btn-cooking-cancel" catchtap="onCancelCooking" data-order-id="{{item.orderId}}">取消</button>
                  <button class="btn-complete" catchtap="onCompleteOrder" data-order-id="{{item.orderId}}">完成</button>
                </block>
              </view>
            </view>
          </view>

          <!-- 我的订单卡片样式 -->
          <view wx:if="{{activeTab === 'mine'}}" class="order-card slide-in {{smoothUpdating ? 'updating' : ''}}" wx:for="{{orderList}}" wx:key="orderId" bindtap="goToOrderDetail" data-order-id="{{item.orderId}}">
            <!-- 订单头部：店铺信息 -->
            <view class="my-order-header">
              <view class="avatar-area">
              <smart-image
                custom-class="my-avatar"
                src="{{item.shopInfo.avatar || defaultImages.KITCHEN_AVATAR}}"
                mode="aspectFill"
                width="60rpx"
                height="60rpx"
                border-radius="30"
              />
              </view>
              <view class="my-info">
                <view class="my-name">{{item.shopInfo.name || '店铺'}}</view>
                <view class="order-brief">菜品×{{item.itemCount}} {{item.totalPrice}}大米</view>
              </view>
              <view class="order-status-tag {{item.status}}">
                {{item.status === 'pending' ? '待接单' :
                  item.status === 'accepted' ? '已接单' :
                  item.status === 'cooking' ? '烹饪中' :
                  item.status === 'completed' ? '已完成' : '已取消'}}
              </view>
            </view>

            <!-- 订单菜品展示 -->
            <view class="divider"></view>
            <scroll-view scroll-x class="dishes-scroll" show-scrollbar="{{false}}">
              <view class="dishes-container">
                <!-- 显示真实的菜品数据 -->
                <view class="dish-item" wx:for="{{item.dishes || []}}" wx:key="dishId" wx:for-item="dish">
                  <smart-image
                    custom-class="dish-image"
                    src="{{dish.image || defaultImages.DISH}}"
                    mode="aspectFill"
                    width="150rpx"
                    height="150rpx"
                    border-radius="8"
                    lazy="{{true}}"
                    lazy-delay="{{200}}"
                  />
                  <view class="dish-name">{{dish.name}}</view>
                </view>
                <!-- 如果没有菜品数据时显示提示 -->
                <block wx:if="{{!item.dishes || item.dishes.length === 0}}">
                  <view class="empty-dish-hint">暂无菜品信息</view>
                </block>
              </view>
            </scroll-view>

            <!-- 订单底部 -->
            <view class="divider"></view>
            <view class="order-footer">
              <view class="order-time">{{item.createTime}}</view>
              <view class="order-actions">
                <!-- 待接单状态的我的订单 -->
                <block wx:if="{{item.status === 'pending'}}">
                  <button class="btn-cancel" catchtap="onCancelOrder" data-order-id="{{item.orderId}}">取消</button>
                </block>
              </view>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{orderList.length === 0 && !refreshing}}">
          <text class="empty-text">暂无订单</text>
        </view>

        <!-- 加载完成提示 -->
        <view class="order-end-text" wx:if="{{orderList.length > 0 && !hasMore}}">
          —— 已经到底啦 ——
        </view>

        <!-- 底部安全间距 -->
        <view class="safe-bottom-area"></view>
      </view>
    </scroll-view>
  </view>

  <!-- 确认对话框 -->
  <confirm-dialog
    visible="{{showConfirmDialog}}"
    title="{{confirmDialogTitle}}"
    content="{{confirmDialogContent}}"
    confirmText="{{confirmDialogConfirmText}}"
    bind:cancel="onConfirmDialogCancel"
    bind:confirm="onConfirmDialogConfirm"
  />
</view>