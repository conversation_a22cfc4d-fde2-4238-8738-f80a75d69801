/**
 * 系统设置种子数据
 * 创建默认的系统设置
 */
import { SystemSetting } from '../../models';
import logger from '../../utils/logger';

/**
 * 创建系统设置种子数据
 */
async function seedSystemSettings(): Promise<void> {
  try {
    logger.info('开始创建系统设置种子数据...');

    // 默认系统设置
    const defaultSettings = [
      {
        key: 'comment_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: '是否开启评论功能'
      },
      {
        key: 'site_description',
        value: '智能厨房管理系统',
        type: 'string' as const,
        description: '网站描述'
      },
      {
        key: 'contact_email',
        value: '<EMAIL>',
        type: 'string' as const,
        description: '联系邮箱'
      },
      {
        key: 'default_coins',
        value: '1000',
        type: 'number' as const,
        description: '新用户默认大米数量'
      },
      {
        key: 'daily_signin_coins',
        value: '10',
        type: 'number' as const,
        description: '每日签到奖励大米数量'
      },
      {
        key: 'ad_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: '是否开启广告功能'
      }
    ];

    // 检查是否已存在设置，如果存在则跳过
    for (const setting of defaultSettings) {
      const existingSetting = await SystemSetting.findOne({
        where: { key: setting.key }
      });

      if (existingSetting) {
        logger.info(`系统设置 ${setting.key} 已存在，跳过创建`);
        continue;
      }

      await SystemSetting.create(setting);
      logger.info(`创建系统设置: ${setting.key} = ${setting.value} (${setting.type})`);
    }

    logger.info(`系统设置种子数据创建完成，共处理 ${defaultSettings.length} 个设置`);
  } catch (error) {
    logger.error('创建系统设置种子数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本，则执行创建系统设置
if (require.main === module) {
  seedSystemSettings()
    .then(() => {
      logger.info('创建系统设置种子数据脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('创建系统设置种子数据脚本执行失败:', error);
      process.exit(1);
    });
}

export default seedSystemSettings;
