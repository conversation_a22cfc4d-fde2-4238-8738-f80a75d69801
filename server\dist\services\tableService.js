"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 桌号服务
 * 处理桌号相关的业务逻辑
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
/**
 * 获取桌号列表
 * @param userId 用户ID（可选，支持匿名访问）
 * @param kitchenId 厨房ID
 * @returns 桌号列表
 */
const getTableList = (userId, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 如果提供了用户ID，检查用户是否是厨房成员
    // 如果没有提供用户ID（匿名访问），则允许查看桌号列表（分享厨房模式）
    if (userId) {
        const member = yield models_1.KitchenMember.findOne({
            where: {
                user_id: userId,
                kitchen_id: kitchenId,
            },
        });
        if (!member) {
            // 用户不是厨房成员，但仍然允许查看桌号列表（分享厨房模式）
            console.log(`用户 ${userId} 不是厨房 ${kitchenId} 的成员，但允许查看桌号列表（分享厨房模式）`);
        }
    }
    // 获取桌号列表
    const tables = yield models_1.Table.findAll({
        where: { kitchen_id: kitchenId },
        order: [['sort', 'ASC']],
    });
    return tables.map(table => ({
        id: table.id,
        name: table.name,
        sort: table.sort,
    }));
});
/**
 * 添加桌号
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param name 桌号名称
 * @returns 添加结果
 */
const addTable = (userId, kitchenId, name) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 检查用户是否有权限添加桌号
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限添加桌号');
    }
    // 检查用户是否是会员（桌号管理是会员特权）
    if (kitchen.owner_id !== userId) {
        const membership = yield models_1.Membership.findOne({
            where: { user_id: userId },
        });
        const isMember = membership && membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;
        if (!isMember) {
            throw new error_1.BusinessError('桌号管理是会员特权，请先订阅会员');
        }
    }
    // 检查桌号数量是否已达到上限
    const tableCount = yield models_1.Table.count({
        where: { kitchen_id: kitchenId },
    });
    if (tableCount >= 50) {
        throw new error_1.BusinessError('每个厨房最多只能添加50个桌号');
    }
    // 检查桌号名称是否已存在
    const existingTable = yield models_1.Table.findOne({
        where: {
            kitchen_id: kitchenId,
            name,
        },
    });
    if (existingTable) {
        throw new error_1.BusinessError('桌号名称已存在');
    }
    // 获取最大排序值
    let maxSort = 0;
    try {
        const result = yield models_1.Table.max('sort', {
            where: { kitchen_id: kitchenId },
        });
        // 确保maxSort是数字类型
        if (result !== null && result !== undefined && typeof result === 'number') {
            maxSort = result;
        }
    }
    catch (error) {
        logger_1.default.error('获取最大排序值失败:', error);
        // 使用默认值0
    }
    // 创建桌号
    const table = yield models_1.Table.create({
        kitchen_id: kitchenId,
        name,
        sort: maxSort + 1,
    });
    return {
        id: table.id,
        name: table.name,
        sort: table.sort,
    };
});
/**
 * 更新桌号
 * @param userId 用户ID
 * @param tableId 桌号ID
 * @param kitchenId 厨房ID
 * @param name 桌号名称
 */
const updateTable = (userId, tableId, kitchenId, name) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 检查用户是否有权限更新桌号
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限更新桌号');
    }
    // 检查用户是否是会员（桌号管理是会员特权）
    if (kitchen.owner_id !== userId) {
        const membership = yield models_1.Membership.findOne({
            where: { user_id: userId },
        });
        const isMember = membership && membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;
        if (!isMember) {
            throw new error_1.BusinessError('桌号管理是会员特权，请先订阅会员');
        }
    }
    // 检查桌号是否存在
    const table = yield models_1.Table.findOne({
        where: {
            id: tableId,
            kitchen_id: kitchenId,
        },
    });
    if (!table) {
        throw new error_1.BusinessError('桌号不存在');
    }
    // 检查桌号名称是否已存在（排除当前桌号）
    if (name !== table.name) {
        const existingTable = yield models_1.Table.findOne({
            where: {
                kitchen_id: kitchenId,
                name,
                id: { [sequelize_1.Op.ne]: tableId },
            },
        });
        if (existingTable) {
            throw new error_1.BusinessError('桌号名称已存在');
        }
    }
    // 更新桌号
    yield table.update({ name });
});
/**
 * 删除桌号
 * @param userId 用户ID
 * @param tableId 桌号ID
 * @param kitchenId 厨房ID
 */
const deleteTable = (userId, tableId, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 检查用户是否有权限删除桌号
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限删除桌号');
    }
    // 检查用户是否是会员（桌号管理是会员特权）
    if (kitchen.owner_id !== userId) {
        const membership = yield models_1.Membership.findOne({
            where: { user_id: userId },
        });
        const isMember = membership && membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;
        if (!isMember) {
            throw new error_1.BusinessError('桌号管理是会员特权，请先订阅会员');
        }
    }
    // 检查桌号是否存在
    const table = yield models_1.Table.findOne({
        where: {
            id: tableId,
            kitchen_id: kitchenId,
        },
    });
    if (!table) {
        throw new error_1.BusinessError('桌号不存在');
    }
    // 删除桌号
    yield table.destroy();
});
/**
 * 桌号排序
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param tables 桌号排序数据
 */
const sortTables = (userId, kitchenId, tables) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 检查用户是否有权限排序桌号
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限排序桌号');
    }
    // 检查用户是否是会员（桌号管理是会员特权）
    if (kitchen.owner_id !== userId) {
        const membership = yield models_1.Membership.findOne({
            where: { user_id: userId },
        });
        const isMember = membership && membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;
        if (!isMember) {
            throw new error_1.BusinessError('桌号管理是会员特权，请先订阅会员');
        }
    }
    // 更新桌号排序
    const transaction = yield database_1.default.transaction();
    try {
        for (let i = 0; i < tables.length; i++) {
            yield models_1.Table.update({ sort: i }, {
                where: {
                    id: tables[i].id,
                    kitchen_id: kitchenId,
                },
                transaction,
            });
        }
        yield transaction.commit();
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
exports.default = {
    getTableList,
    addTable,
    updateTable,
    deleteTable,
    sortTables,
};
//# sourceMappingURL=tableService.js.map