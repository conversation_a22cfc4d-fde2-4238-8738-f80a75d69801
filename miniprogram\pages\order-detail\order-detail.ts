import { getOrderDetail, getPublicOrderDetail, cancelOrder, rejectOrder, acceptOrder, completeOrder, cancelCooking, startCooking, cancelAcceptedOrder } from '../../api/orderApi'
import { getCurrentKitchenBaseInfo } from '../../api/kitchenApi'
import { formatDateTime } from '../../utils/dateFormat'
import { DEFAULT_IMAGES } from '../../utils/constants'

interface OrderItem {
  dishId: string;
  name: string;
  image: string;
  price: number;
  count: number;
  tags: string[];
}

interface OrderDetail {
  orderId: string;
  createTime: string;
  status: string;
  totalPrice: number;
  tableNo: string;
  remark: string;
  cookingTime?: string;
  completedTime?: string;
  userInfo: {
    nickName: string;
    avatarUrl: string;
  };
  items: OrderItem[];
}

Page({
  data: {
    loading: true,
    orderId: '',
    orderType: 'mine', // mine-我的订单, kitchen-厨房订单
    fromSubmit: false, // 是否从下单成功页面跳转过来
    orderDetail: {
      orderId: '',
      createTime: '',
      status: '',
      totalPrice: 0,
      tableNo: '',
      remark: '',
      userInfo: {
        nickName: '',
        avatarUrl: ''
      },
      items: [] as OrderItem[]
    } as OrderDetail,
    isHistoryOrder: false, // 已完成或已取消的订单

    // 确认对话框
    showConfirmDialog: false,
    confirmDialogTitle: '',
    confirmDialogContent: '',
    confirmDialogConfirmText: '确定',
    confirmDialogAction: '', // 确认后执行的操作

    // 状态文本
    statusText: '',
    statusDesc: '',

    // 导航栏背景样式
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 默认使用第一个预设渐变色

    // 厨房信息
    currentKitchenId: '', // 当前选中的厨房ID

    // 时间显示文本
    cookingTimeText: '',
    completedTimeText: '',

    // 步骤样式类
    stepClasses: {
      pending: '',
      cooking: '',
      completed: ''
    },

    // 商品总数
    totalCount: 0,

    // 用户权限控制
    canManageOrder: false, // 是否可以管理订单（厨房主或下单用户）
    isOrderOwner: false,   // 是否是下单用户
    isKitchenOwner: false, // 是否是厨房主
    currentUserId: '',     // 当前用户ID
  },

  // 生命周期函数--监听页面加载
  onLoad(options: { orderId?: string, id?: string, orderType?: string, fromSubmit?: string }) {
    // 支持两种参数格式：orderId 和 id（用于分享链接）
    const orderId = options.orderId || options.id || '';
    const orderType = options.orderType || 'mine';
    const fromSubmit = options.fromSubmit === 'true';

    this.setData({
      orderId,
      orderType,
      fromSubmit,
      loading: true
    });

    // 加载背景设置
    this.loadBackgroundSettings();

    // 检查当前厨房ID
    this.checkCurrentKitchen();

    // 加载订单详情
    this.loadOrderDetail();

    // 注册主题变更监听器
    const app = getApp<IAppOption>();
    app.addThemeChangeListener(this.onThemeChanged.bind(this));
  },

  // 生命周期函数--监听页面显示
  onShow() {
    // 检查当前厨房ID是否变更
    this.checkCurrentKitchen();

    // 每次页面显示时重新加载订单详情，确保数据最新
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 检查当前选中的厨房
  checkCurrentKitchen() {
    const lastSelectedKitchen = wx.getStorageSync('last_selected_kitchen');

    // 如果当前厨房ID与存储中的不同，更新并重新加载数据
    if (lastSelectedKitchen && this.data.currentKitchenId !== lastSelectedKitchen) {
      this.setData({
        currentKitchenId: lastSelectedKitchen,
        loading: true
      });

      // 重新加载订单详情
      this.loadOrderDetail();
    } else if (!this.data.currentKitchenId && lastSelectedKitchen) {
      // 首次加载，设置当前厨房ID
      this.setData({
        currentKitchenId: lastSelectedKitchen
      });
    }
  },

  // 生命周期函数--页面卸载
  onUnload() {
    // 移除主题变更监听器
    const app = getApp<IAppOption>();
    app.removeThemeChangeListener(this.onThemeChanged.bind(this));
  },

  // 页面隐藏时的处理
  onHide() {
    // 页面隐藏时的处理，暂时保留空实现
  },

  // 主题变更处理函数
  onThemeChanged(settings: any) {
    // 更新页面数据
    this.setData({
      navBgStyle: settings.navBgStyle
    });
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';

    this.setData({
      navBgStyle
    });
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      // 优先使用公开访问API，这样分享链接就能正常访问了
      const res = await getPublicOrderDetail(this.data.orderId);

      if (res.error === 0) {
        const orderData = res.body;

        // 将后端返回的数据格式转换为前端期望的格式
        const orderDetail: OrderDetail = {
          orderId: orderData.id,           // id -> orderId
          createTime: formatDateTime(orderData.createdAt), // createdAt -> createTime 并格式化
          status: orderData.status,
          totalPrice: orderData.totalPrice,
          tableNo: orderData.tableNo,
          remark: orderData.remark,
          cookingTime: formatDateTime(orderData.cookingTime || orderData.cooking_time), // 支持两种字段名并格式化
          completedTime: formatDateTime(orderData.completedTime || orderData.completed_time), // 支持两种字段名并格式化
          userInfo: {
            nickName: orderData.userName,    // userName -> nickName
            avatarUrl: orderData.userAvatar || DEFAULT_IMAGES.USER_AVATAR  // 使用默认用户头像
          },
          items: orderData.items.map((item: any) => ({
            dishId: item.dishId,
            name: item.name,
            image: item.image,
            price: item.price,
            count: item.count,
            tags: item.tags || []
          }))
        };

        const isHistoryOrder = orderDetail.status === 'completed' || orderDetail.status === 'canceled';

        // 设置状态文本
        const statusText = this.getStatusText(orderDetail.status);
        const statusDesc = this.getStatusDesc(orderDetail.status);

        // 计算时间显示文本
        const cookingTimeText = this.getCookingTime(orderDetail.status, orderDetail.cookingTime);
        const completedTimeText = this.getCompletedTime(orderDetail.status, orderDetail.completedTime);

        // 计算步骤样式类
        const stepClasses = this.getStepClasses(orderDetail.status);

        // 计算商品总数
        const totalCount = this.getTotalCount(orderDetail.items);

        // 获取当前用户信息，判断权限
        const userInfo = wx.getStorageSync('userInfo');
        const currentUserId = userInfo ? userInfo.userId : '';
        
        // 判断是否是下单用户（确保数据类型一致）
        const isOrderOwner = currentUserId && String(currentUserId) === String(orderData.userId);
        
        // 判断是否是厨房主（通过API获取厨房信息）
        let isKitchenOwner = false;
        if (currentUserId && orderData.kitchenId) {
          try {
            const kitchenRes = await getCurrentKitchenBaseInfo(orderData.kitchenId);
            if (kitchenRes.error === 0 && kitchenRes.body) {
              // 使用owner.id而不是owner字段，确保数据类型一致
              const kitchenOwnerId = kitchenRes.body.owner && kitchenRes.body.owner.id;
              isKitchenOwner = kitchenOwnerId && String(currentUserId) === String(kitchenOwnerId);
            }
          } catch (error) {
            console.log('获取厨房信息失败，默认非厨房主:', error);
            isKitchenOwner = false;
          }
        }
        
        // 判断是否可以管理订单
        const canManageOrder = isOrderOwner || isKitchenOwner;

        this.setData({
          orderDetail,
          isHistoryOrder,
          statusText,
          statusDesc,
          cookingTimeText,
          completedTimeText,
          stepClasses,
          totalCount,
          currentUserId,
          isOrderOwner,
          isKitchenOwner,
          canManageOrder,
          loading: false
        });
      } else {
        // 根据错误码提供更友好的提示
        let errorMessage = '订单不存在或已被删除';
        if (res.error === 401) {
          errorMessage = '请先登录查看订单详情';
        } else if (res.message) {
          errorMessage = res.message;
        }
        
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
        
        this.setData({
          loading: false
        });
      }
    } catch (err) {
      console.error('加载订单详情失败', err);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
      
      this.setData({
        loading: false
      });
    }
  },

  // 获取状态对应的文本
  getStatusText(status: string) {
    switch (status) {
      case 'pending':
        return '等待接单';
      case 'accepted':
        return '已接单';
      case 'cooking':
        return '烹饪中';
      case 'completed':
        return '已完成';
      case 'canceled':
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  },

  // 获取状态对应的描述
  getStatusDesc(status: string) {
    switch (status) {
      case 'pending':
        return '厨师正在查看您的订单，请耐心等待';
      case 'accepted':
        return '厨师已接单，即将开始烹饪';
      case 'cooking':
        return '厨师正在为您准备美食，请耐心等待';
      case 'completed':
        return '您的美食已完成，请享用';
      case 'canceled':
      case 'cancelled':
        return '订单已取消';
      default:
        return '';
    }
  },

  // 计算总商品数量
  getTotalCount(items: OrderItem[]) {
    if (!items || items.length === 0) {
      return 0;
    }
    return items.reduce((total, item: OrderItem) => total + item.count, 0);
  },

  // 获取步骤样式类
  getStepClasses(status: string) {
    const stepClasses = {
      pending: '',
      cooking: '',
      completed: ''
    };

    if (status === 'canceled' || status === 'cancelled') {
      stepClasses.pending = 'completed';
      return stepClasses;
    }

    // 下单成功步骤始终是完成状态
    stepClasses.pending = 'completed';

    // 烹饪步骤：烹饪中或已完成时为激活状态
    if (status === 'cooking' || status === 'completed') {
      stepClasses.cooking = 'active';
    }

    // 完成步骤：只有已完成时为激活状态
    if (status === 'completed') {
      stepClasses.completed = 'active';
    }

    return stepClasses;
  },

  // 获取烹饪时间
  getCookingTime(status: string, cookingTime?: string) {
    // 如果还是待接单状态
    if (status === 'pending') {
      return '等待接单';
    }

    // 如果是已接单但还未开始烹饪
    if (status === 'accepted') {
      return '已接单，准备烹饪';
    }

    // 对于烹饪中和已完成状态，优先使用实际记录的烹饪时间
    if ((status === 'cooking' || status === 'completed') && cookingTime && cookingTime !== '暂无') {
      return cookingTime;
    }

    // 如果没有实际时间，根据状态显示相应信息
    if (status === 'cooking') {
      return '正在烹饪中';
    }

    if (status === 'completed') {
      return '已完成烹饪';
    }

    return '暂无';
  },

  // 获取完成时间
  getCompletedTime(status: string, completedTime?: string) {
    // 如果不是已完成状态
    if (status !== 'completed') {
      return '未完成';
    }

    // 优先使用实际记录的完成时间
    if (completedTime && completedTime !== '暂无') {
      return completedTime;
    }

    return '已完成';
  },

  // 自定义返回处理
  onCustomBack() {
    // 恢复原始厨房ID（如果是分享厨房订单）
    this.restoreOriginalKitchen();

    // 检查页面栈深度，如果只有1页说明是通过分享链接直接进入的
    const pages = getCurrentPages();
    
    if (pages.length <= 1) {
      // 分享链接进入，跳转到订单页面
      wx.switchTab({
        url: '/pages/order/order'
      });
      return;
    }
    
    // 如果是从下单页面进入的订单详情，返回餐厅页面
    if (this.data.fromSubmit) {
      wx.switchTab({
        url: '/pages/restaurant/restaurant'
      });
    } else {
      // 其他情况使用正常的返回逻辑
      wx.navigateBack({
        delta: 1,
        fail: () => {
          // 如果返回失败（比如没有上一页），则跳转到订单页面
          wx.switchTab({
            url: '/pages/order/order'
          });
        }
      });
    }
  },

  // 恢复原来的厨房ID（分享厨房下单后）
  restoreOriginalKitchen() {
    const isSharedKitchenOrder = wx.getStorageSync('is_shared_kitchen_order');
    if (isSharedKitchenOrder) {
      console.log('分享厨房订单详情返回，恢复原来的厨房ID');
    
      // 恢复原来的厨房ID
      const originalKitchenId = wx.getStorageSync('original_kitchen_id');
      if (originalKitchenId) {
        wx.setStorageSync('last_selected_kitchen', originalKitchenId);
        console.log('恢复厨房ID:', originalKitchenId);
      } else {
        // 如果没有原来的厨房ID，清除当前的
        wx.removeStorageSync('last_selected_kitchen');
        console.log('清除厨房ID，因为没有原始厨房');
      }

      // 清除临时标记
      wx.removeStorageSync('is_shared_kitchen_order');
      wx.removeStorageSync('original_kitchen_id');
    }
  },

  // 取消订单
  onCancelOrder() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消订单',
      confirmDialogContent: '确定要取消该订单吗？',
      confirmDialogConfirmText: '确定',
      confirmDialogAction: 'cancelOrder'
    });
  },

  // 拒绝订单
  onRejectOrder() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '拒绝订单',
      confirmDialogContent: '确定要拒绝该订单吗？',
      confirmDialogConfirmText: '确定拒绝',
      confirmDialogAction: 'rejectOrder'
    });
  },

  // 接单
  onAcceptOrder() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '接单确认',
      confirmDialogContent: '接单后将开始烹饪，确定接单吗？',
      confirmDialogConfirmText: '确定接单',
      confirmDialogAction: 'acceptOrder'
    });
  },

  // 取消烹饪
  onCancelCooking() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消烹饪',
      confirmDialogContent: '确定要取消烹饪吗？订单将回到待接单状态。',
      confirmDialogConfirmText: '确定取消',
      confirmDialogAction: 'cancelCooking'
    });
  },

  // 取消已接单订单
  onCancelAcceptedOrder() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消接单',
      confirmDialogContent: '确定要取消接单吗？订单将回到待接单状态。',
      confirmDialogConfirmText: '确定取消',
      confirmDialogAction: 'cancelAcceptedOrder'
    });
  },

  // 开始烹饪
  onStartCooking() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '开始烹饪',
      confirmDialogContent: '确定要开始烹饪吗？',
      confirmDialogConfirmText: '开始烹饪',
      confirmDialogAction: 'startCooking'
    });
  },

  // 完成订单
  onCompleteOrder() {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '完成订单',
      confirmDialogContent: '确认该订单已完成吗？',
      confirmDialogConfirmText: '确认完成',
      confirmDialogAction: 'completeOrder'
    });
  },

  // 确认对话框取消
  onConfirmDialogCancel() {
    this.setData({
      showConfirmDialog: false
    });
  },

  // 确认对话框确认
  async onConfirmDialogConfirm() {
    const orderId = this.data.orderId;
    const action = this.data.confirmDialogAction;

    // 关闭对话框
    this.setData({
      showConfirmDialog: false
    });

    try {
      let res;
      let successMsg = '操作成功';

      switch (action) {
        case 'cancelOrder':
          res = await cancelOrder(orderId, this.data.currentKitchenId);
          successMsg = '订单已取消';
          break;
        case 'rejectOrder':
          res = await rejectOrder(orderId, this.data.currentKitchenId);
          successMsg = '订单已拒绝';
          break;
        case 'acceptOrder':
          res = await acceptOrder(orderId, this.data.currentKitchenId);
          successMsg = '已接单';
          break;
        case 'completeOrder':
          res = await completeOrder(orderId, this.data.currentKitchenId);
          successMsg = '订单已完成';
          break;
        case 'cancelCooking':
          res = await cancelCooking(orderId, this.data.currentKitchenId);
          successMsg = '已取消烹饪';
          break;
        case 'startCooking':
          res = await startCooking(orderId, this.data.currentKitchenId);
          successMsg = '已开始烹饪';
          break;
        case 'cancelAcceptedOrder':
          res = await cancelAcceptedOrder(orderId, this.data.currentKitchenId);
          successMsg = '已取消接单';
          break;
        default:
          return;
      }

      if (res.error === 0) {
        wx.showToast({
          title: successMsg,
          icon: 'success'
        });
        // 刷新订单详情
        this.loadOrderDetail();
      } else {
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('操作失败', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  // 分享订单
  onShareAppMessage() {
    return {
      title: `我在川味小馆点了一份美食，快来看看吧！`,
      path: `/pages/order-detail/order-detail?orderId=${this.data.orderId}&orderType=kitchen`,
      imageUrl: this.data.orderDetail.items && this.data.orderDetail.items.length > 0
        ? this.data.orderDetail.items[0].image
        : '/static/images/share-default.jpg'
    };
  }
})
