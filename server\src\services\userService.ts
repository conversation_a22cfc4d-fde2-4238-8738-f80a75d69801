/**
 * 用户服务
 * 处理用户相关的业务逻辑
 */
import { Op } from 'sequelize';
import moment from 'moment';
import config from '../config/config';
import logger from '../utils/logger';
import { BusinessError } from '../middlewares/error';
import { User, Membership, Transaction, SignIn, BackgroundSetting, SearchHistory, AdView } from '../models';
import { getCurrentDate, parseDate, processImageUrl, getRandomUserAvatar, generateUserId } from '../utils/helpers';
import tokenUtils from '../utils/tokenUtils';
import wechatService from './wechatService';
import systemSettingService from './systemSettingService';
import { convertToRelativePath } from '../utils/urlManager';

/**
 * 用户登录
 * @param code 微信登录code
 * @returns 用户信息和token
 */
const login = async (code: string): Promise<any> => {
  try {
    logger.info(`尝试用户登录，code: ${code.substring(0, 5)}***`);
    
    if (!code || code.trim() === '') {
      logger.warn('登录失败: code为空');
      throw new BusinessError('登录参数错误，code不能为空');
    }
    
    // 验证微信配置
    if (!wechatService.validateWechatConfig()) {
      throw new BusinessError('微信配置错误，请联系管理员');
    }
    
    // 调用微信API获取用户openId
    const wechatUserInfo = await wechatService.getWechatUserInfo(code);
    const openId = wechatUserInfo.openId;
    
    logger.info(`从微信API获取到openId: ${openId.substring(0, 8)}***`);
    
    // 查找或创建用户
    try {
      let user = await User.findOne({ where: { open_id: openId } });
      logger.info(`查找用户结果: ${user ? '已存在' : '新用户'}`);

      if (!user) {
        // 获取最后一个用户ID生成新的用户ID
        const lastUser = await User.findOne({
          order: [['id', 'DESC']],
          attributes: ['id']
        });
        
        const newUserId = generateUserId(lastUser?.id);
        logger.info(`生成新用户ID: ${newUserId}`);
        
        // 获取系统设置的默认大米数量
        const defaultCoins = await systemSettingService.getDefaultCoins();
        logger.info(`从系统设置获取默认大米数量: ${defaultCoins}`);

        // 创建新用户
        try {
          user = await User.create({
            id: newUserId, // 显式设置用户ID
            open_id: openId,
            nick_name: `用户${Math.floor(Math.random() * 10000)}`,
            avatar_url: getRandomUserAvatar(),
            gender: 0,
            coins: defaultCoins, // 使用系统设置的默认大米数量
          });
          logger.info(`创建新用户成功，ID: ${user.id}，初始大米: ${defaultCoins}`);

          // 创建会员记录
          await Membership.create({
            user_id: user.id,
            is_member: false,
          });
          logger.info(`创建会员记录成功，用户ID: ${user.id}`);

          // 创建背景设置
          await BackgroundSetting.create({
            user_id: user.id,
          });
          logger.info(`创建背景设置成功，用户ID: ${user.id}`);

          // 记录初始大米交易
          await Transaction.create({
            user_id: user.id,
            amount: defaultCoins,
            title: '新用户注册奖励',
            type: 'in',
            time: new Date(),
          });
          logger.info(`记录初始交易成功，用户ID: ${user.id}，金额: ${defaultCoins}`);
        } catch (createErr) {
          logger.error(`创建用户相关记录失败: ${createErr instanceof Error ? createErr.message : '未知错误'}`, createErr);
          throw new BusinessError('创建用户失败，请稍后再试');
        }
      }

      // 生成token
      const token = tokenUtils.generateToken(user);
      user.token = token;
      await user.save();
      logger.info(`用户登录成功，ID: ${user.id}, ID类型: ${typeof user.id}`);

      // 确保userId是字符串类型
      const userIdStr = String(user.id);

      return {
        token,
        userId: userIdStr, // 确保返回的是字符串类型
        userInfo: {
          id: user.id,
          nickName: user.nick_name,
          avatarUrl: processImageUrl(user.avatar_url, 'user-avatar'),
          gender: user.gender,
          coins: user.coins,
        }
      };
    } catch (dbErr) {
      logger.error(`数据库操作失败: ${dbErr instanceof Error ? dbErr.message : '未知错误'}`, dbErr);
      throw new BusinessError('数据库操作失败，请稍后重试');
    }
  } catch (error) {
    if (error instanceof BusinessError) {
      // 业务错误直接抛出
      throw error;
    }
    // 其他错误记录详细日志
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    const errorStack = error instanceof Error && error.stack ? error.stack : '无堆栈信息';
    logger.error(`用户登录失败: ${errorMessage}\n${errorStack}`);
    throw new BusinessError('登录失败，请稍后重试');
  }
};

/**
 * 获取用户信息
 * @param userId 用户ID
 * @returns 用户信息
 */
const getUserInfo = async (userId: number): Promise<any> => {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new BusinessError('用户不存在');
  }

  return {
    userId: user.id.toString(), // 前端期望userId字段，并确保是字符串类型
    nickName: user.nick_name,
    avatarUrl: processImageUrl(user.avatar_url, 'user-avatar'),
    gender: user.gender,
    coins: user.coins,
    likes: user.likes,
    dishes: user.dishes,
    kitchens: user.kitchens,
    openid: user.open_id, // 添加openid字段用于支付
  };
};

/**
 * 更新用户信息
 * @param userId 用户ID
 * @param userInfo 用户信息
 */
const updateUserInfo = async (userId: number, userInfo: any): Promise<void> => {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new BusinessError('用户不存在');
  }

  // 转换头像URL为相对路径
  if (userInfo.avatar_url) {
    userInfo.avatar_url = convertToRelativePath(userInfo.avatar_url);
  }

  // 如果更新头像，需要删除旧的头像文件
  if (userInfo.avatar_url !== undefined && user.avatar_url && user.avatar_url !== userInfo.avatar_url) {
    try {
      // 提取旧头像的文件名
      const oldAvatarUrl = user.avatar_url;
      const oldFilename = oldAvatarUrl.split('/').pop();
      
      if (oldFilename && !oldAvatarUrl.includes('default')) {
        // 导入deleteFile函数
        const { deleteFile } = require('../middlewares/upload');
        deleteFile(oldFilename, 'avatar');
        logger.info(`已删除旧用户头像: ${oldFilename}`);
      }
    } catch (error) {
      logger.error('删除旧用户头像失败:', error);
      // 不抛出错误，继续更新操作
    }
  }

  // 只允许更新特定字段
  const allowedFields = ['nick_name', 'avatar_url', 'gender'];
  const updateData: any = {};

  for (const field of allowedFields) {
    if (userInfo[field] !== undefined) {
      updateData[field] = userInfo[field];
    }
  }

  await user.update(updateData);
};

/**
 * 获取搜索历史
 * @param userId 用户ID
 * @returns 搜索历史列表
 */
const getSearchHistory = async (userId: number): Promise<any[]> => {
  const histories = await SearchHistory.findAll({
    where: { user_id: userId },
    order: [['created_at', 'DESC']],
    limit: 10,
  });

  return histories.map(h => h.keyword);
};

/**
 * 添加搜索历史
 * @param userId 用户ID
 * @param keyword 搜索关键词
 */
const addSearchHistory = async (userId: number, keyword: string): Promise<void> => {
  // 先删除相同关键词的历史
  await SearchHistory.destroy({
    where: {
      user_id: userId,
      keyword,
    },
  });

  // 添加新的搜索历史
  await SearchHistory.create({
    user_id: userId,
    keyword,
  });

  // 限制历史记录数量
  const count = await SearchHistory.count({
    where: { user_id: userId },
  });

  if (count > 10) {
    const oldestHistories = await SearchHistory.findAll({
      where: { user_id: userId },
      order: [['created_at', 'ASC']],
      limit: count - 10,
    });

    await SearchHistory.destroy({
      where: {
        id: {
          [Op.in]: oldestHistories.map(h => h.id),
        },
      },
    });
  }
};

/**
 * 清除搜索历史
 * @param userId 用户ID
 */
const clearSearchHistory = async (userId: number): Promise<void> => {
  await SearchHistory.destroy({
    where: { user_id: userId },
  });
};

/**
 * 获取用户背景设置
 * @param userId 用户ID
 * @returns 背景设置
 */
const getUserBackgroundSettings = async (userId: number): Promise<any> => {
  let settings = await BackgroundSetting.findOne({
    where: { user_id: userId },
  });

  if (!settings) {
    settings = await BackgroundSetting.create({
      user_id: userId,
    });
  }

  return {
    shopBg: settings.shop_bg,
    navBgStyle: settings.nav_bg_style,
    navBgIndex: settings.nav_bg_index,
  };
};

/**
 * 更新用户背景设置
 * @param userId 用户ID
 * @param settings 背景设置
 * @returns 更新后的背景设置
 */
const updateUserBackgroundSettings = async (userId: number, settings: any): Promise<any> => {
  // 检查用户是否是会员
  const membership = await Membership.findOne({
    where: { user_id: userId },
  });

  const isMember = membership && membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;

  if (!isMember && settings.shopBg) {
    throw new BusinessError('非会员用户不能设置自定义背景');
  }

  // 转换背景图片URL为相对路径
  if (settings.shopBg) {
    settings.shopBg = convertToRelativePath(settings.shopBg);
  }

  let bgSettings = await BackgroundSetting.findOne({
    where: { user_id: userId },
  });

  if (!bgSettings) {
    bgSettings = await BackgroundSetting.create({
      user_id: userId,
    });
  }

  // 如果更新背景图，需要删除旧的背景图文件
  if (settings.shopBg !== undefined && bgSettings.shop_bg && bgSettings.shop_bg !== settings.shopBg) {
    try {
      // 提取旧背景图的文件名
      const oldBackgroundUrl = bgSettings.shop_bg;
      const oldFilename = oldBackgroundUrl.split('/').pop();
      
      if (oldFilename && !oldBackgroundUrl.includes('default')) {
        // 导入deleteFile函数
        const { deleteFile } = require('../middlewares/upload');
        deleteFile(oldFilename, 'background');
        logger.info(`已删除旧用户背景图: ${oldFilename}`);
      }
    } catch (error) {
      logger.error('删除旧用户背景图失败:', error);
      // 不抛出错误，继续更新操作
    }
  }

  const updateData: any = {};

  if (settings.shopBg !== undefined) {
    updateData.shop_bg = settings.shopBg;
  }

  if (settings.navBgStyle !== undefined) {
    updateData.nav_bg_style = settings.navBgStyle;
  }

  if (settings.navBgIndex !== undefined) {
    updateData.nav_bg_index = settings.navBgIndex;
  }

  await bgSettings.update(updateData);

  return {
    shopBg: bgSettings.shop_bg,
    navBgStyle: bgSettings.nav_bg_style,
    navBgIndex: bgSettings.nav_bg_index,
  };
};

/**
 * 获取用户大米信息
 * @param userId 用户ID
 * @returns 大米信息
 */
const getUserCoins = async (userId: number): Promise<any> => {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new BusinessError('用户不存在');
  }

  // 计算累计获得（所有收入交易的总和）
  const totalEarned = await Transaction.sum('amount', {
    where: {
      user_id: userId,
      type: 'in'
    }
  }) || 0;

  // 计算已经使用（所有支出交易的总和）
  const totalUsed = await Transaction.sum('amount', {
    where: {
      user_id: userId,
      type: 'out'
    }
  }) || 0;

  return {
    balance: user.coins,        // 当前余额
    total: totalEarned,         // 累计获得
    used: totalUsed            // 已经使用
  };
};

/**
 * 充值大米
 * @param userId 用户ID
 * @param amount 充值数量
 * @returns 充值结果
 */
const rechargeCoins = async (userId: number, amount: number): Promise<any> => {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new BusinessError('用户不存在');
  }

  // 计算新的大米数量
  const newCoins = user.coins + amount;

  // 更新用户大米数量
  await user.update({
    coins: newCoins,
  });

  // 记录交易
  await Transaction.create({
    user_id: userId,
    amount,
    title: '充值大米',
    type: 'in',
    time: new Date(),
  });

  logger.info(`用户 ${userId} 充值大米成功，充值数量: ${amount}，更新前: ${user.coins}，更新后: ${newCoins}`);

  return {
    coins: newCoins, // 返回更新后的大米数量
  };
};

/**
 * 消费大米
 * @param userId 用户ID
 * @param amount 消费数量
 * @param title 交易标题
 * @returns 消费结果
 */
const consumeCoins = async (userId: number, amount: number, title: string): Promise<any> => {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new BusinessError('用户不存在');
  }

  if (user.coins < amount) {
    throw new BusinessError('大米不足');
  }

  // 计算新的大米数量
  const newCoins = user.coins - amount;

  // 更新用户大米数量
  await user.update({
    coins: newCoins,
  });

  // 记录交易
  await Transaction.create({
    user_id: userId,
    amount,
    title,
    type: 'out',
    time: new Date(),
  });

  logger.info(`用户 ${userId} 消费大米成功，消费数量: ${amount}，更新前: ${user.coins}，更新后: ${newCoins}`);

  return {
    coins: newCoins, // 返回更新后的大米数量
  };
};

/**
 * 获取大米交易记录
 * @param userId 用户ID
 * @param type 交易类型
 * @returns 交易记录列表
 */
const getCoinRecords = async (userId: number, type: string = 'all'): Promise<any[]> => {
  const where: any = { user_id: userId };

  if (type !== 'all') {
    where.type = type;
  }

  const transactions = await Transaction.findAll({
    where,
    order: [['time', 'DESC']],
    limit: 50,
  });

  return transactions.map(t => ({
    id: t.id,
    amount: t.amount,
    title: t.title,
    type: t.type,
    time: moment(t.time).format('YYYY-MM-DD HH:mm:ss'),
  }));
};

/**
 * 获取用户签到数据
 * @param userId 用户ID
 * @returns 签到数据
 */
const getSignInData = async (userId: number): Promise<any> => {
  const today = getCurrentDate();

  // 获取当月签到记录
  const startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  const endOfMonth = moment().endOf('month').format('YYYY-MM-DD');

  const signIns = await SignIn.findAll({
    where: {
      user_id: userId,
      date: {
        [Op.between]: [startOfMonth, endOfMonth],
      },
    },
    order: [['date', 'ASC']],
  });

  // 检查今天是否已签到
  const todaySignIn = signIns.find(s => s.date.toString() === today);
  const signedToday = !!todaySignIn;

  // 计算连续签到天数
  let continuousDays = 0;
  let currentDate = moment();

  while (true) {
    const dateStr = currentDate.format('YYYY-MM-DD');
    const found = signIns.find(s => s.date.toString() === dateStr);

    if (!found) break;

    continuousDays++;
    currentDate = currentDate.subtract(1, 'days');
  }

  // 获取本月签到天数
  const monthDays = signIns.length;

  // 生成7天的签到数据（前3天，当天，后3天）
  const signInData = [];
  const todayMoment = moment();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  for (let i = -3; i <= 3; i++) {
    const date = moment(todayMoment).add(i, 'days');
    const dateStr = date.format('YYYY-MM-DD');
    const isToday = i === 0;
    
    // 检查这一天是否已签到
    const hasSigned = signIns.find(s => moment(s.date).format('YYYY-MM-DD') === dateStr);
    const signed = !!hasSigned;
    
    // 过去的日期如果没签到可以补签（今天除外，今天只能正常签到）
    const isPastDate = i < 0;
    const canSignIn = isPastDate && !signed;
    
    signInData.push({
      date: dateStr,
      day: date.date(),
      weekday: weekdays[date.day()],
      signed,
      canSignIn,
      isToday
    });
  }

  // 计算签到奖励总数（简化计算）
  const signInRewards = monthDays * 20 + Math.floor(continuousDays / 7) * 50;

  return {
    signInData,
    signInCount: monthDays,
    continuousSignIn: continuousDays,
    signInRewards,
    todaySigned: signedToday,
    // 保留原有字段以防其他地方使用
    signedToday,
    continuousDays,
    monthDays,
    signInDates: signIns.map(s => moment(s.date).format('YYYY-MM-DD')),
  };
};

/**
 * 补签
 * @param userId 用户ID
 * @param date 补签日期（YYYY-MM-DD格式）
 * @returns 补签结果
 */
const compensateSignIn = async (userId: number, date: string): Promise<any> => {
  const today = getCurrentDate();
  
  // 验证日期格式
  if (!moment(date, 'YYYY-MM-DD', true).isValid()) {
    throw new BusinessError('日期格式错误');
  }
  
  // 不能补签今天或未来的日期
  if (moment(date).isSameOrAfter(moment(today))) {
    throw new BusinessError('不能补签今天或未来的日期');
  }
  
  // 不能补签超过30天前的日期
  if (moment(date).isBefore(moment().subtract(30, 'days'))) {
    throw new BusinessError('不能补签超过30天前的日期');
  }

  // 检查这天是否已经签到过
  const existingSignIn = await SignIn.findOne({
    where: {
      user_id: userId,
      date: parseDate(date),
    },
  });

  if (existingSignIn) {
    throw new BusinessError('该日期已经签到过了');
  }

  // 补签奖励（使用系统设置的基础奖励，不计入连续签到）
  const reward = await systemSettingService.getDailySignInCoins();

  // 创建补签记录
  await SignIn.create({
    user_id: userId,
    date: parseDate(date),
    reward,
  });

  // 更新用户大米数量
  const user = await User.findByPk(userId);
  if (user) {
    await user.update({
      coins: user.coins + reward,
    });

    // 记录交易
    await Transaction.create({
      user_id: userId,
      amount: reward,
      title: `补签奖励 (${date})`,
      type: 'in',
      time: new Date(),
    });
  }

  // 获取更新后的签到数据
  const updatedSignInData = await getSignInData(userId);

  return {
    reward,
    date,
    coins: user ? user.coins : 0,
    // 返回更新后的完整签到数据
    ...updatedSignInData
  };
};

/**
 * 签到
 * @param userId 用户ID
 * @returns 签到结果
 */
const signIn = async (userId: number): Promise<any> => {
  const today = getCurrentDate();

  // 检查今天是否已签到
  const existingSignIn = await SignIn.findOne({
    where: {
      user_id: userId,
      date: today,
    },
  });

  if (existingSignIn) {
    throw new BusinessError('今天已经签到过了');
  }

  // 获取连续签到天数
  const signInData = await getSignInData(userId);
  const continuousDays = signInData.continuousDays;

  // 计算奖励（使用系统设置的基础奖励）
  let reward = await systemSettingService.getDailySignInCoins();

  // 连续签到奖励（保持原有的连续奖励逻辑）
  if (continuousDays + 1 === 7) {
    reward += config.signIn.continuousReward[7];
  } else if (continuousDays + 1 === 15) {
    reward += config.signIn.continuousReward[15];
  }

  // 创建签到记录
  await SignIn.create({
    user_id: userId,
    date: parseDate(today),
    reward,
  });

  // 更新用户大米数量
  const user = await User.findByPk(userId);
  if (user) {
    await user.update({
      coins: user.coins + reward,
    });

    // 记录交易
    await Transaction.create({
      user_id: userId,
      amount: reward,
      title: `签到奖励 (连续${continuousDays + 1}天)`,
      type: 'in',
      time: new Date(),
    });
  }

  // 获取更新后的签到数据
  const updatedSignInData = await getSignInData(userId);

  return {
    reward,
    continuousDays: continuousDays + 1,
    coins: user ? user.coins : 0,
    // 返回更新后的完整签到数据
    ...updatedSignInData
  };
};

/**
 * 观看广告
 * @param userId 用户ID
 * @returns 观看结果
 */
const watchAd = async (userId: number): Promise<any> => {
  const today = getCurrentDate();

  // 检查今天观看次数
  const adViews = await AdView.findAll({
    where: {
      user_id: userId,
      date: today,
    },
  });

  if (adViews.length >= config.adView.maxPerDay) {
    throw new BusinessError(`今日观看次数已达上限(${config.adView.maxPerDay}次)`);
  }

  const reward = config.adView.reward;

  // 创建观看记录
  await AdView.create({
    user_id: userId,
    reward,
    date: parseDate(today),
  });

  // 更新用户大米数量
  const user = await User.findByPk(userId);
  if (user) {
    await user.update({
      coins: user.coins + reward,
    });

    // 记录交易
    await Transaction.create({
      user_id: userId,
      amount: reward,
      title: '观看广告奖励',
      type: 'in',
      time: new Date(),
    });
  }

  return {
    reward,
    viewCount: adViews.length + 1,
    maxCount: config.adView.maxPerDay,
    coins: user ? user.coins : 0,
  };
};

/**
 * 获取会员状态
 * @param userId 用户ID
 * @returns 会员状态
 */
const getMembershipStatus = async (userId: number): Promise<any> => {
  let membership = await Membership.findOne({
    where: { user_id: userId },
  });

  if (!membership) {
    membership = await Membership.create({
      user_id: userId,
      is_member: false,
    });
  }

  const isValid = membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date;

  return {
    isMember: isValid,
    memberType: isValid ? membership.member_type : null,
    expireDate: isValid ? moment(membership.expire_date).format('YYYY-MM-DD') : null,
    prices: {
      monthly: config.membership.monthly.price,
      yearly: config.membership.yearly.price,
    },
  };
};

/**
 * 订阅会员
 * @param userId 用户ID
 * @param memberType 会员类型
 * @returns 订阅结果
 */
const subscribeMembership = async (userId: number, memberType: string): Promise<any> => {
  // 限制会员类型只能是monthly或yearly
  if (!['monthly', 'yearly'].includes(memberType)) {
    throw new BusinessError('无效的会员类型');
  }

  const user = await User.findByPk(userId);
  if (!user) {
    throw new BusinessError('用户不存在');
  }

  // 使用类型断言确保TypeScript知道memberType只能是'monthly'或'yearly'
  const membershipType = memberType as 'monthly' | 'yearly';

  // 获取会员价格和天数
  const price = config.membership[membershipType].price;
  const days = config.membership[membershipType].days;

  // 检查大米是否足够
  if (user.coins < price) {
    throw new BusinessError('大米不足，无法订阅会员');
  }

  // 获取当前会员状态
  let membership = await Membership.findOne({
    where: { user_id: userId },
  });

  if (!membership) {
    membership = await Membership.create({
      user_id: userId,
      is_member: false,
    });
  }

  // 计算新的到期时间
  let expireDate;
  if (membership.is_member && membership.expire_date !== null && new Date() < membership.expire_date) {
    // 如果已经是会员，则在原到期时间基础上增加天数
    expireDate = moment(membership.expire_date).add(days, 'days').toDate();
  } else {
    // 如果不是会员，则从当前时间开始计算
    expireDate = moment().add(days, 'days').toDate();
  }

  // 更新会员信息
  await membership.update({
    is_member: true,
    member_type: memberType,
    expire_date: expireDate,
  });

  // 扣除大米
  await user.update({
    coins: user.coins - price,
  });

  // 记录交易
  await Transaction.create({
    user_id: userId,
    amount: price,
    title: `订阅${memberType === 'monthly' ? '月度' : '年度'}会员`,
    type: 'out',
    time: new Date(),
  });

  return {
    isMember: true,
    memberType,
    expireDate: moment(expireDate).format('YYYY-MM-DD'),
    coins: user.coins,
  };
};

/**
 * 创建交易记录（支持事务）
 * @param userId 用户ID
 * @param type 交易类型 ('in' | 'out')
 * @param amount 交易金额
 * @param title 交易标题
 * @param transaction 事务对象（可选）
 * @returns 交易记录
 */
const createTransaction = async (
  userId: number,
  type: 'in' | 'out',
  amount: number,
  title: string,
  transaction?: any
): Promise<any> => {
  // 创建交易记录
  const options = transaction ? { transaction } : {};

  const transactionRecord = await Transaction.create({
    user_id: userId,
    amount,
    title,
    type,
    time: new Date(),
  }, options);

  return {
    id: transactionRecord.id,
    amount: transactionRecord.amount,
    title: transactionRecord.title,
    type: transactionRecord.type,
    time: moment(transactionRecord.time).format('YYYY-MM-DD HH:mm:ss'),
  };
};

export default {
  login,
  getUserInfo,
  updateUserInfo,
  getSearchHistory,
  addSearchHistory,
  clearSearchHistory,
  getUserBackgroundSettings,
  updateUserBackgroundSettings,
  getUserCoins,
  rechargeCoins,
  consumeCoins,
  getCoinRecords,
  getSignInData,
  signIn,
  watchAd,
  getMembershipStatus,
  subscribeMembership,
  createTransaction,
  compensateSignIn,
};
