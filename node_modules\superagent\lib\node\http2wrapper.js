"use strict";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return typeof key === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (typeof input !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (typeof res !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
const Stream = require('stream');
const util = require('util');
const net = require('net');
const tls = require('tls');
// eslint-disable-next-line node/no-deprecated-api
const _require = require('url'),
  parse = _require.parse;
const process = require('process');
const semverGte = require('semver/functions/gte');
let http2;
if (semverGte(process.version, 'v10.10.0')) http2 = require('http2');else throw new Error('superagent: this version of Node.js does not support http2');
const _http2$constants = http2.constants,
  HTTP2_HEADER_PATH = _http2$constants.HTTP2_HEADER_PATH,
  HTTP2_HEADER_STATUS = _http2$constants.HTTP2_HEADER_STATUS,
  HTTP2_HEADER_METHOD = _http2$constants.HTTP2_HEADER_METHOD,
  HTTP2_HEADER_AUTHORITY = _http2$constants.HTTP2_HEADER_AUTHORITY,
  HTTP2_HEADER_HOST = _http2$constants.HTTP2_HEADER_HOST,
  HTTP2_HEADER_SET_COOKIE = _http2$constants.HTTP2_HEADER_SET_COOKIE,
  NGHTTP2_CANCEL = _http2$constants.NGHTTP2_CANCEL;
function setProtocol(protocol) {
  return {
    request(options) {
      return new Request(protocol, options);
    }
  };
}
function Request(protocol, options) {
  Stream.call(this);
  const defaultPort = protocol === 'https:' ? 443 : 80;
  const defaultHost = 'localhost';
  const port = options.port || defaultPort;
  const host = options.host || defaultHost;
  delete options.port;
  delete options.host;
  this.method = options.method;
  this.path = options.path;
  this.protocol = protocol;
  this.host = host;
  delete options.method;
  delete options.path;
  const sessionOptions = _objectSpread({}, options);
  if (options.socketPath) {
    sessionOptions.socketPath = options.socketPath;
    sessionOptions.createConnection = this.createUnixConnection.bind(this);
  }
  this._headers = {};
  const session = http2.connect(`${protocol}//${host}:${port}`, sessionOptions);
  this.setHeader('host', `${host}:${port}`);
  session.on('error', error => this.emit('error', error));
  this.session = session;
}

/**
 * Inherit from `Stream` (which inherits from `EventEmitter`).
 */
util.inherits(Request, Stream);
Request.prototype.createUnixConnection = function (authority, options) {
  switch (this.protocol) {
    case 'http:':
      return net.connect(options.socketPath);
    case 'https:':
      options.ALPNProtocols = ['h2'];
      options.servername = this.host;
      options.allowHalfOpen = true;
      return tls.connect(options.socketPath, options);
    default:
      throw new Error('Unsupported protocol', this.protocol);
  }
};
Request.prototype.setNoDelay = function (bool) {
  // We can not use setNoDelay with HTTP/2.
  // Node 10 limits http2session.socket methods to ones safe to use with HTTP/2.
  // See also https://nodejs.org/api/http2.html#http2_http2session_socket
};
Request.prototype.getFrame = function () {
  if (this.frame) {
    return this.frame;
  }
  const method = {
    [HTTP2_HEADER_PATH]: this.path,
    [HTTP2_HEADER_METHOD]: this.method
  };
  let headers = this.mapToHttp2Header(this._headers);
  headers = Object.assign(headers, method);
  const frame = this.session.request(headers);
  frame.once('response', (headers, flags) => {
    headers = this.mapToHttpHeader(headers);
    frame.headers = headers;
    frame.statusCode = headers[HTTP2_HEADER_STATUS];
    frame.status = frame.statusCode;
    this.emit('response', frame);
  });
  this._headerSent = true;
  frame.once('drain', () => this.emit('drain'));
  frame.on('error', error => this.emit('error', error));
  frame.on('close', () => this.session.close());
  this.frame = frame;
  return frame;
};
Request.prototype.mapToHttpHeader = function (headers) {
  const keys = Object.keys(headers);
  const http2Headers = {};
  for (var _i = 0, _keys = keys; _i < _keys.length; _i++) {
    let key = _keys[_i];
    let value = headers[key];
    key = key.toLowerCase();
    switch (key) {
      case HTTP2_HEADER_SET_COOKIE:
        value = Array.isArray(value) ? value : [value];
        break;
      default:
        break;
    }
    http2Headers[key] = value;
  }
  return http2Headers;
};
Request.prototype.mapToHttp2Header = function (headers) {
  const keys = Object.keys(headers);
  const http2Headers = {};
  for (var _i2 = 0, _keys2 = keys; _i2 < _keys2.length; _i2++) {
    let key = _keys2[_i2];
    let value = headers[key];
    key = key.toLowerCase();
    switch (key) {
      case HTTP2_HEADER_HOST:
        key = HTTP2_HEADER_AUTHORITY;
        value = /^http:\/\/|^https:\/\//.test(value) ? parse(value).host : value;
        break;
      default:
        break;
    }
    http2Headers[key] = value;
  }
  return http2Headers;
};
Request.prototype.setHeader = function (name, value) {
  this._headers[name.toLowerCase()] = value;
};
Request.prototype.getHeader = function (name) {
  return this._headers[name.toLowerCase()];
};
Request.prototype.write = function (data, encoding) {
  const frame = this.getFrame();
  return frame.write(data, encoding);
};
Request.prototype.pipe = function (stream, options) {
  const frame = this.getFrame();
  return frame.pipe(stream, options);
};
Request.prototype.end = function (data) {
  const frame = this.getFrame();
  frame.end(data);
};
Request.prototype.abort = function (data) {
  const frame = this.getFrame();
  frame.close(NGHTTP2_CANCEL);
  this.session.destroy();
};
exports.setProtocol = setProtocol;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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