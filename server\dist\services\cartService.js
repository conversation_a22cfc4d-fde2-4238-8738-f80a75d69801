"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
/**
 * 获取购物车列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @returns 购物车列表
 */
const getCartList = (userId, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    const cartItems = yield models_1.CartItem.findAll({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
        include: [
            {
                model: models_1.Dish,
                as: 'dish',
                include: [
                    {
                        model: models_1.Category,
                        as: 'category',
                        attributes: ['id', 'name'],
                    },
                    {
                        model: models_1.Ingredient,
                        as: 'ingredients',
                        attributes: ['id', 'name', 'amount'],
                        order: [['sort', 'ASC']],
                    },
                ],
            },
            {
                model: models_1.Kitchen,
                as: 'kitchen',
                attributes: ['id', 'name', 'avatar_url'],
            },
        ],
    });
    return cartItems.map(item => {
        var _a, _b;
        // 从配料表生成标签，取前5个配料的名称
        const tags = ((_a = item.dish) === null || _a === void 0 ? void 0 : _a.ingredients) ? item.dish.ingredients.slice(0, 5).map((ingredient) => ingredient.name) : [];
        return {
            id: item.id,
            dishId: item.dish_id,
            kitchenId: item.kitchen_id,
            count: item.count,
            dish: item.dish ? {
                id: item.dish.id,
                name: item.dish.name,
                image: item.dish.image,
                price: item.dish.price,
                originalPrice: item.dish.original_price,
                description: item.dish.description,
                tags: tags, // 使用配料表生成的标签而不是原有的tags字段
                status: item.dish.status,
                categoryId: item.dish.category_id,
                categoryName: (_b = item.dish.category) === null || _b === void 0 ? void 0 : _b.name,
            } : null,
            kitchen: item.kitchen ? {
                id: item.kitchen.id,
                name: item.kitchen.name,
                avatarUrl: item.kitchen.avatar_url,
            } : null,
        };
    });
});
/**
 * 添加到购物车
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 * @param count 数量
 * @returns 添加结果
 */
const addToCart = (userId_1, dishId_1, kitchenId_1, ...args_1) => __awaiter(void 0, [userId_1, dishId_1, kitchenId_1, ...args_1], void 0, function* (userId, dishId, kitchenId, count = 1) {
    // 检查菜品是否存在
    const dish = yield models_1.Dish.findOne({
        where: {
            id: dishId,
            kitchen_id: kitchenId,
            status: 'on', // 只能添加上架的菜品
        },
    });
    if (!dish) {
        throw new error_1.BusinessError('菜品不存在或已下架');
    }
    // 检查购物车中是否已存在该菜品
    let cartItem = yield models_1.CartItem.findOne({
        where: {
            user_id: userId,
            dish_id: dishId,
        },
    });
    if (cartItem) {
        // 更新数量
        yield cartItem.update({
            count: cartItem.count + count,
        });
    }
    else {
        // 创建新的购物车项
        cartItem = yield models_1.CartItem.create({
            user_id: userId,
            kitchen_id: kitchenId,
            dish_id: dishId,
            count,
        });
    }
    return {
        id: cartItem.id,
        dishId: cartItem.dish_id,
        kitchenId: cartItem.kitchen_id,
        count: cartItem.count,
    };
});
/**
 * 更新购物车项数量
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param count 数量
 * @param kitchenId 厨房ID
 * @returns 更新结果
 */
const updateCartItemCount = (userId, dishId, count, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查购物车项是否存在
    const cartItem = yield models_1.CartItem.findOne({
        where: {
            user_id: userId,
            dish_id: dishId,
            kitchen_id: kitchenId
        },
    });
    if (!cartItem) {
        throw new error_1.BusinessError('购物车项不存在');
    }
    if (count <= 0) {
        // 删除购物车项
        yield cartItem.destroy();
        return {
            deleted: true,
        };
    }
    else {
        // 更新数量
        yield cartItem.update({
            count,
        });
        return {
            id: cartItem.id,
            dishId: cartItem.dish_id,
            kitchenId: cartItem.kitchen_id,
            count: cartItem.count,
        };
    }
});
/**
 * 清空购物车
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 */
const clearCart = (userId, kitchenId) => __awaiter(void 0, void 0, void 0, function* () {
    yield models_1.CartItem.destroy({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
    });
});
exports.default = {
    getCartList,
    addToCart,
    updateCartItemCount,
    clearCart,
};
//# sourceMappingURL=cartService.js.map