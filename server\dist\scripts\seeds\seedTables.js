"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 桌号种子数据脚本
 * 创建测试桌号数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
// 测试桌号数据
const testTables = [
    // 厨房1的桌号
    {
        kitchen_id: 'KIT001',
        name: 'A1',
        sort: 1,
    },
    {
        kitchen_id: 'KIT001',
        name: 'A2',
        sort: 2,
    },
    {
        kitchen_id: 'KIT001',
        name: 'A3',
        sort: 3,
    },
    {
        kitchen_id: 'KIT001',
        name: 'B1',
        sort: 4,
    },
    {
        kitchen_id: 'KIT001',
        name: 'B2',
        sort: 5,
    },
    // 厨房2的桌号
    {
        kitchen_id: 'KIT002',
        name: '1号桌',
        sort: 1,
    },
    {
        kitchen_id: 'KIT002',
        name: '2号桌',
        sort: 2,
    },
    {
        kitchen_id: 'KIT002',
        name: '3号桌',
        sort: 3,
    },
    {
        kitchen_id: 'KIT002',
        name: 'VIP包间',
        sort: 4,
    },
    // 厨房3的桌号
    {
        kitchen_id: 'KIT003',
        name: '大厅1',
        sort: 1,
    },
    {
        kitchen_id: 'KIT003',
        name: '大厅2',
        sort: 2,
    },
    {
        kitchen_id: 'KIT003',
        name: '大厅3',
        sort: 3,
    },
    {
        kitchen_id: 'KIT003',
        name: '包间1',
        sort: 4,
    },
    {
        kitchen_id: 'KIT003',
        name: '包间2',
        sort: 5,
    },
    {
        kitchen_id: 'KIT003',
        name: '包间3',
        sort: 6,
    },
];
/**
 * 创建测试桌号数据
 */
function seedTables() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试桌号数据...');
        try {
            // 清空现有数据
            yield models_1.Table.destroy({ where: {} });
            logger_1.default.info('已清空现有桌号数据');
            // 创建测试桌号
            for (const tableData of testTables) {
                const table = yield models_1.Table.create(tableData);
                logger_1.default.info(`创建桌号: ${table.name} (ID: ${table.id}, 厨房: ${table.kitchen_id})`);
            }
            logger_1.default.info(`共创建 ${testTables.length} 个测试桌号`);
        }
        catch (error) {
            logger_1.default.error('创建测试桌号数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试桌号
if (require.main === module) {
    seedTables()
        .then(() => {
        logger_1.default.info('创建测试桌号数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试桌号数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedTables;
//# sourceMappingURL=seedTables.js.map