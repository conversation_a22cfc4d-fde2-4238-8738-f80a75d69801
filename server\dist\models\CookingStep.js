"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 烹饪步骤模型
 * 存储菜品的烹饪步骤信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 烹饪步骤模型类
class CookingStep extends sequelize_1.Model {
}
// 初始化烹饪步骤模型
CookingStep.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '步骤ID',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    step_number: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '步骤编号',
    },
    title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        comment: '步骤标题',
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        comment: '步骤描述',
    },
    image: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '步骤图片',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'CookingStep',
    tableName: 'cooking_steps',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_dish_step',
            fields: ['dish_id', 'step_number'],
        },
    ],
});
exports.default = CookingStep;
//# sourceMappingURL=CookingStep.js.map