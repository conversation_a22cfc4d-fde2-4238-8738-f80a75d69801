/**
 * API接口相关类型定义
 */

// 请求选项接口
export interface RequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data: any;
  header?: any;
  showLoading?: boolean;
  loadingText?: string;
  noAuth?: boolean; // 是否不需要身份验证（用于公开访问）
}

// 响应数据接口
export interface ResponseData {
  error: number;
  body: any;
  message: string;
}

// 会员状态接口
export interface MembershipStatus {
  isMember: boolean;      // 是否是会员
  expireDate: string;     // 到期时间
  memberType: string;     // 会员类型：monthly-月度会员，yearly-年度会员
  privileges?: {          // 会员特权
    customTheme: boolean; // 自定义主题
    tableManagement: boolean; // 桌号管理
  }
}

// 用户信息接口
export interface UserInfo {
  userId: string;
  nickName: string;
  avatarUrl: string;
  gender: number;
  coins: number;    // 大米数量
  likes: number;    // 获赞数量
  dishes: number;   // 添加的菜品数量
  kitchens: number; // 厨房数量
}

// 背景设置接口
export interface BackgroundSettings {
  shopBg?: string;
  navBgStyle?: string;
  navBgIndex?: number;
}