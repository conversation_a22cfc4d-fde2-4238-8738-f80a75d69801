"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cancelAcceptedOrder = exports.cancelCooking = exports.completeOrder = exports.startCooking = exports.acceptOrder = exports.rejectOrder = exports.cancelOrder = exports.getPublicOrderDetail = exports.getOrderDetail = exports.getOrderList = exports.submitOrder = void 0;
const orderService_1 = __importDefault(require("../services/orderService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
const validator_1 = __importDefault(require("../utils/validator"));
/**
 * 提交订单
 * @route POST /api/order/submit
 */
const submitOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, tableNo, remark } = req.body;
        // 验证厨房ID
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        validator_1.default.validateId(kitchenId, '厨房ID');
        // 验证桌号（非必选，允许为空或"未选择"）
        let validatedTableNo = '未选择';
        if (tableNo && tableNo !== '未选择' && tableNo.trim() !== '') {
            validator_1.default.validateStringLength(tableNo, 1, 20, '桌号');
            validatedTableNo = tableNo;
        }
        // 验证备注（非必选，允许为空）
        let validatedRemark = '';
        if (remark && remark.trim() !== '') {
            validator_1.default.validateStringLength(remark, 1, 200, '备注');
            // 过滤XSS
            validatedRemark = validator_1.default.sanitizeInput(remark);
        }
        const result = yield orderService_1.default.submitOrder(userId, kitchenId, validatedTableNo, validatedRemark);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
exports.submitOrder = submitOrder;
/**
 * 获取订单列表
 * @route GET /api/order/list
 */
const getOrderList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId, status, page = 1, pageSize = 10 } = req.query;
        // 验证厨房ID（如果提供）
        if (kitchenId) {
            validator_1.default.validateId(kitchenId, '厨房ID');
        }
        // 验证订单状态（如果提供）
        if (status && status !== 'all') {
            validator_1.default.validateEnum(status, ['pending', 'accepted', 'cooking', 'completed', 'cancelled'], '订单状态');
        }
        // 验证分页参数
        const pageNum = parseInt(page);
        const pageSizeNum = parseInt(pageSize);
        validator_1.default.validateNumberRange(pageNum, 1, 1000, '页码');
        validator_1.default.validateNumberRange(pageSizeNum, 1, 100, '每页数量');
        const orders = yield orderService_1.default.getOrderList(userId, kitchenId, status, pageNum, pageSizeNum);
        (0, response_1.success)(res, orders);
    }
    catch (err) {
        next(err);
    }
});
exports.getOrderList = getOrderList;
/**
 * 获取订单详情
 * @route GET /api/order/detail
 */
const getOrderDetail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.query;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        const order = yield orderService_1.default.getOrderDetail(userId, id);
        (0, response_1.success)(res, order);
    }
    catch (err) {
        next(err);
    }
});
exports.getOrderDetail = getOrderDetail;
/**
 * 获取订单详情（公开访问，用于分享）
 * @route GET /api/order/public/:id
 */
const getPublicOrderDetail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        const order = yield orderService_1.default.getPublicOrderDetail(id);
        (0, response_1.success)(res, order);
    }
    catch (err) {
        next(err);
    }
});
exports.getPublicOrderDetail = getPublicOrderDetail;
/**
 * 取消订单
 * @route POST /api/order/cancel
 */
const cancelOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.cancelOrder(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.cancelOrder = cancelOrder;
/**
 * 拒绝订单
 * @route POST /api/order/reject
 */
const rejectOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.rejectOrder(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.rejectOrder = rejectOrder;
/**
 * 接单
 * @route POST /api/order/accept
 */
const acceptOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.acceptOrder(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.acceptOrder = acceptOrder;
/**
 * 开始烹饪
 * @route POST /api/order/startCooking
 */
const startCooking = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.startCooking(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.startCooking = startCooking;
/**
 * 完成订单
 * @route POST /api/order/complete
 */
const completeOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.completeOrder(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.completeOrder = completeOrder;
/**
 * 取消烹饪
 * @route POST /api/order/cancelCooking
 */
const cancelCooking = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.cancelCooking(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.cancelCooking = cancelCooking;
/**
 * 取消已接单订单
 * @route POST /api/order/cancelAcceptedOrder
 */
const cancelAcceptedOrder = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { id } = req.body;
        if (!id) {
            throw new error_1.BusinessError('缺少参数: id', response_1.ResponseCode.VALIDATION);
        }
        yield orderService_1.default.cancelAcceptedOrder(userId, id);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.cancelAcceptedOrder = cancelAcceptedOrder;
//# sourceMappingURL=orderController.js.map