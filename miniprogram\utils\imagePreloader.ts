/**
 * 图片预加载工具
 * 用于预加载常用图标和图片，提升用户体验
 */

interface PreloadResult {
  success: string[];
  failed: string[];
}

/**
 * 预加载图片列表
 * @param imageUrls 图片URL数组
 * @returns Promise<PreloadResult>
 */
export const preloadImages = (imageUrls: string[]): Promise<PreloadResult> => {
  return new Promise((resolve) => {
    const result: PreloadResult = {
      success: [],
      failed: []
    };

    let completedCount = 0;
    const totalCount = imageUrls.length;

    if (totalCount === 0) {
      resolve(result);
      return;
    }

    imageUrls.forEach((url) => {
      wx.getImageInfo({
        src: url,
        success: () => {
          result.success.push(url);
          completedCount++;
          if (completedCount === totalCount) {
            resolve(result);
          }
        },
        fail: () => {
          result.failed.push(url);
          completedCount++;
          if (completedCount === totalCount) {
            resolve(result);
          }
        }
      });
    });
  });
};

/**
 * 预加载常用图标
 * 在应用启动时调用，预加载高频使用的图标
 */
export const preloadCommonIcons = async (): Promise<void> => {
  const commonIcons = [
    // 星级图标（高频使用）
    '/static/images/icons/star.png',
    '/static/images/icons/star1.png',
    
    // 操作图标（高频使用）
    '/static/images/icons/plus.png',
    '/static/images/icons/minus.png',
    '/static/images/icons/cart.png',
    
    // 功能图标（中频使用）
    '/static/images/icons/home.png',
    '/static/images/icons/kitchen.png',
    '/static/images/icons/search.png',
    '/static/images/icons/check.png',
    '/static/images/icons/check1.png',
    
    // 用户相关图标
    '/static/images/icons/male.png',
    '/static/images/icons/female.png',
    '/static/images/icons/crown.png'
  ];

  try {
    const result = await preloadImages(commonIcons);
    console.log('常用图标预加载完成:', result.success.length, '成功,', result.failed.length, '失败');
    
    if (result.failed.length > 0) {
      console.warn('预加载失败的图标:', result.failed);
    }
  } catch (error) {
    console.error('常用图标预加载出错:', error);
  }
};

/**
 * 预加载首屏菜品图片
 * @param dishImages 菜品图片URL数组（限制数量避免过度预加载）
 */
export const preloadDishImages = async (dishImages: string[]): Promise<void> => {
  // 限制预加载数量，避免占用过多资源
  const limitedImages = dishImages.slice(0, 8);
  
  if (limitedImages.length === 0) return;

  try {
    const result = await preloadImages(limitedImages);
    console.log('菜品图片预加载完成:', result.success.length, '成功');
  } catch (error) {
    console.error('菜品图片预加载出错:', error);
  }
};

/**
 * 智能预加载策略
 * 根据网络状况和设备性能调整预加载策略
 */
export const smartPreload = (): void => {
  // 获取网络状况
  wx.getNetworkType({
    success: (res) => {
      const networkType = res.networkType;
      
      // 根据网络类型调整预加载策略
      if (networkType === 'wifi') {
        // WiFi环境下，积极预加载
        preloadCommonIcons();
      } else if (networkType === '4g') {
        // 4G环境下，只预加载最重要的图标
        const criticalIcons = [
          '/static/images/icons/star.png',
          '/static/images/icons/star1.png',
          '/static/images/icons/plus.png',
          '/static/images/icons/minus.png'
        ];
        preloadImages(criticalIcons);
      } else {
        // 其他网络环境（3g、2g等），跳过预加载
        console.log('网络环境较差，跳过图片预加载');
      }
    },
    fail: () => {
      // 无法获取网络信息，采用保守策略
      console.log('无法获取网络信息，采用保守预加载策略');
      const essentialIcons = [
        '/static/images/icons/star.png',
        '/static/images/icons/star1.png'
      ];
      preloadImages(essentialIcons);
    }
  });
}; 