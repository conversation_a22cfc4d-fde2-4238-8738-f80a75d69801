"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 厨房模型
 * 存储厨房信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
const helpers_1 = require("../utils/helpers");
// 厨房模型类
class Kitchen extends sequelize_1.Model {
    // 检查是否可以添加分类
    canAddCategory() {
        return this.category_count < this.category_limit;
    }
    // 检查是否可以添加菜品
    canAddDish() {
        return this.dish_count < this.dish_limit;
    }
    // 检查是否可以添加成员
    canAddMember() {
        return this.member_count < this.member_limit;
    }
    // 获取厨房等级信息
    getLevelInfo() {
        return {
            level: this.level,
            categoryLimit: this.category_limit,
            dishLimit: this.dish_limit,
            memberLimit: this.member_limit,
        };
    }
}
// 初始化厨房模型
Kitchen.init({
    id: {
        type: sequelize_1.DataTypes.STRING(10),
        primaryKey: true,
        defaultValue: () => (0, helpers_1.generateKitchenId)(),
        comment: '厨房ID（6位字母数字组合）',
    },
    name: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '厨房名称',
    },
    level: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '厨房等级',
    },
    owner_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '厨房拥有者ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    kitchen_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '厨房数量',
    },
    kitchen_limit: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '厨房数量上限',
    },
    category_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '分类数量',
    },
    category_limit: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 5,
        comment: '分类数量上限',
    },
    dish_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '菜品数量',
    },
    dish_limit: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 30,
        comment: '菜品数量上限',
    },
    member_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1, // 默认包含拥有者
        comment: '成员数量',
    },
    member_limit: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 5,
        comment: '成员数量上限',
    },
    notice: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: true,
        comment: '厨房公告',
    },
    avatar_url: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '厨房头像URL',
    },
    background_url: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '厨房背景图URL',
    },
    qr_code_url: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '厨房二维码URL',
    },
    qr_code_expire_time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '厨房二维码过期时间',
    },
    order_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '订单数量',
    },
    total_sales: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总销售额',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Kitchen',
    tableName: 'kitchens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_owner_id',
            fields: ['owner_id'],
        },
        {
            name: 'idx_level',
            fields: ['level'],
        },
    ],
});
exports.default = Kitchen;
//# sourceMappingURL=Kitchen.js.map