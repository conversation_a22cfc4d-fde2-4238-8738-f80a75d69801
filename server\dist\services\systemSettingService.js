"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 系统设置服务
 * 处理系统配置相关的业务逻辑
 */
const models_1 = require("../models");
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
const response_1 = require("../utils/response");
class SystemSettingService {
    /**
     * 获取设置值
     * @param key 设置键名
     * @returns 设置值
     */
    getSetting(key) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const setting = yield models_1.SystemSetting.findOne({
                    where: { key }
                });
                if (!setting) {
                    return null;
                }
                // 根据类型转换值
                switch (setting.type) {
                    case 'boolean':
                        return setting.value === 'true';
                    case 'number':
                        return Number(setting.value);
                    case 'json':
                        try {
                            return JSON.parse(setting.value);
                        }
                        catch (_a) {
                            return setting.value;
                        }
                    default:
                        return setting.value;
                }
            }
            catch (error) {
                logger_1.default.error(`获取系统设置失败: ${error}`);
                throw new error_1.BusinessError('获取系统设置失败', response_1.ResponseCode.SERVER_ERROR);
            }
        });
    }
    /**
     * 设置配置值
     * @param key 设置键名
     * @param value 设置值
     * @param type 值类型
     * @param description 描述
     */
    setSetting(key_1, value_1) {
        return __awaiter(this, arguments, void 0, function* (key, value, type = 'string', description) {
            try {
                let stringValue;
                // 根据类型转换值
                switch (type) {
                    case 'boolean':
                        stringValue = String(Boolean(value));
                        break;
                    case 'number':
                        stringValue = String(Number(value));
                        break;
                    case 'json':
                        stringValue = JSON.stringify(value);
                        break;
                    default:
                        stringValue = String(value);
                }
                yield models_1.SystemSetting.upsert({
                    key,
                    value: stringValue,
                    type,
                    description: description || ''
                });
                logger_1.default.info(`更新系统设置: ${key} = ${stringValue}`);
            }
            catch (error) {
                logger_1.default.error(`设置系统配置失败: ${error}`);
                throw new error_1.BusinessError('设置系统配置失败', response_1.ResponseCode.SERVER_ERROR);
            }
        });
    }
    /**
     * 获取所有设置
     * @returns 所有设置列表
     */
    getAllSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const settings = yield models_1.SystemSetting.findAll({
                    order: [['key', 'ASC']]
                });
                return settings.map(setting => ({
                    key: setting.key,
                    value: this.parseValue(setting.value, setting.type),
                    type: setting.type,
                    description: setting.description,
                    rawValue: setting.value
                }));
            }
            catch (error) {
                logger_1.default.error(`获取所有系统设置失败: ${error}`);
                throw new error_1.BusinessError('获取系统设置失败', response_1.ResponseCode.SERVER_ERROR);
            }
        });
    }
    /**
     * 删除设置
     * @param key 设置键名
     */
    deleteSetting(key) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield models_1.SystemSetting.destroy({
                    where: { key }
                });
                logger_1.default.info(`删除系统设置: ${key}`);
            }
            catch (error) {
                logger_1.default.error(`删除系统设置失败: ${error}`);
                throw new error_1.BusinessError('删除系统设置失败', response_1.ResponseCode.SERVER_ERROR);
            }
        });
    }
    /**
     * 检查评论功能是否开启
     * @returns 是否开启评论功能
     */
    isCommentEnabled() {
        return __awaiter(this, void 0, void 0, function* () {
            const enabled = yield this.getSetting('comment_enabled');
            return enabled !== null ? enabled : true; // 默认开启
        });
    }
    /**
     * 获取新用户默认大米数量
     * @returns 默认大米数量
     */
    getDefaultCoins() {
        return __awaiter(this, void 0, void 0, function* () {
            const coins = yield this.getSetting('default_coins');
            return coins !== null ? coins : 1000; // 默认1000大米
        });
    }
    /**
     * 获取每日签到奖励大米数量
     * @returns 签到奖励大米数量
     */
    getDailySignInCoins() {
        return __awaiter(this, void 0, void 0, function* () {
            const coins = yield this.getSetting('daily_signin_coins');
            return coins !== null ? coins : 20; // 默认20大米
        });
    }
    /**
     * 解析设置值
     * @param value 原始值
     * @param type 类型
     * @returns 解析后的值
     */
    parseValue(value, type) {
        switch (type) {
            case 'boolean':
                return value === 'true';
            case 'number':
                return Number(value);
            case 'json':
                try {
                    return JSON.parse(value);
                }
                catch (_a) {
                    return value;
                }
            default:
                return value;
        }
    }
}
exports.default = new SystemSettingService();
//# sourceMappingURL=systemSettingService.js.map