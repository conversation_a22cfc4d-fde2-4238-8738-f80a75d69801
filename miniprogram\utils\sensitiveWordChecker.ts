/**
 * 敏感词检查工具 - 微信小程序兼容版本
 */

import { getSensitiveWordsByType, getAllSensitiveWords } from './sensitiveWords';

// 敏感词检查结果
export interface SensitiveWordCheckResult {
  hasSensitiveWord: boolean;
  sensitiveWords: string[];
  filteredText?: string;
}

/**
 * 检查文本是否包含敏感词
 */
export function checkSensitiveWord(text: string, type?: string): SensitiveWordCheckResult {
  if (!text) {
    return {
      hasSensitiveWord: false,
      sensitiveWords: []
    };
  }

  // 获取敏感词列表
  var targetWords: string[] = [];
  
  if (type === 'username') {
    targetWords = getSensitiveWordsByType('username');
  } else if (type === 'content') {
    targetWords = getSensitiveWordsByType('content');
  } else if (type === 'dish') {
    targetWords = getSensitiveWordsByType('dish');
  } else if (type === 'comment') {
    targetWords = getSensitiveWordsByType('comment');
  } else {
    targetWords = getAllSensitiveWords();
  }

  var foundWords: string[] = [];
  var lowerText = text.toLowerCase();

  // 检查敏感词
  for (var i = 0; i < targetWords.length; i++) {
    var word = targetWords[i];
    var lowerWord = word.toLowerCase();
    if (lowerText.indexOf(lowerWord) >= 0) {
      foundWords.push(word);
    }
  }

  return {
    hasSensitiveWord: foundWords.length > 0,
    sensitiveWords: foundWords
  };
}

/**
 * 过滤敏感词
 */
export function filterSensitiveWord(text: string, type?: string): string {
  if (!text) {
    return text;
  }

  var result = checkSensitiveWord(text, type);
  if (!result.hasSensitiveWord) {
    return text;
  }

  var filteredText = text;
  for (var i = 0; i < result.sensitiveWords.length; i++) {
    var word = result.sensitiveWords[i];
    var replacement = '';
    for (var j = 0; j < word.length; j++) {
      replacement += '*';
    }
    filteredText = filteredText.replace(new RegExp(word, 'gi'), replacement);
  }

  return filteredText;
}

/**
 * 检查是否包含敏感词
 */
export function hasSensitiveWord(text: string, type?: string): boolean {
  return checkSensitiveWord(text, type).hasSensitiveWord;
} 