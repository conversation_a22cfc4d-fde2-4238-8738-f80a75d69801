"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
/**
 * 系统设置表迁移文件
 */
const sequelize_1 = require("sequelize");
const up = (queryInterface) => __awaiter(void 0, void 0, void 0, function* () {
    yield queryInterface.createTable('system_settings', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            comment: '设置ID',
        },
        key: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '设置键名',
        },
        value: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '设置值',
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('boolean', 'string', 'number', 'json'),
            allowNull: false,
            defaultValue: 'string',
            comment: '值类型',
        },
        description: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true,
            comment: '设置描述',
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间',
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间',
        },
    });
    // 创建索引
    yield queryInterface.addIndex('system_settings', ['key'], {
        name: 'idx_key',
        unique: true,
    });
    yield queryInterface.addIndex('system_settings', ['type'], {
        name: 'idx_type',
    });
    // 插入默认设置
    yield queryInterface.bulkInsert('system_settings', [
        {
            key: 'comment_enabled',
            value: 'true',
            type: 'boolean',
            description: '是否开启评论功能',
            created_at: new Date(),
            updated_at: new Date(),
        },
    ]);
});
exports.up = up;
const down = (queryInterface) => __awaiter(void 0, void 0, void 0, function* () {
    yield queryInterface.dropTable('system_settings');
});
exports.down = down;
//# sourceMappingURL=020-create-system-settings.js.map