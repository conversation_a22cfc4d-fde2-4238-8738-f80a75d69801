-- 图片URL转换为相对路径的迁移脚本
-- 执行前请备份数据库

-- 1. 更新用户表的头像URL
UPDATE users 
SET avatar_url = CASE 
    WHEN avatar_url IS NULL OR avatar_url = '' THEN avatar_url
    WHEN avatar_url LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(avatar_url, 28)
    WHEN avatar_url LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(avatar_url, 27)
    WHEN avatar_url LIKE '/uploads/%' THEN avatar_url
    ELSE avatar_url
END
WHERE avatar_url IS NOT NULL AND avatar_url != '';

-- 2. 更新厨房表的头像URL
UPDATE kitchens 
SET avatar_url = CASE 
    WHEN avatar_url IS NULL OR avatar_url = '' THEN avatar_url
    WHEN avatar_url LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(avatar_url, 28)
    WHEN avatar_url LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(avatar_url, 27)
    WHEN avatar_url LIKE '/uploads/%' THEN avatar_url
    ELSE avatar_url
END
WHERE avatar_url IS NOT NULL AND avatar_url != '';

-- 3. 更新厨房表的背景图URL
UPDATE kitchens 
SET background_url = CASE 
    WHEN background_url IS NULL OR background_url = '' THEN background_url
    WHEN background_url LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(background_url, 28)
    WHEN background_url LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(background_url, 27)
    WHEN background_url LIKE '/uploads/%' THEN background_url
    ELSE background_url
END
WHERE background_url IS NOT NULL AND background_url != '';

-- 4. 更新厨房表的二维码URL
UPDATE kitchens 
SET qr_code_url = CASE 
    WHEN qr_code_url IS NULL OR qr_code_url = '' THEN qr_code_url
    WHEN qr_code_url LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(qr_code_url, 28)
    WHEN qr_code_url LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(qr_code_url, 27)
    WHEN qr_code_url LIKE '/uploads/%' THEN qr_code_url
    ELSE qr_code_url
END
WHERE qr_code_url IS NOT NULL AND qr_code_url != '';

-- 5. 更新菜品表的主图片URL
UPDATE dishes 
SET image = CASE 
    WHEN image IS NULL OR image = '' THEN image
    WHEN image LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(image, 28)
    WHEN image LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(image, 27)
    WHEN image LIKE '/uploads/%' THEN image
    ELSE image
END
WHERE image IS NOT NULL AND image != '';

-- 6. 更新菜品图片表的URL
UPDATE dish_images 
SET url = CASE 
    WHEN url IS NULL OR url = '' THEN url
    WHEN url LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(url, 28)
    WHEN url LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(url, 27)
    WHEN url LIKE '/uploads/%' THEN url
    ELSE url
END
WHERE url IS NOT NULL AND url != '';

-- 7. 更新背景设置表的店铺背景URL
UPDATE background_settings 
SET shop_bg = CASE 
    WHEN shop_bg IS NULL OR shop_bg = '' THEN shop_bg
    WHEN shop_bg LIKE 'https://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(shop_bg, 28)
    WHEN shop_bg LIKE 'http://dzcdd.zj1.natnps.cn%' THEN SUBSTRING(shop_bg, 27)
    WHEN shop_bg LIKE '/uploads/%' THEN shop_bg
    ELSE shop_bg
END
WHERE shop_bg IS NOT NULL AND shop_bg != '';

-- 验证迁移结果（可选执行）
-- SELECT 'users', COUNT(*) as total, 
--        COUNT(CASE WHEN avatar_url LIKE '/uploads/%' THEN 1 END) as relative_paths,
--        COUNT(CASE WHEN avatar_url LIKE 'http%' THEN 1 END) as full_urls
-- FROM users WHERE avatar_url IS NOT NULL AND avatar_url != ''
-- UNION ALL
-- SELECT 'kitchens_avatar', COUNT(*) as total,
--        COUNT(CASE WHEN avatar_url LIKE '/uploads/%' THEN 1 END) as relative_paths,
--        COUNT(CASE WHEN avatar_url LIKE 'http%' THEN 1 END) as full_urls
-- FROM kitchens WHERE avatar_url IS NOT NULL AND avatar_url != ''
-- UNION ALL
-- SELECT 'dishes', COUNT(*) as total,
--        COUNT(CASE WHEN image LIKE '/uploads/%' THEN 1 END) as relative_paths,
--        COUNT(CASE WHEN image LIKE 'http%' THEN 1 END) as full_urls
-- FROM dishes WHERE image IS NOT NULL AND image != '';

COMMIT; 