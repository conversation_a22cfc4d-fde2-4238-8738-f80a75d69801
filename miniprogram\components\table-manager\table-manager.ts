// 桌号管理组件
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    isVip: {
      type: Boolean,
      value: false
    },
    tables: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    },
    ready() {
      // 组件完全准备好
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isAddingTable: false,
    editTable: {
      id: '',
      name: '',
      sort: 0
    },
    editingIndex: -1, // 当前编辑的桌号索引，-1表示新增
    currentDragId: '', // 当前拖动的桌号ID
    startY: 0, // 拖动开始的Y坐标
    currentIndex: -1, // 当前拖动的桌号索引
    moveToIndex: -1, // 移动到的目标索引
    showConfirmDialog: false, // 确认对话框显示状态
    confirmContent: '', // 确认对话框内容
    deleteIndex: -1, // 要删除的桌号索引
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 阻止滑动穿透
    preventTouchMove(): boolean {
      return false;
    },
    // 关闭弹窗
    closeModal(): void {
      // 使用setTimeout确保在下一个事件循环中关闭弹窗，避免滚动条引起的布局问题
      setTimeout(() => {
        this.triggerEvent('close');
      }, 50);
    },

    // 显示新增桌号表单
    showAddTableForm(): void {
      // 检查桌号数量限制
      if (this.data.tables && this.data.tables.length >= 50) {
        wx.showToast({
          title: '每个厨房最多只能添加50个桌号',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      this.setData({
        isAddingTable: true,
        editingIndex: -1,
        editTable: {
          id: '',
          name: '',
          sort: this.data.tables.length
        }
      });
    },

    // 取消新增桌号
    cancelAddTable(): void {
      this.setData({
        isAddingTable: false
      });
    },

    // 编辑桌号
    editTable(e: WechatMiniprogram.TouchEvent): void {
      const { index } = e.currentTarget.dataset;
      const table = this.data.tables[index];

      this.setData({
        isAddingTable: true,
        editingIndex: index,
        editTable: {
          id: table.id,
          name: table.name,
          sort: table.sort || index
        }
      });
    },

    // 显示删除确认对话框
    showDeleteConfirm(e: WechatMiniprogram.TouchEvent): void {
      const { index } = e.currentTarget.dataset;
      const table = this.data.tables[index];

      this.setData({
        showConfirmDialog: true,
        confirmContent: `确定要删除"${table.name}"桌号吗？`,
        deleteIndex: index
      });
    },

    // 取消删除
    onCancelDelete(): void {
      this.setData({
        showConfirmDialog: false,
        deleteIndex: -1
      });
    },

    // 确认删除
    onConfirmDelete(): void {
      const { deleteIndex } = this.data;
      if (deleteIndex !== -1) {
        const table = this.data.tables[deleteIndex];
        this.triggerEvent('delete', { index: deleteIndex, table });
      }

      this.setData({
        showConfirmDialog: false,
        deleteIndex: -1
      });
    },

    // 桌号名称输入
    onNameInput(e: WechatMiniprogram.Input): void {
      const value = e.detail.value;
      
      this.setData({
        'editTable.name': value
      });

      // 实时敏感词检查
      if (value && value.length > 1) {
        const checkResult = checkSensitiveWord(value, 'content');
        if (checkResult.hasSensitiveWord) {
          console.warn('桌号名称包含敏感词:', checkResult.sensitiveWords);
        }
      }
    },

    // 保存桌号
    saveTable(): void {
      const { editTable, editingIndex } = this.data;

      if (!editTable.name || !editTable.name.trim()) {
        wx.showToast({
          title: '请输入桌号名称',
          icon: 'none'
        });
        return;
      }

      // 验证桌号名称长度
      if (editTable.name.trim().length > 10) {
        wx.showToast({
          title: '桌号名称不能超过10个字符',
          icon: 'none'
        });
        return;
      }

      // 敏感词检查
      const nameCheck = checkSensitiveWord(editTable.name.trim(), 'content');
      if (nameCheck.hasSensitiveWord) {
        wx.showModal({
          title: '内容审核',
          content: '桌号名称包含不当内容，请重新输入',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF6B35'
        });
        return;
      }

      // 编辑模式
      if (editingIndex !== -1) {
        this.triggerEvent('update', { index: editingIndex, table: editTable });
      } else {
        // 新增模式
        this.triggerEvent('add', { table: editTable });
      }

      // 重置并返回桌号列表
      this.setData({
        isAddingTable: false
      });
    },

    // 触摸开始，记录开始位置
    onTouchStart(e: WechatMiniprogram.TouchEvent): void {
      // 确保事件来自拖动按钮
      const { index, id } = e.currentTarget.dataset;
      const startY = e.touches[0].clientY;

      this.setData({
        currentDragId: id,
        startY,
        currentIndex: index
      });
    },

    // 触摸移动，判断移动位置
    onTouchMove(e: WechatMiniprogram.TouchEvent): void {
      const { currentIndex, currentDragId } = this.data;
      if (!currentDragId) return;

      // 获取当前Y坐标
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.data.startY;

      // 获取所有桌号项的高度
      const itemHeight = 90; // 约90rpx

      // 计算应该移动到哪个索引
      const moveOffset = Math.round(deltaY / itemHeight);
      let moveToIndex = currentIndex + moveOffset;

      // 限制索引范围
      moveToIndex = Math.max(0, Math.min(this.data.tables.length - 1, moveToIndex));

      // 如果位置有变化，更新UI
      if (moveToIndex !== this.data.moveToIndex) {
        this.setData({
          moveToIndex
        });
      }
    },

    // 触摸结束，执行排序
    onTouchEnd(e: WechatMiniprogram.TouchEvent): void {
      const { currentIndex, moveToIndex, currentDragId } = this.data;

      // 如果没有拖动或目标位置相同，不做处理
      if (!currentDragId || currentIndex === moveToIndex || moveToIndex === -1) {
        this.setData({
          currentDragId: '',
          startY: 0,
          currentIndex: -1,
          moveToIndex: -1
        });
        return;
      }

      // 获取当前桌号列表的副本
      const tables = [...this.properties.tables];

      // 执行排序，将当前桌号移动到目标位置
      const [movedItem] = tables.splice(currentIndex, 1);
      tables.splice(moveToIndex, 0, movedItem);

      // 更新排序值
      const sortedTables = tables.map((item, index) => ({
        ...item,
        sort: index
      }));

      // 通知父组件更新排序
      this.triggerEvent('sort', { tables: sortedTables });

      // 重置拖动状态
      this.setData({
        currentDragId: '',
        startY: 0,
        currentIndex: -1,
        moveToIndex: -1
      });
    }
  }
}) 