const { Sequelize } = require('sequelize');

// 数据库配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: 'localhost',
  port: 3306,
  database: 'restaurant_menu_db',
  username: 'root',
  password: '123123',
  logging: console.log,
  timezone: '+08:00'
});

async function fixSystemSettings() {
  try {
    console.log('🔧 开始修复系统设置...\n');
    
    // 1. 检查表是否存在
    console.log('1. 检查system_settings表...');
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'system_settings'");
    
    if (tables.length === 0) {
      console.log('❌ system_settings表不存在，正在创建...');
      await createSystemSettingsTable();
    } else {
      console.log('✅ system_settings表存在');
      
      // 检查表结构
      const [columns] = await sequelize.query('DESCRIBE system_settings');
      console.log('表结构:', columns.map(col => col.Field).join(', '));
      
      // 检查数据
      const [settings] = await sequelize.query('SELECT * FROM system_settings');
      console.log('现有设置数量:', settings.length);
      
      if (settings.length === 0) {
        console.log('❌ 没有默认设置，正在插入...');
        await insertDefaultSettings();
      } else {
        console.log('现有设置:');
        settings.forEach(setting => {
          console.log(`  - ${setting.key}: ${setting.value} (${setting.type})`);
        });
        
        // 检查是否有comment_enabled设置
        const commentSetting = settings.find(s => s.key === 'comment_enabled');
        if (!commentSetting) {
          console.log('❌ 缺少comment_enabled设置，正在添加...');
          await sequelize.query(`
            INSERT INTO system_settings (\`key\`, value, type, description) VALUES
            ('comment_enabled', 'true', 'boolean', '是否开启评论功能')
          `);
          console.log('✅ comment_enabled设置添加成功');
        } else {
          console.log('✅ comment_enabled设置已存在');
        }
      }
    }
    
    // 2. 测试API接口
    console.log('\n2. 测试系统设置API...');
    try {
      const axios = require('axios');
      const response = await axios.get('https://dzcdd.zj1.natnps.cn/api/system/comment-enabled');
      console.log('✅ 评论开关API响应:', response.data);
    } catch (error) {
      console.log('❌ API测试失败:', error.message);
    }
    
    console.log('\n🎉 系统设置修复完成！');
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error.message);
  } finally {
    await sequelize.close();
  }
}

async function createSystemSettingsTable() {
  await sequelize.query(`
    CREATE TABLE system_settings (
      id INT AUTO_INCREMENT PRIMARY KEY,
      \`key\` VARCHAR(100) NOT NULL UNIQUE,
      value TEXT NOT NULL,
      type ENUM('boolean', 'string', 'number', 'json') NOT NULL DEFAULT 'string',
      description VARCHAR(500) NULL,
      created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_key (\`key\`),
      INDEX idx_type (type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log('✅ system_settings表创建成功');
  await insertDefaultSettings();
}

async function insertDefaultSettings() {
  await sequelize.query(`
    INSERT INTO system_settings (\`key\`, value, type, description) VALUES
    ('comment_enabled', 'true', 'boolean', '是否开启评论功能')
  `);
  console.log('✅ 默认设置插入成功');
}

fixSystemSettings();
