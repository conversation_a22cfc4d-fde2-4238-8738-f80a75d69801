"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 菜品种子数据脚本
 * 创建测试菜品数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
/**
 * 创建测试菜品数据
 */
function seedDishes() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试菜品数据...');
        try {
            // 清空现有数据
            yield models_1.Dish.destroy({ where: {} });
            yield models_1.DishImage.destroy({ where: {} });
            yield models_1.Ingredient.destroy({ where: {} });
            yield models_1.Nutrition.destroy({ where: {} });
            yield models_1.CookingStep.destroy({ where: {} });
            logger_1.default.info('已清空现有菜品相关数据');
            // 获取所有分类
            const categories = yield models_1.Category.findAll();
            if (categories.length === 0) {
                throw new Error('没有找到分类数据，请先运行 seedCategories 脚本');
            }
            // 为每个分类创建测试菜品
            for (const category of categories) {
                // 为每个分类创建2-3个菜品
                const dishCount = Math.floor(Math.random() * 2) + 2; // 2-3个菜品
                for (let i = 1; i <= dishCount; i++) {
                    // 创建菜品
                    const dish = yield models_1.Dish.create({
                        kitchen_id: category.kitchen_id,
                        category_id: category.id,
                        name: `${category.name}示例${i}`,
                        image: `https://example.com/dishes/${category.kitchen_id}_${category.id}_${i}.jpg`,
                        price: Math.floor(Math.random() * 50) + 10, // 10-59元
                        original_price: Math.floor(Math.random() * 80) + 20, // 20-99元
                        description: `这是${category.name}分类下的示例菜品${i}，口感鲜美，营养丰富。`,
                        tags: `美味,推荐,${category.name}`,
                        status: 'on',
                        sales: Math.floor(Math.random() * 100),
                        rating: parseFloat((Math.random() * 2 + 3).toFixed(1)), // 3.0-5.0分
                        created_by: parseInt(category.kitchen_id.replace('KIT00', '')) + 10000, // 根据厨房ID生成创建者ID
                    });
                    logger_1.default.info(`创建菜品: ${dish.name} (ID: ${dish.id}, 分类: ${category.name})`);
                    // 创建菜品图片
                    const imageCount = Math.floor(Math.random() * 3) + 1; // 1-3张图片
                    for (let j = 1; j <= imageCount; j++) {
                        yield models_1.DishImage.create({
                            dish_id: dish.id,
                            url: `https://example.com/dishes/${category.kitchen_id}_${category.id}_${i}_${j}.jpg`,
                            sort: j,
                        });
                    }
                    // 创建配料
                    const ingredients = [
                        { name: '主料', amount: '适量' },
                        { name: '调料', amount: '适量' },
                        { name: '配料', amount: '适量' },
                    ];
                    for (let j = 0; j < ingredients.length; j++) {
                        yield models_1.Ingredient.create({
                            dish_id: dish.id,
                            name: ingredients[j].name,
                            amount: ingredients[j].amount,
                            sort: j + 1,
                        });
                    }
                    // 创建营养成分
                    yield models_1.Nutrition.create({
                        dish_id: dish.id,
                        calories: Math.floor(Math.random() * 500) + 100, // 100-599卡路里
                        protein: Math.floor(Math.random() * 20) + 5, // 5-24克
                        fat: Math.floor(Math.random() * 15) + 2, // 2-16克
                        carbs: Math.floor(Math.random() * 30) + 10, // 10-39克
                        fiber: Math.floor(Math.random() * 5) + 1, // 1-5克
                        sugar: Math.floor(Math.random() * 10) + 1, // 1-10克
                        sodium: Math.floor(Math.random() * 500) + 100, // 100-599毫克
                    });
                    // 创建烹饪步骤
                    const steps = [
                        { title: '准备阶段', description: '准备食材', image: `https://example.com/steps/step1.jpg` },
                        { title: '烹饪阶段', description: '开始烹饪', image: `https://example.com/steps/step2.jpg` },
                        { title: '完成阶段', description: '装盘上菜', image: `https://example.com/steps/step3.jpg` },
                    ];
                    for (let j = 0; j < steps.length; j++) {
                        yield models_1.CookingStep.create({
                            dish_id: dish.id,
                            step_number: j + 1,
                            title: steps[j].title,
                            description: steps[j].description,
                            image: steps[j].image,
                        });
                    }
                }
            }
            // 更新厨房菜品数量
            const dishes = yield models_1.Dish.findAll();
            logger_1.default.info(`共创建 ${dishes.length} 个测试菜品`);
            // 统计每个厨房的菜品数量
            const kitchenDishCounts = {};
            for (const dish of dishes) {
                if (!kitchenDishCounts[dish.kitchen_id]) {
                    kitchenDishCounts[dish.kitchen_id] = 0;
                }
                kitchenDishCounts[dish.kitchen_id]++;
            }
            // 更新每个厨房的菜品数量
            for (const kitchenId in kitchenDishCounts) {
                const kitchen = yield require('../../models').Kitchen.findByPk(kitchenId);
                if (kitchen) {
                    yield kitchen.update({ dish_count: kitchenDishCounts[kitchenId] });
                    logger_1.default.info(`更新厨房 ${kitchenId} 的菜品数量为 ${kitchenDishCounts[kitchenId]}`);
                }
            }
        }
        catch (error) {
            logger_1.default.error('创建测试菜品数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试菜品
if (require.main === module) {
    seedDishes()
        .then(() => {
        logger_1.default.info('创建测试菜品数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试菜品数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedDishes;
//# sourceMappingURL=seedDishes.js.map