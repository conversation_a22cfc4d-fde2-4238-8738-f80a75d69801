/**
 * 应用常量配置
 */

// 从配置中获取服务器基础URL
export const SERVER_BASE_URL = 'https://dzcdd.zj1.natnps.cn'; // 可通过环境变量或配置文件修改

/**
 * 默认图片配置
 */
export const DEFAULT_IMAGES = {
  // 用户默认头像
  USER_AVATAR: `${SERVER_BASE_URL}/uploads/default/tou1.jpg`,
  // 厨房默认头像
  KITCHEN_AVATAR: `${SERVER_BASE_URL}/uploads/default/chu1.jpg`,
  // 厨房默认背景
  KITCHEN_BACKGROUND: `${SERVER_BASE_URL}/uploads/default/bj6.jpg`,
  // 菜品默认图片
  DISH: `${SERVER_BASE_URL}/uploads/default/dish.jpg`,
  // 默认Logo
  LOGO: `${SERVER_BASE_URL}/uploads/default/logo.jpg`,
  // 微信二维码
  WECHAT_QR: `${SERVER_BASE_URL}/uploads/default/wx.jpg`
};

/**
 * 用户头像列表
 */
export const USER_AVATARS = [
  `${SERVER_BASE_URL}/uploads/default/tou1.jpg`,
  `${SERVER_BASE_URL}/uploads/default/tou2.jpg`,
  `${SERVER_BASE_URL}/uploads/default/tou3.jpg`,
  `${SERVER_BASE_URL}/uploads/default/tou4.jpg`,
  `${SERVER_BASE_URL}/uploads/default/tou5.jpg`,
  `${SERVER_BASE_URL}/uploads/default/tou6.jpg`
];

/**
 * 厨房头像列表
 */
export const KITCHEN_AVATARS = [
  `${SERVER_BASE_URL}/uploads/default/chu1.jpg`,
  `${SERVER_BASE_URL}/uploads/default/chu2.jpg`,
  `${SERVER_BASE_URL}/uploads/default/chu3.jpg`,
  `${SERVER_BASE_URL}/uploads/default/chu4.jpg`,
  `${SERVER_BASE_URL}/uploads/default/chu5.jpg`,
  `${SERVER_BASE_URL}/uploads/default/chu6.jpg`
];

/**
 * 厨房背景图片列表
 */
export const KITCHEN_BACKGROUNDS = [
  `${SERVER_BASE_URL}/uploads/default/bj1.jpg`,
  `${SERVER_BASE_URL}/uploads/default/bj2.jpg`,
  `${SERVER_BASE_URL}/uploads/default/bj3.jpg`,
  `${SERVER_BASE_URL}/uploads/default/bj4.jpg`,
  `${SERVER_BASE_URL}/uploads/default/bj5.jpg`,
  `${SERVER_BASE_URL}/uploads/default/bj6.jpg`
];

/**
 * 获取随机用户头像
 */
export const getRandomUserAvatar = (): string => {
  const randomIndex = Math.floor(Math.random() * USER_AVATARS.length);
  return USER_AVATARS[randomIndex];
};

/**
 * 获取随机厨房头像
 */
export const getRandomKitchenAvatar = (): string => {
  const randomIndex = Math.floor(Math.random() * KITCHEN_AVATARS.length);
  return KITCHEN_AVATARS[randomIndex];
};

/**
 * 获取随机厨房背景
 */
export const getRandomKitchenBackground = (): string => {
  const randomIndex = Math.floor(Math.random() * KITCHEN_BACKGROUNDS.length);
  return KITCHEN_BACKGROUNDS[randomIndex];
};

/**
 * 通用配置
 */
export const COMMON_CONFIG = {
  BASE_URL: SERVER_BASE_URL,
  // 添加其他通用配置...
};

/**
 * 图片URL处理函数
 * @param relativePath 相对路径或完整URL
 * @returns 完整URL
 */
export const getImageUrl = (relativePath: string): string => {
  if (!relativePath) return '';
  
  // 如果已经是完整URL，直接返回
  if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
    return relativePath;
  }
  
  // 如果是相对路径，拼接基础URL
  const path = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;
  return `${SERVER_BASE_URL}${path}`;
};

// API相关常量
export const API_CONFIG = {
  BASE_URL: SERVER_BASE_URL,
  TIMEOUT: 10000
};

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  LAST_SELECTED_KITCHEN: 'last_selected_kitchen',
  SHOP_BG: 'shopBg',
  CURRENT_KITCHEN_BG: 'current_kitchen_bg',
  NAV_BG_STYLE: 'navBgStyle',
  NAV_BG_INDEX: 'navBgIndex'
};

export default {
  SERVER_BASE_URL,
  DEFAULT_IMAGES,
  USER_AVATARS,
  KITCHEN_AVATARS,
  KITCHEN_BACKGROUNDS,
  getRandomUserAvatar,
  getRandomKitchenAvatar,
  getRandomKitchenBackground,
  getImageUrl,
  API_CONFIG,
  STORAGE_KEYS
}; 