/* 提交订单页面样式 */
.submit-order-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F9F9F9;
  position: relative;
}

/* 顶部信息区域 */
.top-info-area {
  position: relative;
  width: 100%;
  height: 220rpx;
  overflow: hidden;
}

.bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.7;
  filter: brightness(0.7) blur(1px);
}

/* 店铺信息样式 */
.shop-info {
  position: absolute;
  display: flex;
  align-items: center;
  z-index: 10;
  left: 20rpx;
  padding-right: 20rpx;
  border-radius: 8rpx;
  background-color: rgba(0, 0, 0, 0.2);
}

.shop-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

.shop-name {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 订单数量指示器 */
.order-count {
  display: flex;
  align-items: center;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  margin-left: 20rpx;
}

.count-text {
  font-size: 24rpx;
  color: #666666;
}

.price-text {
  font-size: 34rpx;
  font-weight: bold;
}

/* 订单信息区域 */
.order-info-area {
  margin: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.order-info-item:last-child {
  border-bottom: none;
}

.remark-item {
  align-items: flex-start;
}

.item-title {
  display: flex;
  align-items: center;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 32rpx;
  height: 32rpx;
  object-fit: contain;
}

.item-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.item-value {
  flex: 1;
  text-align: right;
  color: #666666;
  font-size: 28rpx;
  display: flex;
  justify-content: flex-end;
  padding-left: 20rpx;
}

.table-picker {
  display: flex;
  align-items: center;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}

.remark-input-area {
  flex: 1;
}

.remark-input {
  width: 100%;
  text-align: right;
  color: #666666;
}

/* 订单商品区域 */
.order-items-area {
  margin: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.area-title {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.order-items-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.order-item {
  display: flex;
  padding: 20rpx;
  align-items: center;
  border-bottom: 1rpx solid #F5F5F5;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  margin: 0 20rpx;
  overflow: hidden;
}

.item-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
}

.item-tag {
  font-size: 22rpx;
  color: #999999;
  background-color: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.item-price {
  font-size: 30rpx;
  color: #FF6B35;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.item-count {
  font-size: 26rpx;
  color: #999999;
}

/* 订单总价区域 */
.order-total-area {
  margin: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.total-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.total-label {
  font-size: 28rpx;
  color: #666666;
}

.total-value {
  font-size: 28rpx;
  color: #666666;
}

.total-divider {
  height: 1rpx;
  background-color: #EEEEEE;
  margin: 10rpx 0;
}

.highlight .total-label {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.highlight .total-value {
  font-size: 34rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 底部提交按钮 */
.submit-action-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding: 20rpx 0;
  box-sizing: border-box;
}

.submit-action-inner {
  display: flex;
  width: 96%;
  margin: 0 auto;
  justify-content: space-between;
}

.cancel-btn, .confirm-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-sizing: border-box;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.confirm-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
}

/* 确认下单弹窗内容 */
.confirm-dialog-content {
  padding: 20rpx 0;
}

.confirm-dialog-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.confirm-label {
  font-size: 28rpx;
  color: #666666;
}

.confirm-value {
  font-size: 28rpx;
  color: #333333;
}

.confirm-value.highlight {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: bold;
}

.dialog-tips {
  margin-top: 30rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999999;
}

/* 自定义成功弹窗样式 */
.custom-success-dialog {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: modalFadeIn 0.3s ease-out forwards;
}

.custom-modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  animation: maskFadeIn 0.3s ease-out forwards;
}

.custom-modal-content {
  position: relative;
  width: 90%;
  max-width: 650rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  opacity: 0;
  animation: smoothSlideIn 0.3s ease-out forwards;
}

.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.custom-modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  text-align: center;
}

.custom-close-btn {
  font-size: 30rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  position: absolute;
  right: 20rpx;
  top: 20rpx;
}

.custom-close-btn:active {
  background-color: #F5F5F5;
}

.custom-modal-body {
  padding: 30rpx;
  flex: 1;
  overflow: auto;
  opacity: 0;
  animation: contentFadeIn 0.3s ease-out 0.1s forwards;
}

.custom-modal-footer {
  display: flex;
  justify-content: center;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.custom-share-btn, .custom-confirm-btn {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border-radius: 32rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  border: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.custom-share-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.custom-share-btn::after {
  display: none;
}

.custom-confirm-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
}

.custom-share-btn:active, .custom-confirm-btn:active {
  transform: scale(0.95);
}

/* 弹窗动画 */
@keyframes modalFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes maskFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes smoothSlideIn {
  0% {
    transform: scale(0.95) translateY(20rpx);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 支付成功弹窗内容 */
.success-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.success-icon image {
  width: 80rpx;
  height: 80rpx;
  object-fit: contain;
}

.success-text {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.success-tips {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 桌号选择弹窗样式 */
.table-dialog-content {
  padding: 30rpx 0;
}

.table-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.table-option {
  width: 28%;
  height: 80rpx;
  margin-bottom: 20rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333333;
  transition: all 0.2s ease;
}

.table-option.selected {
  background-color: #FF6B35;
  color: #FFFFFF;
  font-weight: bold;
}

/* 模态框按钮优化 */
.modal-dialog .modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.modal-dialog .modal-footer button {
  font-size: 28rpx;
  padding: 12rpx 40rpx;
  border-radius: 40rpx;
  margin: 0;
  line-height: 1.5;
  min-height: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none !important;
  background-color: transparent;
  box-sizing: border-box;
}

.modal-dialog .modal-footer button::after {
  display: none !important;
}

.modal-dialog .cancel-btn {
  background-color: #F5F5F5 !important;
  color: #666666 !important;
}

.modal-dialog .confirm-btn {
  background-color: #FF6B35 !important;
  color: #FFFFFF !important;
} 