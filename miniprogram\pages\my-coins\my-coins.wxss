/* 我的大米页面样式 */
.coins-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  padding-bottom: 40rpx;
}

.content {
  padding: 20rpx;
}

/* 大米信息卡片 */
.coins-card {
  background: linear-gradient(to bottom, #FF6B35, #FF9066);
  color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 53, 0.2);
  position: relative;
  overflow: hidden;
}

.coins-card:after {
  content: '';
  position: absolute;
  bottom: -30rpx;
  right: -30rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.coins-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 15rpx;
  position: relative;
  z-index: 2;
  text-align: center;
}

.coins-balance {
  font-size: 80rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
  text-align: center;
}

.coins-stats {
  display: flex;
  border-top: 1rpx solid rgba(255, 255, 255, 0.3);
  padding-top: 25rpx;
  position: relative;
  z-index: 2;
}

.stats-item {
  flex: 1;
  position: relative;
  text-align: center;
}

.stats-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.stats-value {
  font-size: 36rpx;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  margin-top: 30rpx;
  position: relative;
  z-index: 2;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin: 0 15rpx;
}

.action-btn:first-child {
  margin-left: 0;
}

.action-btn:last-child {
  margin-right: 0;
}

.recharge-btn {
  background-color: #FFFFFF;
  color: #FF6B35;
}

.detail-btn {
  background-color: rgba(255, 255, 255, 0.3);
  color: #FFFFFF;
}

/* 赚取大米部分 */
.earn-coins-section, .usage-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.earn-method-card {
  display: flex;
  align-items: center;
  padding: 30rpx 25rpx;
  background-color: #F9F9F9;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.earn-method-card:last-child {
  margin-bottom: 0;
}

.earn-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: transparent;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.earn-icon image {
  width: 48rpx;
  height: 48rpx;
  object-fit: contain;
}

.earn-content {
  flex: 1;
}

.earn-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}

.earn-desc {
  font-size: 26rpx;
  color: #999999;
}

.earn-arrow {
  font-size: 28rpx;
  color: #CCCCCC;
  padding: 0 10rpx;
}

/* 大米用途部分 */
.usage-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.usage-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  height: 180rpx;
  background-color: #F9F9F9;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.usage-card:after {
  content: '';
  position: absolute;
  bottom: -40rpx;
  right: -40rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
}

.usage-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-icon image {
  width: 56rpx;
  height: 56rpx;
  object-fit: contain;
}

.usage-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.usage-desc {
  font-size: 22rpx;
  color: #999999;
  text-align: center;
  padding: 0 20rpx;
}

/* 大米使用说明 */
.instruction-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  text-align: center;
}

.instruction-list {
  padding: 20rpx 0;
}

.instruction-item {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.8;
  margin-bottom: 15rpx;
  position: relative;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

/* 充值弹窗样式 */
.recharge-form {
  padding: 20rpx 0;
}

/* 大米兑换率提示 */
.exchange-rate-tip {
  text-align: center;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx dashed #EEEEEE;
}

.recharge-options {
  display: flex;
  margin-bottom: 20rpx;
}

.recharge-option {
  flex: 1;
  height: 140rpx;
  border: 2rpx solid #EEEEEE;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  transition: all 0.3s;
  margin: 0 10rpx;
}

.recharge-option:first-child {
  margin-left: 0;
}

.recharge-option:last-child {
  margin-right: 0;
}

.recharge-option.selected {
  border-color: #FF6B35;
  background-color: #FFF8F5;
}

.option-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.option-price {
  font-size: 26rpx;
  color: #999999;
}

.option-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FF6B35;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 0 16rpx 0 16rpx;
}

.payment-methods {
  margin-top: 30rpx;
}

.payment-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 20rpx 25rpx;
  background-color: #F9F9F9;
  border-radius: 16rpx;
}

.method-icon {
  margin-right: 20rpx;
  font-size: 40rpx;
}

.method-name {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.method-check {
  color: #FF6B35;
  font-weight: bold;
}

/* 明细弹窗样式 */
.detail-content {
  height: 600rpx;
  display: flex;
  flex-direction: column;
}

.detail-tabs {
  display: flex;
  border-bottom: 2rpx solid #EEEEEE;
  margin-bottom: 20rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #FF6B35;
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF6B35;
  border-radius: 2rpx;
}

.detail-list {
  flex: 1;
  padding: 0 10rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.detail-left {
  flex: 1;
}

.detail-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.detail-time {
  font-size: 24rpx;
  color: #999999;
}

.detail-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.detail-amount.income {
  color: #FF6B35;
}

.detail-amount.expense {
  color: #4CAF50;
}

.empty-records {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease forwards;
}

/* 卡片点击效果 */
.earn-method-card, .usage-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.earn-method-card:active, .usage-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 自定义金额样式 */
.custom-amount-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx dashed #EEEEEE;
}

.custom-amount-title {
  text-align: center;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.custom-amount-input-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F8F9FA;
  border: 2rpx solid #EEEEEE;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  transition: border-color 0.3s;
}

.custom-amount-input-wrapper:focus-within {
  border-color: #FF6B35;
  background-color: #FFF8F5;
}

.currency-symbol {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 10rpx;
}

.custom-amount-input {
  flex: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  background: transparent;
  border: none;
  outline: none;
}

.amount-unit {
  font-size: 28rpx;
  color: #666666;
  margin-left: 10rpx;
}

.custom-amount-tip {
  text-align: center;
  font-size: 22rpx;
  color: #999999;
  line-height: 1.4;
}