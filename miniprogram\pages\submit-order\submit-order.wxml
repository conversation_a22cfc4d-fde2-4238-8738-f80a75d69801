<!-- 提交订单页面 -->
<view class="submit-order-container">
  <!-- 顶部信息区域 -->
  <view class="top-info-area">
    <smart-image 
      class="bg-image" 
      src="{{shopBg}}" 
      mode="aspectFill"
      width="100%"
      height="100%"
      border-radius="0"
      show-animation="{{false}}"
    />
    
    <!-- 店铺信息 -->
    <view class="shop-info" style="top: {{statusBarHeight}}px;">
      <smart-image 
        class="shop-logo" 
        src="{{restaurantInfo.logo || defaultImages.KITCHEN_AVATAR}}" 
        mode="aspectFill"
        width="100%"
        height="100%"
        border-radius="50"
      />
      <text class="shop-name">{{restaurantInfo.name}}</text>
    </view>
  </view>

  <!-- 订单信息区域 -->
  <view class="order-info-area">
    <!-- 桌号选择 -->
    <view class="order-info-item">
      <view class="item-title">
        <image class="item-icon" src="/static/images/icons/table.png" mode="aspectFit"></image>
        <text class="item-name">桌号</text>
      </view>
      <view class="item-value" bindtap="showTablePicker">
        <view class="table-picker">
          <text>{{tableIndex >= 0 ? tables[tableIndex].name : '未选择'}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </view>
    </view>
    
    <!-- 备注输入 -->
    <view class="order-info-item remark-item">
      <view class="item-title">
        <image class="item-icon" src="/static/images/icons/task1.png" mode="aspectFit"></image>
        <text class="item-name">备注</text>
      </view>
      <view class="item-value remark-input-area">
        <input class="remark-input" placeholder="口味、偏好等要求" bindinput="onRemarkInput" value="{{remark}}" maxlength="200" />
      </view>
    </view>
  </view>

  <!-- 订单商品区域 -->
  <view class="order-items-area">
    <view class="area-title">
      <text class="title-text">已选商品</text>
      <text class="count-text">共 {{totalItems}} 个菜</text>
    </view>
    
    <view class="order-items-list">
      <view class="order-item" wx:for="{{orderItems}}" wx:key="id">
        <smart-image 
          class="item-image" 
          src="{{item.image}}" 
          mode="aspectFill"
          width="100%"
          height="100%"
          border-radius="8"
        />
        <view class="item-info">
          <text class="item-name text-ellipsis">{{item.name}}</text>
          <view class="item-tags">
            <text wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this" class="item-tag">{{tag}}</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-price">{{item.price}} 大米</text>
          <view class="item-count">x{{item.count}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单总价区域 -->
  <view class="order-total-area">
    <view class="total-item">
      <text class="total-label">商品总价</text>
      <text class="total-value">{{totalPrice}} 大米</text>
    </view>
    <view class="total-divider"></view>
    <view class="total-item highlight">
      <text class="total-label">实付金额</text>
      <text class="total-value">{{totalPrice}} 大米</text>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="submit-action-area">
    <view class="submit-action-inner">
      <view class="cancel-btn" bindtap="onCancel">取消</view>
      <view class="confirm-btn" bindtap="onConfirmOrder">确认下单</view>
    </view>
  </view>

  <!-- 确认下单弹窗 -->
  <modal-dialog 
    visible="{{showConfirmDialog}}"
    title="确认下单"
    confirmText="确认支付"
    cancelText="取消"
    bind:close="onCloseConfirmDialog"
    bind:cancel="onCloseConfirmDialog"
    bind:confirm="submitOrder"
  >
    <view class="confirm-dialog-content">
      <view class="confirm-dialog-item">
        <text class="confirm-label">餐桌号码</text>
        <text class="confirm-value">{{tableIndex >= 0 ? tables[tableIndex].name : '未选择'}}</text>
      </view>
      <view class="confirm-dialog-item" wx:if="{{remark}}">
        <text class="confirm-label">备注信息</text>
        <text class="confirm-value">{{remark}}</text>
      </view>
      <view class="confirm-dialog-item">
        <text class="confirm-label">订单金额</text>
        <text class="confirm-value highlight">{{totalPrice}} 大米</text>
      </view>
      <view class="dialog-tips">
        <text class="tips-text">确认支付将从您的账户中扣除相应金额</text>
      </view>
    </view>
  </modal-dialog>

  <!-- 取消下单确认弹窗 -->
  <confirm-dialog
    visible="{{showCancelDialog}}"
    title="取消下单"
    content="确定要取消下单吗？"
    cancelText="再想想"
    confirmText="确定取消"
    confirmColor="#FF6B35"
    bind:cancel="onCloseCancelDialog"
    bind:confirm="onConfirmCancel"
  />

  <!-- 支付成功弹窗 -->
  <view class="custom-success-dialog" wx:if="{{showSuccessDialog}}">
    <view class="custom-modal-mask" bindtap="onOrderSuccess"></view>
    <view class="custom-modal-content">
      <view class="custom-modal-header">
        <text class="custom-modal-title">下单成功</text>
        <view class="custom-close-btn" bindtap="onOrderSuccess">×</view>
      </view>
      <view class="custom-modal-body">
    <view class="success-dialog-content">
      <view class="success-icon">
        <image src="/static/images/icons/check1.png" mode="aspectFit"></image>
      </view>
      <view class="success-text">订单提交成功</view>
      <view class="success-tips">分享给厨师准备美味大餐吧！</view>
    </view>
      </view>
      <view class="custom-modal-footer">
        <button class="custom-share-btn" open-type="share">分享给厨师</button>
        <view class="custom-confirm-btn" bindtap="onViewOrder">查看订单</view>
      </view>
    </view>
  </view>

  <!-- 桌号选择弹窗 -->
  <modal-dialog 
    visible="{{showTableDialog}}"
    title="选择桌号"
    confirmText="确定"
    cancelText="取消"
    bind:close="onCloseTableDialog"
    bind:cancel="onCloseTableDialog"
    bind:confirm="onConfirmTableSelection"
  >
    <view class="table-dialog-content">
      <view class="table-options">
        <view 
          wx:for="{{tables}}" 
          wx:key="id" 
          class="table-option {{tempTableIndex === index ? 'selected' : ''}}"
          data-index="{{index}}"
          bindtap="onSelectTable"
        >
          {{item.name}}
        </view>
      </view>
    </view>
  </modal-dialog>
</view> 