"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 数据库初始化脚本
 * 用于创建数据库并设置字符集和排序规则
 */
const promise_1 = __importDefault(require("mysql2/promise"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
/**
 * 初始化数据库
 * 创建数据库（如果不存在）并设置字符集和排序规则
 */
function initDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始初始化数据库...');
        // 创建不带数据库名的连接
        const connection = yield promise_1.default.createConnection({
            host: config_1.default.database.host,
            port: config_1.default.database.port,
            user: config_1.default.database.user,
            password: config_1.default.database.password,
        });
        try {
            // 创建数据库（如果不存在）
            logger_1.default.info(`正在创建数据库 ${config_1.default.database.name}...`);
            yield connection.query(`CREATE DATABASE IF NOT EXISTS \`${config_1.default.database.name}\`
       CHARACTER SET utf8mb4
       COLLATE utf8mb4_unicode_ci`);
            logger_1.default.info(`数据库 ${config_1.default.database.name} 创建成功或已存在`);
            // 使用数据库
            yield connection.query(`USE \`${config_1.default.database.name}\``);
            // 设置数据库参数
            logger_1.default.info('正在设置数据库参数...');
            yield connection.query('SET NAMES utf8mb4');
            yield connection.query('SET CHARACTER SET utf8mb4');
            yield connection.query('SET character_set_connection=utf8mb4');
            logger_1.default.info('数据库初始化完成');
        }
        catch (error) {
            logger_1.default.error('数据库初始化失败:', error);
            throw error;
        }
        finally {
            yield connection.end();
        }
    });
}
// 如果直接运行此脚本，则执行初始化
if (require.main === module) {
    initDatabase()
        .then(() => {
        logger_1.default.info('数据库初始化脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('数据库初始化脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = initDatabase;
//# sourceMappingURL=initDatabase.js.map