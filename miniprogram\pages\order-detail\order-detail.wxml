<!-- 订单详情页 -->
<view class="container">
  <!-- 使用自定义导航栏组件 -->
  <custom-navbar
    title="订单详情"
    showBack="{{true}}"
    navBgStyle="{{navBgStyle}}"
    useShadow="{{true}}"
    customBack="{{true}}"
    bind:back="onCustomBack"
  />

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主内容区域 -->
  <view class="content-container" wx:else>
    <!-- 顶部状态区域 -->
    <view class="status-container {{orderDetail.status}}">
      <view class="status-icon">
        <text class="icon-text" wx:if="{{orderDetail.status === 'pending'}}">⏱️</text>
        <text class="icon-text" wx:elif="{{orderDetail.status === 'accepted'}}">👨‍🍳</text>
        <text class="icon-text" wx:elif="{{orderDetail.status === 'cooking'}}">🔥</text>
        <text class="icon-text" wx:elif="{{orderDetail.status === 'completed'}}">✅</text>
        <text class="icon-text" wx:elif="{{orderDetail.status === 'canceled' || orderDetail.status === 'cancelled'}}">❌</text>
      </view>
      <view class="status-text">
        <text class="status-title">{{statusText}}</text>
        <text class="status-desc">{{statusDesc}}</text>
      </view>
    </view>

    <!-- 订单进度 -->
    <view class="progress-container">
      <view class="progress-steps">
        <view class="step-item {{stepClasses.pending}}">
          <view class="step-dot"></view>
          <view class="step-line"></view>
          <view class="step-content">
            <view class="step-title">下单成功</view>
            <view class="step-time">{{orderDetail.createTime}}</view>
          </view>
        </view>
        <view class="step-item {{stepClasses.cooking}}">
          <view class="step-dot"></view>
          <view class="step-line"></view>
          <view class="step-content">
            <view class="step-title">开始烹饪</view>
            <view class="step-time">{{cookingTimeText}}</view>
          </view>
        </view>
        <view class="step-item {{stepClasses.completed}}">
          <view class="step-dot"></view>
          <view class="step-content">
            <view class="step-title">烹饪完成</view>
            <view class="step-time">{{completedTimeText}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <text class="info-label">订单号</text>
        <text class="info-value">{{orderDetail.orderId}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">桌号</text>
        <text class="info-value">{{orderDetail.tableNo}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{orderDetail.createTime}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">备注</text>
        <text class="info-value remark-text">{{orderDetail.remark || '无'}}</text>
      </view>
    </view>

    <!-- 菜品列表 -->
    <view class="dish-list-card">
      <view class="card-title">菜品列表</view>
      <view class="dish-list">
        <view class="dish-item" wx:for="{{orderDetail.items}}" wx:key="dishId">
          <image class="dish-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="dish-info">
            <view class="dish-name">{{item.name}}</view>
            <view class="dish-tags">
              <view class="dish-tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">{{tag}}</view>
            </view>
          </view>
          <view class="dish-price-count">
            <view class="dish-price">{{item.price}} 大米</view>
            <view class="dish-count">x{{item.count}}</view>
          </view>
        </view>
      </view>
      <view class="order-total">
        <text>共 {{totalCount}} 件商品，合计：</text>
        <text class="total-price">{{orderDetail.totalPrice}} 大米</text>
      </view>
    </view>

    <!-- 底部占位空间 -->
    <view class="bottom-placeholder"></view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions" wx:if="{{!isHistoryOrder && canManageOrder}}">
    <!-- 厨房订单操作（厨房主权限优先） -->
    <block wx:if="{{isKitchenOwner}}">
      <!-- 待接单状态 -->
      <block wx:if="{{orderDetail.status === 'pending'}}">
        <button class="action-btn reject-btn" bindtap="onRejectOrder">拒绝</button>
        <button class="action-btn accept-btn" bindtap="onAcceptOrder">接单</button>
      </block>
      <!-- 已接单状态 -->
      <block wx:if="{{orderDetail.status === 'accepted'}}">
        <button class="action-btn cancel-btn" bindtap="onCancelAcceptedOrder">取消接单</button>
        <button class="action-btn complete-btn" bindtap="onStartCooking">开始烹饪</button>
      </block>
      <!-- 烹饪中状态 -->
      <block wx:if="{{orderDetail.status === 'cooking'}}">
        <button class="action-btn cancel-btn" bindtap="onCancelCooking">取消烹饪</button>
        <button class="action-btn complete-btn" bindtap="onCompleteOrder">完成订单</button>
      </block>
    </block>

    <!-- 我的订单操作（仅在非厨房主时显示） -->
    <block wx:elif="{{isOrderOwner}}">
      <button class="action-btn cancel-btn" wx:if="{{orderDetail.status === 'pending'}}" bindtap="onCancelOrder">取消订单</button>
    </block>
  </view>

  <!-- 确认对话框 -->
  <confirm-dialog
    visible="{{showConfirmDialog}}"
    title="{{confirmDialogTitle}}"
    content="{{confirmDialogContent}}"
    cancelText="取消"
    confirmText="{{confirmDialogConfirmText}}"
    confirmColor="#FF6B35"
    bind:cancel="onConfirmDialogCancel"
    bind:confirm="onConfirmDialogConfirm"
  />
</view>
