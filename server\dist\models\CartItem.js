"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 购物车项模型
 * 存储用户购物车中的菜品
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 购物车项模型类
class CartItem extends sequelize_1.Model {
}
// 初始化购物车项模型
CartItem.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '购物车项ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    kitchen_id: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '厨房ID',
        references: {
            model: 'kitchens',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '数量',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'CartItem',
    tableName: 'cart_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_kitchen_id',
            fields: ['kitchen_id'],
        },
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_user_dish',
            unique: true,
            fields: ['user_id', 'dish_id'],
        },
        {
            name: 'idx_user_kitchen',
            fields: ['user_id', 'kitchen_id'],
        },
    ],
});
exports.default = CartItem;
//# sourceMappingURL=CartItem.js.map