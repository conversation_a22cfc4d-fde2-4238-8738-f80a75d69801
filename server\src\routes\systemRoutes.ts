/**
 * 系统设置路由
 */
import { Router } from 'express';
import systemSettingController from '../controllers/systemSettingController';
import { verifyAdminToken } from '../middlewares/auth';

const router = Router();

// 获取评论功能开关状态（公开接口）
router.get('/comment-enabled', systemSettingController.isCommentEnabled);

// 以下接口需要管理员权限
router.use(verifyAdminToken);

// 获取所有系统设置
router.get('/settings', systemSettingController.getAllSettings);

// 获取指定设置
router.get('/setting/:key', systemSettingController.getSetting);

// 更新系统设置
router.post('/setting', systemSettingController.updateSetting);

export default router; 