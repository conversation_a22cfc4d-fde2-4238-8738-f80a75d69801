import { getOrderList, cancelOrder, rejectOrder, acceptOrder, startCooking, completeOrder, cancelCooking, cancelAcceptedOrder } from '../../api/orderApi'
import { formatDateTime } from '../../utils/dateFormat'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { checkLogin } from '../../utils/util'

interface TabEvent {
  currentTarget: {
    dataset: {
      tab: string;
    }
  }
}

interface StatusEvent {
  currentTarget: {
    dataset: {
      status: string;
    }
  }
}

interface OrderIdEvent {
  currentTarget: {
    dataset: {
      orderId: string;
    }
  },
  stopPropagation?: () => void; // 添加stopPropagation可选属性
}

// 订单数据接口
interface OrderItem {
  orderId: string;
  createTime: string;
  status: string;
  totalPrice: number;
  tableNo: string;
  itemCount: number;
  userInfo: {
    nickName: string;
    avatarUrl: string;
  };
  kitchenName: string;
  remark: string;
  items: any[];
  dishes: any[];
  shopInfo: {
    name: string;
    avatar: string;
  };
  [key: string]: any; // 添加索引签名允许添加更多字段
}

Page({
  // 页面数据
  data: {
    statusBarHeight: 0,
    navHeight: 44,
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 默认使用第一个预设渐变色
    activeTab: 'kitchen', // 当前选中的Tab: kitchen-厨房订单, mine-我的订单
    status: 'all', // 订单状态筛选: all-全部, pending-待接单, accepted-已接单, cooking-烹饪中, completed-已完成, cancelled-已取消
    orderList: [] as OrderItem[], // 订单列表
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否有更多数据
    refreshing: false, // 是否正在下拉刷新
    smoothUpdating: false, // 是否正在平滑更新

    // 确认对话框
    showConfirmDialog: false,
    confirmDialogTitle: '',
    confirmDialogContent: '',
    confirmDialogConfirmText: '确定',
    confirmDialogAction: '', // 确认后执行的操作
    currentOrderId: '', // 当前操作的订单ID

    // 厨房信息
    currentKitchenId: '', // 当前选中的厨房ID

    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
  },

  // 生命周期函数--监听页面加载
  onLoad() {
    // 获取状态栏高度
    this.getStatusBarHeight();

    // 加载背景设置
    this.loadBackgroundSettings();

    // 检查当前厨房ID
    this.checkCurrentKitchen();

    // 加载订单列表
    this.loadOrderList(false);

    // 注册主题变更监听器
    const app = getApp<IAppOption>();
    app.addThemeChangeListener(this.onThemeChanged.bind(this));
  },

  // 生命周期函数--监听页面显示
  onShow() {
    // 首先检查当前厨房ID是否变更
    const previousKitchenId = this.data.currentKitchenId;
    this.checkCurrentKitchen();
    
    // 只有在厨房ID真正发生变化，或者是首次加载时才刷新
    const currentKitchenId = this.data.currentKitchenId;
    
    // 如果厨房ID变化了，checkCurrentKitchen已经处理了数据重新加载
    if (previousKitchenId !== currentKitchenId) {
      console.log('厨房ID发生变化，已重新加载数据');
      return;
    }
    
    // 如果没有订单数据，则进行首次加载
    if (this.data.orderList.length === 0) {
      console.log('首次加载订单数据');
      this.loadOrderList(false);
      return;
    }
    
    // 如果有订单数据且厨房ID没有变化，进行静默刷新
    // 这样可以获取最新状态但不会影响用户浏览
    console.log('进行静默刷新');
    this.silentRefresh();
  },

  // 生命周期函数--页面卸载
  onUnload() {
    // 移除主题变更监听器
    const app = getApp<IAppOption>();
    app.removeThemeChangeListener(this.onThemeChanged.bind(this));
  },

  // 检查当前选中的厨房
  checkCurrentKitchen() {
    const lastSelectedKitchen = wx.getStorageSync('last_selected_kitchen');

    // 无论当前标签是什么，都要检查厨房ID变化
    if (lastSelectedKitchen && this.data.currentKitchenId !== lastSelectedKitchen) {
      console.log('检测到厨房ID变化:', this.data.currentKitchenId, '->', lastSelectedKitchen);
      
      this.setData({
        currentKitchenId: lastSelectedKitchen,
        orderList: [], // 清空现有订单列表
        page: 1, // 重置页码
        hasMore: true // 重置加载状态
      });

      // 如果当前是厨房订单标签，需要重新加载数据
      if (this.data.activeTab === 'kitchen') {
        // 延迟加载，确保状态已更新
        setTimeout(() => {
          this.loadOrderList(false);
        }, 100);
      }
    } else if (!this.data.currentKitchenId && lastSelectedKitchen) {
      // 首次加载，设置当前厨房ID
      console.log('首次设置厨房ID:', lastSelectedKitchen);
      this.setData({
        currentKitchenId: lastSelectedKitchen
      });
    }
  },

  // 主题变更处理函数
  onThemeChanged(settings: any) {
    // 更新页面数据
    this.setData({
      navBgStyle: settings.navBgStyle
    });
  },

  // 获取状态栏高度
  getStatusBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navHeight: 44  // 导航栏固定高度
    });
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';

    this.setData({
      navBgStyle
    });
  },

  // 页面相关事件处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.setData({
      refreshing: true
    });
    this.refreshOrderList();
  },

  // 刷新订单列表
  refreshOrderList() {
    // 不改变滚动位置，只替换数据
    this.setData({
      page: 1,
      hasMore: true,
      orderList: []
    });

    this.loadOrderList().finally(() => {
      this.setData({
        refreshing: false
      });
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    });
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.refreshing) {
      this.loadMoreOrders();
    }
  },

  // 加载更多订单
  async loadMoreOrders() {
    if (!this.data.hasMore || this.data.refreshing) return;

    this.setData({
      page: this.data.page + 1
    });

    try {
      await this.loadOrderList(true);
    } catch (err) {
      console.error('加载更多失败', err);
    }
  },

  // 切换订单类型Tab
  switchTab(e: TabEvent) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.activeTab) return;

    // 切换标签并更新数据
    this.setData({
      activeTab: tab
    });

    // 如果切换到厨房订单标签，强制检查和更新厨房ID
    if (tab === 'kitchen') {
      const lastSelectedKitchen = wx.getStorageSync('last_selected_kitchen');
      console.log('切换到厨房订单，当前厨房ID:', this.data.currentKitchenId, '存储中的厨房ID:', lastSelectedKitchen);
      
      // 强制清空列表并重置状态
      this.setData({
        orderList: [],
        page: 1,
        hasMore: true
      });

      // 如果厨房ID不一致，更新厨房ID
      if (lastSelectedKitchen && this.data.currentKitchenId !== lastSelectedKitchen) {
        this.setData({
          currentKitchenId: lastSelectedKitchen
        });
      }
    } else {
      // 切换到我的订单时，也清空列表
      this.setData({
        orderList: [],
        page: 1,
        hasMore: true
      });
    }

    // 使用平滑更新方式更新订单列表，与切换状态标签保持一致
    this.smoothUpdateOrderList();
  },

  // 切换状态筛选
  switchStatus(e: StatusEvent) {
    const status = e.currentTarget.dataset.status;
    if (status !== this.data.status) {
      // 先更新状态，不清空列表，保持用户当前位置
      this.setData({
        status: status
      });

      // 使用平滑更新方式，不显示loading
      this.smoothUpdateOrderList();
    }
  },

  // 平滑更新订单列表（不重置滚动位置）
  async smoothUpdateOrderList() {
    try {
      // 设置平滑更新状态
      this.setData({
        smoothUpdating: true
      });

      // 重置页码但不清空现有列表
      this.setData({
        page: 1,
        hasMore: true
      });

      const params: any = {
        status: this.data.status,
        orderType: this.data.activeTab,
        page: 1,
        pageSize: this.data.pageSize
      };

      // 只有在厨房订单标签下才添加厨房ID参数
      if (this.data.activeTab === 'kitchen' && this.data.currentKitchenId) {
        params.kitchenId = this.data.currentKitchenId;
      }

      const res = await getOrderList(params);

      if (res.error === 0) {
        // 将后端返回的数据格式转换为前端期望的格式
        const rawList = res.body.list || [];
        const convertedList = rawList.map((item: any) => ({
          orderId: item.id,
          createTime: formatDateTime(item.createdAt),
          status: item.status,
          totalPrice: item.totalPrice,
          tableNo: item.tableNo,
          itemCount: item.items && item.items.length ? item.items.length : 0,
          userInfo: {
            nickName: item.userName,
            avatarUrl: item.userAvatar || DEFAULT_IMAGES.USER_AVATAR
          },
          kitchenName: item.kitchenName,
          remark: item.remark,
          items: item.items || [],
          dishes: item.items ? item.items.map((dish: any) => ({
            dishId: dish.dishId,
            name: dish.name,
            image: dish.image,
            price: dish.price,
            count: dish.count
          })) : [],
          shopInfo: {
            name: item.kitchenName || '店铺',
            avatar: item.kitchenAvatar || DEFAULT_IMAGES.KITCHEN_AVATAR
          }
        }));

        // 平滑更新列表内容
        this.setData({
          orderList: convertedList,
          hasMore: this.data.page < res.body.pages
        });

        // 预加载新订单图片
        this.preloadOrderImages(convertedList);
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none',
          duration: 1500
        });
      }
    } catch (err) {
      console.error('更新订单列表失败', err);
      wx.showToast({
        title: '更新失败',
        icon: 'none',
        duration: 1500
      });
    } finally {
      // 重置平滑更新状态
      this.setData({
        smoothUpdating: false
      });
    }
  },

  // 加载订单列表
  async loadOrderList(isLoadMore = false, silentMode = false): Promise<void> {
    try {
      const params: any = {
        status: this.data.status,
        orderType: this.data.activeTab,
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      // 只有在厨房订单标签下才添加厨房ID参数
      if (this.data.activeTab === 'kitchen' && this.data.currentKitchenId) {
        params.kitchenId = this.data.currentKitchenId;
      }

      const res = await getOrderList(params);

      if (res.error === 0) {
        // 将后端返回的数据格式转换为前端期望的格式
        const rawList = res.body.list || [];
        const convertedList = rawList.map((item: any) => ({
          orderId: item.id,                    // id -> orderId
          createTime: formatDateTime(item.createdAt),          // createdAt -> createTime 并格式化
          status: item.status,
          totalPrice: item.totalPrice,
          tableNo: item.tableNo,
          itemCount: item.items && item.items.length ? item.items.length : 0,  // 计算菜品数量
          userInfo: {
            nickName: item.userName,           // userName -> nickName
            avatarUrl: item.userAvatar || DEFAULT_IMAGES.USER_AVATAR  // 使用默认用户头像
          },
          kitchenName: item.kitchenName,
          remark: item.remark,
          items: item.items || [],
          // 添加真实的菜品数据（用于显示）
          dishes: item.items ? item.items.map((dish: any) => ({
            dishId: dish.dishId,
            name: dish.name,
            image: dish.image,
            price: dish.price,
            count: dish.count
          })) : [],
          // 添加店铺信息（用于我的订单标签页）
          shopInfo: {
            name: item.kitchenName || '店铺',   // 使用厨房名称作为店铺名称
            avatar: item.kitchenAvatar || DEFAULT_IMAGES.KITCHEN_AVATAR  // 使用默认厨房头像
          }
        }));

        const list = isLoadMore
          ? [...this.data.orderList, ...convertedList]
          : convertedList;

        this.setData({
          orderList: list,
          hasMore: this.data.page < res.body.pages
        });

        // 预加载新加载的订单图片
        this.preloadOrderImages(convertedList);

        // 返回成功
        return Promise.resolve();
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none',
          duration: 2000
        });

        // 返回失败
        return Promise.reject(new Error(res.message || '加载失败'));
      }
    } catch (err) {
      console.error('加载订单列表失败', err);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 返回失败
      return Promise.reject(err);
    }
  },

  // 跳转到订单详情
  goToOrderDetail(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}&orderType=${this.data.activeTab}`
    });
  },

  // 取消订单按钮点击
  onCancelOrder(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消订单',
      confirmDialogContent: '确定要取消该订单吗？',
      confirmDialogConfirmText: '确定',
      confirmDialogAction: 'cancelOrder',
      currentOrderId: orderId
    });
  },

  // 接单按钮点击
  onAcceptOrder(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '接单确认',
      confirmDialogContent: '接单后将开始烹饪，确定接单吗？',
      confirmDialogConfirmText: '确定接单',
      confirmDialogAction: 'acceptOrder',
      currentOrderId: orderId
    });
  },

  // 完成订单按钮点击
  onCompleteOrder(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '完成订单',
      confirmDialogContent: '确认该订单已完成吗？',
      confirmDialogConfirmText: '确认完成',
      confirmDialogAction: 'completeOrder',
      currentOrderId: orderId
    });
  },

  // 取消烹饪按钮点击
  onCancelCooking(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消烹饪',
      confirmDialogContent: '确定要取消烹饪吗？订单将回到待接单状态。',
      confirmDialogConfirmText: '确定取消',
      confirmDialogAction: 'cancelCooking',
      currentOrderId: orderId
    });
  },

  // 取消已接单订单按钮点击
  onCancelAcceptedOrder(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '取消接单',
      confirmDialogContent: '确定要取消接单吗？订单将回到待接单状态。',
      confirmDialogConfirmText: '确定取消',
      confirmDialogAction: 'cancelAcceptedOrder',
      currentOrderId: orderId
    });
  },

  // 拒绝订单按钮点击
  onRejectOrder(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '拒绝订单',
      confirmDialogContent: '确定要拒绝该订单吗？',
      confirmDialogConfirmText: '确定拒绝',
      confirmDialogAction: 'rejectOrder',
      currentOrderId: orderId
    });
  },

  // 开始烹饪按钮点击
  onStartCooking(e: OrderIdEvent) {
    const orderId = e.currentTarget.dataset.orderId;

    // 防止事件冒泡
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: '开始烹饪',
      confirmDialogContent: '确定要开始烹饪吗？',
      confirmDialogConfirmText: '开始烹饪',
      confirmDialogAction: 'startCooking',
      currentOrderId: orderId
    });
  },

  // 确认对话框取消
  onConfirmDialogCancel() {
    this.setData({
      showConfirmDialog: false
    });
  },

  // 确认对话框确认
  async onConfirmDialogConfirm() {
    // 关闭对话框
    this.setData({
      showConfirmDialog: false
    });

    const orderId = this.data.currentOrderId;
    const action = this.data.confirmDialogAction;

    try {
      let res;
      let successMsg = '操作成功';

      switch (action) {
        case 'cancelOrder':
          res = await cancelOrder(orderId, this.data.currentKitchenId);
          successMsg = '订单已取消';
          break;
        case 'acceptOrder':
          res = await acceptOrder(orderId, this.data.currentKitchenId);
          successMsg = '已接单';
          break;
        case 'completeOrder':
          res = await completeOrder(orderId, this.data.currentKitchenId);
          successMsg = '订单已完成';
          break;
        case 'cancelCooking':
          res = await cancelCooking(orderId, this.data.currentKitchenId);
          successMsg = '已取消烹饪';
          break;
        case 'cancelAcceptedOrder':
          res = await cancelAcceptedOrder(orderId, this.data.currentKitchenId);
          successMsg = '已取消接单';
          break;
        case 'startCooking':
          res = await startCooking(orderId, this.data.currentKitchenId);
          successMsg = '已开始烹饪';
          break;
        case 'rejectOrder':
          res = await rejectOrder(orderId, this.data.currentKitchenId);
          successMsg = '已拒绝订单';
          break;
        default:
          return;
      }

      if (res.error === 0) {
        // 不刷新整个列表，只更新当前订单状态
        this.updateOrderStatus(orderId, action);

        wx.showToast({
          title: successMsg,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('操作失败', err);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  // 更新订单状态，不刷新整个列表
  updateOrderStatus(orderId: string, action: string) {
    const orderList = [...this.data.orderList] as OrderItem[];
    const orderIndex = orderList.findIndex(order => order.orderId === orderId);

    if (orderIndex === -1) return;

    const newStatus = action === 'cancelOrder' ? 'cancelled' :
                     action === 'rejectOrder' ? 'cancelled' :
                     action === 'acceptOrder' ? 'accepted' :
                     action === 'startCooking' ? 'cooking' :
                     action === 'completeOrder' ? 'completed' :
                     action === 'cancelCooking' ? 'accepted' :
                     action === 'cancelAcceptedOrder' ? 'pending' :
                     orderList[orderIndex].status;

    // 深拷贝订单对象，确保不影响其他订单
    const updatedOrder = { ...orderList[orderIndex] };
    // 更新状态
    updatedOrder.status = newStatus;

    // 更新订单列表中的特定订单
    orderList[orderIndex] = updatedOrder;

    this.setData({
      orderList: orderList
    });
  },

  // 静默刷新订单列表，不显示loading提示
  silentRefresh() {
    this.setData({
      page: 1,
      hasMore: true
    });

    this.loadOrderList(false, true).finally(() => {
      // 停止下拉刷新动画（如果有）
      wx.stopPullDownRefresh();
    });
  },

  // 预加载订单图片
  preloadOrderImages(orderList: OrderItem[]) {
    // 图片预加载功能已移除
    console.log('图片预加载功能已移除');
  }
})