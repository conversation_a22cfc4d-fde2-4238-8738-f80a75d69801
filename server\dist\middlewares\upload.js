"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteFile = exports.getFileUrl = exports.uploadMultipleFiles = exports.uploadSingleFile = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("./error");
const imageCompressor_1 = require("../utils/imageCompressor");
const urlManager_1 = require("../utils/urlManager"); // 引入URL管理器
// 确保上传目录存在
const uploadDir = path_1.default.resolve(process.cwd(), config_1.default.upload.dir);
if (!fs_1.default.existsSync(uploadDir)) {
    fs_1.default.mkdirSync(uploadDir, { recursive: true });
}
// 创建子目录
const createSubDir = (subDir) => {
    const dir = path_1.default.join(uploadDir, subDir);
    if (!fs_1.default.existsSync(dir)) {
        fs_1.default.mkdirSync(dir, { recursive: true });
    }
    return dir;
};
// 创建各类型文件的子目录
createSubDir('dish'); // 菜品图片目录
createSubDir('avatar'); // 头像图片目录
createSubDir('background'); // 背景图片目录
createSubDir('category'); // 分类图片目录
createSubDir('kitchen'); // 厨房图片目录
// 临时存储配置（用于接收原始文件）
const tempStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        // 临时目录
        const tempDir = path_1.default.join(uploadDir, 'temp');
        if (!fs_1.default.existsSync(tempDir)) {
            fs_1.default.mkdirSync(tempDir, { recursive: true });
        }
        cb(null, tempDir);
    },
    filename: (req, file, cb) => {
        // 临时文件名
        const tempName = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${path_1.default.extname(file.originalname)}`;
        cb(null, tempName);
    },
});
// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 检查文件类型
    if (config_1.default.upload.allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new error_1.BusinessError(`不支持的文件类型: ${file.mimetype}，仅支持 ${config_1.default.upload.allowedTypes.join(', ')}`));
    }
};
// 创建multer实例
const upload = (0, multer_1.default)({
    storage: tempStorage,
    fileFilter,
    limits: {
        fileSize: config_1.default.upload.maxFileSize, // 文件大小限制
    },
});
/**
 * 获取文件类型
 * @param requestPath 请求路径
 * @returns 文件类型和子目录
 */
const getFileTypeFromPath = (requestPath) => {
    if (requestPath.includes('avatar') || requestPath.includes('userAvatar')) {
        return { type: 'avatar', subDir: 'avatar' };
    }
    else if (requestPath.includes('background')) {
        return { type: 'background', subDir: 'background' };
    }
    else if (requestPath.includes('category')) {
        return { type: 'category', subDir: 'category' };
    }
    else if (requestPath.includes('kitchen')) {
        return { type: 'kitchen', subDir: 'kitchen' };
    }
    else {
        return { type: 'dish', subDir: 'dish' };
    }
};
/**
 * 处理图片压缩和重命名
 * @param req 请求对象
 * @param file 文件对象
 * @returns 处理后的文件信息
 */
const processImage = (req, file) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { type, subDir } = getFileTypeFromPath(req.path);
    const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
    if (!userId) {
        throw new error_1.BusinessError('用户未登录');
    }
    // 获取厨房ID（如果需要）
    let kitchenId;
    if (type === 'kitchen' || type === 'dish' || type === 'category') {
        kitchenId = req.body.kitchenId || req.query.kitchenId;
        if (!kitchenId) {
            throw new error_1.BusinessError(`${type}类型的图片需要提供厨房ID`);
        }
    }
    // 生成新文件名
    const newFileName = (0, imageCompressor_1.generateFileName)(userId, type, kitchenId, '.jpg' // 压缩后统一为jpg格式
    );
    // 目标文件路径
    const targetDir = path_1.default.join(uploadDir, subDir);
    const targetPath = path_1.default.join(targetDir, newFileName);
    // 压缩图片
    yield (0, imageCompressor_1.compressImage)(file.path, targetPath, type);
    // 删除临时文件
    if (fs_1.default.existsSync(file.path)) {
        fs_1.default.unlinkSync(file.path);
    }
    return {
        filename: newFileName,
        path: targetPath,
        type,
        subDir
    };
});
/**
 * 单文件上传中间件
 * @param fieldName 文件字段名
 */
const uploadSingleFile = (fieldName = 'file') => {
    return (req, res, next) => {
        logger_1.default.debug(`开始处理文件上传请求: ${req.path}, 字段名: ${fieldName}`);
        upload.single(fieldName)(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
            if (err) {
                if (err instanceof multer_1.default.MulterError) {
                    // Multer错误
                    if (err.code === 'LIMIT_FILE_SIZE') {
                        logger_1.default.warn(`文件大小超过限制: ${config_1.default.upload.maxFileSize / 1024 / 1024}MB`);
                        return next(new error_1.BusinessError(`文件大小超过限制 (${config_1.default.upload.maxFileSize / 1024 / 1024}MB)`));
                    }
                    else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
                        logger_1.default.warn(`意外的文件字段: ${err.field}, 期望: ${fieldName}`);
                        return next(new error_1.BusinessError(`上传失败: 表单字段名称错误，应为 "${fieldName}"`));
                    }
                    logger_1.default.warn(`文件上传错误: ${err.code} - ${err.message}`);
                    return next(new error_1.BusinessError(`文件上传错误: ${err.message}`));
                }
                logger_1.default.error(`上传处理错误:`, err);
                return next(err);
            }
            // 检查请求中是否有文件
            if (!req.file) {
                logger_1.default.warn('请求中没有文件');
                return next(new error_1.BusinessError('未提供文件'));
            }
            try {
                // 处理图片压缩和重命名
                const processedFile = yield processImage(req, req.file);
                // 更新req.file信息
                req.file.filename = processedFile.filename;
                req.file.path = processedFile.path;
                // 记录上传信息
                logger_1.default.info(`文件上传并压缩成功: ${processedFile.filename}`, {
                    originalname: req.file.originalname,
                    mimetype: req.file.mimetype,
                    size: req.file.size,
                    type: processedFile.type,
                    subDir: processedFile.subDir,
                    newPath: processedFile.path
                });
                next();
            }
            catch (error) {
                // 清理临时文件
                if (req.file && fs_1.default.existsSync(req.file.path)) {
                    fs_1.default.unlinkSync(req.file.path);
                }
                logger_1.default.error(`图片处理失败:`, error);
                return next(error);
            }
        }));
    };
};
exports.uploadSingleFile = uploadSingleFile;
/**
 * 多文件上传中间件
 * @param fieldName 文件字段名
 * @param maxCount 最大文件数量
 */
const uploadMultipleFiles = (fieldName = 'files', maxCount = 5) => {
    return (req, res, next) => {
        upload.array(fieldName, maxCount)(req, res, (err) => __awaiter(void 0, void 0, void 0, function* () {
            if (err) {
                if (err instanceof multer_1.default.MulterError) {
                    // Multer错误
                    if (err.code === 'LIMIT_FILE_SIZE') {
                        return next(new error_1.BusinessError(`文件大小超过限制 (${config_1.default.upload.maxFileSize / 1024 / 1024}MB)`));
                    }
                    else if (err.code === 'LIMIT_FILE_COUNT') {
                        return next(new error_1.BusinessError(`文件数量超过限制 (${maxCount})`));
                    }
                    return next(new error_1.BusinessError(`文件上传错误: ${err.message}`));
                }
                return next(err);
            }
            // 如果没有上传文件
            if (!req.files || req.files.length === 0) {
                return next(new error_1.BusinessError('未提供文件'));
            }
            try {
                const files = req.files;
                const processedFiles = [];
                // 处理每个文件
                for (const file of files) {
                    const processedFile = yield processImage(req, file);
                    // 更新文件信息
                    file.filename = processedFile.filename;
                    file.path = processedFile.path;
                    processedFiles.push(processedFile);
                }
                // 记录上传信息
                logger_1.default.info(`多文件上传并压缩成功: ${processedFiles.length}个文件`, {
                    files: processedFiles.map(f => ({
                        filename: f.filename,
                        type: f.type
                    }))
                });
                next();
            }
            catch (error) {
                // 清理临时文件
                if (req.files) {
                    const files = req.files;
                    files.forEach(file => {
                        if (fs_1.default.existsSync(file.path)) {
                            fs_1.default.unlinkSync(file.path);
                        }
                    });
                }
                logger_1.default.error(`多文件处理失败:`, error);
                return next(error);
            }
        }));
    };
};
exports.uploadMultipleFiles = uploadMultipleFiles;
/**
 * 获取文件URL（返回相对路径）
 * @param filename 文件名
 * @param subDir 子目录
 * @returns 相对路径
 */
const getFileUrl = (filename, subDir = 'dish') => {
    // 使用URL管理器生成相对路径
    return (0, urlManager_1.getRelativePath)(filename, subDir);
};
exports.getFileUrl = getFileUrl;
/**
 * 删除文件
 * @param filename 文件名
 * @param subDir 子目录
 */
const deleteFile = (filename, subDir = 'dish') => {
    try {
        const filePath = path_1.default.join(uploadDir, subDir, filename);
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
            logger_1.default.info(`文件删除成功: ${filePath}`);
        }
    }
    catch (error) {
        logger_1.default.error(`文件删除失败: ${error}`);
    }
};
exports.deleteFile = deleteFile;
exports.default = {
    uploadSingleFile: exports.uploadSingleFile,
    uploadMultipleFiles: exports.uploadMultipleFiles,
    getFileUrl: exports.getFileUrl,
    deleteFile: exports.deleteFile,
};
//# sourceMappingURL=upload.js.map