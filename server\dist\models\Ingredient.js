"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 配料模型
 * 存储菜品的配料信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 配料模型类
class Ingredient extends sequelize_1.Model {
}
// 初始化配料模型
Ingredient.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '配料ID',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    name: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '配料名称',
    },
    amount: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '用量',
    },
    sort: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序值',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Ingredient',
    tableName: 'ingredients',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_dish_id',
            fields: ['dish_id'],
        },
        {
            name: 'idx_dish_sort',
            fields: ['dish_id', 'sort'],
        },
    ],
});
exports.default = Ingredient;
//# sourceMappingURL=Ingredient.js.map