.feedback-management {
  padding: 24px;

  .filter-bar {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    
    span {
      font-weight: 500;
      color: #262626;
    }
  }

  .pagination-wrapper {
    margin-top: 16px;
    text-align: right;
  }

  .feedback-detail {
    .detail-item {
      margin-bottom: 16px;
      
      .label {
        display: inline-block;
        width: 100px;
        font-weight: 500;
        color: #262626;
      }
      
      .description {
        margin-top: 8px;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 4px;
        white-space: pre-wrap;
        word-break: break-word;
      }
      
      .images {
        margin-top: 8px;
        
        .ant-image {
          border-radius: 4px;
          border: 1px solid #d9d9d9;
        }
      }
      
      .reply {
        margin-top: 8px;
        padding: 12px;
        background: #e6f7ff;
        border-radius: 4px;
        border-left: 4px solid #1890ff;
      }
      
      .reply-time {
        margin-top: 8px;
        font-size: 12px;
        color: #8c8c8c;
      }
      
      .device-info {
        margin-top: 8px;
        
        pre {
          background: #f5f5f5;
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          max-height: 200px;
          overflow-y: auto;
        }
      }
    }
  }

  .reply-form {
    .form-item {
      margin-bottom: 16px;
      
      .label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #262626;
      }
      
      .content {
        padding: 12px;
        background: #f5f5f5;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
    }
    
    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5;
    }
  }

  .ant-tag {
    margin-right: 0;
  }

  .ant-btn {
    &.ant-btn-text {
      padding: 0 4px;
      height: auto;
      line-height: 1.5715;
    }
  }
} 