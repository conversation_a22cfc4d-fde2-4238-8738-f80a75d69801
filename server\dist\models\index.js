"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemSetting = exports.Feedback = exports.DiscoverItem = exports.Message = exports.OrderItem = exports.Order = exports.CartItem = exports.HotKeyword = exports.Comment = exports.Report = exports.Like = exports.CookingStep = exports.Nutrition = exports.Ingredient = exports.DishImage = exports.Dish = exports.Category = exports.Table = exports.KitchenMember = exports.Kitchen = exports.AdView = exports.SearchHistory = exports.BackgroundSetting = exports.SignIn = exports.Transaction = exports.Membership = exports.User = exports.initializeAssociations = void 0;
/**
 * 数据库模型索引文件
 * 统一导出所有模型，并建立模型之间的关联关系
 */
const database_1 = __importDefault(require("../config/database"));
const logger_1 = __importDefault(require("../utils/logger"));
// 导入模型
// 用户相关模型
const User_1 = __importDefault(require("./User"));
exports.User = User_1.default;
const Membership_1 = __importDefault(require("./Membership"));
exports.Membership = Membership_1.default;
const Transaction_1 = __importDefault(require("./Transaction"));
exports.Transaction = Transaction_1.default;
const SignIn_1 = __importDefault(require("./SignIn"));
exports.SignIn = SignIn_1.default;
const BackgroundSetting_1 = __importDefault(require("./BackgroundSetting"));
exports.BackgroundSetting = BackgroundSetting_1.default;
const SearchHistory_1 = __importDefault(require("./SearchHistory"));
exports.SearchHistory = SearchHistory_1.default;
const AdView_1 = __importDefault(require("./AdView"));
exports.AdView = AdView_1.default;
// 厨房相关模型
const Kitchen_1 = __importDefault(require("./Kitchen"));
exports.Kitchen = Kitchen_1.default;
const KitchenMember_1 = __importDefault(require("./KitchenMember"));
exports.KitchenMember = KitchenMember_1.default;
const Table_1 = __importDefault(require("./Table"));
exports.Table = Table_1.default;
// 菜品相关模型
const Category_1 = __importDefault(require("./Category"));
exports.Category = Category_1.default;
const Dish_1 = __importDefault(require("./Dish"));
exports.Dish = Dish_1.default;
const DishImage_1 = __importDefault(require("./DishImage"));
exports.DishImage = DishImage_1.default;
const Ingredient_1 = __importDefault(require("./Ingredient"));
exports.Ingredient = Ingredient_1.default;
const Nutrition_1 = __importDefault(require("./Nutrition"));
exports.Nutrition = Nutrition_1.default;
const CookingStep_1 = __importDefault(require("./CookingStep"));
exports.CookingStep = CookingStep_1.default;
const Like_1 = __importDefault(require("./Like"));
exports.Like = Like_1.default;
const Report_1 = __importDefault(require("./Report"));
exports.Report = Report_1.default;
const Comment_1 = __importDefault(require("./Comment"));
exports.Comment = Comment_1.default;
const HotKeyword_1 = __importDefault(require("./HotKeyword"));
exports.HotKeyword = HotKeyword_1.default;
// 订单相关模型
const CartItem_1 = __importDefault(require("./CartItem"));
exports.CartItem = CartItem_1.default;
const Order_1 = __importDefault(require("./Order"));
exports.Order = Order_1.default;
const OrderItem_1 = __importDefault(require("./OrderItem"));
exports.OrderItem = OrderItem_1.default;
// 消息相关模型
const Message_1 = __importDefault(require("./Message"));
exports.Message = Message_1.default;
const DiscoverItem_1 = __importDefault(require("./DiscoverItem"));
exports.DiscoverItem = DiscoverItem_1.default;
// 反馈相关模型
const Feedback_1 = __importDefault(require("./Feedback"));
exports.Feedback = Feedback_1.default;
// 系统相关模型
const SystemSetting_1 = __importDefault(require("./SystemSetting"));
exports.SystemSetting = SystemSetting_1.default;
// 建立模型之间的关联关系
const initializeAssociations = () => {
    logger_1.default.info('初始化模型关联关系...');
    // 用户与会员关系
    User_1.default.hasOne(Membership_1.default, { foreignKey: 'user_id', as: 'membership' });
    Membership_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与交易记录关系
    User_1.default.hasMany(Transaction_1.default, { foreignKey: 'user_id', as: 'transactions' });
    Transaction_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与签到记录关系
    User_1.default.hasMany(SignIn_1.default, { foreignKey: 'user_id', as: 'signIns' });
    SignIn_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与背景设置关系
    User_1.default.hasOne(BackgroundSetting_1.default, { foreignKey: 'user_id', as: 'backgroundSetting' });
    BackgroundSetting_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与搜索历史关系
    User_1.default.hasMany(SearchHistory_1.default, { foreignKey: 'user_id', as: 'searchHistories' });
    SearchHistory_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与广告观看记录关系
    User_1.default.hasMany(AdView_1.default, { foreignKey: 'user_id', as: 'adViews' });
    AdView_1.default.belongsTo(User_1.default, { foreignKey: 'user_id' });
    // 用户与厨房关系
    User_1.default.hasMany(Kitchen_1.default, { foreignKey: 'owner_id', as: 'ownedKitchens' });
    Kitchen_1.default.belongsTo(User_1.default, { foreignKey: 'owner_id', as: 'owner' });
    // 用户与厨房成员关系
    User_1.default.hasMany(KitchenMember_1.default, { foreignKey: 'user_id', as: 'kitchenMemberships' });
    KitchenMember_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    // 厨房与厨房成员关系
    Kitchen_1.default.hasMany(KitchenMember_1.default, { foreignKey: 'kitchen_id', as: 'members' });
    KitchenMember_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 厨房与桌号关系
    Kitchen_1.default.hasMany(Table_1.default, { foreignKey: 'kitchen_id', as: 'tables' });
    Table_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 厨房与分类关系
    Kitchen_1.default.hasMany(Category_1.default, { foreignKey: 'kitchen_id', as: 'categories' });
    Category_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 分类与菜品关系
    Category_1.default.hasMany(Dish_1.default, { foreignKey: 'category_id', as: 'dishes' });
    Dish_1.default.belongsTo(Category_1.default, { foreignKey: 'category_id', as: 'category' });
    // 厨房与菜品关系
    Kitchen_1.default.hasMany(Dish_1.default, { foreignKey: 'kitchen_id', as: 'dishes' });
    Dish_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 用户与创建的菜品关系
    User_1.default.hasMany(Dish_1.default, { foreignKey: 'created_by', as: 'createdDishes' });
    Dish_1.default.belongsTo(User_1.default, { foreignKey: 'created_by', as: 'creator' });
    // 菜品与菜品图片关系
    Dish_1.default.hasMany(DishImage_1.default, { foreignKey: 'dish_id', as: 'images' });
    DishImage_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    // 菜品与配料关系
    Dish_1.default.hasMany(Ingredient_1.default, { foreignKey: 'dish_id', as: 'ingredients' });
    Ingredient_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    // 菜品与营养成分关系
    Dish_1.default.hasOne(Nutrition_1.default, { foreignKey: 'dish_id', as: 'nutrition' });
    Nutrition_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    // 菜品与烹饪步骤关系
    Dish_1.default.hasMany(CookingStep_1.default, { foreignKey: 'dish_id', as: 'cookingSteps' });
    CookingStep_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    // 菜品与点赞关系
    Dish_1.default.hasMany(Like_1.default, { foreignKey: 'dish_id', as: 'likes' });
    Like_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    User_1.default.hasMany(Like_1.default, { foreignKey: 'user_id', as: 'userLikes' }); // 修改关联名称，避免命名冲突
    Like_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    // 菜品与举报关系
    Dish_1.default.hasMany(Report_1.default, { foreignKey: 'dish_id', as: 'reports' });
    Report_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    User_1.default.hasMany(Report_1.default, { foreignKey: 'user_id', as: 'reports' });
    Report_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    // 菜品与评论关系
    Dish_1.default.hasMany(Comment_1.default, { foreignKey: 'dish_id', as: 'comments' });
    Comment_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    User_1.default.hasMany(Comment_1.default, { foreignKey: 'user_id', as: 'comments' });
    Comment_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    // 用户与购物车关系
    User_1.default.hasMany(CartItem_1.default, { foreignKey: 'user_id', as: 'cartItems' });
    CartItem_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    Dish_1.default.hasMany(CartItem_1.default, { foreignKey: 'dish_id', as: 'cartItems' });
    CartItem_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    Kitchen_1.default.hasMany(CartItem_1.default, { foreignKey: 'kitchen_id', as: 'cartItems' });
    CartItem_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 用户与订单关系
    User_1.default.hasMany(Order_1.default, { foreignKey: 'user_id', as: 'orders' });
    Order_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    Kitchen_1.default.hasMany(Order_1.default, { foreignKey: 'kitchen_id', as: 'orders' });
    Order_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 订单与订单项关系
    Order_1.default.hasMany(OrderItem_1.default, { foreignKey: 'order_id', as: 'items' });
    OrderItem_1.default.belongsTo(Order_1.default, { foreignKey: 'order_id', as: 'order' });
    Dish_1.default.hasMany(OrderItem_1.default, { foreignKey: 'dish_id', as: 'orderItems' });
    OrderItem_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    // 用户与消息关系
    User_1.default.hasMany(Message_1.default, { foreignKey: 'user_id', as: 'messages' });
    Message_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    // 用户与发现项关系
    User_1.default.hasMany(DiscoverItem_1.default, { foreignKey: 'user_id', as: 'discoverItems' });
    DiscoverItem_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    Dish_1.default.hasMany(DiscoverItem_1.default, { foreignKey: 'dish_id', as: 'discoverItems' });
    DiscoverItem_1.default.belongsTo(Dish_1.default, { foreignKey: 'dish_id', as: 'dish' });
    Kitchen_1.default.hasMany(DiscoverItem_1.default, { foreignKey: 'kitchen_id', as: 'discoverItems' });
    DiscoverItem_1.default.belongsTo(Kitchen_1.default, { foreignKey: 'kitchen_id', as: 'kitchen' });
    // 用户与反馈关系
    User_1.default.hasMany(Feedback_1.default, { foreignKey: 'user_id', as: 'feedbacks' });
    Feedback_1.default.belongsTo(User_1.default, { foreignKey: 'user_id', as: 'user' });
    logger_1.default.info('模型关联关系初始化完成');
};
exports.initializeAssociations = initializeAssociations;
// 导出数据库实例
exports.default = database_1.default;
//# sourceMappingURL=index.js.map