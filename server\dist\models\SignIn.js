"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 签到记录模型
 * 存储用户签到记录
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 签到记录模型类
class SignIn extends sequelize_1.Model {
}
// 初始化签到记录模型
SignIn.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '签到ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    date: {
        type: sequelize_1.DataTypes.DATEONLY,
        allowNull: false,
        comment: '签到日期',
    },
    reward: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 20,
        comment: '奖励',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'SignIn',
    tableName: 'sign_ins',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_date',
            fields: ['date'],
        },
        {
            name: 'idx_user_date',
            unique: true,
            fields: ['user_id', 'date'],
        },
    ],
});
exports.default = SignIn;
//# sourceMappingURL=SignIn.js.map