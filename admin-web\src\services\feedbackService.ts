import { request } from '@/utils/request'

export interface FeedbackItem {
  id: string
  type: 'bug' | 'feature' | 'other'
  description: string
  images: string[]
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  submitTime: string
  userId: string
  userNickname: string
  reply?: string
  replyTime?: string
  deviceInfo?: any
}

export interface FeedbackListParams {
  page?: number
  pageSize?: number
  type?: string
  status?: string
}

export interface FeedbackListResponse {
  list: FeedbackItem[]
  total: number
  page: number
  pageSize: number
}

export interface UpdateStatusParams {
  feedbackId: string
  status: string
  reply?: string
}

export interface UpdateStatusResponse {
  feedbackId: string
  status: string
  reply?: string
  updateTime: string
}

// 获取反馈列表
export const getFeedbackList = async (params: FeedbackListParams): Promise<FeedbackListResponse> => {
  return request.post('/feedback/list', params)
}

// 更新反馈状态
export const updateFeedbackStatus = async (params: UpdateStatusParams): Promise<UpdateStatusResponse> => {
  return request.post('/feedback/update-status', params)
} 