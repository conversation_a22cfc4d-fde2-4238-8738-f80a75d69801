/* 订单详情页样式 */
.container {
  min-height: 100vh;
  background-color: #F9F9F9;
  box-sizing: border-box;
  padding-bottom: 40rpx;
  overflow-y: auto; /* 确保可以滚动 */
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 内容容器 */
.content-container {
  padding: 20rpx 30rpx;
}

/* 顶部状态区域 */
.status-container {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-container.pending {
  background: linear-gradient(135deg, #FFF8E1, #FFECB3);
}

.status-container.accepted {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
}

.status-container.cooking {
  background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
}

.status-container.completed {
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
}

.status-container.canceled,
.status-container.cancelled {
  background: linear-gradient(135deg, #FAFAFA, #E0E0E0);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 50%;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 40rpx;
}

.status-text {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.status-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

/* 进度条 */
.progress-container {
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-steps {
  position: relative;
}

.step-item {
  position: relative;
  padding-left: 30rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: flex-start;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-dot {
  position: absolute;
  left: 0;
  top: 10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #CCCCCC;
  z-index: 2;
}

.step-line {
  position: absolute;
  left: 10rpx;
  top: 30rpx;
  width: 2rpx;
  height: calc(100% + 20rpx);
  background-color: #EEEEEE;
  z-index: 1;
}

.step-item:last-child .step-line {
  display: none;
}

.step-content {
  flex: 1;
  padding-left: 20rpx;
}

.step-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.step-time {
  font-size: 24rpx;
  color: #999999;
}

/* 激活的步骤 */
.step-item.active .step-dot,
.step-item.completed .step-dot {
  background-color: #FF6B35;
}

.step-item.active .step-line,
.step-item.completed .step-line {
  background-color: #FF6B35;
}

.step-item.active .step-title,
.step-item.completed .step-title {
  color: #FF6B35;
}

/* 订单信息卡片 */
.order-info-card, .dish-list-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  max-width: 65%;
  text-align: right;
}

.remark-text {
  color: #666666;
  font-size: 26rpx;
}

/* 菜品列表 */
.dish-list {
  margin-bottom: 20rpx;
}

.dish-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}

.dish-item:last-child {
  border-bottom: none;
}

.dish-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.dish-info {
  flex: 1;
  margin: 0 20rpx;
  overflow: hidden;
}

.dish-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-tags {
  display: flex;
  flex-wrap: wrap;
}

.dish-tag {
  font-size: 22rpx;
  color: #999999;
  background-color: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.dish-price-count {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.dish-price {
  font-size: 30rpx;
  color: #FF6B35;
  font-weight: 500;
}

.dish-count {
  font-size: 26rpx;
  color: #999999;
}

.order-total {
  text-align: right;
  padding-top: 20rpx;
  border-top: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #666666;
}

.total-price {
  font-size: 34rpx;
  color: #FF6B35;
  font-weight: bold;
  margin-left: 10rpx;
}

/* 底部操作区域 */
.bottom-actions {
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  box-sizing: border-box;
}

.action-btn {
  min-width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin: 0 15rpx;
  flex: 1;
  max-width: 220rpx;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.reject-btn {
  background-color: #FFEBEE;
  color: #E53935;
}

.accept-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
}

.complete-btn {
  background-color: #4CAF50;
  color: #FFFFFF;
}

/* 为底部按钮添加占位空间 */
.bottom-placeholder {
  height: 120rpx;
  width: 100%;
}
