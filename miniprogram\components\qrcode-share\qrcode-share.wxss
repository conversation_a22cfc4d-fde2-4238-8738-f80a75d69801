.qrcode-container {
  padding: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.kitchen-info {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15rpx 0;
  border-bottom: 1px solid #EEEEEE;
  margin-bottom: 20rpx;
}

.kitchen-avatar {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.kitchen-detail {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.kitchen-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 6rpx;
}

.kitchen-id {
  font-size: 24rpx;
  color: #666666;
}

.qrcode-wrapper {
  width: 300rpx;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F9F9F9;
  border-radius: 12rpx;
  margin: 15rpx 0;
  overflow: hidden;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-loading {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

.shared-kitchen-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.tip-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.tip-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.tip-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.share-tips {
  margin-top: 15rpx;
  text-align: center;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}