// 背景设置组件
import { uploadBackgroundImage } from '../../api/uploadApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { updateKitchenInfo, getKitchenInfo } from '../../api/kitchenApi'
import { updateUserBackgroundSettings } from '../../api/userApi'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false
    },
    // 当前店铺背景图URL
    currentShopBg: {
      type: String,
      value: ''
    },
    // 当前导航栏背景渐变值
    currentNavBg: {
      type: String,
      value: ''
    },
    // 当前导航栏背景索引（用于预设渐变）
    currentNavBgIndex: {
      type: Number,
      value: 0
    },
    isVip: {
      type: Boolean,
      value: false
    },
    // 新增：当前厨房ID
    kitchenId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前激活的标签
    activeTab: 'shop',
    // 店铺背景图预览
    shopBgPreview: '',
    // 默认店铺背景图
    defaultShopBg: DEFAULT_IMAGES.KITCHEN_BACKGROUND,
    // 导航栏背景索引
    navBgIndex: 0,
    // 自定义颜色
    startColor: '#FFFFFF',
    endColor: '#FF6B35',
    // 预设渐变背景
    gradients: [
      'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 淡蓝渐变（原为暖橙渐变）
      'linear-gradient(to bottom, #4CAF50, #8BC34A)', // 绿色渐变
      'linear-gradient(to bottom, #2196F3, #03A9F4)', // 蓝色渐变
      'linear-gradient(to bottom, #9C27B0, #E1BEE7)', // 紫色渐变
      'linear-gradient(to bottom, #FF5722, #FFAB91)', // 橙红渐变
      'linear-gradient(to bottom, #607D8B, #CFD8DC)'  // 灰蓝渐变
    ],
    loading: false
  },

  observers: {
    'visible': function(visible) {
      if (visible) {
        // 当弹窗打开时，初始化数据
        this.setData({
          shopBgPreview: this.properties.currentShopBg,
          navBgIndex: this.properties.currentNavBgIndex || 0  // 如果没有指定，则默认为0
        });

        // 如果有当前导航栏背景，且是自定义的颜色
        if (this.properties.currentNavBg && this.properties.currentNavBgIndex === -1) {
          // 解析渐变值，提取颜色
          const match = this.properties.currentNavBg.match(/linear-gradient\(to\s+bottom,\s+(#[A-Fa-f0-9]+),\s+(#[A-Fa-f0-9]+)\)/);
          if (match && match.length === 3) {
            this.setData({
              startColor: match[1],
              endColor: match[2]
            });
          }
        }

        // 获取当前厨房的背景图和用户背景设置
        this.fetchBackgroundSettings();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 获取背景设置（只获取厨房背景图）
    async fetchBackgroundSettings() {
      try {
        // 如果没有厨房ID，使用当前传入的背景图
        if (!this.properties.kitchenId) {
          console.log('没有厨房ID，使用当前传入的背景图');
          this.setData({
            shopBgPreview: this.properties.currentShopBg || this.data.defaultShopBg
          });
          return;
        }

        // 获取厨房信息
        const kitchenResult = await getKitchenInfo(this.properties.kitchenId);

        let kitchenBg = '';

        // 处理厨房背景图结果
        if (kitchenResult && kitchenResult.error === 0 && kitchenResult.body) {
          kitchenBg = kitchenResult.body.backgroundUrl || '';
          console.log('获取到厨房背景图:', kitchenBg);
        } else {
          console.log('获取厨房信息失败:', kitchenResult);
        }

        // 使用厨房背景图，如果没有则使用默认背景图
        const finalShopBg = kitchenBg || this.data.defaultShopBg;

        console.log('背景设置组件最终背景图:', {
          kitchenBg,
          finalShopBg,
          currentShopBg: this.properties.currentShopBg,
          currentPreview: this.data.shopBgPreview
        });

        // 总是更新预览图，确保显示最新的背景图
        this.setData({
          shopBgPreview: finalShopBg
        });

        // 如果是新的背景图，清除图片缓存确保显示最新内容
        if (kitchenBg && kitchenBg !== this.properties.currentShopBg) {
          // 延迟一点时间让组件更新完成
          setTimeout(() => {
            // 触发smart-image组件重新加载
            const smartImageComponents = this.selectAllComponents('.background-preview-image');
            if (smartImageComponents && smartImageComponents.length > 0) {
              smartImageComponents.forEach((component: any) => {
                if (component && component.resetAndReload) {
                  component.resetAndReload();
                }
              });
            }
          }, 100);
        }

      } catch (error) {
        console.error('获取厨房背景设置失败', error);
        // 如果获取失败，使用当前传入的背景图或默认背景图
        this.setData({
          shopBgPreview: this.properties.currentShopBg || this.data.defaultShopBg
        });
      }
    },

    // 切换标签
    switchTab(e: WechatMiniprogram.BaseEvent) {
      const tab = e.currentTarget.dataset.tab;
      this.setData({
        activeTab: tab
      });
    },

    // 选择店铺背景图
    chooseShopBg() {
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];

          this.setData({ loading: true });

          // 上传图片到服务器
          uploadBackgroundImage(tempFilePath)
            .then(async (result: any) => {
              if (result && result.error === 0 && result.body && result.body.imageUrl) {
                // 使用服务器返回的图片URL
                const imageUrl = result.body.imageUrl;
                
                // 添加时间戳参数破坏缓存，确保新背景图片立即显示
                const imageUrlWithCacheBuster = `${imageUrl}?v=${Date.now()}`;
                
                // 不再立即保存到后端，避免重复保存导致文件被删除
                // 只更新预览，等用户点击保存按钮时再保存到后端
                
                this.setData({
                  shopBgPreview: imageUrlWithCacheBuster, // 前端显示带缓存破坏参数的URL
                  loading: false
                });

                wx.showToast({
                  title: '上传成功，请点击保存',
                  icon: 'success'
                });
              } else {
                throw new Error('上传失败');
              }
            })
            .catch(error => {
              console.error('上传图片失败', error);
              this.setData({
                loading: false
              });

              // 上传失败后的提示
              let errorMessage = error && error.message ? error.message : '上传失败，请重试';
              wx.showToast({
                title: errorMessage,
                icon: 'none',
                duration: 2500
              });
            });
        },
        fail: (err) => {
          console.error('选择图片失败', err);
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    // 选择预设渐变背景
    selectGradient(e: WechatMiniprogram.BaseEvent) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        navBgIndex: index
      });
    },

    // 起始颜色输入
    onStartColorInput(e: WechatMiniprogram.Input) {
      this.setData({
        startColor: e.detail.value,
        navBgIndex: -1 // 自定义颜色时，设置为-1
      });
    },

    // 结束颜色输入
    onEndColorInput(e: WechatMiniprogram.Input) {
      this.setData({
        endColor: e.detail.value,
        navBgIndex: -1 // 自定义颜色时，设置为-1
      });
    },

    // 点击自定义渐变预览
    onTapCustomGradient() {
      // 应用自定义渐变
      this.setData({
        navBgIndex: -1
      });

      wx.showToast({
        title: '已选择自定义渐变',
        icon: 'none',
        duration: 1500
      });
    },

    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 保存设置
    async onSave() {
      if (this.data.loading) {
        wx.showToast({
          title: '请等待图片上传完成',
          icon: 'none'
        });
        return;
      }

      // 获取最终数据
      const { shopBgPreview, navBgIndex, startColor, endColor, gradients } = this.data;

      // 检查图片路径是否是临时文件
      if (shopBgPreview && shopBgPreview.startsWith('http://tmp/')) {
        // 如果是临时文件路径，提示用户需要上传
        wx.showToast({
          title: '背景图片未成功上传，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 构建导航栏背景样式
      const navBgStyle = navBgIndex >= 0 ? gradients[navBgIndex] : `linear-gradient(to bottom, ${startColor}, ${endColor})`;
      
      // 构建返回数据
      const result = {
        shopBg: shopBgPreview,
        navBgIndex,
        navBg: navBgStyle  // 修改为navBg，与页面接收参数保持一致
      };

      // 使用安全的loading方法
      const { safeShowLoading, safeHideLoading } = require('../../utils/request');
      safeShowLoading({
        title: '保存中...',
        mask: true
      });

      try {
        // 检查背景图是否有变化，避免重复保存
        // 移除缓存破坏参数后再比较URL
        const cleanShopBgPreview = shopBgPreview.split('?')[0]; // 移除?v=timestamp参数
        const cleanCurrentShopBg = this.properties.currentShopBg.split('?')[0]; // 移除?v=timestamp参数
        
        const backgroundChanged = cleanShopBgPreview !== cleanCurrentShopBg;
        const navStyleChanged = navBgStyle !== this.properties.currentNavBg;
        const navIndexChanged = navBgIndex !== this.properties.currentNavBgIndex;

        console.log('保存检查:', {
          backgroundChanged,
          navStyleChanged, 
          navIndexChanged,
          currentShopBg: cleanCurrentShopBg,
          newShopBg: cleanShopBgPreview,
          originalPreview: shopBgPreview
        });

        // 如果没有任何变化，直接返回成功
        if (!backgroundChanged && !navStyleChanged && !navIndexChanged) {
          safeHideLoading();
          wx.showToast({
            title: '无变化，无需保存',
            icon: 'success'
          });
          this.triggerEvent('save', result);
          return;
        }

        const promises = [];

        // 1. 只有背景图变化时，才更新厨房的背景图
        if (backgroundChanged && this.properties.kitchenId) {
          console.log('更新厨房背景图:', cleanShopBgPreview);
          promises.push(updateKitchenInfo(this.properties.kitchenId, {
            backgroundUrl: cleanShopBgPreview // 传递不带缓存参数的原始URL
          }));
        }

        // 2. 只有导航栏设置变化时，才保存用户的导航栏背景设置
        if (navStyleChanged || navIndexChanged) {
          console.log('更新用户导航栏背景设置');
          promises.push(updateUserBackgroundSettings({
            shopBg: cleanShopBgPreview, // 传递不带缓存参数的原始URL
            navBgStyle: navBgStyle,
            navBgIndex: navBgIndex
          }));
        } else if (backgroundChanged) {
          // 如果只有背景图变化，但导航栏设置没变化，仍需要更新用户背景设置中的背景图
          console.log('只更新用户背景图设置');
          promises.push(updateUserBackgroundSettings({
            shopBg: cleanShopBgPreview, // 传递不带缓存参数的原始URL
            navBgStyle: this.properties.currentNavBg,
            navBgIndex: this.properties.currentNavBgIndex
          }));
        }

        // 如果有需要保存的内容，执行保存
        if (promises.length > 0) {
          // 并行执行保存操作
          const results = await Promise.all(promises);

          // 检查所有操作是否成功
          const allSuccess = results.every(result => result.error === 0);

          if (allSuccess) {
            console.log('所有设置保存成功');
          } else {
            // 找到第一个失败的结果
            const failedResult = results.find(result => result.error !== 0);
            const errorMessage = failedResult && failedResult.message ? failedResult.message : '保存失败';
            throw new Error(errorMessage);
          }
        }

        safeHideLoading();

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 触发保存事件，将数据传递给父组件
        this.triggerEvent('save', result);

      } catch (error) {
        console.error('保存背景设置失败', error);
        safeHideLoading();
        wx.showToast({
          title: (error as any).message || '保存失败，请重试',
          icon: 'none'
        });
      }
    }
  }
})