"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const systemSettingService_1 = __importDefault(require("../services/systemSettingService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取所有系统设置
 * @route GET /api/system/settings
 */
const getAllSettings = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const settings = yield systemSettingService_1.default.getAllSettings();
        (0, response_1.success)(res, { settings });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取指定设置
 * @route GET /api/system/setting/:key
 */
const getSetting = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { key } = req.params;
        if (!key) {
            throw new error_1.BusinessError('缺少参数: key', response_1.ResponseCode.VALIDATION);
        }
        const value = yield systemSettingService_1.default.getSetting(key);
        (0, response_1.success)(res, { value });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新系统设置
 * @route POST /api/system/setting
 */
const updateSetting = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { key, value, type = 'string', description } = req.body;
        if (!key || value === undefined) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        // 验证类型
        const validTypes = ['boolean', 'string', 'number', 'json'];
        if (!validTypes.includes(type)) {
            throw new error_1.BusinessError('无效的值类型', response_1.ResponseCode.VALIDATION);
        }
        yield systemSettingService_1.default.setSetting(key, value, type, description);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 检查评论功能是否开启
 * @route GET /api/system/comment-enabled
 */
const isCommentEnabled = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const enabled = yield systemSettingService_1.default.isCommentEnabled();
        (0, response_1.success)(res, { enabled });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getAllSettings,
    getSetting,
    updateSetting,
    isCommentEnabled
};
//# sourceMappingURL=systemSettingController.js.map