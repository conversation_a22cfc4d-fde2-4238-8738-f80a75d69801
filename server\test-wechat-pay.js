const WxPay = require('wechatpay-node-v3');
const fs = require('fs');
const path = require('path');

// 读取配置
const config = {
  appId: 'wx41b8de9ea1e51474',
  mchId: '1719611095',
  apiV3Key: 'z8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2',
  serialNo: '19E5CF9FAD0789EAA2CFD178D7101AB83F7E2D02',
  privateKeyPath: path.join(__dirname, 'certs/wechatpay/apiclient_key.pem'),
  certificatePath: path.join(__dirname, 'certs/wechatpay/apiclient_cert.pem'),
  notifyUrl: 'https://dzcdd.zj1.natnps.cn/api/payment/notify'
};

async function testWechatPay() {
  try {
    console.log('开始测试微信支付配置...');

    // 检查证书文件
    console.log('1. 检查证书文件...');
    if (!fs.existsSync(config.privateKeyPath)) {
      throw new Error(`私钥文件不存在: ${config.privateKeyPath}`);
    }
    if (!fs.existsSync(config.certificatePath)) {
      throw new Error(`证书文件不存在: ${config.certificatePath}`);
    }
    console.log('✅ 证书文件存在');

    // 读取证书内容
    console.log('2. 读取证书内容...');
    const privateKey = fs.readFileSync(config.privateKeyPath, 'utf8');
    const publicKey = fs.readFileSync(config.certificatePath, 'utf8');

    if (!privateKey.includes('BEGIN PRIVATE KEY')) {
      throw new Error('私钥文件格式不正确');
    }
    if (!publicKey.includes('BEGIN CERTIFICATE')) {
      throw new Error('证书文件格式不正确');
    }
    console.log('✅ 证书内容格式正确');

    // 初始化微信支付
    console.log('3. 初始化微信支付实例...');
    const payment = new WxPay({
      appid: config.appId,
      mchid: config.mchId,
      publicKey: publicKey,
      privateKey: privateKey,
      key: config.apiV3Key,
    });
    console.log('✅ 微信支付实例初始化成功');

    console.log('\n=== 问题诊断 ===');
    console.log('从日志可以看到，微信支付配置正确，但是使用了无效的openid');
    console.log('可能的原因：');
    console.log('1. 用户没有通过正确的微信登录流程');
    console.log('2. 用户的openid为空或无效');
    console.log('3. 用户表中的open_id字段数据有问题');

    console.log('\n=== 解决方案 ===');
    console.log('1. 检查用户登录流程，确保获取到有效的openid');
    console.log('2. 在支付前验证用户的openid是否有效');
    console.log('3. 添加更详细的日志记录用户的openid信息');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  }
}

testWechatPay();
