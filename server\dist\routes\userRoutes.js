"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 用户模块路由
 * 处理用户相关的API路由
 */
const express_1 = __importDefault(require("express"));
const userController_1 = __importDefault(require("../controllers/userController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 登录接口
router.post('/login', userController_1.default.login);
// 获取用户信息接口
router.get('/info', auth_1.verifyToken, userController_1.default.getUserInfo);
// 更新用户信息接口
router.post('/update', auth_1.verifyToken, userController_1.default.updateUserInfo);
// 获取搜索历史接口
router.get('/searchHistory', auth_1.verifyToken, userController_1.default.getSearchHistory);
// 清除搜索历史接口
router.post('/clearSearchHistory', auth_1.verifyToken, userController_1.default.clearSearchHistory);
// 获取用户背景设置接口
router.get('/backgroundSettings', auth_1.verifyToken, userController_1.default.getUserBackgroundSettings);
// 更新用户背景设置接口
router.post('/updateBackgroundSettings', auth_1.verifyToken, userController_1.default.updateUserBackgroundSettings);
// 获取用户大米信息接口
router.get('/coins', auth_1.verifyToken, userController_1.default.getUserCoins);
// 充值大米接口
router.post('/recharge', auth_1.verifyToken, userController_1.default.rechargeCoins);
// 获取大米交易记录接口
router.get('/coinRecords', auth_1.verifyToken, userController_1.default.getCoinRecords);
// 获取用户签到数据接口
router.get('/signInData', auth_1.verifyToken, userController_1.default.getSignInData);
// 签到接口
router.post('/signIn', auth_1.verifyToken, userController_1.default.signIn);
// 观看广告接口
router.post('/watchAd', auth_1.verifyToken, userController_1.default.watchAd);
// 补签接口
router.post('/compensateSignIn', auth_1.verifyToken, userController_1.default.compensateSignIn);
// 获取会员状态接口
router.get('/membershipStatus', auth_1.verifyToken, userController_1.default.getMembershipStatus);
// 订阅会员接口
router.post('/subscribeMembership', auth_1.verifyToken, userController_1.default.subscribeMembership);
exports.default = router;
//# sourceMappingURL=userRoutes.js.map