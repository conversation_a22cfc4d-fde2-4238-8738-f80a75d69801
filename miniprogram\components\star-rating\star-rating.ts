/**
 * 星级评分组件 - 优化版本
 * 功能：统一管理星级显示，减少重复DOM节点，预加载图标
 */

// 预加载星级图标
const preloadStarIcons = () => {
  const icons = [
    '/static/images/icons/star.png',
    '/static/images/icons/star1.png'
  ];
  
  icons.forEach(src => {
    // 使用小程序的图片预加载
    wx.getImageInfo({
      src: src,
      success: () => {
        console.log('星级图标预加载成功:', src);
      },
      fail: () => {
        console.log('星级图标预加载失败:', src);
      }
    });
  });
};

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 评分值 (0-5)
    rating: {
      type: Number,
      value: 0
    },
    // 星星大小
    size: {
      type: String,
      value: '24rpx'
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 星星间距
    spacing: {
      type: String,
      value: '4rpx'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    stars: [] as boolean[]
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 预加载星级图标
      preloadStarIcons();
      this.updateStars();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'rating': function() {
      this.updateStars();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新星星状态
     */
    updateStars() {
      const rating = Math.max(0, Math.min(5, this.properties.rating));
      const stars: boolean[] = [];
      
      for (let i = 0; i < 5; i++) {
        stars.push(i < rating);
      }
      
      this.setData({ stars });
    },

    /**
     * 星星点击事件
     */
    onStarTap(e: any) {
      if (!this.properties.clickable) return;
      
      const index = e.currentTarget.dataset.index;
      const newRating = index + 1;
      
      this.triggerEvent('change', {
        rating: newRating
      });
    }
  }
}); 