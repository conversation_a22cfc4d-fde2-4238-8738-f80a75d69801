"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 厨房管理模块路由
 * 处理厨房相关的API路由
 */
const express_1 = __importDefault(require("express"));
const kitchenController_1 = __importDefault(require("../controllers/kitchenController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取厨房列表
router.get('/list', auth_1.verifyToken, kitchenController_1.default.getKitchenList);
// 获取用户创建的厨房列表
router.get('/owned', auth_1.verifyToken, kitchenController_1.default.getOwnedKitchenList);
// 获取用户加入的厨房列表
router.get('/joined', auth_1.verifyToken, kitchenController_1.default.getJoinedKitchenList);
// 获取厨房详细信息
router.get('/info', auth_1.verifyToken, kitchenController_1.default.getKitchenInfo);
// 获取厨房基本信息
router.get('/baseInfo', auth_1.optionalAuth, kitchenController_1.default.getKitchenBaseInfo);
// 创建厨房
router.post('/create', auth_1.verifyToken, kitchenController_1.default.createKitchen);
// 更新厨房信息
router.post('/update', auth_1.verifyToken, kitchenController_1.default.updateKitchen);
// 加入厨房
router.post('/join', auth_1.verifyToken, kitchenController_1.default.joinKitchen);
// 退出厨房
router.post('/leave', auth_1.verifyToken, kitchenController_1.default.leaveKitchen);
// 解散厨房
router.post('/dismiss', auth_1.verifyToken, kitchenController_1.default.dismissKitchen);
// 升级厨房
router.post('/upgrade', auth_1.verifyToken, kitchenController_1.default.upgradeKitchen);
// 获取厨房成员列表
router.get('/members', auth_1.verifyToken, kitchenController_1.default.getKitchenMembers);
// 更新成员权限
router.post('/updateMemberPermission', auth_1.verifyToken, kitchenController_1.default.updateMemberPermission);
// 移除成员
router.post('/removeMember', auth_1.verifyToken, kitchenController_1.default.removeMember);
// 获取厨房二维码 - 支持匿名访问（分享厨房模式）
router.get('/qrcode', auth_1.optionalAuth, kitchenController_1.default.getKitchenQrcode);
// 根据ID查找厨房
router.get('/findById', auth_1.optionalAuth, kitchenController_1.default.findKitchenById);
// 搜索厨房信息
router.get('/search', auth_1.verifyToken, kitchenController_1.default.searchKitchen);
// 获取源厨房分类列表
router.get('/categories', auth_1.verifyToken, kitchenController_1.default.getKitchenCategories);
// 获取源厨房分类下的菜品
router.get('/categoryDishes', auth_1.verifyToken, kitchenController_1.default.getCategoryDishes);
// 克隆菜品到目标厨房
router.post('/cloneDishes', auth_1.verifyToken, kitchenController_1.default.cloneDishes);
exports.default = router;
//# sourceMappingURL=kitchenRoutes.js.map