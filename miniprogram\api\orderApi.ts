import { request } from '../utils/request'

// 获取订单列表
export const getOrderList = (params: {
  status?: string;  // 订单状态：all-全部，canceled-已取消，pending-待接单，cooking-烹饪中，completed-已完成
  orderType?: string; // 订单类型：kitchen-厨房订单，mine-我的订单
  page?: number;
  pageSize?: number;
  kitchenId?: string; // 厨房ID
}) => {
  return request({
    url: '/api/order/list',
    method: 'GET',
    data: params,
    showLoading: false
  })
}

// 获取订单详情
export const getOrderDetail = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/detail',
    method: 'GET',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 取消订单
export const cancelOrder = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/cancel',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 拒绝订单
export const rejectOrder = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/reject',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 确认接单
export const acceptOrder = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/accept',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 开始烹饪
export const startCooking = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/startCooking',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 取消烹饪（从烹饪中状态回到待接单状态）
export const cancelCooking = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/cancelCooking',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 取消已接单订单（从已接单状态回到待接单状态）
export const cancelAcceptedOrder = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/cancelAcceptedOrder',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 完成订单
export const completeOrder = (orderId: string, kitchenId?: string) => {
  return request({
    url: '/api/order/complete',
    method: 'POST',
    data: { id: orderId, kitchenId },
    showLoading: false
  })
}

// 获取订单详情（公开访问，用于分享）
export const getPublicOrderDetail = (orderId: string) => {
  return request({
    url: `/api/order/public/${orderId}`,
    method: 'GET',
    data: {},
    showLoading: false,
    // 公开访问不需要token
    noAuth: true
  })
}

