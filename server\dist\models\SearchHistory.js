"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 搜索历史模型
 * 存储用户搜索历史
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 搜索历史模型类
class SearchHistory extends sequelize_1.Model {
}
// 初始化搜索历史模型
SearchHistory.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '历史ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    keyword: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        comment: '搜索关键词',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'SearchHistory',
    tableName: 'search_histories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_keyword',
            fields: ['keyword'],
        },
        {
            name: 'idx_created_at',
            fields: ['created_at'],
        },
    ],
});
exports.default = SearchHistory;
//# sourceMappingURL=SearchHistory.js.map