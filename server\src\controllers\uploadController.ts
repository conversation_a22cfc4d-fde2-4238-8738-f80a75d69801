/**
 * 上传控制器
 * 处理文件上传相关的请求
 */
import { Request, Response, NextFunction } from 'express';
import uploadService from '../services/uploadService';
import logger from '../utils/logger';
import { success, error, ResponseCode } from '../utils/response';
import { BusinessError } from '../middlewares/error';
import { getFileUrl } from '../middlewares/upload';
import { convertToFullUrl } from '../utils/urlManager';

/**
 * 上传菜品图片
 * @route POST /api/upload/dishImage
 */
const uploadDishImage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      throw new BusinessError('未上传文件', ResponseCode.VALIDATION);
    }
    
    const relativePath = getFileUrl(req.file.filename, 'dish');
    const fullUrl = convertToFullUrl(relativePath);
    success(res, { imageUrl: fullUrl });
  } catch (err) {
    next(err);
  }
};

/**
 * 上传背景图片
 * @route POST /api/upload/backgroundImage
 */
const uploadBackgroundImage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      throw new BusinessError('未上传文件', ResponseCode.VALIDATION);
    }
    
    const relativePath = getFileUrl(req.file.filename, 'background');
    const fullUrl = convertToFullUrl(relativePath);
    success(res, { imageUrl: fullUrl });
  } catch (err) {
    next(err);
  }
};

/**
 * 上传分类图标
 * @route POST /api/upload/categoryIcon
 */
const uploadCategoryIcon = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      throw new BusinessError('未上传文件', ResponseCode.VALIDATION);
    }
    
    const relativePath = getFileUrl(req.file.filename, 'category');
    const fullUrl = convertToFullUrl(relativePath);
    success(res, { imageUrl: fullUrl });
  } catch (err) {
    next(err);
  }
};

/**
 * 上传厨房头像
 * @route POST /api/upload/kitchenAvatar
 */
const uploadKitchenAvatar = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      throw new BusinessError('未上传文件', ResponseCode.VALIDATION);
    }
    
    const relativePath = getFileUrl(req.file.filename, 'kitchen');
    const fullUrl = convertToFullUrl(relativePath);
    success(res, { imageUrl: fullUrl });
  } catch (err) {
    next(err);
  }
};

/**
 * 上传用户头像
 * @route POST /api/upload/userAvatar
 */
const uploadUserAvatar = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      throw new BusinessError('未上传文件', ResponseCode.VALIDATION);
    }
    
    const relativePath = getFileUrl(req.file.filename, 'avatar');
    const fullUrl = convertToFullUrl(relativePath);
    success(res, { imageUrl: fullUrl });
  } catch (err) {
    next(err);
  }
};

export default {
  uploadDishImage,
  uploadBackgroundImage,
  uploadCategoryIcon,
  uploadKitchenAvatar,
  uploadUserAvatar
};
