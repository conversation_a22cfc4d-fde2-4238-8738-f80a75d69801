"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 消息模块路由
 * 处理消息相关的API路由
 */
const express_1 = __importDefault(require("express"));
const messageController_1 = __importDefault(require("../controllers/messageController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取消息数量
router.get('/messageCount', auth_1.verifyToken, messageController_1.default.getMessageCount);
// 获取标签页内容
router.get('/tabContent', auth_1.verifyToken, messageController_1.default.getTabContent);
// 标记消息为已读
router.post('/markRead', auth_1.verifyToken, messageController_1.default.markMessageRead);
// 批量标记消息为已读
router.post('/markAllRead', auth_1.verifyToken, messageController_1.default.markAllRead);
// 获取所有未读消息数量
router.get('/unreadCounts', auth_1.verifyToken, messageController_1.default.getUnreadCounts);
exports.default = router;
//# sourceMappingURL=messageRoutes.js.map