"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 消息服务
 * 处理消息相关的业务逻辑
 */
const sequelize_1 = require("sequelize");
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
const helpers_1 = require("../utils/helpers");
const config_1 = __importDefault(require("../config/config"));
/**
 * 获取消息数量
 * @param userId 用户ID
 * @returns 消息数量
 */
const getMessageCount = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    // 获取各类型未读消息数量 - 包含用户消息和系统消息
    const likeCounts = yield models_1.Message.count({
        where: {
            [sequelize_1.Op.or]: [
                { user_id: userId },
                { user_id: 0 } // 系统消息
            ],
            type: 'like',
            is_read: false,
        },
    });
    const addCounts = yield models_1.Message.count({
        where: {
            [sequelize_1.Op.or]: [
                { user_id: userId },
                { user_id: 0 } // 系统消息
            ],
            type: 'add',
            is_read: false,
        },
    });
    const systemCounts = yield models_1.Message.count({
        where: {
            [sequelize_1.Op.or]: [
                { user_id: userId },
                { user_id: 0 } // 系统消息
            ],
            type: {
                [sequelize_1.Op.in]: ['system', 'notice', 'promotion', 'warning'] // 包含所有系统消息类型
            },
            is_read: false,
        },
    });
    const commentCounts = yield models_1.Message.count({
        where: {
            [sequelize_1.Op.or]: [
                { user_id: userId },
                { user_id: 0 } // 系统消息
            ],
            type: 'comment',
            is_read: false,
        },
    });
    // 获取总未读消息数量
    const totalCounts = yield models_1.Message.count({
        where: {
            [sequelize_1.Op.or]: [
                { user_id: userId },
                { user_id: 0 } // 系统消息
            ],
            is_read: false,
        },
    });
    return {
        like: likeCounts,
        add: addCounts,
        system: systemCounts,
        comment: commentCounts,
        total: totalCounts,
        count: totalCounts,
    };
});
/**
 * 获取标签页内容
 * @param userId 用户ID
 * @param type 消息类型
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 消息列表
 */
const getTabContent = (userId_1, type_1, ...args_1) => __awaiter(void 0, [userId_1, type_1, ...args_1], void 0, function* (userId, type, page = 1, pageSize = 10) {
    // 检查类型是否有效
    if (!['like', 'add', 'system', 'comment', 'all'].includes(type)) {
        throw new error_1.BusinessError('无效的消息类型');
    }
    // 构建查询条件 - 包含用户消息和系统消息
    const where = {
        [sequelize_1.Op.or]: [
            { user_id: userId },
            { user_id: 0 } // 系统消息
        ]
    };
    if (type !== 'all') {
        if (type === 'system') {
            // 系统消息包含多种类型
            where.type = {
                [sequelize_1.Op.in]: ['system', 'notice', 'promotion', 'warning']
            };
        }
        else {
            where.type = type;
        }
    }
    // 分页参数
    const { limit, offset } = (0, helpers_1.getPagination)(page, pageSize);
    // 查询消息
    const { count, rows } = yield models_1.Message.findAndCountAll({
        where,
        include: [
            {
                model: models_1.User,
                as: 'user',
                attributes: ['id', 'nick_name', 'avatar_url'],
                required: false // 允许user为null（系统消息）
            },
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset,
    });
    // 构建返回结果
    const messages = rows.map(message => ({
        id: message.id.toString(),
        type: message.type,
        title: message.title,
        content: message.content,
        image: message.image,
        isRead: message.is_read,
        time: message.created_at,
        user: message.user_id === 0 ? {
            id: 0,
            nickName: '系统',
            avatarUrl: `${config_1.default.server.corsOrigin}/uploads/default/avatar.jpg`
        } : (message.user ? {
            id: message.user.id,
            nickName: message.user.nick_name,
            avatarUrl: message.user.avatar_url,
        } : null),
    }));
    return (0, helpers_1.buildPaginationResult)(messages, count, page, pageSize);
});
/**
 * 标记消息为已读
 * @param userId 用户ID
 * @param messageId 消息ID（可选）
 * @param type 消息类型（可选）
 */
const markMessageRead = (userId, messageId, type) => __awaiter(void 0, void 0, void 0, function* () {
    // 构建查询条件 - 包含用户消息和系统消息
    const where = {
        [sequelize_1.Op.or]: [
            { user_id: userId },
            { user_id: 0 } // 系统消息
        ]
    };
    if (messageId) {
        where.id = messageId;
    }
    else if (type) {
        if (type === 'system') {
            // 系统消息包含多种类型
            where.type = {
                [sequelize_1.Op.in]: ['system', 'notice', 'promotion', 'warning']
            };
        }
        else {
            where.type = type;
        }
    }
    // 更新消息状态
    yield models_1.Message.update({ is_read: true }, { where });
});
/**
 * 获取所有未读消息数量
 * @param userId 用户ID
 * @returns 未读消息数量
 */
const getUnreadCounts = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    // 获取消息数量
    const messageCounts = yield getMessageCount(userId);
    // 获取发现页未读数量（这里简化处理，实际应该从发现服务获取）
    const discoverCounts = 0;
    return {
        message: messageCounts.total,
        discover: discoverCounts,
        total: messageCounts.total + discoverCounts,
    };
});
/**
 * 创建消息
 * @param userId 用户ID
 * @param type 消息类型
 * @param title 消息标题
 * @param content 消息内容
 * @param image 图片URL（可选）
 * @returns 创建的消息
 */
const createMessage = (userId, type, title, content, image) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查类型是否有效
    if (!['like', 'add', 'system', 'comment'].includes(type)) {
        throw new error_1.BusinessError('无效的消息类型');
    }
    // 创建消息
    const message = yield models_1.Message.create({
        user_id: userId,
        type,
        title,
        content,
        image: image || '',
        is_read: false,
    });
    return {
        id: message.id,
        type: message.type,
        title: message.title,
        content: message.content,
        image: message.image,
        isRead: message.is_read,
        createdAt: message.created_at,
    };
});
/**
 * 批量标记消息为已读
 * @param userId 用户ID
 * @param type 消息类型（可选，不传则标记所有消息为已读）
 */
const markAllRead = (userId, type) => __awaiter(void 0, void 0, void 0, function* () {
    // 构建查询条件 - 包含用户消息和系统消息
    const where = {
        [sequelize_1.Op.or]: [
            { user_id: userId },
            { user_id: 0 } // 系统消息
        ],
        is_read: false
    };
    if (type) {
        if (type === 'system') {
            // 系统消息包含多种类型
            where.type = {
                [sequelize_1.Op.in]: ['system', 'notice', 'promotion', 'warning']
            };
        }
        else {
            where.type = type;
        }
    }
    // 更新消息状态
    yield models_1.Message.update({ is_read: true }, { where });
});
exports.default = {
    getMessageCount,
    getTabContent,
    markMessageRead,
    getUnreadCounts,
    createMessage,
    markAllRead,
};
//# sourceMappingURL=messageService.js.map