// 厨房管理页面
import { checkLogin } from '../../utils/util'
import { getKitchenInfo, get<PERSON><PERSON><PERSON><PERSON>ist, KitchenInfo, dismissKitchen, upgrade<PERSON><PERSON><PERSON>, getCurrent<PERSON>itchenBaseInfo, leave<PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON>, join<PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getJoined<PERSON><PERSON>enList, findKitchenById, getKitchenCategories, getKitchenCategoryDishes, cloneDishes, updateKitchenInfo, getKitchenMembers, updateMemberPermission, getTableList, TableInfo, addTable, updateTable, deleteTable, sortTables } from '../../api/kitchenApi'
import { getUserBackgroundSettings, updateUserBackgroundSettings, getUserInfo, getMembershipStatus } from '../../api/userApi'
import { getDishCategories } from '../../api/dishApi'
import { uploadKitchenAvatar } from '../../api/uploadApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker'

// 创建事件通道实例
const eventChannel = {
  listeners: {} as Record<string, Function[]>,

  // 监听事件
  on(eventName: string, callback: Function) {
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = []
    }
    this.listeners[eventName].push(callback)
  },

  // 移除监听
  off(eventName: string, callback: Function) {
    if (this.listeners[eventName]) {
      const index = this.listeners[eventName].indexOf(callback)
      if (index > -1) {
        this.listeners[eventName].splice(index, 1)
      }
    }
  },

  // 触发事件
  emit(eventName: string, data: any) {
    if (this.listeners[eventName]) {
      this.listeners[eventName].forEach(callback => {
        callback(data)
      })
    }
  }
}

// 本地存储键名
const LAST_SELECTED_KITCHEN_KEY = 'last_selected_kitchen'

Page({
  // 页面数据
  data: {
    isLoggedIn: false,
    kitchenInfo: {} as KitchenInfo,
    kitchenList: [] as KitchenInfo[],
    currentKitchenIndex: 0, // 当前选中的厨房索引
    showDismissConfirm: false, // 解散厨房确认弹窗
    switchKitchenVisible: false, // 切换厨房弹窗显示状态
    showUpgradeConfirm: false, // 升级厨房确认弹窗
    showAddKitchenModal: false, // 新增/加入厨房弹窗显示状态
    showMyKitchenModal: false, // 我的厨房弹窗显示状态
    myKitchenActiveTab: 'owned', // 我的厨房当前选中的标签，owned 或 joined
    ownedKitchens: [] as KitchenInfo[], // 我创建的厨房列表
    joinedKitchens: [] as KitchenInfo[], // 我加入的厨房列表
    activeTab: 'create', // 当前选中的标签，create 或 join
    newKitchenName: '', // 新厨房名称
    newKitchenNotice: '', // 新厨房公告
    newKitchenAvatarUrl: '', // 新厨房头像URL
    tempAvatarPath: '', // 临时头像路径（用于新增厨房）
    joinKitchenId: '', // 要加入的厨房ID

    // 背景设置相关
    showBackgroundSettings: false, // 显示背景设置组件
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 导航栏背景样式
    navBgIndex: 0, // 导航栏背景索引
    shopBg: '', // 店铺背景

    // 克隆菜谱相关
    showCloneMenuModal: false, // 克隆菜谱弹窗显示状态
    cloneStep: 1, // 克隆步骤：1-输入厨房ID, 2-选择分类, 3-选择菜品, 4-结果
    sourceKitchenId: '', // 源厨房ID
    sourceKitchen: {} as KitchenInfo, // 源厨房信息
    sourceCategories: [] as any[], // 源厨房分类列表
    selectedCategoryId: '', // 选中的分类ID
    sourceDishes: [] as any[], // 源厨房分类下的菜品
    targetCategories: [] as any[], // 当前厨房的分类列表
    targetCategoryIndex: 0, // 目标分类索引
    selectedDishes: [] as string[], // 选中的菜品ID列表
    cloneResult: { success: false, clonedCount: 0, message: '' }, // 克隆结果
    showTargetCategoryModal: false, // 显示目标分类选择模态框
    targetCategorySelectIndex: 0, // 目标分类选择索引
    showManageKitchenModal: false, // 显示管理厨房弹窗
    editKitchenName: '', // 编辑厨房名称
    editKitchenNotice: '', // 编辑厨房公告
    editKitchenAvatarUrl: '', // 编辑厨房头像URL
    editKitchenBackgroundUrl: '', // 编辑厨房背景图URL
    userCoins: 0, // 用户大米余额
    showKitchenMember: false, // 是否显示厨房会员弹窗
    showMemberManageModal: false, // 是否显示成员管理弹窗
    kitchenMembers: [] as any[], // 厨房成员列表
    showTableManager: false, // 是否显示桌号管理弹窗
    tableList: [] as TableInfo[], // 桌号列表
    isVip: false, // 是否是会员用户

    // 勾选框样式选择
    checkboxStyle: 'bubble' as 'bubble', // 勾选框样式：圆形气泡

    // 默认图片配置
    defaultImages: DEFAULT_IMAGES,
  },

  // 生命周期函数--监听页面加载
  async onLoad() {
    console.log('厨房管理页面加载');

    // 立即设置默认背景和基础状态，避免加载闪烁
    const savedNavBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';
    const savedNavBgIndex = wx.getStorageSync('navBgIndex') || 0;
    const savedShopBg = wx.getStorageSync('shopBg') || '';
    
    // 预设默认的厨房信息，减少空白状态显示时间
    const defaultKitchenInfo: KitchenInfo = {
      id: '',
      name: '加载中...',
      level: 1,
      owner: '',
      isOwner: false,
      kitchenCount: 0,
      kitchenLimit: 1,
      categoryCount: 0,
      categoryLimit: 5,
      dishCount: 0,
      dishLimit: 50,
      memberCount: 0,
      createdAt: ''
    };
    
    this.setData({
      navBgStyle: savedNavBgStyle,
      navBgIndex: savedNavBgIndex,
      shopBg: savedShopBg,
      kitchenInfo: defaultKitchenInfo
    });

    // 检查用户是否登录
    this.checkLoginStatus();

    if (!this.data.isLoggedIn) {
      // 如果未登录，重置厨房信息
      this.setData({
        kitchenInfo: {
          id: '',
          name: '请先登录',
          level: 1,
          owner: '',
          isOwner: false,
          kitchenCount: 0,
          kitchenLimit: 1,
          categoryCount: 0,
          categoryLimit: 5,
          dishCount: 0,
          dishLimit: 50,
          memberCount: 0,
          createdAt: ''
        } as KitchenInfo
      });
      return;
    }

    try {
      // 并发加载基础数据，优化加载性能
      const promises = await Promise.all([
        getUserInfo().catch(() => ({ error: 1, body: null })),
        getKitchenList().catch(() => ({ error: 1, body: [] }))
      ]);
      
      const userRes = promises[0];
      const kitchenListRes = promises[1];

      // 更新用户余额
      if (userRes.error === 0) {
        this.setData({
          userCoins: (userRes.body && userRes.body.coins) ? userRes.body.coins : 0
        });
      }

      // 处理厨房列表
      if (kitchenListRes.error === 0) {
        const kitchens = kitchenListRes.body || [];
        console.log('获取到厨房列表:', kitchens);

        this.setData({
          kitchenList: kitchens
        });

        if (kitchens.length > 0) {
          // 有厨房列表时，立即加载上次选择的厨房
          this.loadLastSelectedKitchen();
        } else {
          console.log('用户没有厨房数据，显示空状态');
          this.setData({
            kitchenInfo: {
              id: '',
              name: '暂无厨房',
              level: 1,
              owner: '',
              isOwner: false,
              kitchenCount: 0,
              kitchenLimit: 1,
              categoryCount: 0,
              categoryLimit: 5,
              dishCount: 0,
              dishLimit: 50,
              memberCount: 0,
              createdAt: ''
            } as KitchenInfo
          });

          // 延迟显示创建厨房提示，避免干扰初始加载
          setTimeout(() => {
            wx.showModal({
              title: '创建厨房',
              content: '您还没有厨房，现在创建一个吧！',
              confirmText: '立即创建',
              confirmColor: '#FF6B35',
              success: (res) => {
                if (res.confirm) {
                  this.showAddKitchenModal();
                }
              }
            });
          }, 500);
        }
      } else {
        console.error('获取厨房列表失败');
        this.setData({
          kitchenInfo: {
            id: '',
            name: '加载失败',
            level: 1,
            owner: '',
            isOwner: false,
            kitchenCount: 0,
            kitchenLimit: 1,
            categoryCount: 0,
            categoryLimit: 5,
            dishCount: 0,
            dishLimit: 50,
            memberCount: 0,
            createdAt: ''
          } as KitchenInfo
        });
      }

      // 并发加载其他不影响主要UI的数据
      Promise.all([
        this.loadBackgroundSettings(),
        this.fetchMembershipStatus()
      ]).then(function() {
        // 加载成功
      }).catch(function(error) {
        console.error('加载辅助数据失败:', error);
      });

      // 注册主题变更监听器
      const app = getApp<IAppOption>();
      app.addThemeChangeListener(this.onThemeChanged.bind(this));

    } catch (error) {
      console.error('页面加载出错:', error);
      this.setData({
        kitchenInfo: {
          id: '',
          name: '加载出错',
          level: 1,
          owner: '',
          isOwner: false,
          kitchenCount: 0,
          kitchenLimit: 1,
          categoryCount: 0,
          categoryLimit: 5,
          dishCount: 0,
          dishLimit: 50,
          memberCount: 0,
          createdAt: ''
        } as KitchenInfo
      });
    }
  },

  // 生命周期函数--页面显示时
  async onShow() {
    console.log('厨房管理页面显示');

    // 每次页面显示时检查登录状态
    this.checkLoginStatus();

    // 如果用户已登录，检查是否需要刷新数据
    if (this.data.isLoggedIn) {
      try {
        // 仅更新用户余额，避免频繁请求厨房列表
        const res = await getUserInfo();
        if (res.error === 0) {
          this.setData({
            userCoins: res.body.coins || 0
          });
        }

        // 只有在厨房列表为空时才重新获取（避免频繁请求）
        if (!this.data.kitchenList || this.data.kitchenList.length === 0) {
          console.log('厨房列表为空，重新获取');
          const result = await getKitchenList();

          if (result.error === 0) {
            const kitchens = result.body || [];
            console.log('重新获取到厨房列表:', kitchens);

            this.setData({
              kitchenList: kitchens
            });

            if (kitchens.length > 0) {
              this.loadLastSelectedKitchen();
            } else {
              console.log('用户没有厨房数据，显示空状态');
              this.setData({
                kitchenInfo: {
                  id: '',
                  name: '暂无厨房',
                  level: 1,
                  owner: '',
                  isOwner: false,
                  kitchenCount: 0,
                  kitchenLimit: 1,
                  categoryCount: 0,
                  categoryLimit: 5,
                  dishCount: 0,
                  dishLimit: 50,
                  memberCount: 0,
                  createdAt: ''
                } as KitchenInfo
              });
            }
          } else {
            console.error('获取厨房列表失败:', result.message);
          }
        } else {
          // 如果有厨房列表且当前没有选中厨房，则加载第一个
          if (!this.data.kitchenInfo.id && this.data.kitchenList.length > 0) {
            this.loadLastSelectedKitchen();
          }
        }
      } catch (error) {
        console.error('页面显示时加载数据失败', error);
      }
    }
  },

  // 页面隐藏时
  onHide() {
    // 移除全局监听
    this.removeEventListeners()
  },

  // 页面卸载时
  onUnload() {
    // 移除全局监听
    this.removeEventListeners()
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = checkLogin()
    this.setData({ isLoggedIn })

    // 移除强制登录弹窗，允许用户浏览页面
    // 未登录时显示相应的UI状态即可
  },

  // 获取餐厅基本信息
  async fetchRestaurantInfo() {
    try {
      const result = await getCurrentKitchenBaseInfo()

      if (result.error === 0) {
        console.log('获取餐厅基本信息成功:', result.body)
        // 这里可以根据需要更新页面数据
      }
    } catch (error) {
      console.error('获取餐厅基本信息失败', error)
    }
  },

  // 获取厨房信息
  async fetchKitchenInfo(kitchenId?: string) {
    try {
      // 调用API获取厨房信息
      const result = await getKitchenInfo(kitchenId)

      if (result.error === 0) {
        this.setData({
          kitchenInfo: result.body
        })

        // 获取会员状态
        this.fetchMembershipStatus()
      } else {
        wx.showToast({
          title: result.message || '获取厨房信息失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取厨房信息失败', error)
      wx.showToast({
        title: '获取厨房信息失败',
        icon: 'none'
      })
    }
  },

  // 获取厨房列表
  async fetchKitchenList() {
    try {
      const result = await getKitchenList();

      if (result.error === 0) {
        // 处理后端返回的厨房列表数据
        const kitchens = result.body || [];

        this.setData({
          kitchenList: kitchens
        });

        if (kitchens.length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        wx.showToast({
          title: result.message || '获取厨房列表失败',
          icon: 'none'
        });
        return false;
      }
    } catch (error) {
      console.error('获取厨房列表失败:', error);
      wx.showToast({
        title: '获取厨房列表失败',
        icon: 'none'
      });
      return false;
    }
  },

  // 加载上次选择的厨房
  loadLastSelectedKitchen() {
    try {
      const lastKitchenId = wx.getStorageSync(LAST_SELECTED_KITCHEN_KEY)
      if (lastKitchenId && this.data.kitchenList.length > 0) {
        // 查找上次选择的厨房索引
        const index = this.data.kitchenList.findIndex(kitchen => kitchen.id === lastKitchenId)
        if (index !== -1) {
          // 找到了上次选择的厨房，立即显示基础信息
          const selectedKitchen = this.data.kitchenList[index];
          this.setData({ 
            currentKitchenIndex: index,
            kitchenInfo: {
              id: selectedKitchen.id,
              name: selectedKitchen.name,
              level: selectedKitchen.level || 1,
              owner: selectedKitchen.owner || '',
              isOwner: selectedKitchen.isOwner || false,
              // 其他信息稍后异步加载
              kitchenCount: 0,
              kitchenLimit: 1,
              categoryCount: 0,
              categoryLimit: 5,
              dishCount: 0,
              dishLimit: 50,
              memberCount: 0,
              createdAt: selectedKitchen.createdAt || ''
            }
          });
          
          // 异步加载详细信息，不阻塞页面显示
          try {
            this.fetchKitchenInfo(lastKitchenId);
          } catch (error) {
            console.error('加载厨房详细信息失败', error);
          }
          return
        }
      }

      // 如果没有上次选择的厨房或找不到该厨房，则加载第一个厨房
      if (this.data.kitchenList.length > 0) {
        const firstKitchen = this.data.kitchenList[0];
        // 立即显示第一个厨房的基础信息
        this.setData({ 
          currentKitchenIndex: 0,
          kitchenInfo: {
            id: firstKitchen.id,
            name: firstKitchen.name,
            level: firstKitchen.level || 1,
            owner: firstKitchen.owner || '',
            isOwner: firstKitchen.isOwner || false,
            // 其他信息稍后异步加载
            kitchenCount: 0,
            kitchenLimit: 1,
            categoryCount: 0,
            categoryLimit: 5,
            dishCount: 0,
            dishLimit: 50,
            memberCount: 0,
            createdAt: firstKitchen.createdAt || ''
          }
        });

        // 保存第一个厨房ID到本地存储
        try {
          wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, firstKitchen.id)
        } catch (error) {
          console.error('保存厨房选择失败', error)
        }

        // 异步加载详细信息
        try {
          this.fetchKitchenInfo(firstKitchen.id);
        } catch (error) {
          console.error('加载厨房详细信息失败', error);
        }
      }
    } catch (error) {
      console.error('加载上次选择的厨房失败', error)
      // 出错时加载第一个厨房
      if (this.data.kitchenList.length > 0) {
        const firstKitchen = this.data.kitchenList[0];
        this.setData({ 
          currentKitchenIndex: 0,
          kitchenInfo: {
            id: firstKitchen.id,
            name: firstKitchen.name,
            level: firstKitchen.level || 1,
            owner: firstKitchen.owner || '',
            isOwner: firstKitchen.isOwner || false,
            kitchenCount: 0,
            kitchenLimit: 1,
            categoryCount: 0,
            categoryLimit: 5,
            dishCount: 0,
            dishLimit: 50,
            memberCount: 0,
            createdAt: firstKitchen.createdAt || ''
          }
        });

        // 保存第一个厨房ID到本地存储
        try {
          wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, firstKitchen.id)
        } catch (error) {
          console.error('保存厨房选择失败', error)
        }

        // 异步加载详细信息
        try {
          this.fetchKitchenInfo(firstKitchen.id);
        } catch (error) {
          console.error('加载厨房详细信息失败', error);
        }
      }
    }
  },

  // 设置全局事件监听
  setupEventListeners() {
    // 监听订单状态变化事件
    eventChannel.on('orderStatusChanged', this.handleOrderStatusChanged)

    // 监听厨房信息变化事件
    eventChannel.on('kitchenInfoChanged', this.handleKitchenInfoChanged)
  },

  // 移除全局事件监听
  removeEventListeners() {
    // 移除订单状态变化事件监听
    eventChannel.off('orderStatusChanged', this.handleOrderStatusChanged)

    // 移除厨房信息变化事件监听
    eventChannel.off('kitchenInfoChanged', this.handleKitchenInfoChanged)
  },

  // 处理订单状态变化事件
  handleOrderStatusChanged(data: any) {
    console.log('订单状态已变更', data)
    // 这里可以添加处理订单状态变化的逻辑
  },

  // 处理厨房信息变化事件
  handleKitchenInfoChanged(data: any) {
    console.log('厨房信息已变更', data)
    // 重新获取厨房信息
    this.fetchKitchenInfo(this.data.kitchenInfo.id)
  },

  // 显示切换厨房弹窗
  switchKitchen() {
    // 显示切换厨房弹窗
    this.setData({
      switchKitchenVisible: true
    })
  },

  // 关闭切换厨房弹窗
  closeSwitchKitchen() {
    this.setData({
      switchKitchenVisible: false
    })
  },

  // 选择厨房
  selectKitchen(e: any) {
    const index = e.currentTarget.dataset.index
    if (index !== this.data.currentKitchenIndex) {
      this.changeCurrentKitchen(index)
    }
    this.closeSwitchKitchen()
  },

  // 更改当前厨房
  async changeCurrentKitchen(index: number) {
    const { kitchenList } = this.data
    if (index >= 0 && index < kitchenList.length) {
      const kitchenId = kitchenList[index].id

      this.setData({
        currentKitchenIndex: index
      })

      // 保存当前选择的厨房ID到本地存储
      try {
        wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, kitchenId)
      } catch (error) {
        console.error('保存厨房选择失败', error)
      }

      // 获取新厨房的详细信息
      const result = await getKitchenInfo(kitchenId)

      if (result.error === 0) {
        this.setData({
          kitchenInfo: result.body
        })

        // 发送全局厨房切换事件，通知其他页面
        const app = getApp<IAppOption>();
        if (app.globalData) {
          // 安全设置全局厨房ID，避免类型错误
          (app.globalData as any).currentKitchenId = kitchenId;
        }
        
        // 触发全局事件，通知其他页面厨房已切换
        eventChannel.emit('kitchenSwitched', {
          kitchenId: kitchenId,
          kitchenInfo: result.body
        });

        wx.showToast({
          title: '已切换到 ' + result.body.name,
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.message || '切换厨房失败',
          icon: 'none'
        })
      }
    }
  },

  // 复制厨房ID
  copyKitchenId() {
    wx.setClipboardData({
      data: this.data.kitchenInfo.id,
      success: () => {
        wx.showToast({
          title: '厨房ID已复制',
          icon: 'success'
        })
      }
    })
  },

  // 显示升级厨房确认弹窗
  showUpgradeConfirm() {
    this.setData({
      showUpgradeConfirm: true
    })
  },

  // 取消升级厨房
  cancelUpgrade() {
    this.setData({
      showUpgradeConfirm: false
    })
  },

  // 确认升级厨房
  async confirmUpgrade() {
    this.setData({
      showUpgradeConfirm: false
    })

    // 检查大米余额是否足够（改为38大米）
    if (this.data.userCoins < 38) {
      wx.showToast({
        title: '大米余额不足',
        icon: 'none'
      })
      return
    }

    try {
      const result = await upgradeKitchen(this.data.kitchenInfo.id)

      if (result.error === 0) {
        // 更新余额（扣除38大米）
        this.setData({
          userCoins: this.data.userCoins - 38
        });

        wx.showToast({
          title: '厨房升级成功',
          icon: 'success'
        })

        // 更新厨房信息
        this.fetchKitchenInfo(this.data.kitchenInfo.id)
      } else {
        wx.showToast({
          title: result.message || '升级失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('升级厨房失败', error)
      wx.showToast({
        title: '升级失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示解散厨房确认弹窗
  showDismissConfirm() {
    this.setData({
      showDismissConfirm: true
    })
  },

  // 取消解散厨房
  cancelDismiss() {
    this.setData({
      showDismissConfirm: false
    })
  },

  // 确认解散厨房或退出厨房
  async confirmDismiss() {
    this.setData({
      showDismissConfirm: false
    })

    try {
      let result;

      // 根据是否是创建者决定是解散还是退出厨房
      if (this.data.kitchenInfo.isOwner) {
        // 解散厨房
        result = await dismissKitchen(this.data.kitchenInfo.id)
      } else {
        // 退出厨房
        result = await leaveKitchen(this.data.kitchenInfo.id)
      }

      if (result.error === 0) {
        // 提示操作成功
        wx.showToast({
          title: this.data.kitchenInfo.isOwner ? '厨房已解散' : '已退出厨房',
          icon: 'success'
        })

        // 更新厨房列表
        await this.fetchKitchenList()

        // 如果还有其他厨房，切换到第一个厨房
        if (this.data.kitchenList.length > 0) {
          // 保存新的厨房ID到本地存储
          try {
            wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, this.data.kitchenList[0].id)
          } catch (error) {
            console.error('保存厨房选择失败', error)
          }

          this.changeCurrentKitchen(0)
        } else {
          // 如果没有其他厨房，清除本地存储的厨房ID
          try {
            wx.removeStorageSync(LAST_SELECTED_KITCHEN_KEY)
          } catch (error) {
            console.error('清除厨房选择失败', error)
          }

          // 返回上一页
          wx.navigateBack()
        }
      } else {
        wx.showToast({
          title: result.message || (this.data.kitchenInfo.isOwner ? '解散失败' : '退出失败'),
          icon: 'none'
        })
      }
    } catch (error) {
      console.error(this.data.kitchenInfo.isOwner ? '解散厨房失败' : '退出厨房失败', error)
      wx.showToast({
        title: this.data.kitchenInfo.isOwner ? '解散失败，请重试' : '退出失败，请重试',
        icon: 'none'
      })
    }
  },

  // 管理厨房
  manageKitchen() {
    // 显示管理厨房弹窗
    this.setData({
      showManageKitchenModal: true,
      editKitchenName: this.data.kitchenInfo.name || '',
      editKitchenNotice: this.data.kitchenInfo.notice || '',
      editKitchenAvatarUrl: this.data.kitchenInfo.avatarUrl || '',
      editKitchenBackgroundUrl: this.data.kitchenInfo.backgroundUrl || ''
    })
  },

  // 关闭管理厨房弹窗
  closeManageKitchenModal() {
    this.setData({
      showManageKitchenModal: false
    })
  },

  // 输入编辑厨房名称
  inputEditKitchenName(e: any) {
    const value = e.detail.value;
    
    this.setData({
      editKitchenName: value
    });

    // 实时敏感词检查
    if (value && value.length > 1) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('厨房名称包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 输入编辑厨房公告
  inputEditKitchenNotice(e: any) {
    const value = e.detail.value;
    
    this.setData({
      editKitchenNotice: value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('厨房公告包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 选择厨房头像
  async chooseEditKitchenAvatar() {
    try {
      const res = await new Promise<any>((resolve, reject) => {
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: resolve,
          fail: reject
        })
      })

      // 获取图片临时路径
      const tempFilePath = res.tempFilePaths[0]

      // 显示上传进度
      wx.showLoading({ title: '压缩上传中...' })

      // 上传头像到服务器，传递厨房ID
      const uploadResult: any = await uploadKitchenAvatar(tempFilePath, this.data.kitchenInfo.id)

      wx.hideLoading()

      if (uploadResult.error === 0) {
        // 上传成功，添加时间戳参数破坏缓存，确保新头像立即显示
        const avatarUrlWithCacheBuster = `${uploadResult.body.imageUrl}?v=${Date.now()}`;
        this.setData({
          editKitchenAvatarUrl: avatarUrlWithCacheBuster
        })

        wx.showToast({
          title: '头像上传成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: uploadResult.message || '上传失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('选择或上传头像失败', error)
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      })
    }
  },

  // 确认更新厨房信息
  async confirmUpdateKitchen() {
    const { kitchenInfo, editKitchenName, editKitchenNotice, editKitchenAvatarUrl, editKitchenBackgroundUrl } = this.data

    // 字段验证
    if (!editKitchenName.trim()) {
      wx.showToast({
        title: '请输入厨房名称',
        icon: 'none'
      })
      return
    }

    // 敏感词检查
    const nameCheck = checkSensitiveWord(editKitchenName.trim(), 'content');
    if (nameCheck.hasSensitiveWord) {
      wx.showModal({
        title: '内容审核',
        content: '厨房名称包含不当内容，请重新输入',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
      return;
    }

    if (editKitchenNotice.trim()) {
      const noticeCheck = checkSensitiveWord(editKitchenNotice.trim(), 'content');
      if (noticeCheck.hasSensitiveWord) {
        wx.showModal({
          title: '内容审核',
          content: '厨房公告包含不当内容，请重新输入',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF6B35'
        });
        return;
      }
    }

    // 检查是否有变化
    const hasChanges =
      editKitchenName !== kitchenInfo.name ||
      editKitchenNotice !== (kitchenInfo.notice || '') ||
      editKitchenAvatarUrl !== (kitchenInfo.avatarUrl || '') ||
      editKitchenBackgroundUrl !== (kitchenInfo.backgroundUrl || '')

    if (!hasChanges) {
      wx.showToast({
        title: '未做任何修改',
        icon: 'none'
      })
      return
    }

    try {
      // 调用更新厨房API
      const result = await updateKitchenInfo(kitchenInfo.id, {
        name: editKitchenName,
        notice: editKitchenNotice,
        avatarUrl: editKitchenAvatarUrl,
        backgroundUrl: editKitchenBackgroundUrl
      })

      if (result.error === 0 && result.body.success) {
        wx.showToast({
          title: '厨房信息更新成功',
          icon: 'success'
        })

        // 关闭弹窗
        this.closeManageKitchenModal()

        // 更新厨房信息和列表
        this.fetchKitchenInfo(kitchenInfo.id)
        this.fetchKitchenList()

        // 发送厨房信息变更通知
        eventChannel.emit('kitchenInfoChanged', { id: kitchenInfo.id })
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新厨房信息失败', error)
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示新增/加入厨房弹窗
  showAddKitchenModal() {
    this.setData({
      showAddKitchenModal: true,
      activeTab: 'create', // 默认显示创建厨房标签
      newKitchenName: '',
      newKitchenNotice: '',
      newKitchenAvatarUrl: '',
      tempAvatarPath: '', // 临时头像路径（用于新增厨房）
      joinKitchenId: ''
    })
  },

  // 关闭新增/加入厨房弹窗
  closeAddKitchenModal() {
    this.setData({
      showAddKitchenModal: false
    })
  },

  // 切换标签页
  switchTab(e: any) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 输入新厨房名称
  inputNewKitchenName(e: any) {
    const value = e.detail.value;
    
    this.setData({
      newKitchenName: value
    });

    // 实时敏感词检查
    if (value && value.length > 1) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('厨房名称包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 输入新厨房公告
  inputNewKitchenNotice(e: any) {
    const value = e.detail.value;
    
    this.setData({
      newKitchenNotice: value
    });

    // 实时敏感词检查
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, 'content');
      if (checkResult.hasSensitiveWord) {
        console.warn('厨房公告包含敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 选择厨房头像
  async chooseKitchenAvatar() {
    try {
      const res = await new Promise<any>((resolve, reject) => {
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: resolve,
          fail: reject
        })
      })

      // 获取图片临时路径，暂存到data中
      const tempFilePath = res.tempFilePaths[0]

      // 暂存临时路径，等厨房创建成功后再上传
      this.setData({
        newKitchenAvatarUrl: tempFilePath, // 暂存临时路径
        tempAvatarPath: tempFilePath // 保存临时路径用于后续上传
      })

      wx.showToast({
        title: '头像已选择',
        icon: 'success'
      })
    } catch (error) {
      console.error('选择头像失败', error)
      wx.showToast({
        title: '选择头像失败',
        icon: 'none'
      })
    }
  },

  // 输入要加入的厨房ID
  inputJoinKitchenId(e: any) {
    this.setData({
      joinKitchenId: e.detail.value
    })
  },

  // 确认新增/加入厨房
  async confirmAddKitchen() {
    const { activeTab, newKitchenName, newKitchenNotice, newKitchenAvatarUrl, joinKitchenId } = this.data

    // 字段验证
    if (activeTab === 'create') {
      if (!newKitchenName.trim()) {
        wx.showToast({
          title: '请输入厨房名称',
          icon: 'none'
        })
        return
      }

      // 创建新厨房
      await this.createNewKitchen()
    } else {
      if (!joinKitchenId.trim()) {
        wx.showToast({
          title: '请输入厨房ID',
          icon: 'none'
        })
        return
      }

      // 加入厨房
      await this.joinExistingKitchen()
    }
  },

  // 创建新厨房
  async createNewKitchen() {
    if (!this.data.newKitchenName.trim()) {
      wx.showToast({
        title: '请输入厨房名称',
        icon: 'none'
      })
      return
    }

    // 敏感词检查
    const nameCheck = checkSensitiveWord(this.data.newKitchenName.trim(), 'content');
    if (nameCheck.hasSensitiveWord) {
      wx.showModal({
        title: '内容审核',
        content: '厨房名称包含不当内容，请重新输入',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
      return;
    }

    if (this.data.newKitchenNotice.trim()) {
      const noticeCheck = checkSensitiveWord(this.data.newKitchenNotice.trim(), 'content');
      if (noticeCheck.hasSensitiveWord) {
        wx.showModal({
          title: '内容审核',
          content: '厨房公告包含不当内容，请重新输入',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF6B35'
        });
        return;
      }
    }

    try {
      wx.showLoading({ title: '创建中...' })

      // 先创建厨房（不包含头像）
      const result = await createKitchen({
        name: this.data.newKitchenName.trim(),
        notice: this.data.newKitchenNotice.trim(),
        avatarUrl: '' // 先不设置头像
      })

      if (result.error === 0) {
        const newKitchenId = result.body.id
        let finalAvatarUrl = ''

        // 如果有选择头像，则上传头像
        if (this.data.tempAvatarPath) {
          try {
            wx.showLoading({ title: '上传头像中...' })

            const uploadResult: any = await uploadKitchenAvatar(this.data.tempAvatarPath, newKitchenId)

            if (uploadResult.error === 0) {
              finalAvatarUrl = uploadResult.body.imageUrl

              // 更新厨房头像
              await updateKitchenInfo(newKitchenId, {
                avatarUrl: finalAvatarUrl
              })
            }
          } catch (uploadError) {
            console.error('头像上传失败:', uploadError)
            // 头像上传失败不影响厨房创建
          }
        }

        wx.hideLoading()

        // 增强成功提示，显示详细信息
        wx.showModal({
          title: '厨房创建成功',
          content: `恭喜您成功创建厨房"${this.data.newKitchenName}"！\n\n已消耗 288 大米\n剩余大米：${this.data.userCoins - 288}\n\n现在您可以开始添加菜品分类和菜品了！`,
          showCancel: false,
          confirmText: '开始管理',
          confirmColor: '#FF6B35',
          success: () => {
            // 继续后续操作
          }
        })

        // 重新获取厨房列表
        await this.fetchKitchenList()

        // 自动切换到新创建的厨房
        const newKitchenIndex = this.data.kitchenList.findIndex(kitchen => kitchen.id === newKitchenId)

        if (newKitchenIndex !== -1) {
          // 切换到新厨房
          this.setData({
            currentKitchenIndex: newKitchenIndex
          })

          // 保存最后选择的厨房ID（使用厨房ID而不是索引）
          wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, newKitchenId)

          // 获取新厨房的详细信息，传递正确的厨房ID
          await this.fetchKitchenInfo(newKitchenId)
        }

        // 关闭创建厨房弹窗并重置表单
        this.setData({
          showAddKitchenModal: false,
          newKitchenName: '',
          newKitchenNotice: '',
          newKitchenAvatarUrl: '',
          tempAvatarPath: '' // 清除临时路径
        })
      } else {
        wx.hideLoading()
        wx.showToast({
          title: result.message || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建厨房失败', error)
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      })
    }
  },

  // 加入现有厨房
  async joinExistingKitchen() {
    try {
      wx.showLoading({ title: '加入中...' })

      const result = await joinKitchen(this.data.joinKitchenId)

      wx.hideLoading()

      if (result.error === 0) {
        // 加入成功
        wx.showToast({
          title: '成功加入厨房',
          icon: 'success'
        })

        // 关闭弹窗
        this.closeAddKitchenModal()

        // 更新厨房列表
        await this.fetchKitchenList()

        // 自动切换到新加入的厨房
        // 查找新加入的厨房在列表中的索引
        const joinedKitchenIndex = this.data.kitchenList.findIndex(k => k.id === this.data.joinKitchenId)
        if (joinedKitchenIndex !== -1) {
          await this.changeCurrentKitchen(joinedKitchenIndex)
        }
      } else {
        wx.showToast({
          title: result.message || '加入失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加入厨房失败', error)
      wx.showToast({
        title: '加入失败，请检查厨房ID是否正确',
        icon: 'none'
      })
    }
  },

  // 导航到功能页面
  navigateToFunction(e: any) {
    const { function: functionName } = e.currentTarget.dataset;

    switch (functionName) {
      case 'addKitchen':
        this.showAddKitchenModal();
        break;
      case 'myKitchen':
        this.showMyKitchenModal();
        break;
      case 'kitchenMember':
        this.showKitchenMember();
        break;
      case 'kitchenTheme':
        this.showKitchenTheme();
        break;
      case 'cloneMenu':
        this.navigateToCloneMenu();
        break;
      case 'memberManage':
        this.showMemberManage();
        break;
      case 'kitchenTable':
        this.showTableManager();
        break;
      default:
        break;
    }
  },

  /**
   * 显示我的厨房弹窗
   */
  async showMyKitchenModal() {
    try {
      this.setData({
        showMyKitchenModal: true
      });

      // 获取我创建的和加入的厨房
      // 获取用户创建的厨房列表
      const ownedRes = await getOwnedKitchenList();
      if (ownedRes.error === 0) {
        const ownedKitchens = ownedRes.body || [];
        this.setData({ ownedKitchens });
      }

      // 获取用户加入的厨房列表
      const joinedRes = await getJoinedKitchenList();
      if (joinedRes.error === 0) {
        const joinedKitchens = joinedRes.body || [];
        this.setData({ joinedKitchens });
      }
    } catch (err) {
      console.error('获取厨房列表失败:', err);
      wx.showToast({
        title: '获取厨房列表失败',
        icon: 'none'
      });
    }
  },

  // 关闭我的厨房弹窗
  closeMyKitchenModal() {
    this.setData({
      showMyKitchenModal: false
    })
  },

  // 切换我的厨房标签页
  switchMyKitchenTab(e: any) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      myKitchenActiveTab: tab
    })
  },

  // 从"我的厨房"弹窗中解散厨房
  async dismissKitchenFromModal(e: any) {
    const kitchenId = e.currentTarget.dataset.id
    const index = e.currentTarget.dataset.index

    // 确认弹窗
    wx.showModal({
      title: '确认解散',
      content: '确定要解散该厨房吗？解散后数据将无法恢复。',
      confirmColor: '#E53935',
      success: async (res) => {
        if (res.confirm) {
    try {
            // 调用API解散厨房
      const result = await dismissKitchen(kitchenId)

      if (result.error === 0) {
        wx.showToast({
          title: '厨房已解散',
          icon: 'success'
        })

              // 更新厨房列表
              const newOwnedKitchens = [...this.data.ownedKitchens]
              newOwnedKitchens.splice(index, 1)
              this.setData({
                ownedKitchens: newOwnedKitchens
              })

              // 如果当前查看的正是被解散的厨房，则切换到另一个厨房
        if (this.data.kitchenInfo.id === kitchenId) {
                await this.fetchKitchenList()

          if (this.data.kitchenList.length > 0) {
                  // 保存新的厨房ID到本地存储
                  try {
                    wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, this.data.kitchenList[0].id)
                  } catch (error) {
                    console.error('保存厨房选择失败', error)
                  }

            this.changeCurrentKitchen(0)
                } else {
                  // 如果没有其他厨房，清除本地存储的厨房ID
                  try {
                    wx.removeStorageSync(LAST_SELECTED_KITCHEN_KEY)
                  } catch (error) {
                    console.error('清除厨房选择失败', error)
                  }

                  // 返回上一页
                  wx.navigateBack()
                }
              }

              // 关闭弹窗
              this.closeMyKitchenModal()
      } else {
        wx.showToast({
          title: result.message || '解散失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('解散厨房失败', error)
      wx.showToast({
        title: '解散失败，请重试',
        icon: 'none'
      })
    }
        }
      }
    })
  },

  // 从"我的厨房"弹窗中退出厨房
  async leaveKitchenFromModal(e: any) {
    const kitchenId = e.currentTarget.dataset.id
    const index = e.currentTarget.dataset.index

    // 确认弹窗
    wx.showModal({
      title: '确认退出',
      content: '确定要退出该厨房吗？',
      confirmColor: '#E53935',
      success: async (res) => {
        if (res.confirm) {
    try {
            // 调用API退出厨房
      const result = await leaveKitchen(kitchenId)

      if (result.error === 0) {
        wx.showToast({
          title: '已退出厨房',
          icon: 'success'
        })

              // 更新厨房列表
              const newJoinedKitchens = [...this.data.joinedKitchens]
              newJoinedKitchens.splice(index, 1)
              this.setData({
                joinedKitchens: newJoinedKitchens
              })

              // 如果当前查看的正是退出的厨房，则切换到另一个厨房
        if (this.data.kitchenInfo.id === kitchenId) {
                await this.fetchKitchenList()

          if (this.data.kitchenList.length > 0) {
                  // 保存新的厨房ID到本地存储
                  try {
                    wx.setStorageSync(LAST_SELECTED_KITCHEN_KEY, this.data.kitchenList[0].id)
                  } catch (error) {
                    console.error('保存厨房选择失败', error)
                  }

            this.changeCurrentKitchen(0)
                } else {
                  // 如果没有其他厨房，清除本地存储的厨房ID
                  try {
                    wx.removeStorageSync(LAST_SELECTED_KITCHEN_KEY)
                  } catch (error) {
                    console.error('清除厨房选择失败', error)
                  }

                  // 返回上一页
                  wx.navigateBack()
                }
              }

              // 关闭弹窗
              this.closeMyKitchenModal()
      } else {
        wx.showToast({
          title: result.message || '退出失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('退出厨房失败', error)
      wx.showToast({
        title: '退出失败，请重试',
        icon: 'none'
      })
    }
        }
      }
    })
  },

  // 主题变更处理函数
  onThemeChanged(settings: any) {
    // 更新页面数据
    this.setData({
      navBgStyle: settings.navBgStyle,
      navBgIndex: settings.navBgIndex,
      shopBg: settings.shopBg
    });
  },

  // 加载背景设置
  async loadBackgroundSettings() {
    try {
      const res = await getUserBackgroundSettings();
      if (res.error === 0 && res.body) {
        const { shopBg, navBgStyle, navBgIndex } = res.body;

        // 更新本地数据
        this.setData({
          navBgStyle: navBgStyle || this.data.navBgStyle,
          navBgIndex: typeof navBgIndex === 'number' ? navBgIndex : 0,
          shopBg: shopBg || ''
        });

        // 同步到全局应用
        const app = getApp<IAppOption>();
        if (app.globalData) {
          app.globalData.backgroundSettings = {
            navBgStyle,
            navBgIndex,
            shopBg
          };
        }
      }
    } catch (error) {
      console.error('获取背景设置失败', error);
    }
  },

  // 关闭背景设置
  closeBackgroundSettings() {
    this.setData({
      showBackgroundSettings: false
    })
  },

  // 保存背景设置
  saveBackgroundSettings(e: any) {
    const { shopBg, navBgIndex, navBg } = e.detail

    this.setData({
      shopBg,
      navBgIndex,
      navBgStyle: navBg
    })

    // 更新全局
    const app = getApp<IAppOption>()
    if (app.globalData) {
      app.globalData.backgroundSettings = {
        shopBg,
        navBgIndex,
        navBgStyle: navBg
      }
    }

    // 同步更新到其他页面
    app.updateBackgroundSettings({
      shopBg,
      navBgIndex,
      navBgStyle: navBg
    })

    // 保存到本地存储
    wx.setStorageSync('navBgStyle', navBg)
    wx.setStorageSync('navBgIndex', navBgIndex)
    wx.setStorageSync('shopBg', shopBg)

    // 关闭设置面板
    this.setData({
      showBackgroundSettings: false
    })

    wx.showToast({
      title: '主题设置已保存',
      icon: 'success'
    })
  },

  // 显示克隆菜谱弹窗
  async showCloneMenuModal() {
    try {
      // 获取当前厨房的分类列表
      const currentKitchenId = this.data.kitchenInfo.id;

      if (!currentKitchenId) {
        wx.showToast({
          title: '请先选择厨房',
          icon: 'none'
        });
        return;
      }

      // 性能优化：先显示弹窗，再异步加载数据
      this.setData({
        showCloneMenuModal: true,
        cloneStep: 1,
        sourceKitchenId: '',
        sourceKitchen: {} as KitchenInfo,
        sourceCategories: [],
        selectedCategoryId: '',
        sourceDishes: [],
        targetCategories: [], // 先设为空，异步加载
        targetCategoryIndex: 0,
        selectedDishes: [],
        cloneResult: { success: false, clonedCount: 0, message: '' }
      });

      // 异步加载分类数据
      this.loadTargetCategories(currentKitchenId);

    } catch (error) {
      console.error('显示克隆弹窗失败', error);
      wx.showToast({
        title: '显示弹窗失败',
        icon: 'none'
      });
    }
  },

  // 异步加载目标分类数据
  async loadTargetCategories(currentKitchenId: string) {
    try {
      // 使用getKitchenCategories代替getDishCategories，并传入当前厨房ID
      const result = await getKitchenCategories(currentKitchenId);

      if (result.error === 0 && result.body) {
        const initialKitchenInfo: KitchenInfo = {
          id: '',
          name: '',
          level: 0,
          owner: '',
          isOwner: false,
          kitchenCount: 0,
          kitchenLimit: 0,
          categoryCount: 0,
          categoryLimit: 0,
          dishCount: 0,
          dishLimit: 0,
          memberCount: 0,
          createdAt: '',
          notice: '',
          avatarUrl: ''
        }

        // 获取分类数据 - 适配API返回格式变化
        const categoriesData = Array.isArray(result.body) ?
          result.body :
          (result.body.categories || []);

        console.log('获取到的目标分类数据:', categoriesData);

        if (categoriesData.length === 0) {
          wx.showToast({
            title: '当前厨房暂无分类，请先创建分类',
            icon: 'none'
          });
          return;
        }

        // 性能优化：简化分类数据，不预加载菜品数量
        const enhancedCategories = categoriesData.map((category: any) => ({
          ...category,
          dishCount: category.dishCount || 0 // 使用API返回的数量，如果没有则设为0
        }));

        // 更新分类数据
        this.setData({
          targetCategories: enhancedCategories
        });
      } else {
        wx.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取分类失败', error);
      wx.showToast({
        title: '获取分类失败',
        icon: 'none'
      });
    }
  },

  // 关闭克隆菜谱弹窗
  closeCloneMenuModal() {
    this.setData({
      showCloneMenuModal: false
    })
  },

  // 输入源厨房ID
  inputSourceKitchenId(e: any) {
    this.setData({
      sourceKitchenId: e.detail.value
    })
  },

  // 搜索源厨房
  async searchSourceKitchen() {
    const { sourceKitchenId } = this.data

    if (!sourceKitchenId.trim()) {
      wx.showToast({
        title: '请输入厨房ID',
        icon: 'none'
      })
      return
    }

    try {
      // 查找厨房
      const kitchenResult = await findKitchenById(sourceKitchenId)

      if (kitchenResult.error === 0 && kitchenResult.body) {
        // 获取厨房分类
        const categoriesResult = await getKitchenCategories(sourceKitchenId)

        if (categoriesResult.error === 0 && categoriesResult.body) {
          // 获取分类数据 - 适配API返回格式变化
          // 现在API直接返回分类数组，不再嵌套在categories字段中
          const categoriesData = Array.isArray(categoriesResult.body) ?
            categoriesResult.body :
            (categoriesResult.body.categories || []);

          console.log('获取到的分类数据:', categoriesData);

          if (categoriesData.length === 0) {
            wx.showToast({
              title: '该厨房暂无分类',
              icon: 'none'
            })
            return
          }

          // 为每个分类获取菜品数量
          const enhancedCategories = await Promise.all(
            categoriesData.map(async (category: any) => {
              try {
                // 获取分类下的菜品来计算数量
                const dishesResult = await getKitchenCategoryDishes(sourceKitchenId, category.id);
                let dishCount = 0;

                if (dishesResult.error === 0 && dishesResult.body) {
                  const dishes = Array.isArray(dishesResult.body) ? dishesResult.body : [];
                  dishCount = dishes.length;
                }

                return {
                  ...category,
                  dishCount: dishCount
                };
              } catch (error) {
                console.error('获取分类菜品数量失败:', error);
                return {
                  ...category,
                  dishCount: 0
                };
              }
            })
          );

          this.setData({
            sourceKitchen: kitchenResult.body,
            sourceCategories: enhancedCategories,
            cloneStep: 2 // 进入选择分类步骤
          })
        } else {
          wx.showToast({
            title: '获取分类失败',
            icon: 'none'
          })
        }
      } else {
        wx.showToast({
          title: kitchenResult.message || '未找到该厨房',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('搜索厨房失败', error)
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      })
    }
  },

  // 选择分类
  async selectCategory(e: any) {
    const categoryId = e.currentTarget.dataset.id
    const { sourceKitchenId } = this.data

    console.log('选择分类:', categoryId, '源厨房ID:', sourceKitchenId);

    // 检查categoryId是否存在和有效
    if (!categoryId) {
      wx.showToast({
        title: '无效的分类ID',
        icon: 'none'
      })
      return
    }

    try {
      // 获取分类下的菜品
      const result = await getKitchenCategoryDishes(sourceKitchenId, categoryId)

      console.log('获取分类菜品结果:', result);

      if (result.error === 0) {
        // 处理响应数据
        let dishesData = result.body;

        // 增强数据处理健壮性
        if (!dishesData) {
          dishesData = [];
        } else if (!Array.isArray(dishesData) && dishesData.dishes) {
          // 如果是嵌套结构，提取dishes属性
          dishesData = dishesData.dishes;
        }

        console.log('处理后的菜品数据:', dishesData);

        if (dishesData.length === 0) {
          wx.showToast({
            title: '该分类暂无菜品',
            icon: 'none'
          })
          return
        }

        this.setData({
          selectedCategoryId: categoryId,
          sourceDishes: dishesData,
          selectedDishes: [], // 重置选中的菜品
          cloneStep: 3 // 进入选择菜品步骤
        })
      } else {
        wx.showToast({
          title: result.message || '获取菜品失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('获取菜品失败', error)
      wx.showToast({
        title: '获取菜品失败，请重试',
        icon: 'none'
      })
    }
  },

  // 选择目标分类
  onTargetCategoryChange(e: any) {
    this.setData({
      targetCategoryIndex: parseInt(e.detail.value)
    })
  },

  // 菜品复选框变化
  toggleDishSelection(e: any) {
    const dishId = e.currentTarget.dataset.id;

    // 确保ID统一为字符串类型
    const dishIdStr = String(dishId);
    const selectedDishes = [...this.data.selectedDishes];
    const index = selectedDishes.indexOf(dishIdStr);

    if (index !== -1) {
      // 如果已经选中，则取消选中
      selectedDishes.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedDishes.push(dishIdStr);
    }

    // 更新数据
    this.setData({
      selectedDishes: selectedDishes
    });

    // 用户反馈
    wx.showToast({
      title: index !== -1 ? '已取消' : '已选中',
      icon: 'none',
      duration: 500
    });
  },

  // 确认克隆菜品
  async confirmCloneMenu() {
    const { cloneStep, sourceKitchenId, selectedDishes, targetCategories, targetCategoryIndex, kitchenInfo } = this.data

    // 根据当前步骤执行不同操作
    if (cloneStep === 1) {
      // 第一步：搜索厨房
      this.searchSourceKitchen()
      return
    } else if (cloneStep === 2) {
      // 第二步：提示选择分类
      wx.showToast({
        title: '请选择一个分类',
        icon: 'none'
      })
      return
    } else if (cloneStep === 3) {
      // 第三步：克隆菜品
      if (selectedDishes.length === 0) {
        wx.showToast({
          title: '请选择至少一个菜品',
          icon: 'none'
        })
        return
      }

      try {
        wx.showLoading({ title: '克隆中...' })

        // 调用克隆API
        const targetCategory = targetCategories && targetCategoryIndex < targetCategories.length
          ? targetCategories[targetCategoryIndex]
          : null;

        // 确保targetCategory是一个对象且含有id属性
        const targetCategoryId = targetCategory && typeof targetCategory === 'object' && 'id' in targetCategory
          ? String(targetCategory.id)
          : '';

        if (!targetCategoryId) {
          wx.hideLoading();
          wx.showToast({
            title: '无效的目标分类',
            icon: 'none'
          });
          return;
        }

        // 使用当前厨房ID作为目标厨房ID
        const currentKitchenId = kitchenInfo.id;

        if (!currentKitchenId) {
          wx.hideLoading();
          wx.showToast({
            title: '请先选择一个厨房',
            icon: 'none'
          });
          return;
        }

        // 传递源厨房ID、目标厨房ID(当前厨房)、目标分类ID和选中的菜品ID列表
        const result = await cloneDishes(sourceKitchenId, currentKitchenId, targetCategoryId, selectedDishes);

        wx.hideLoading()

        if (result.error === 0 && result.body) {
          this.setData({
            cloneResult: result.body,
            cloneStep: 4 // 进入结果步骤
          })
        } else {
          wx.showToast({
            title: result.message || '克隆失败',
            icon: 'none'
          })
        }
      } catch (error) {
        wx.hideLoading()
        console.error('克隆菜品失败', error)
        wx.showToast({
          title: '克隆失败，请重试',
          icon: 'none'
        })
      }
    } else if (cloneStep === 4) {
      // 第四步：关闭弹窗
      this.closeCloneMenuModal()
      return
    }
  },

  // 显示目标分类选择模态框
  showTargetCategoryModal() {
    this.setData({
      showTargetCategoryModal: true,
      targetCategorySelectIndex: this.data.targetCategoryIndex // 初始化选中当前的分类
    })
  },

  // 关闭目标分类选择模态框
  closeTargetCategoryModal() {
    this.setData({
      showTargetCategoryModal: false
    })
  },

  // 选择目标分类
  selectTargetCategory(e: any) {
    const index = e.currentTarget.dataset.index
    this.setData({
      targetCategorySelectIndex: index
    })
  },

  // 确认选择目标分类
  confirmTargetCategory() {
    this.setData({
      targetCategoryIndex: this.data.targetCategorySelectIndex,
      showTargetCategoryModal: false
    })
  },

  // 显示厨房会员弹窗
  showKitchenMember() {
    this.setData({
      showKitchenMember: true
    });
  },

  // 关闭厨房会员弹窗
  closeKitchenMember() {
    this.setData({
      showKitchenMember: false
    });
  },

  /**
   * 获取会员状态
   */
  async fetchMembershipStatus() {
    try {
      // 会员状态是基于用户的，不需要传参数
      const res = await getMembershipStatus();
      if (res.error === 0 && res.body) {
        this.setData({
          isVip: res.body.isMember
        });
        console.log('会员状态:', res.body);
      } else {
        console.error('获取会员状态失败:', res.message);
      }
    } catch (err) {
      console.error('获取会员状态异常:', err);
    }
  },

  /**
   * 会员订阅成功回调
   */
  onMembershipSuccess() {
    // 会员订阅成功后，直接更新状态为会员
    this.setData({
      isVip: true
    });
    wx.showToast({
      title: '会员开通成功',
      icon: 'success'
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.kitchenInfo.name || '厨房'} - 欢迎光临我的厨房`,
      path: '/pages/restaurant/restaurant'
    }
  },

  // 显示我的厨房
  showMyKitchen() {
    this.showMyKitchenModal();
  },

  // 显示厨房主题设置
  showKitchenTheme() {
    // 显示背景设置组件
    this.setData({
      showBackgroundSettings: true
    });
  },

  // 导航到克隆菜谱页面
  navigateToCloneMenu() {
    // 显示克隆菜谱弹窗
    this.showCloneMenuModal();
  },

  // 显示成员管理功能
  showMemberManage() {
    this.setData({
      showMemberManageModal: true
    });
  },

  // 关闭成员管理弹窗
  closeMemberManageModal() {
    this.setData({
      showMemberManageModal: false
    });
  },

  // 获取厨房成员列表
  async fetchKitchenMembers() {
    try {
      const result = await getKitchenMembers(this.data.kitchenInfo.id);
      if (result.error === 0 && result.body && result.body.members) {
        this.setData({
          kitchenMembers: result.body.members
        });
      } else {
        wx.showToast({
          title: '获取厨房成员失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取厨房成员失败', error);
      wx.showToast({
        title: '获取厨房成员失败',
        icon: 'none'
      });
    }
  },

  // 更新成员权限
  async updateMemberPermission(memberId: string, permission: boolean) {
    try {
      const result = await updateMemberPermission(this.data.kitchenInfo.id, memberId, permission);
      if (result.error === 0) {
        wx.showToast({
          title: '权限更新成功',
          icon: 'success'
        });

        // 刷新成员列表
        await this.fetchKitchenMembers();
      } else {
        wx.showToast({
          title: result.message || '权限更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('更新成员权限失败', error);
      wx.showToast({
        title: '更新成员权限失败',
        icon: 'none'
      });
    }
  },

  // 厨房桌号管理
  async showTableManager() {
    try {
      // 获取桌号列表
      const result = await getTableList(this.data.kitchenInfo.id);
      console.log('获取桌号列表结果:', result);

      if (result.error === 0) {
        // 检查返回数据结构，从正确的位置获取桌号列表
        let tableList = [];
        if (result.body && result.body.tables) {
          // 如果返回的是 { tables: [...] } 格式
          tableList = Array.isArray(result.body.tables) ? result.body.tables : [];
        } else {
          // 如果返回的直接是数组
          tableList = Array.isArray(result.body) ? result.body : [];
        }

        console.log('解析后的桌号列表:', tableList);

        this.setData({
          tableList: tableList,
          showTableManager: true
        });
      } else {
        wx.showToast({
          title: result.message || '获取桌号列表失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取桌号列表失败', error);
      wx.showToast({
        title: '获取桌号列表失败',
        icon: 'none'
      });
    }
  },

  // 关闭桌号管理弹窗
  closeTableManager() {
    this.setData({
      showTableManager: false
    });
  },

  // 添加桌号
  async addTable(e: WechatMiniprogram.CustomEvent) {
    const { table } = e.detail;

    try {
      // 调用添加桌号的API
      const result = await addTable(this.data.kitchenInfo.id, table);
      console.log('添加桌号结果:', result);

      if (result.error === 0) {
        // 确保tableList是一个数组
        const currentTableList = Array.isArray(this.data.tableList) ? this.data.tableList : [];

        // 获取新添加的桌号数据
        let newTable = result.body;

        // 更新桌号列表
        const tableList = [...currentTableList, newTable];
        this.setData({ tableList });

        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '添加失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('添加桌号失败', error);
      wx.showToast({
        title: '添加桌号失败',
        icon: 'none'
      });
    }
  },

  // 更新桌号
  async updateTable(e: WechatMiniprogram.CustomEvent) {
    const { index, table } = e.detail;

    // 确保tableList是一个数组
    const currentTableList = Array.isArray(this.data.tableList) ? this.data.tableList : [];
    if (currentTableList.length === 0 || index >= currentTableList.length) {
      wx.showToast({
        title: '桌号数据异常',
        icon: 'none'
      });
      return;
    }

    const tableId = currentTableList[index].id;

    try {
      // 调用更新桌号的API
      const result = await updateTable(this.data.kitchenInfo.id, tableId, table);

      if (result.error === 0) {
        // 更新桌号列表
        const tableList = [...currentTableList];
        tableList[index] = result.body;
        this.setData({ tableList });

        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('更新桌号失败', error);
      wx.showToast({
        title: '更新桌号失败',
        icon: 'none'
      });
    }
  },

  // 删除桌号
  async deleteTable(e: WechatMiniprogram.CustomEvent) {
    const { index, table } = e.detail;

    // 确保tableList是一个数组
    const currentTableList = Array.isArray(this.data.tableList) ? this.data.tableList : [];

    try {
      // 调用删除桌号的API
      const result = await deleteTable(this.data.kitchenInfo.id, table.id);

      if (result.error === 0) {
        // 更新桌号列表
        const tableList = [...currentTableList];
        tableList.splice(index, 1);
        this.setData({ tableList });

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('删除桌号失败', error);
      wx.showToast({
        title: '删除桌号失败',
        icon: 'none'
      });
    }
  },

  // 排序桌号
  async sortTables(e: WechatMiniprogram.CustomEvent) {
    const { tables } = e.detail;

    // 确保tables是一个数组
    if (!Array.isArray(tables) || tables.length === 0) {
      wx.showToast({
        title: '桌号数据异常',
        icon: 'none'
      });
      return;
    }

    try {
      // 调用排序桌号的API
      const result = await sortTables(this.data.kitchenInfo.id, tables);

      if (result.error === 0) {
        // 更新桌号列表
        this.setData({ tableList: tables });

        wx.showToast({
          title: '排序成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '排序失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('排序桌号失败', error);
      wx.showToast({
        title: '排序桌号失败',
        icon: 'none'
      });
    }
  },
})