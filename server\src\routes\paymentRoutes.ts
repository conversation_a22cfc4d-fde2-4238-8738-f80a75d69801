/**
 * 支付相关路由
 * 处理微信支付API路由
 */
import express from 'express';
import paymentController from '../controllers/paymentController';
import { verifyToken } from '../middlewares/auth';

const router = express.Router();

/**
 * 创建支付订单
 * POST /api/payment/create
 * 需要用户认证
 */
router.post('/create', verifyToken, paymentController.createPaymentOrder);

/**
 * 查询支付订单状态
 * GET /api/payment/query/:outTradeNo
 * 需要用户认证
 */
router.get('/query/:outTradeNo', verifyToken, paymentController.queryPaymentOrder);

/**
 * 关闭支付订单
 * POST /api/payment/close
 * 需要用户认证
 */
router.post('/close', verifyToken, paymentController.closePaymentOrder);

/**
 * 微信支付回调通知
 * POST /api/payment/notify
 * 无需认证（微信服务器回调）
 */
router.post('/notify', paymentController.paymentNotify);

/**
 * 申请退款
 * POST /api/payment/refund
 * 需要用户认证
 */
router.post('/refund', verifyToken, paymentController.createRefund);

/**
 * 查询退款状态
 * GET /api/payment/refund/:outRefundNo
 * 需要用户认证
 */
router.get('/refund/:outRefundNo', verifyToken, paymentController.queryRefund);

/**
 * 微信退款回调通知
 * POST /api/payment/refund-notify
 * 无需认证（微信服务器回调）
 */
router.post('/refund-notify', paymentController.refundNotify);

export default router; 