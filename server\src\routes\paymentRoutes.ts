/**
 * 支付相关路由
 * 处理微信支付API路由
 */
import express from 'express';
import paymentController from '../controllers/paymentController';
import { verifyToken } from '../middlewares/auth';

const router = express.Router();

// 微信支付回调需要原始请求体的中间件
const rawBodyMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (req.path === '/notify' || req.path === '/refund-notify') {
    let data = '';
    req.setEncoding('utf8');
    req.on('data', (chunk: string) => {
      data += chunk;
    });
    req.on('end', () => {
      (req as any).rawBody = data;
      try {
        req.body = JSON.parse(data);
      } catch (error) {
        req.body = {};
      }
      next();
    });
  } else {
    next();
  }
};

/**
 * 创建支付订单
 * POST /api/payment/create
 * 需要用户认证
 */
router.post('/create', verifyToken, paymentController.createPaymentOrder);

/**
 * 查询支付订单状态
 * GET /api/payment/query/:outTradeNo
 * 需要用户认证
 */
router.get('/query/:outTradeNo', verifyToken, paymentController.queryPaymentOrder);

/**
 * 关闭支付订单
 * POST /api/payment/close
 * 需要用户认证
 */
router.post('/close', verifyToken, paymentController.closePaymentOrder);

/**
 * 微信支付回调通知
 * POST /api/payment/notify
 * 无需认证（微信服务器回调）
 */
router.post('/notify', rawBodyMiddleware, paymentController.paymentNotify);

/**
 * 申请退款
 * POST /api/payment/refund
 * 需要用户认证
 */
router.post('/refund', verifyToken, paymentController.createRefund);

/**
 * 查询退款状态
 * GET /api/payment/refund/:outRefundNo
 * 需要用户认证
 */
router.get('/refund/:outRefundNo', verifyToken, paymentController.queryRefund);

/**
 * 微信退款回调通知
 * POST /api/payment/refund-notify
 * 无需认证（微信服务器回调）
 */
router.post('/refund-notify', rawBodyMiddleware, paymentController.refundNotify);

/**
 * 手动确认支付（测试用）
 * POST /api/payment/confirm
 * 需要用户认证
 */
router.post('/confirm', verifyToken, paymentController.confirmPayment);

export default router;