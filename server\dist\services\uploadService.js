"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 上传服务
 * 处理文件上传相关的业务逻辑
 */
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const uuid_1 = require("uuid");
const error_1 = require("../middlewares/error");
const config_1 = __importDefault(require("../config/config"));
/**
 * 保存上传文件
 * @param file 文件对象
 * @param type 文件类型
 * @returns 相对路径
 */
const saveUploadFile = (file, type) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查文件是否存在
    if (!file) {
        throw new error_1.BusinessError('文件不存在');
    }
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
        throw new error_1.BusinessError('不支持的文件类型');
    }
    // 检查文件大小
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        throw new error_1.BusinessError('文件大小超过限制');
    }
    // 生成文件名
    const fileName = `${(0, uuid_1.v4)()}${path_1.default.extname(file.originalname)}`;
    // 确定存储目录
    let uploadDir = '';
    switch (type) {
        case 'dish':
            uploadDir = 'dish';
            break;
        case 'category':
            uploadDir = 'category';
            break;
        case 'kitchen':
            uploadDir = 'kitchen';
            break;
        case 'avatar':
            uploadDir = 'avatar';
            break;
        case 'background':
            uploadDir = 'background';
            break;
        default:
            uploadDir = 'dish';
    }
    // 创建目录（如果不存在）
    const dirPath = path_1.default.join(config_1.default.upload.path, uploadDir);
    if (!fs_1.default.existsSync(dirPath)) {
        fs_1.default.mkdirSync(dirPath, { recursive: true });
    }
    // 保存文件
    const filePath = path_1.default.join(dirPath, fileName);
    fs_1.default.copyFileSync(file.path, filePath);
    // 删除临时文件
    fs_1.default.unlinkSync(file.path);
    // 返回相对路径
    return `/uploads/${uploadDir}/${fileName}`;
});
/**
 * 上传菜品图片
 * @param file 文件对象
 * @returns 图片URL
 */
const uploadDishImage = (file) => __awaiter(void 0, void 0, void 0, function* () {
    return yield saveUploadFile(file, 'dish');
});
/**
 * 上传背景图片
 * @param file 文件对象
 * @returns 图片URL
 */
const uploadBackgroundImage = (file) => __awaiter(void 0, void 0, void 0, function* () {
    return yield saveUploadFile(file, 'background');
});
/**
 * 上传分类图标
 * @param file 文件对象
 * @returns 图标URL
 */
const uploadCategoryIcon = (file) => __awaiter(void 0, void 0, void 0, function* () {
    return yield saveUploadFile(file, 'category');
});
/**
 * 上传厨房头像
 * @param file 文件对象
 * @returns 头像URL
 */
const uploadKitchenAvatar = (file) => __awaiter(void 0, void 0, void 0, function* () {
    return yield saveUploadFile(file, 'kitchen');
});
/**
 * 上传用户头像
 * @param file 文件对象
 * @returns 头像URL
 */
const uploadUserAvatar = (file) => __awaiter(void 0, void 0, void 0, function* () {
    return yield saveUploadFile(file, 'avatar');
});
exports.default = {
    uploadDishImage,
    uploadBackgroundImage,
    uploadCategoryIcon,
    uploadKitchenAvatar,
    uploadUserAvatar,
};
//# sourceMappingURL=uploadService.js.map