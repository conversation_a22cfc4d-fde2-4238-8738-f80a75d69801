"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 营养成分模型
 * 存储菜品的营养成分信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 营养成分模型类
class Nutrition extends sequelize_1.Model {
}
// 初始化营养成分模型
Nutrition.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '营养ID',
    },
    dish_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '菜品ID',
        references: {
            model: 'dishes',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    calories: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '热量(千卡)',
    },
    protein: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '蛋白质(克)',
    },
    fat: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '脂肪(克)',
    },
    carbs: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '碳水化合物(克)',
    },
    fiber: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '膳食纤维(克)',
    },
    sugar: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '糖(克)',
    },
    sodium: {
        type: sequelize_1.DataTypes.FLOAT,
        allowNull: true,
        comment: '钠(毫克)',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Nutrition',
    tableName: 'nutritions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_dish_id',
            unique: true,
            fields: ['dish_id'],
        },
    ],
});
exports.default = Nutrition;
//# sourceMappingURL=Nutrition.js.map