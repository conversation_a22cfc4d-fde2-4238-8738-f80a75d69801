# 餐厅点菜小程序

## 项目简介
这是一个餐厅点菜微信小程序，用户可以在线浏览菜品、点菜、下单。项目采用TypeScript开发，具有良好的代码可维护性和可扩展性。UI设计采用现代化风格，提供流畅的用户体验。

## 功能特点
- 分类浏览菜品，支持多种筛选方式
- 搜索功能，快速查找所需菜品
- 五星评分系统，直观展示菜品评级
- 详细菜品信息页，展示配料、营养成分和用户评价
- 购物车功能，支持添加、修改数量、删除菜品
- 流畅的动画效果，包括购物车、搜索框和菜品加载动画
- 菜品管理功能，支持修改、删除和上下架菜品
- 分类管理功能，支持添加、删除和拖拽排序分类
- 菜品排序功能，支持对同一分类下的菜品进行拖拽排序
- 厨房管理功能，支持创建厨房、加入厨房、升级厨房等操作
- 在线支付功能
- 用户中心，查看订单、个人信息等
- 支持分享小程序给朋友和分享到朋友圈
- 响应式设计，适配不同尺寸的设备
- 动态获取餐厅信息，包括店铺头像、店铺名称和店铺公告
- 消息通知系统，包括赞和添加、系统消息、用餐评价

## 项目结构
```
miniprogram/
  ├── api/              # API接口文件
  │   ├── dishApi.ts    # 菜品相关API
  │   ├── userApi.ts    # 用户相关API 
  │   ├── kitchenApi.ts # 厨房管理相关API
  │   ├── orderApi.ts   # 订单相关API
  │   ├── discoverApi.ts # 发现页相关API
  │   ├── messageApi.ts # 消息相关API
  │   └── mock/         # Mock数据
  ├── components/       # 自定义组件
  │   ├── dish-card/    # 菜品卡片组件
  │   ├── cart/         # 购物车组件
  │   ├── search-bar/   # 搜索组件
  │   ├── modal-dialog/ # 通用模态对话框组件
  │   ├── confirm-dialog/ # 确认对话框组件
  │   ├── category-manager/ # 分类管理组件
  │   ├── background-settings/ # 背景设置组件
  │   ├── custom-navbar/ # 自定义导航栏组件
  │   ├── kitchen-member/ # 厨房会员组件
  │   ├── kitchen-manager/ # 厨房管理组件
  │   ├── kitchen-list/ # 厨房列表组件
  │   ├── clone-recipe/ # 克隆菜谱组件
  │   └── theme-selector/ # 主题选择器组件
  ├── pages/            # 页面文件
  │   ├── restaurant/   # 餐厅页面
  │   ├── kitchen-manage/ # 厨房管理页面
  │   ├── dish-detail/  # 菜品详情页面
  │   ├── add-dish/     # 添加菜品页面
  │   ├── order/        # 订单页面
  │   ├── order-detail/ # 订单详情页面
  │   ├── submit-order/ # 提交订单页面
  │   ├── message/      # 消息页面
  │   ├── discover/     # 发现页面
  │   ├── mine/         # 我的页面
  │   ├── my-coins/     # 我的大米页面
  │   ├── login/        # 登录页面
  │   └── index/        # 引导页面
  ├── static/           # 静态资源
  │   └── images/       # 图片资源
  └── utils/            # 工具函数
      ├── request.ts    # 请求封装
      └── util.ts       # 通用工具函数
```

## 页面说明

### 餐厅页面(restaurant)
餐厅页面是小程序的首页，主要展示餐厅信息和菜品列表。
- 顶部显示餐厅信息（店铺头像、店铺名称、公告）和用户信息，支持快速登录
- 中部展示菜品分类和菜品列表，支持横向滚动分类和纵向滚动菜品
- 支持搜索功能，可以快速查找菜品
- 菜品卡片展示详细信息，包括五星评分、标签、价格和销量
- 点击菜品卡片可跳转至菜品详情页，查看更多信息
- 底部显示购物车信息和下单按钮，支持动画效果
- 点击购物车可以展开查看已添加菜品清单
- 提供编辑模式，可以管理菜品（修改、删除、上下架）
- 支持分类管理功能，可以添加、删除和拖拽排序分类

### 厨房管理页面(kitchen-manage)
厨房管理页面提供了厨房相关的管理功能，包括查看厨房信息、切换厨房、创建和加入厨房等功能。
- 顶部显示当前厨房的头像、名称、等级和ID，支持复制ID功能
- 显示厨房公告，方便用户了解厨房的最新信息
- 中间区域展示厨房统计信息，包括厨房数量、分类额度、菜品额度和成员数量
- 提供三个主要操作按钮：升级厨房、解散/退出厨房和管理厨房
- 功能入口区域提供多个快捷功能入口：新增厨房、我的厨房、厨房会员、厨房主题和克隆菜谱
- 切换厨房功能，显示用户创建和加入的所有厨房（包含厨房头像），可快速切换
- 新增/加入厨房功能：
  - 创建新厨房：可设置厨房名称、公告和头像
  - 加入已有厨房：通过厨房ID加入
- 升级厨房功能，通过消耗大米提升厨房等级，增加资源上限
- 智能识别当前用户身份，厨房拥有者显示"解散厨房"，普通成员显示"退出厨房"
- 我的厨房弹窗功能：
  - 通过标签页切换查看"我创建的"和"我加入的"厨房
  - 显示每个厨房的头像、名称、ID和等级信息
  - 提供快速解散/退出操作按钮
  - 支持空状态提示，当没有创建或加入的厨房时显示提示信息
  - 使用平滑的切换动画效果，提升用户体验
- 厨房主题功能：
  - 支持自定义店铺背景图和导航栏背景色
  - 提供预设渐变背景色和自定义颜色选择
  - 设置会全局应用到所有使用自定义导航栏的页面
  - 设置实时同步生效
  - 与用户页面的背景设置功能共享同一套设置

### 添加菜品页面
提供添加新菜品的表单界面，包含以下功能和特性：
- 支持最多上传5张菜品图片，图片上传按钮居中显示
- 必填字段包括菜品名称、分类、价格等
- 提供星级评分系统（1-5星），默认为3星
- 价格默认为0（如用户未输入）
- 表单底部的取消和保存按钮固定在页面底部

### 菜品详情页面(dish-detail)
菜品详情页面展示单个菜品的详细信息，并提供加入购物车和立即购买功能。

**页面布局**：
- 使用自定义导航栏，保持统一的视觉风格
- 顶部展示菜品大图轮播，支持多张图片展示
- 菜品基本信息卡片展示菜品名称、评分、销量、价格和描述
- 配料表部分详细列出菜品所含食材及用量
- 营养信息部分显示热量、蛋白质、碳水化合物和脂肪含量
- 烹饪步骤模块展示详细的制作过程和步骤图片
- 用户评价部分展示精选评论预览
- 底部固定操作栏包含评论输入框、点赞和添加按钮

**图标优化特性**：
- **举报功能优化**：举报按钮移动到菜品标题右侧，使用jubao图标，布局更加合理
- **星级评分优化**：使用star（实星）和star1（空星）图标替代emoji，显示更加清晰
- **价格显示优化**：将大米价格以小字体形式移动到评分销量行的右侧，简化布局
- **功能图标统一**：配料表使用task1图标，营养信息使用yang图标，烹饪步骤使用kitchen图标，用户评价使用chat图标
- **操作按钮优化**：点赞功能使用link（已赞）和link1（未赞）图标，添加功能使用check1（已添加）和plus（未添加）图标
- **状态同步优化**：修复了与发现页面的添加状态同步问题，确保菜品添加状态在不同页面间保持一致
- **API统一化**：使用与发现页面相同的添加接口，避免状态不同步的问题

**交互特性**：
- 提供加入购物车的数量选择面板，支持平滑的弹出动画
- 点击购物车图标可查看当前购物车内容
- 支持分享功能，可将菜品分享给朋友
- 支持举报功能，提供多种举报原因选择
- 点赞和添加状态实时更新，提供即时反馈
- 评论功能支持实时输入和提交

**技术特点**：
- 图片轮播支持自动播放和手动切换
- 智能图片颜色分析，自动调整状态栏颜色
- 响应式设计，适配不同屏幕尺寸
- 优化的图标系统，提升视觉一致性和用户体验

### 订单页面(order)
订单页面分为"厨房订单"和"我的订单"两个标签，提供完整的订单管理功能。

**页面布局**：
- 使用自定义导航栏，与全局主题设置系统集成
- 双标签设计：厨房订单（管理接单）和我的订单（查看下单记录）
- 状态筛选器：支持按订单状态筛选（全部、待接单、已接单、烹饪中、已完成、已取消）
- 订单列表：以卡片形式展示订单信息，支持下拉刷新和上拉加载更多

**优化特性**：
- **头像显示优化**：修复了smart-image组件在订单页面的显示问题，确保头像正确显示为圆形并保持合适的尺寸
- **状态切换优化**：改进了订单状态筛选的交互体验，切换状态时不再刷新整个列表，而是平滑更新内容，保持用户当前的滚动位置
- **视觉反馈增强**：添加了微妙的动画效果，状态切换时提供视觉反馈，让用户清楚知道内容正在更新
- **性能优化**：状态切换时避免重复请求，减少不必要的网络开销

**功能特点**：
- 支持厨房ID切换，自动检测当前选中的厨房
- 实时订单状态更新，无需手动刷新
- 丰富的订单操作：接单、拒绝、开始烹饪、完成订单、取消操作
- 智能的权限控制，根据用户角色显示不同的操作选项
- 订单详情快速跳转，便于查看完整信息

### 订单详情页(order-detail)
订单详情页展示单个订单的详细信息，并提供相应的订单操作功能。

- 使用自定义导航栏，保持统一的视觉风格
- 顶部区域根据订单状态显示不同的背景色和状态信息
- 步骤时间轴直观展示订单进度（下单、接单、完成）及操作时间记录
- 详细展示订单信息：订单号、桌号、下单时间、备注等
- 菜品列表展示订单中的所有菜品，包括图片、名称、标签、价格和数量
- 底部按钮根据订单状态和用户角色动态显示不同操作选项：
  - 厨房端：待接单状态显示"拒绝"和"接单"按钮
  - 厨房端：烹饪中状态显示"取消烹饪"和"完成订单"按钮
  - 客户端：待接单状态显示"取消订单"按钮
- 所有操作都有二次确认弹窗，防止误操作
- 支持分享功能，可将订单分享给朋友
- 自适应不同屏幕尺寸，提供良好的用户体验
- 实时记录厨师接单和完成时间，并在订单时间轴上精确显示

### 任务大厅页面(task-hall)
任务大厅页面是用户获取大米奖励的主要渠道，提供了多种互动任务和签到功能。

- 使用自定义导航栏，与全局主题设置系统集成
- 分为三个主要功能区：每日签到、日常任务和大米使用说明
- 每日签到区域展示最近7天的签到记录，包含日期和周几
- 签到日历直观显示已签到、可补签和待签到状态
- 签到后获得大米奖励，并显示成功提示
- 支持查看签到详情，包括签到规则和当月签到统计
- 日常任务区域提供多种获取大米的任务：
  - 观看广告任务：每日最多15次，每次获得10大米
  - 作品分享任务：分享到社交平台获得赞励，每个赞奖励20大米
  - 五星好评任务：每日一次，完成好评获得40大米奖励
- 每个任务配有详细的操作指引和视觉反馈
- 实时显示任务完成进度和可获得的奖励
- 所有奖励自动同步到用户的大米账户
- 支持下拉刷新，保持数据最新
- 响应式设计，确保在不同设备上的良好体验
- 动画过渡效果，提升用户交互体验

### 分类管理功能
分类管理功能是一个重要的后台管理功能，用于维护菜品分类。
- 支持拖拽排序，轻松调整分类显示顺序
- 提供添加新分类功能，可上传分类图标
- 支持编辑现有分类的名称和图标
- 提供删除分类功能，但会进行安全确认
- 实时更新分类列表，无需手动刷新

### 搜索功能
搜索功能允许用户快速找到所需菜品：
- 支持关键词搜索，可按菜品名称、描述或标签搜索
- 提供热门搜索关键词推荐
- 记录用户搜索历史，支持一键清除
- 搜索结果支持分页加载，提高性能

### 我的页面(mine)
我的页面展示用户个人信息和相关功能。
- 顶部显示用户头像、昵称等信息
- 顶部背景支持自定义渐变色，通过背景设置功能修改
- 提供背景设置按钮，快速打开背景设置面板
- 我的订单功能，分类展示不同状态的订单
- 支持主题切换功能（明亮/暗黑模式）
- 支持复制用户ID功能
- 用户数据统计展示（大米、获赞、添加、厨房）
- 提供编辑资料功能，可修改头像、昵称和性别
- 提供快捷功能入口和其他应用功能
- 登录状态下提供退出登录功能

### 我的大米页面(my-coins)
我的大米页面提供用户查看和管理虚拟货币"大米"的功能。

**主要功能**：
- 顶部显示大米余额、累计获得和已使用的数量，设计美观直观
- 支持通过"充值"按钮进行大米充值，提供多种充值金额选择
- 通过"明细"按钮查看大米收支记录，支持筛选查看全部/收入/支出
- 提供赚取大米的方式引导，包括完成任务和邀请好友
- 展示大米的多种用途：兑换VIP会员、参与抽奖、升级店铺、点餐下单
- 页面底部提供大米使用说明，帮助用户了解平台规则
- 与全局主题设置系统集成，支持自定义导航栏背景

**特色设计**：
- 统一的卡片式布局，视觉层次清晰
- 顶部余额卡片使用渐变背景，突出重要信息
- 充值弹窗设计合理，支持多种充值金额快速选择
- 明细记录使用标签页切换不同类型，操作便捷
- 支持下拉刷新更新大米信息
- 余额信息与用户系统集成，保持数据一致性

**技术特点**：
- 使用自定义导航栏，支持全局主题色设置
- 模块化设计，分离数据获取、UI展示和用户交互逻辑
- 使用modal-dialog和confirm-dialog组件实现充值和明细功能
- 实现响应式设计，适配不同屏幕尺寸
- 优化大米数据获取，优先使用缓存数据减少请求

### 消息页面(message)
消息页面提供了用户交流和系统通知的集中展示，帮助用户轻松管理所有交互信息。

- 页面使用自定义导航栏，顶部显示返回按钮，与全局主题设置集成
- 标签页设计包含四个选项：全部、赞和添加、系统消息、用餐评价
- 标签切换使用平滑滑动动画，提供良好的视觉反馈
- 消息列表采用卡片式设计，区分已读和未读状态
- 每种消息类型使用不同图标表示：点赞、添加、系统通知和评价消息
- 支持下拉刷新和上拉加载更多功能
- 消息列表使用级联动画效果，提升视觉体验
- 点击消息项可自动标记为已读并跳转至相应内容
- 提供空状态显示，当无数据时显示提示信息
- 与发现页面紧密集成，可从发现页标签直接跳转至特定类型的消息页面

### 发现页面(discover)
发现页面是用户探索和社交的平台，提供了菜品分享和互动功能。
- 顶部提供三个标签页：赞和添加、系统消息、用餐评价
- 标签页设计为图标+文字的直观样式，系统消息标签显示未读消息数量
- 支持搜索功能，可以快速查找感兴趣的菜品
- 提供排序功能，可按热门程度或时间排序
- 菜品以网格布局展示，每行两个菜品卡片
- 菜品卡片包含菜品图片、名称、用户信息和添加按钮
- 点击菜品卡片可导航至菜品详情页面，查看完整信息
- 点击添加按钮可以将菜品添加到指定分类
- 已添加的菜品显示勾选标记
- 菜品卡片底部显示发布用户头像、昵称和添加人数
- 支持下拉刷新和上拉加载更多功能
- 提供空状态显示，当无数据时显示提示信息
- 使用模态对话框进行分类选择
- 保持与整体应用一致的UI风格和动画效果

## UI设计说明

### 配色方案
- 主题色: #FF6B35（食欲橙）- 激发食欲的温暖色调
- 辅助色: #4CAF50（清新绿）- 提供对比和平衡
- 强调色: #E53935（柔和红）- 用于重要操作和提示
- 背景色: #F9F9F9（浅灰白）- 干净的背景色
- 卡片背景: #FFFFFF（纯白）- 提供内容区域的清晰边界
- 文字主色: #333333（深灰）- 主要文本内容
- 文字次色: #666666（中灰）- 次要文本内容
- 文字提示色: #999999（浅灰）- 辅助性文本内容

### 交互与动画
- 购物车添加商品时的徽章动画
- 搜索框展开/收起的平滑过渡
- 搜索结果的级联动画效果
- 点击区域的反馈动画
- 购物车展开/收起的动画效果
- 添加到购物车的按钮动画
- 分类拖拽排序的流畅动画效果

## 开发说明

### 数据处理
项目使用集中式的API管理，所有与后端的交互都通过api文件夹中的接口进行，使用真实的后端API获取数据。

### API模块说明
项目API分为以下几个主要模块：

#### 1. 菜品相关API (dishApi.ts)
- 菜品基础功能：列表、详情、分类
- 购物车操作：添加、更新、清空
- 订单提交
- 搜索功能：搜索菜品、热门关键词
- 菜品管理：更新、删除、状态变更
- 分类管理：获取列表、添加、更新、删除、排序
- 餐厅信息：获取餐厅名称、logo和公告

#### 2. 用户相关API (userApi.ts)
- 用户认证：登录
- 用户信息：获取、更新
- 搜索历史：获取、清除
- 背景设置：获取、更新
- 大米管理：获取大米信息、充值、获取交易记录

#### 3. 厨房管理相关API (kitchenApi.ts)
- 厨房基础功能：获取厨房列表、厨房详细信息
- 厨房操作：创建厨房（支持设置名称、公告和头像）、加入厨房、退出厨房、解散厨房
- 厨房升级：通过大米升级厨房等级，提升资源上限
- 厨房成员：获取厨房成员列表
- 厨房基本信息：获取当前厨房的名称、头像和公告
- 厨房信息管理：更新厨房名称、公告和头像

#### 4. 订单相关API (orderApi.ts)
- 订单提交
- 订单详情
- 订单状态更新

#### 5. 发现页相关API (discoverApi.ts)
- 获取菜品列表
- 添加菜品到个人分类
- 获取分类列表

#### 6. 消息相关API (messageApi.ts)
- 获取消息列表
- 标记消息为已读

### API调用方式
所有API调用均通过Promise封装，使用统一的请求封装函数：
```typescript
// 示例调用
import { getDishList } from '../../api/dishApi'

Page({
  async loadDishes(categoryId) {
    try {
      const res = await getDishList(categoryId)
      if (res.error === 0) {
        this.setData({
          dishes: res.body
        })
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error(err)
    }
  }
})
```

### 状态管理
- 采用页面内状态管理
- 使用本地存储保存用户数据和设置

### 设备适配
- 标题栏自动适配不同设备的状态栏高度
- 采用弹性布局和相对单位(rpx)确保各种屏幕尺寸下的一致体验

## 厨房会员特权功能

厨房会员是餐厅点菜小程序中的高级功能，通过消费虚拟货币"大米"开通，解锁更多专属功能和特权。

### 会员种类
- **月度会员**：666大米/月，有效期1个月
- **年度会员**：5888大米/年，有效期12个月（相当于月度会员的9折优惠）

### 会员特权
- **主题定制**：解锁所有高级主题，支持自定义店铺颜色方案
- **桌号管理**：支持设置和管理餐厅桌号，提升点单体验

### 功能特点
- 友好的会员状态展示，清晰显示会员类型和到期时间
- 平滑的选择交互，支持切换不同会员方案
- 智能检测用户大米余额，确保余额充足
- 详细的确认信息，帮助用户做出明智决策
- 对已是会员的用户支持会员类型更换

### 实现方式
- 使用模态对话框组件实现弹窗式会员购买界面
- 通过API接口实时获取和更新会员状态
- 使用动画效果增强用户交互体验
- 完善的错误处理和用户反馈机制

### 特色设计
- 清晰的状态区分，已开通会员和未开通会员有不同样式
- 突出显示推荐方案，引导用户做出选择
- 特权列表使用卡片式布局，直观展示会员优势
- 独特的动画效果，提升视觉吸引力

## 优化与后续计划
- 完善分类管理功能，添加更多批量操作选项
- 进一步优化搜索功能，支持模糊搜索和历史记录
- 完善下单和支付流程
- 添加订单管理功能
- 增加推荐系统，基于用户历史订单推荐菜品
- 添加多语言支持
- 优化图片加载性能
- 添加数据统计和分析功能，帮助餐厅了解销售情况 

## 更新日志

### 最新更新 (2023-09-25)
- 添加厨房会员特权功能，支持月度会员和年度会员两种类型
- 实现会员特权功能，包括主题定制和桌号管理
- 优化会员购买流程，提供清晰的会员状态和确认信息
- 支持会员类型切换和到期时间显示
- 完善会员相关接口和Mock数据实现

### 上一版本更新 (2023-09-20)
- 添加桌号管理功能，支持自定义桌号及排序
- 实现拖拽排序功能，优化桌号顺序调整体验
- 添加桌号编辑和删除功能，支持批量管理
- 与点单系统整合，在下单时可选择桌号
- 桌号数据与服务器同步，支持多设备共享

### 上一版本更新 (2023-09-10)
- 优化订单提交功能，支持从购物车动态添加商品到订单
- 改进了订单生成逻辑，自动生成基于时间戳的唯一订单ID
- 添加了更多的订单字段处理，如备注和桌号
- 确保订单提交后立即显示在订单列表中

### 上一版本更新 (2023-08-15)
- 新增"任务大厅"页面，用户获取大米奖励的主要渠道
- 实现每日签到功能，支持查看签到历史和连续签到奖励
- 添加观看广告、作品分享、五星好评等日常任务
- 使用模态对话框组件实现详细的任务说明和指引
- 集成自定义导航栏和全局主题系统，保持UI风格一致性
- 优化用户交互体验，提供平滑的动画过渡效果

### 上一版本更新 (2023-08-05)
- 新增"我的大米"页面，展示大米余额和使用情况
- 实现大米充值功能，支持多种充值金额选择
- 实现大米明细功能，支持查看收支记录
- 优化大米数据获取，与用户信息系统集成
- 页面与全局主题设置系统集成，支持自定义导航栏背景

### 上一版本更新 (2023-07-25)
- 实现管理厨房功能，支持修改厨房名称、公告和头像
- 复用公共模态框组件，保持统一的UI交互风格
- 添加更新厨房信息的API和mock数据
- 优化厨房信息修改的用户体验，提供实时反馈
- 完善接口文档，更新更新厨房信息接口

### 上一版本更新 (2023-07-20)
- 添加"克隆菜谱"功能，支持从其他厨房复制菜谱到当前厨房

### 上一版本更新 (2023-07-15)
- 添加"厨房主题"功能，与全局背景设置共享机制
- 统一导航栏视觉风格，使用共同的背景设置系统
- 实现厨房主题设置与用户背景设置之间的数据同步
- 改进导航栏自适应，根据背景自动调整文字颜色
- 优化设置界面的交互体验和动画效果

### 上一版本更新 (2023-07-10)
- 添加"我的厨房"弹窗功能，优化厨房管理体验
- 实现标签页切换查看创建的和加入的厨房
- 为每个厨房提供快速解散/退出操作按钮
- 优化弹窗交互动画，提升用户体验
- 支持空状态提示，当没有创建或加入的厨房时显示提示信息

### 上一版本更新 (2023-07-05)
- 扩展厨房管理功能，支持设置和显示厨房头像和公告
- 优化厨房列表和切换厨房界面，显示厨房头像
- 更新厨房创建界面，支持上传厨房头像和添加公告
- 完善厨房管理相关接口和Mock数据实现

### 上一版本更新 (2023-06-17)
- 删除菜品标签和原价字段
- 将价格单位更改为"大米"
- 优化菜品详情页面的显示

### 上一版本更新 (2023-06-15)
- 添加了菜品详情页面，展示菜品的详细信息、配料表和用户评价
- 实现菜品详情页与餐厅页面的联动，支持点击卡片跳转
- 增强购物车功能，支持从详情页添加商品
- 优化页面动画效果，提升用户体验
- 完善推荐菜品功能，便于用户发现更多菜品

### 上一版本更新 (2023-06-10)
- 添加了个性化背景设置功能，支持自定义店铺背景图和导航栏背景色
- 实现背景设置在首页和我的页面之间的全局同步
- 优化用户个人中心页面，添加背景设置入口和主题切换按钮
- 完善页面样式，提升视觉一致性和用户体验
- 在应用启动时自动加载用户背景设置

### 功能更新 (2023-06-01)
- 添加了餐厅信息API，实现店铺名称、头像和公告的动态获取
- 优化了分类列表和菜品列表的滚动体验，隐藏滚动条
- 优化了餐厅公告显示效果，支持文本溢出显示
- 改进了分类管理组件的动画效果，增强了用户体验

### 功能优化 (2023-05-31)
- 修复分类管理弹窗中按钮溢出问题
- 优化菜品栏布局，移除底部多余空白
- 改进分类管理按钮样式，使其更紧凑
- 修复开发环境下菜品排序保存功能
- 完善API连接，添加清空购物车和更新购物车数量API

### 功能优化 (2023-05-20)
- 添加菜品排序功能，支持对同一分类下的菜品进行拖拽排序
- 优化分类管理组件中的分类列表滚动
- 隐藏分类列表和菜品列表的滚动条，提升视觉体验
- 改进分类管理弹窗的交互体验

### 初始版本 (2023-05-10)
- 实现基础点餐功能，包括分类浏览、搜索和购物车
- 添加编辑模式，支持菜品的修改、删除和上下架
- 实现分类管理功能，支持添加、删除和拖拽排序分类 

## 克隆菜谱功能

克隆菜谱功能是小程序的新增功能，允许用户从其他厨房复制菜谱到自己的厨房中。

### 功能概述
- 支持通过厨房ID搜索其他厨房
- 浏览源厨房的分类和菜品
- 选择需要的菜品进行克隆
- 选择目标分类，一键导入菜品
- 精确显示克隆结果和成功率

### 使用流程
1. **进入克隆流程**：在厨房管理页面点击"克隆菜谱"按钮
2. **输入厨房ID**：在搜索框中输入源厨房ID（可使用测试厨房ID: A2B3C4）
3. **浏览分类**：搜索成功后，选择需要的分类查看菜品
4. **选择菜品**：在菜品列表中勾选想要克隆的菜品（支持多选）
5. **选择目标分类**：选择要将菜品克隆到的目标分类
6. **确认克隆**：点击确认按钮，完成克隆操作
7. **查看结果**：系统显示克隆结果，包括成功和失败数量

### 特色功能
- **多步骤导航**：清晰的步骤指引，帮助用户完成克隆流程
- **批量操作**：支持一次选择多个菜品进行克隆
- **实时预览**：显示源厨房信息和菜品详情
- **智能提示**：根据当前厨房容量自动提示可克隆数量
- **测试厨房**：提供测试厨房ID（A2B3C4）方便用户体验功能

### 技术实现
- 通过厨房API实现厨房搜索和分类获取
- 使用菜品API获取源厨房菜品列表和实现克隆功能
- 采用多步骤模态框设计，保持操作流程清晰
- 使用checkbox组件实现菜品多选功能
- 克隆结果页面展示成功和失败统计

### 限制和注意事项
- 每次最多可选择20个菜品进行克隆
- 克隆操作受当前厨房等级限制，等级越高可克隆的菜品越多
- 克隆成功的菜品会立即显示在目标分类中
- 部分特殊菜品可能受保护无法克隆

> **提示**：使用测试厨房ID：A2B3C4可以体验完整的克隆菜谱功能，该测试厨房包含多种分类和丰富的菜品样本。

## 桌号管理功能

桌号管理是一项新增功能，允许厨房管理员设置和管理用于点单的桌号，提升点单体验和管理效率。

### 功能概述
- 查看厨房的所有桌号列表
- 添加新的桌号，自定义桌号名称
- 修改现有桌号信息
- 删除不需要的桌号
- 拖拽排序桌号，调整显示顺序

### 使用流程
1. **进入桌号管理**：在厨房管理页面点击"桌号管理"按钮
2. **查看桌号列表**：系统显示当前厨房的所有桌号
3. **添加桌号**：点击"添加桌号"按钮，输入桌号名称
4. **修改桌号**：点击桌号右侧的编辑按钮，修改桌号信息
5. **删除桌号**：点击桌号右侧的删除按钮，确认后删除
6. **排序桌号**：长按桌号进行拖拽，调整顺序

### 特色功能
- **直观的拖拽排序**：通过长按拖拽实现桌号排序
- **批量管理**：支持一次添加多个桌号
- **智能默认值**：添加桌号时自动提供序号递增的默认名称
- **使用提示**：首次使用时提供操作指引
- **实时同步**：修改即时生效，并同步到点单页面

### 技术实现
- 使用可拖拽列表组件实现桌号排序
- 通过模态框实现桌号的添加和编辑
- 使用confirm-dialog组件实现二次确认
- 桌号数据存储在服务器，确保多设备间的同步

### 限制和注意事项
- 每个厨房最多可添加30个桌号
- 桌号名称不超过15个字符
- 删除桌号不会影响已使用该桌号的历史订单
- 桌号排序影响在点单页面的显示顺序

## 用户信息数据说明

用户信息包含以下字段：
- userId：用户唯一标识，5位数起步
- nickName：用户昵称
- avatarUrl：用户头像URL
- gender：性别，0-未知，1-男，2-女
- coins：大米数量（货币）
- likes：获赞数量
- dishes：添加的菜品数量
- kitchens：厨房数量

用户个人中心页面功能：
- 展示用户基本信息（头像、昵称、用户ID）
- 展示用户数据统计（大米、获赞、添加、厨房）
- 支持复制用户ID
- 提供编辑资料功能，可修改头像、昵称和性别
- 提供快捷功能入口：厨房管理、我的大米、任务大厅、联系客服、帮助教程
- 提供基础功能入口：消息绑定、关于我们、问题反馈
- 登录状态下提供退出登录功能 

## 组件说明

### 确认对话框组件(confirm-dialog)

确认对话框组件用于创建用户操作前的确认提示，风格简洁，交互友好。

- **特点**：
  - 简洁的设计风格，符合移动端交互规范
  - 平滑的动画效果，提升用户体验
  - 可自定义标题、内容和按钮文本
  - 支持自定义确认按钮颜色

- **使用方式**：

```html
<confirm-dialog
  visible="{{showConfirmDialog}}"
  title="操作确认"
  content="确定要执行此操作吗？"
  cancelText="取消"
  confirmText="确定"
  confirmColor="#FF6B35"
  bind:cancel="onCancelAction"
  bind:confirm="onConfirmAction"
/>
```

- **属性说明**：
  - `visible`: 控制对话框的显示与隐藏
  - `title`: 对话框标题文本(默认:"提示")
  - `content`: 对话框内容文本(默认:"确定要执行此操作吗？")
  - `cancelText`: 取消按钮文本(默认:"取消")
  - `confirmText`: 确认按钮文本(默认:"确定")
  - `confirmColor`: 确认按钮文本颜色(默认:"#FF6B35")
  
- **事件**：
  - `bind:cancel`: 点击取消按钮时触发
  - `bind:confirm`: 点击确认按钮时触发

### 通用模态对话框组件(modal-dialog)

通用模态对话框组件用于创建统一风格的弹出层，具有丰富的配置选项和平滑的动画效果。

- **特点**：
  - 支持自定义标题、内容和按钮文本
  - 提供良好的动画过渡效果
  - 可配置蒙层点击是否关闭
  - 支持自定义主题色
  - 响应式设计，适应不同屏幕尺寸

- **使用方式**：

```html
<modal-dialog 
  visible="{{showDialog}}" 
  title="对话框标题" 
  showCancel="{{true}}" 
  cancelText="取消" 
  confirmText="确定"
  themeColor="#FF6B35"
  bind:close="onCloseDialog"
  bind:cancel="onCancelAction"
  bind:confirm="onConfirmAction"
>
  <!-- 自定义内容区域 -->
  <view class="custom-content">
    <!-- 添加您的自定义内容 -->
  </view>
</modal-dialog>
```

- **属性说明**：
  - `visible`: 控制对话框的显示与隐藏
  - `title`: 对话框标题文本
  - `showClose`: 是否显示右上角关闭按钮(默认:true)
  - `maskClosable`: 点击蒙层是否可关闭对话框(默认:true)
  - `showCancel`: 是否显示取消按钮(默认:true)
  - `cancelText`: 取消按钮文本(默认:"取消")
  - `confirmText`: 确认按钮文本(默认:"确定")
  - `width`: 对话框宽度(默认:"90%")
  - `maxWidth`: 对话框最大宽度(默认:"650rpx")
  - `themeColor`: 确认按钮主题色(默认:"#FF6B35")
  
- **事件**：
  - `bind:close`: 关闭对话框时触发
  - `bind:cancel`: 点击取消按钮时触发
  - `bind:confirm`: 点击确认按钮时触发

- **样式类名**：
  - `.fade-in-up`: 可应用于内部元素，提供从下到上的渐入动画

### 分类管理组件(category-manager)

分类管理组件用于管理菜品分类，具有以下功能：
- 添加新分类
- 编辑分类
- 删除分类
- 拖拽排序分类

### 背景设置组件(background-settings)

背景设置组件用于自定义小程序的视觉风格，提供个性化的用户体验。

- **特点**：
  - 使用选项卡式设计，直观分隔不同设置项
  - 平滑的动画过渡效果，提升用户体验
  - 分别提供店铺背景图和导航栏背景设置
  - 支持自定义渐变色背景或选择预设的主题色
  - 设置自动保存并在应用内全局同步生效

- **使用方式**：

```html
<background-settings
  visible="{{showBackgroundSettings}}"
  currentShopBg="{{shopBg}}"
  currentNavBg="{{navBgStyle}}"
  currentNavBgIndex="{{navBgIndex}}"
  bind:close="closeBackgroundSettings"
  bind:save="saveBackgroundSettings"
/>
```

- **属性说明**：
  - `visible`: 控制设置面板的显示与隐藏
  - `currentShopBg`: 当前店铺背景图URL
  - `currentNavBg`: 当前导航栏背景渐变值
  - `currentNavBgIndex`: 当前选中的预设背景索引
  
- **事件**：
  - `bind:close`: 关闭设置面板时触发
  - `bind:save`: 保存设置时触发，返回所选背景图片和颜色信息

- **功能区块**：
  - 店铺背景图：显示当前背景预览，支持从相册或拍照选择新图片
  - 导航栏背景：提供6种预设渐变背景选择，并支持自定义起始和结束颜色
  - 实时预览：所有设置更改都能即时预览效果

- **API接口**：
  - 使用`/api/user/backgroundSettings`接口获取用户背景设置
  - 使用`/api/user/updateBackgroundSettings`接口更新用户背景设置
  - 使用`/api/upload/backgroundImage`接口上传背景图片
  - 请求/响应参数详见接口文档

- **核心逻辑**：
  - 组件初始化时从服务器获取用户当前背景设置
  - 更改设置时提供即时预览
  - 支持店铺背景图的上传、预览和保存
  - 支持导航栏背景的预设选择和自定义颜色
  - 保存时将数据同步到服务器并通知父组件更新全局状态

## 编辑资料功能

编辑资料功能允许用户修改自己的个人信息，包括头像、昵称和性别。

- **基本信息**：
  - 位置: `/pages/mine/mine` 页面内
  - 调用方式: 点击"编辑资料"按钮
  - 实现方式: 使用通用模态对话框组件

- **功能特点**：
  - 支持上传/更换头像
  - 修改昵称
  - 选择性别(男/女/保密)
  - 实时保存到服务器
  - 平滑的动画过渡效果

- **核心代码实现**：
  - 引入通用模态对话框组件
  - 使用动态样式控制表单元素的入场动画
  - 实时验证用户输入
  - 使用Mock数据进行开发测试

- **API接口**：
  - 使用`/api/user/update`接口更新用户信息
  - 请求参数：nickName, avatarUrl, gender
  - 响应参数：success状态 

### 成员管理功能
成员管理功能允许厨房创建者管理厨房成员的权限和状态。

**主要功能**：
- 显示当前厨房的所有成员列表
- 展示成员基本信息：头像、昵称、加入时间
- 厨房创建者可以：
  - 管理成员权限（开启/关闭）
  - 移除成员
- 清晰的权限说明，列出授予成员的权限内容：
  - 修改厨房信息
  - 编辑菜品和分类
  - 新增菜品
  - 升级厨房

**特色设计**：
- 统一的卡片式布局，视觉层次清晰
- 权限开关设计直观，操作便捷
- 移除成员时有二次确认，防止误操作
- 实时显示成员数量统计
- 区分创建者和普通成员的显示

**技术特点**：
- 使用modal-dialog组件实现弹窗展示
- 使用confirm-dialog组件实现二次确认
- 实现响应式设计，适配不同屏幕尺寸
- 优化成员数据获取，保持列表实时更新 