"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 发现模块路由
 * 处理发现页相关的API路由
 */
const express_1 = __importDefault(require("express"));
const discoverController_1 = __importDefault(require("../controllers/discoverController"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
// 获取发现列表
router.get('/list', auth_1.optionalAuth, discoverController_1.default.getDiscoverList);
// 添加到分类
router.post('/addToDish', auth_1.verifyToken, discoverController_1.default.addToDish);
// 获取发现页消息数量
router.get('/messageCount', auth_1.verifyToken, discoverController_1.default.getMessageCount);
// 获取未读数量
router.get('/unreadCounts', auth_1.verifyToken, discoverController_1.default.getUnreadCounts);
// 获取标签内容
router.get('/tabContent', auth_1.optionalAuth, discoverController_1.default.getTabContent);
exports.default = router;
//# sourceMappingURL=discoverRoutes.js.map