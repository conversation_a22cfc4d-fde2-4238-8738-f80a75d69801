"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 数据库同步脚本
 * 用于同步数据库模型和初始化数据
 */
const database_1 = require("../config/database");
const logger_1 = __importDefault(require("../utils/logger"));
// 同步数据库模型
const syncDatabase = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (force = false) {
    try {
        // 测试数据库连接
        yield (0, database_1.testConnection)();
        // 同步数据库模型
        yield (0, database_1.syncModels)(force);
        logger_1.default.info(`数据库同步${force ? '(强制)' : ''} 完成`);
        process.exit(0);
    }
    catch (error) {
        logger_1.default.error('数据库同步失败:', error);
        process.exit(1);
    }
});
// 获取命令行参数
const args = process.argv.slice(2);
const force = args.includes('--force');
// 执行同步
syncDatabase(force);
//# sourceMappingURL=syncDatabase.js.map