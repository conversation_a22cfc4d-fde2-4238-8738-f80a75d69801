"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 订单种子数据脚本
 * 创建测试订单数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
const helpers_1 = require("../../utils/helpers");
/**
 * 创建测试订单数据
 */
function seedOrders() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试订单数据...');
        try {
            // 清空现有数据
            yield models_1.Order.destroy({ where: {} });
            yield models_1.OrderItem.destroy({ where: {} });
            logger_1.default.info('已清空现有订单相关数据');
            // 获取所有厨房
            const kitchens = yield models_1.Kitchen.findAll();
            if (kitchens.length === 0) {
                throw new Error('没有找到厨房数据，请先运行 seedKitchens 脚本');
            }
            // 获取所有桌号
            const tables = yield models_1.Table.findAll();
            if (tables.length === 0) {
                throw new Error('没有找到桌号数据，请先运行 seedTables 脚本');
            }
            // 获取所有菜品
            const dishes = yield models_1.Dish.findAll();
            if (dishes.length === 0) {
                throw new Error('没有找到菜品数据，请先运行 seedDishes 脚本');
            }
            // 为每个厨房创建测试订单
            let totalOrders = 0;
            for (const kitchen of kitchens) {
                // 获取该厨房的桌号
                const kitchenTables = tables.filter(table => table.kitchen_id === kitchen.id);
                if (kitchenTables.length === 0) {
                    logger_1.default.warn(`厨房 ${kitchen.id} 没有桌号，跳过创建订单`);
                    continue;
                }
                // 获取该厨房的菜品
                const kitchenDishes = dishes.filter(dish => dish.kitchen_id === kitchen.id);
                if (kitchenDishes.length === 0) {
                    logger_1.default.warn(`厨房 ${kitchen.id} 没有菜品，跳过创建订单`);
                    continue;
                }
                // 为每个厨房创建3-5个订单
                const orderCount = Math.floor(Math.random() * 3) + 3; // 3-5个订单
                for (let i = 1; i <= orderCount; i++) {
                    // 随机选择用户ID（10001-10003）
                    const userId = Math.floor(Math.random() * 3) + 10001;
                    // 随机选择桌号
                    const table = kitchenTables[Math.floor(Math.random() * kitchenTables.length)];
                    // 随机选择2-4个菜品
                    const orderDishCount = Math.floor(Math.random() * 3) + 2; // 2-4个菜品
                    const orderDishes = [];
                    for (let j = 0; j < orderDishCount; j++) {
                        // 随机选择菜品
                        const dish = kitchenDishes[Math.floor(Math.random() * kitchenDishes.length)];
                        // 随机选择数量（1-3）
                        const count = Math.floor(Math.random() * 3) + 1;
                        // 检查是否已经添加过该菜品
                        const existingDish = orderDishes.find(d => d.id === dish.id);
                        if (existingDish) {
                            existingDish.count += count; // 增加数量
                        }
                        else {
                            orderDishes.push({
                                id: dish.id,
                                name: dish.name,
                                image: dish.image,
                                price: dish.price,
                                count,
                                tags: dish.tags,
                            });
                        }
                    }
                    // 计算订单总价
                    let totalPrice = 0;
                    for (const dish of orderDishes) {
                        totalPrice += dish.price * dish.count;
                    }
                    // 随机选择订单状态
                    const statuses = ['pending', 'accepted', 'cooking', 'completed', 'cancelled'];
                    const statusIndex = Math.floor(Math.random() * 5);
                    const status = statuses[statusIndex];
                    // 创建订单
                    const orderId = (0, helpers_1.generateOrderId)();
                    const now = new Date();
                    const cookingTime = status === 'cooking' || status === 'completed' ? new Date(now.getTime() - 30 * 60 * 1000) : null; // 30分钟前
                    const completedTime = status === 'completed' ? new Date(now.getTime() - 10 * 60 * 1000) : null; // 10分钟前
                    const order = yield models_1.Order.create({
                        id: orderId,
                        user_id: userId,
                        kitchen_id: kitchen.id,
                        total_price: totalPrice,
                        status,
                        table_no: table.name,
                        remark: `测试订单${i}`,
                        cooking_time: cookingTime,
                        completed_time: completedTime,
                        created_at: new Date(now.getTime() - 60 * 60 * 1000), // 1小时前
                    });
                    logger_1.default.info(`创建订单: ${order.id} (厨房: ${kitchen.id}, 状态: ${status})`);
                    // 创建订单项
                    for (const dish of orderDishes) {
                        yield models_1.OrderItem.create({
                            order_id: orderId,
                            dish_id: dish.id,
                            name: dish.name,
                            image: dish.image,
                            price: dish.price,
                            count: dish.count,
                            tags: dish.tags,
                        });
                    }
                    totalOrders++;
                }
            }
            logger_1.default.info(`共创建 ${totalOrders} 个测试订单`);
        }
        catch (error) {
            logger_1.default.error('创建测试订单数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试订单
if (require.main === module) {
    seedOrders()
        .then(() => {
        logger_1.default.info('创建测试订单数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试订单数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedOrders;
//# sourceMappingURL=seedOrders.js.map