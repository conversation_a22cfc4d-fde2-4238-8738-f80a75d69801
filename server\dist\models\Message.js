"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 消息模型
 * 存储用户消息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 消息模型类
class Message extends sequelize_1.Model {
}
// 初始化消息模型
Message.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '消息ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    type: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        comment: '消息类型(like:点赞,add:添加,system:系统消息,comment:评论)',
        validate: {
            isIn: [['like', 'add', 'system', 'comment']],
        },
    },
    title: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        comment: '消息标题',
    },
    content: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: false,
        comment: '消息内容',
    },
    image: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '图片URL',
    },
    is_read: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否已读',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Message',
    tableName: 'messages',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_type',
            fields: ['type'],
        },
        {
            name: 'idx_is_read',
            fields: ['is_read'],
        },
        {
            name: 'idx_created_at',
            fields: ['created_at'],
        },
        {
            name: 'idx_user_type',
            fields: ['user_id', 'type'],
        },
        {
            name: 'idx_user_read',
            fields: ['user_id', 'is_read'],
        },
    ],
});
exports.default = Message;
//# sourceMappingURL=Message.js.map