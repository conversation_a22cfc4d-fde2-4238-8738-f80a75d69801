<view class="smart-image-container custom-class {{adaptiveHeight ? 'adaptive-height' : ''}}" style="width: {{width}}; height: {{adaptiveHeight ? 'auto' : height}}; border-radius: {{borderRadius}}rpx;">
  <!-- 占位层 -->
  <view 
    wx:if="{{loading && !loaded}}" 
    class="smart-image-placeholder {{showAnimation ? 'breathing' : ''}}"
    style="border-radius: {{borderRadius}}rpx; {{adaptiveHeight ? 'height: 200rpx;' : ''}}"
  >
    <view class="placeholder-content">
      <view class="placeholder-icon"></view>
    </view>
  </view>

  <!-- 图片层 -->
  <image 
    wx:if="{{!error && realSrc}}"
    class="smart-image image-class {{loaded ? 'fade-in' : 'fade-out'}}"
    src="{{realSrc}}"
    mode="{{mode}}"
    style="border-radius: {{borderRadius}}rpx;"
    bindload="onImageLoad"
    binderror="onImageError"
  />

  <!-- 错误层 -->
  <view 
    wx:if="{{error}}" 
    class="smart-image-error"
    style="border-radius: {{borderRadius}}rpx; {{adaptiveHeight ? 'height: 200rpx;' : ''}}"
    bind:tap="onErrorTap"
  >
    <view class="error-content">
      <view class="error-icon"></view>
    </view>
  </view>
</view> 