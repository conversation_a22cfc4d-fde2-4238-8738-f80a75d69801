/* 登录页面样式 */
.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
  padding: 0 40rpx;
}

/* 头部样式 */
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 200rpx 0 60rpx;
}

.logo-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.title {
  font-size: 42rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999999;
}

/* 内容区域 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 80rpx 0;
}

/* 登录描述 */
.login-desc {
  background-color: #F9F9F9;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  display: block;
}

.desc-item {
  display: flex;
  align-items: center;
}

.item-icon {
  font-size: 26rpx;
  color: #4CAF50;
  margin-right: 10rpx;
}

.item-text {
  font-size: 26rpx;
  color: #666666;
}

/* 登录按钮 */
.login-btn {
  background-color: #FF6B35;
  color: #FFFFFF;
  font-size: 32rpx;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

/* 隐私提示 */
.privacy-tip {
  display: flex;
  justify-content: center;
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
}

.link-text {
  color: #FF6B35;
}

/* 底部区域 */
.login-footer {
  padding: 40rpx 0;
}

/* 取消按钮 */
.cancel-btn {
  background-color: #FFFFFF;
  color: #666666;
  font-size: 32rpx;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #DDDDDD;
}

/* 隐私政策弹窗内容样式 */
.privacy-content {
  max-height: 40vh;
  padding: 20rpx 0;
}

.privacy-section {
  margin-bottom: 40rpx;
}

.privacy-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.privacy-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.paragraph {
  margin-bottom: 20rpx;
}