<!-- 餐厅页面 -->
<view class="restaurant-container" style="{{textColorStyle}}">
  <!-- 顶部背景区域 -->
  <view class="top-bg-area">
    <!-- 只有在非分享模式，或者分享模式下已经获取到背景图时才显示背景 -->
    <smart-image
      wx:if="{{shopBg && (!isSharedKitchen || (isSharedKitchen && restaurantInfo.name))}}" 
      class="bg-image" 
      src="{{shopBg}}" 
      mode="aspectFill"
      width="100%"
      height="100%"
      border-radius="0"
      show-animation="{{false}}"
    />
    <!-- 非分享模式下且没有背景时才显示默认背景 -->
    <smart-image
      wx:elif="{{!isSharedKitchen}}" 
      class="bg-image" 
      src="{{defaultImages.KITCHEN_BACKGROUND}}" 
      mode="aspectFill"
      width="100%"
      height="100%"
      border-radius="0"
      show-animation="{{false}}"
    />

    <!-- 顶部标题区域 -->
    <view class="top-title" style="top: {{statusBarHeight}}px;">
      <text class="page-title">电子菜单助手</text>
    </view>

    <!-- 餐厅信息区域 -->
    <view class="restaurant-info">
      <view class="info-left">
        <view class="restaurant-avatar-area">
          <!-- 分享厨房模式下显示真实头像，否则根据登录状态显示 -->
          <smart-image
            class="restaurant-avatar" 
            src="{{isSharedKitchen ? restaurantInfo.logo : (isLoggedIn ? restaurantInfo.logo : defaultImages.KITCHEN_AVATAR)}}" 
            mode="aspectFill"
            width="100%"
            height="100%"
            border-radius="50"
          />
        </view>
        <view class="restaurant-detail">
          <block wx:if="{{!isLoggedIn && !isOrderMode && !isSharedKitchen}}">
            <button class="login-btn" bind:tap="onLogin">立即登录</button>
          </block>
          <block wx:else>
            <view class="user-info">
              <text class="restaurant-name">{{restaurantInfo.name}}</text>
              <view class="restaurant-notice">{{restaurantInfo.notice}}</view>
              <!-- 分享厨房登录提示已移除 -->
            </view>
          </block>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="function-btns">
      <view class="function-btn" bind:tap="{{isOrderMode || isSharedKitchen ? 'onBackToHome' : 'onStore'}}">
        <image class="btn-icon" src="{{isOrderMode ? '/static/images/icons/home.png' : (isSharedKitchen ? '/static/images/icons/home.png' : '/static/images/icons/kitchen.png')}}" mode="aspectFit"></image>
      </view>
      <view class="function-btn" bind:tap="{{isOrderMode ? 'onShareQrCode' : 'onShare'}}">
        <image class="btn-icon" src="/static/images/icons/code.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 搜索蒙层，仅在顶部背景区域有效 -->
    <view class="top-search-mask {{isSearching ? 'active' : ''}}" bind:tap="cancelSearch"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 顶部操作栏 -->
    <view class="top-action-bar">
      <view class="tab-area">
        <view class="tab-item {{activeTab === 'order' ? 'active' : ''}}" bind:tap="switchTab" data-tab="order">点餐</view>
        <view class="tab-item {{activeTab === 'edit' ? 'active' : ''}}" bind:tap="switchTab" data-tab="edit" wx:if="{{!isOrderMode && !isSharedKitchen}}">编辑</view>
      </view>
      <view class="action-btns">
        <!-- 在分享厨房模式下隐藏添加食谱按钮 -->
        <view class="action-btn" bind:tap="onAddRecipe" wx:if="{{!isOrderMode && !isSharedKitchen}}">
          <image class="btn-icon" src="/static/images/icons/plus.png" mode="aspectFit"></image>
          <text class="btn-text">添加食谱</text>
        </view>
        <view class="action-btn" bind:tap="onSearch">
          <image class="btn-icon" src="/static/images/icons/search.png" mode="aspectFit"></image>
          <text class="btn-text">搜索</text>
        </view>
      </view>

      <!-- 搜索弹出层 -->
      <view class="search-container {{isSearching ? 'active' : ''}} {{isSearchExiting ? 'exit' : ''}}" catch:tap="stopPropagation">
        <input class="search-input" placeholder="搜索菜品" value="{{searchKeyword}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearchConfirm" focus="{{isSearching}}" />
        <view class="search-cancel" bind:tap="cancelSearch">取消</view>
      </view>
    </view>

    <!-- 菜单区域 -->
    <view class="menu-area">
      <!-- 左侧分类列表 -->
      <scroll-view 
        class="category-list" 
        scroll-y 
        style="overflow: auto; -webkit-overflow-scrolling: touch; hide-scrollbar: true;" 
        enhanced="{{true}}" 
        show-scrollbar="{{false}}" 
        enable-flex
        bounces="{{false}}"
        paging-enabled="{{false}}">
        <view
          wx:for="{{categories}}"
          wx:key="id"
          class="category-item {{activeCategoryId === item.id ? 'active' : ''}}"
          bind:tap="selectCategory"
          data-id="{{item.id}}"
        >
          <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="category-name">{{item.name}}</text>
        </view>

        <!-- 分类管理按钮 -->
        <view class="category-item category-manage" catchtap="onManageCategory" wx:if="{{!isOrderMode && !isSharedKitchen}}">
          <image class="category-icon" src="/static/images/icons/setting.png" mode="aspectFit"></image>
          <text class="category-name">分类管理</text>
        </view>
        
        <!-- 全局排序按钮 -->
        <view class="category-item category-sort" catchtap="toggleSortMode" wx:if="{{!isOrderMode && !isSharedKitchen && activeTab === 'edit'}}">
          <image class="category-icon" src="{{sortModeCategory ? '/static/images/icons/check.png' : '/static/images/icons/sort.png'}}" mode="aspectFit"></image>
          <text class="category-name">{{sortModeCategory ? '完成排序' : '菜品排序'}}</text>
        </view>
        
        <!-- 添加底部边距 -->
        <view style="height: 120rpx;"></view>
      </scroll-view>

      <!-- 右侧菜品列表 -->
      <scroll-view 
        class="dish-list" 
        scroll-y 
        style="overflow: auto; -webkit-overflow-scrolling: touch; hide-scrollbar: true;" 
        scroll-with-animation 
        scroll-into-view="{{scrollIntoView}}" 
        enhanced="{{true}}" 
        show-scrollbar="{{false}}"
        enable-flex
        bounces="{{false}}"
        paging-enabled="{{false}}">
        <!-- 搜索结果 -->
        <block wx:if="{{isSearching && searchKeyword}}">
          <view class="search-results">
            <view class="search-result-header">
              <text class="search-result-title">搜索结果 ({{searchResults.length}})</text>
              <text class="search-result-keyword">关键词: {{searchKeyword}}</text>
            </view>

            <block wx:if="{{searchResults.length > 0}}">
              <view
                wx:for="{{searchResults}}"
                wx:key="id"
                class="dish-card card-shadow search-result-item {{item.disabled ? 'disabled-dish' : ''}}"
                wx:if="{{(activeTab === 'edit' || (activeTab === 'order' && !item.disabled)) && !item.isDeleted}}"
                data-id="{{item.id}}"
              >
                <smart-image
                  class="dish-image" 
                  src="{{item.image || defaultImages.DISH}}" 
                  mode="aspectFill" 
                  width="100%"
                  height="100%"
                  border-radius="8"
                  lazy="{{true}}"
                  lazy-delay="{{200}}"
                  catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}"
                  data-id="{{item.id}}"
                />
                <view class="dish-info">
                  <view class="dish-name-row" catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}" data-id="{{item.id}}">
                    <text class="dish-name text-ellipsis">{{item.name}}</text>
                  </view>
                  <view class="dish-tags">
                    <text wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this" class="dish-tag">{{tag}}</text>
                  </view>
                  <view class="dish-rating-row">
                    <view class="rating-stars">
                      <image class="star" src="{{item.rating >= 1 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                      <image class="star" src="{{item.rating >= 2 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                      <image class="star" src="{{item.rating >= 3 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                      <image class="star" src="{{item.rating >= 4 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                      <image class="star" src="{{item.rating >= 5 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                    </view>
                    <text class="dish-sales" wx:if="{{activeTab === 'order'}}">{{item.sales}}已点</text>
                    <view class="dish-status" wx:if="{{activeTab === 'edit' && !item.disabled}}">上架中</view>
                    <view class="dish-status dish-disabled" wx:if="{{activeTab === 'edit' && item.disabled}}">已下架</view>
                  </view>
                  <view class="dish-price-row" wx:if="{{activeTab === 'order'}}">
                    <view class="dish-price">
                      <text class="price">{{item.price}}</text>
                      <text class="price-unit">大米</text>
                    </view>
                    <view class="dish-action">
                      <view wx:if="{{item.count && item.count > 0}}" class="quantity-control">
                        <view class="quantity-btn" bind:tap="decreaseQuantity" data-dish="{{item}}">
                          <image src="/static/images/icons/minus.png" mode="aspectFit"></image>
                        </view>
                        <view class="quantity-number">{{item.count}}</view>
                        <view class="quantity-btn" bind:tap="increaseQuantity" data-dish="{{item}}">
                          <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                        </view>
                      </view>
                      <view wx:else class="add-btn" bind:tap="addToCart" data-dish="{{item}}">
                        <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                      </view>
                    </view>
                  </view>
                  <view class="dish-edit-row" wx:if="{{activeTab === 'edit'}}">
                    <view class="edit-actions">
                      <view class="edit-btn edit-modify" bind:tap="onModifyDish" data-dish="{{item}}">
                        <text class="edit-text">修改</text>
                      </view>
                      <view class="edit-btn edit-toggle" bind:tap="onToggleDishStatus" data-dish="{{item}}">
                        <text class="edit-text">{{!item.disabled ? '下架' : '上架'}}</text>
                      </view>
                      <view class="edit-btn edit-delete" bind:tap="onDeleteDish" data-dish="{{item}}">
                        <text class="edit-text">删除</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            <block wx:else>
              <view class="empty-search-result">
                <image class="empty-icon" src="/static/images/icons/search.png" mode="aspectFit"></image>
                <text class="empty-text">没有找到相关菜品</text>
                <text class="empty-tip">尝试更换关键词或浏览菜单</text>
              </view>
            </block>
          </view>
        </block>

        <!-- 常规菜品列表 -->
        <block wx:else>
          <!-- 点餐模式下按分类显示 -->
          <block wx:if="{{activeTab === 'order'}}">
            <block wx:for="{{categories}}" wx:key="id" wx:for-item="category">
              <view class="category-title" id="category-{{category.id}}">
                {{category.name}}
              </view>
              
              <!-- 显示该分类下的菜品 -->
              <block wx:for="{{dishList}}" wx:for-item="dish" wx:key="id">
                <view
                  class="dish-card card-shadow {{dish.isFirstInCategory ? 'first-in-category' : ''}} slide-in"
                  id="dish-{{dish.id}}"
                  wx:if="{{dish.categoryId === category.id && !dish.disabled && !dish.isDeleted}}"
                  data-id="{{dish.id}}"
                  data-category-id="{{dish.categoryId}}"
                >
                  <smart-image
                    class="dish-image" 
                    src="{{dish.image || defaultImages.DISH}}" 
                    mode="aspectFill" 
                    width="100%"
                    height="100%"
                    border-radius="8"
                    lazy="{{true}}"
                    lazy-delay="{{200}}"
                    catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}"
                    data-id="{{dish.id}}"
                  />
                  <view class="dish-info">
                    <view class="dish-name-row" catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}" data-id="{{dish.id}}">
                      <text class="dish-name text-ellipsis">{{dish.name}}</text>
                    </view>
                    <view class="dish-tags">
                      <text wx:for="{{dish.tags}}" wx:for-item="tag" wx:key="*this" class="dish-tag">{{tag}}</text>
                    </view>
                    <view class="dish-rating-row">
                      <view class="rating-stars">
                        <image class="star" src="{{dish.rating >= 1 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                        <image class="star" src="{{dish.rating >= 2 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                        <image class="star" src="{{dish.rating >= 3 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                        <image class="star" src="{{dish.rating >= 4 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                        <image class="star" src="{{dish.rating >= 5 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                      </view>
                      <text class="dish-sales">{{dish.sales}}已点</text>
                    </view>
                    <view class="dish-price-row">
                      <view class="dish-price">
                        <text class="price">{{dish.price}}</text>
                        <text class="price-unit">大米</text>
                      </view>
                      <view class="dish-action">
                        <view wx:if="{{dish.count && dish.count > 0}}" class="quantity-control">
                          <view class="quantity-btn" bind:tap="decreaseQuantity" data-dish="{{dish}}">
                            <image src="/static/images/icons/minus.png" mode="aspectFit"></image>
                          </view>
                          <view class="quantity-number">{{dish.count}}</view>
                          <view class="quantity-btn" bind:tap="increaseQuantity" data-dish="{{dish}}">
                            <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                          </view>
                        </view>
                        <view wx:else class="add-btn" bind:tap="addToCart" data-dish="{{dish}}">
                          <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 如果是分类中的最后一个上架菜品，显示总数 -->
                <view wx:if="{{dish.isLastEnabledInCategory && dish.categoryId === category.id}}" class="category-bottom">
                  共{{dish.categoryEnabledCount}}个菜品
                </view>
              </block>
              
              <!-- 如果没有菜品，显示空提示 -->
              <block wx:if="{{!hasDishInCategory[category.id]}}">
                <view class="empty-category-tip">
                  <text>暂无菜品</text>
                </view>
              </block>
            </block>
          </block>
          
          <!-- 编辑模式下的菜品列表 -->
          <block wx:else>
            <block wx:for="{{dishList}}" wx:key="id">
              <!-- 分类标题 - 即使菜品被标记为已删除，也显示分类标题 -->
              <view wx:if="{{item.isFirstInCategory}}" class="category-title" id="category-{{item.categoryId}}">
                {{item.categoryName}}
              </view>

              <!-- 菜品卡片 - 下架的菜品在编辑模式下也显示 -->
              <view
                class="dish-card card-shadow {{item.isFirstInCategory ? 'first-in-category' : ''}} {{item.disabled ? 'disabled-dish' : ''}} {{sortModeCategory ? 'sort-mode' : ''}} {{currentDragDishId === item.id ? 'dragging' : ''}} {{moveToDishIndex === index && currentDragDishId ? 'move-to' : ''}} slide-in"
                id="dish-{{item.id}}"
                wx:if="{{!item.isDeleted}}"
                data-index="{{index}}"
                data-id="{{item.id}}"
                data-category-id="{{item.categoryId}}"
            >
              <view class="drag-handle" wx:if="{{sortModeCategory && activeTab === 'edit'}}"
                catchtouchstart="onDishTouchStart"
                catchtouchmove="onDishTouchMove"
                catchtouchend="onDishTouchEnd"
                data-index="{{index}}"
                data-id="{{item.id}}"
                data-category-id="{{item.categoryId}}">
                <text class="drag-icon">≡</text>
              </view>
              <smart-image
                class="dish-image" 
                src="{{item.image || defaultImages.DISH}}" 
                mode="aspectFill" 
                width="100%"
                height="100%"
                border-radius="8"
                lazy="{{true}}"
                lazy-delay="{{200}}"
                catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}"
                data-id="{{item.id}}"
              />
              <view class="dish-info">
                <view class="dish-name-row" catch:tap="{{activeTab === 'order' ? 'navigateToDishDetail' : ''}}" data-id="{{item.id}}">
                  <text class="dish-name text-ellipsis">{{item.name}}</text>
                </view>
                <view class="dish-tags">
                  <text wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this" class="dish-tag">{{tag}}</text>
                </view>
                <view class="dish-rating-row">
                  <view class="rating-stars">
                    <image class="star" src="{{item.rating >= 1 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                    <image class="star" src="{{item.rating >= 2 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                    <image class="star" src="{{item.rating >= 3 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                    <image class="star" src="{{item.rating >= 4 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                    <image class="star" src="{{item.rating >= 5 ? '/static/images/icons/star.png' : '/static/images/icons/star1.png'}}" mode="aspectFit"></image>
                  </view>
                  <text class="dish-sales" wx:if="{{activeTab === 'order'}}">{{item.sales}}已点</text>
                  <view class="dish-status" wx:if="{{activeTab === 'edit' && !item.disabled}}">上架中</view>
                  <view class="dish-status dish-disabled" wx:if="{{activeTab === 'edit' && item.disabled}}">已下架</view>
                </view>

                <!-- 正常模式下显示价格和添加按钮 -->
                <view class="dish-price-row" wx:if="{{activeTab === 'order'}}">
                  <view class="dish-price">
                    <text class="price">{{item.price}}</text>
                    <text class="price-unit">大米</text>
                  </view>
                  <view class="dish-action">
                    <view wx:if="{{item.count && item.count > 0}}" class="quantity-control">
                      <view class="quantity-btn" bind:tap="decreaseQuantity" data-dish="{{item}}">
                        <image src="/static/images/icons/minus.png" mode="aspectFit"></image>
                      </view>
                      <view class="quantity-number">{{item.count}}</view>
                      <view class="quantity-btn" bind:tap="increaseQuantity" data-dish="{{item}}">
                        <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                      </view>
                    </view>
                    <view wx:else class="add-btn" bind:tap="addToCart" data-dish="{{item}}">
                      <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
                    </view>
                  </view>
                </view>

                <!-- 编辑模式下显示管理按钮 -->
                <view class="dish-edit-row" wx:if="{{activeTab === 'edit'}}">
                  <view class="edit-actions">
                    <view class="edit-btn edit-modify" bind:tap="onModifyDish" data-dish="{{item}}">
                      <text class="edit-text">修改</text>
                    </view>
                    <view class="edit-btn edit-toggle" bind:tap="onToggleDishStatus" data-dish="{{item}}">
                      <text class="edit-text">{{!item.disabled ? '下架' : '上架'}}</text>
                    </view>
                    <view class="edit-btn edit-delete" bind:tap="onDeleteDish" data-dish="{{item}}">
                      <text class="edit-text">删除</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 分类底部信息 -->
            <view wx:if="{{item.isLastInCategory}}" class="category-bottom">
              共{{item.categoryCount}}个菜品
            </view>
          </block>
          <!-- 添加底部边距 -->
          <view style="height: 120rpx;"></view>
        </block>
      </block>
      </scroll-view>
    </view>
  </view>

  <!-- 用于分析背景图片亮度的隐藏Canvas -->
  <canvas canvas-id="bgAnalysisCanvas" style="position: absolute; left: -999px; top: -999px; width: 100px; height: 100px;"></canvas>

  <!-- 购物车栏 -->
  <view class="cart-bar {{showCartList ? 'cart-expanded' : ''}} {{isExiting ? 'exit' : ''}}" wx:if="{{(activeTab === 'order' || isOrderMode) && (isOrderMode || cart.totalCount > 0 || (!isSharedKitchen)) && !(isSharedKitchen && cart.totalCount === 0)}}">
    <view class="cart-icon-container" bind:tap="toggleCartList">
      <view class="cart-icon">
        <image src="/static/images/icons/cart.png" mode="aspectFit"></image>
      </view>
      <view class="cart-badge {{cartBadgeAnimated ? 'badge-animated' : ''}}" wx:if="{{cart.totalCount > 0}}">{{cart.totalCount}}</view>
    </view>
    <view class="cart-info">
      <text class="cart-total-price" wx:if="{{cart.totalCount > 0}}">{{cart.totalPrice}}<text class="price-unit">大米</text></text>
      <text class="cart-empty-tip" wx:else>购物车为空</text>
    </view>
    <view class="cart-actions">
      <button class="invite-btn" open-type="share">邀请下单</button>
      <button class="submit-btn {{cart.totalCount <= 0 ? 'disabled' : ''}}" bind:tap="onSubmitOrder">下单</button>
    </view>
  </view>

  <!-- 购物车清单 -->
  <view class="cart-list-container {{showCartList ? 'show' : ''}}" wx:if="{{cart.totalCount > 0 && (activeTab === 'order' || isOrderMode)}}">
    <view class="cart-list-header">
      <text class="cart-list-title">购物车</text>
      <view class="cart-list-clear" bind:tap="clearCart">清空</view>
    </view>
    <scroll-view class="cart-list" scroll-y="true">
      <view class="cart-list-item" wx:for="{{cart.cartList}}" wx:key="id">
        <text class="cart-item-name">{{item.name}}</text>
        <view class="cart-item-right">
          <text class="cart-item-price">{{item.price * item.count}}大米</text>
          <view class="quantity-control">
            <view class="quantity-btn" bind:tap="decreaseQuantity" data-dish="{{item}}">
              <image src="/static/images/icons/minus.png" mode="aspectFit"></image>
            </view>
            <view class="quantity-number">{{item.count}}</view>
            <view class="quantity-btn" bind:tap="increaseQuantity" data-dish="{{item}}">
              <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 分类管理组件 -->
  <category-manager
    visible="{{showCategoryManager && !isOrderMode}}"
    categories="{{categories}}"
    bind:close="onCloseCategoryManager"
    bind:add="onAddCategory"
    bind:update="onUpdateCategory"
    bind:delete="onDeleteCategory"
    bind:sort="onSortCategories"
  />

  <!-- 二维码分享组件 -->
  <qrcode-share
    visible="{{showQrCodeShare}}"
    kitchenId="{{currentKitchenId}}"
    isSharedKitchen="{{isSharedKitchen}}"
    bind:close="onCloseQrCodeShare"
  />
</view>