import { getDishCategories, addDish, getDishDetail, updateDish } from '../../api/dishApi'
import { uploadDishImage } from '../../api/uploadApi'
import { getKitchenInfo } from '../../api/kitchenApi'
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker'

// 定义上传结果类型
interface UploadResult {
  error: number;
  body?: {
    imageUrl: string;
  };
  message: string;
}

Page({
  data: {
    // 导航栏背景样式
    navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)',
    textColorStyle: '--text-color: #333333;',

    // 页面标题
    pageTitle: '添加菜品',

    // 是否是编辑模式
    isEdit: false,

    // 当前编辑的菜品ID
    dishId: '',

    // 公告信息
    notice: '请勿上传违规图片和内容，包括但不限于色情、暴力、政治敏感等内容，违规用户将被封号处理！添加菜品时请填写完整信息，带*号为必填项',
    noticeOverflow: true, // 是否溢出需要滚动

    // 图片上传
    tempFilePath: '',
    imageList: [] as string[], // 存储多张图片

    // 表单数据
    formData: {
      name: '',
      categoryId: '',
      categoryName: '',
      price: '',
      rating: 3, // 默认3星
      description: '',
      image: '',
      imageList: [] as string[], // 存储多张图片URL
      ingredients: [
        { name: '', amount: '' }
      ],
      cookingSteps: [
        { title: '', description: '', image: '' }
      ],
      nutrition: {
        calories: '' as string | null,
        protein: '' as string | null,
        carbs: '' as string | null,
        fat: '' as string | null
      }
    },

    // 分类选择
    showCategoryModal: false,
    categories: [] as Array<{id: string, name: string, icon: string, sort: number}>,
    selectedCategoryId: '',
    selectedCategoryName: '',

    // 确认对话框
    showConfirmDialog: false,
    confirmDialogTitle: '',
    confirmDialogContent: '',
    confirmDialogAction: '', // 用于区分不同的确认操作

    // 厨房信息
    currentKitchenId: '', // 当前选中的厨房ID
  },

  onLoad(options) {
    console.log('添加菜品页面加载');

    // 检查是否是编辑模式
    if (options && options.isEdit === 'true' && options.dishId) {
      this.setData({
        isEdit: true,
        dishId: options.dishId,
        pageTitle: '编辑菜品'
      });

      // 加载菜品数据
      this.loadDishData(options.dishId);
    }

    // 加载背景设置
    this.loadBackgroundSettings();

    // 加载餐厅公告（使用硬编码）
    this.loadRestaurantInfo();

    // 先获取当前厨房ID
    this.getCurrentKitchenId();

    // 检查厨房ID并加载分类数据
    this.checkCurrentKitchen();

    // 检查公告是否需要滚动
    this.checkNoticeOverflow();

    // 监听全局背景设置变化
    this.listenForBackgroundChanges();
  },

  onShow() {
    // 检查当前厨房ID是否变更
    this.checkCurrentKitchen();
  },

  onUnload() {
    // 页面卸载时清理资源
    console.log('页面卸载');
  },

  // 检查当前选中的厨房
  getCurrentKitchenId() {
    try {
      const kitchenId = wx.getStorageSync('last_selected_kitchen');
      if (kitchenId) {
        this.setData({ currentKitchenId: kitchenId });
        // 在设置完厨房ID后打印，用于调试
        console.log('获取当前厨房ID成功:', kitchenId);
      } else {
        console.error('获取不到厨房ID');
        wx.showToast({
          title: '请先选择厨房',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取当前厨房ID失败', error);
    }
  },

  // 检查当前厨房是否变更
  checkCurrentKitchen() {
    try {
      const kitchenId = wx.getStorageSync('last_selected_kitchen');

      // 如果厨房ID变更，重新加载数据
      if (kitchenId && kitchenId !== this.data.currentKitchenId) {
        console.log('检测到厨房变更，从', this.data.currentKitchenId, '切换到', kitchenId);
        this.setData({ currentKitchenId: kitchenId });

        // 获取新厨房的分类数据
        this.loadCategories();
      } else if (!this.data.currentKitchenId && kitchenId) {
        // 首次加载，设置当前厨房ID
        console.log('首次加载，设置厨房ID:', kitchenId);
        this.setData({ currentKitchenId: kitchenId });

        // 获取新厨房的分类数据
        this.loadCategories();
      } else if (this.data.currentKitchenId) {
        // 厨房ID存在且未变更，加载分类数据
        console.log('厨房ID未变更，使用当前ID:', this.data.currentKitchenId);
        this.loadCategories();
      }
    } catch (error) {
      console.error('检查厨房变更失败', error);
    }
  },

  // 加载背景设置
  loadBackgroundSettings() {
    // 从本地存储中获取背景设置
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)';

    this.setData({ navBgStyle });

    // 根据背景颜色计算合适的文字颜色
    this.updateTextColorByBackground(navBgStyle);
  },

  // 根据背景色计算文字颜色
  updateTextColorByBackground(background: string) {
    // 提取背景颜色值
    let bgColor = '#FFFFFF'; // 默认白色背景

    if (background) {
      // 尝试提取渐变的起始颜色
      const match = background.match(/linear-gradient\(to\s+\w+,\s+(#[A-Fa-f0-9]+),\s+/);
      if (match && match.length > 1) {
        bgColor = match[1];
      }
    }

    // 计算颜色亮度 (简化的亮度计算，仅用于本例)
    const r = parseInt(bgColor.substring(1, 3), 16);
    const g = parseInt(bgColor.substring(3, 5), 16);
    const b = parseInt(bgColor.substring(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 根据亮度选择文本颜色
    let textColor = brightness > 128 ? '#333333' : '#FFFFFF';
    const textColorStyle = `--text-color: ${textColor};`;

    this.setData({ textColorStyle });

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: brightness > 128 ? '#000000' : '#ffffff',
      backgroundColor: bgColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    });
  },

  // 监听全局背景设置变化
  listenForBackgroundChanges() {
    // 简化实现，直接在app.ts中添加更新通知函数
    // 这里采用轮询方式检查本地存储的变化
    setInterval(() => {
      const navBgStyle = wx.getStorageSync('navBgStyle');
      if (navBgStyle && navBgStyle !== this.data.navBgStyle) {
        this.setData({ navBgStyle });
        this.updateTextColorByBackground(navBgStyle);
      }
    }, 1000); // 每秒检查一次
  },

  // 加载餐厅信息
  async loadRestaurantInfo() {
    try {
      const { currentKitchenId } = this.data;

      if (currentKitchenId) {
        // 从API获取厨房公告信息
        const kitchenInfo = await getKitchenInfo(currentKitchenId);
        if (kitchenInfo.error === 0 && kitchenInfo.body) {
          this.setData({
            notice: kitchenInfo.body.notice || '请勿上传违规图片和内容，包括但不限于色情、暴力、政治敏感等内容，违规用户将被封号处理！添加菜品时请填写完整信息，带*号为必填项'
          });
        } else {
          // 使用默认公告
          this.setData({
            notice: '请勿上传违规图片和内容，包括但不限于色情、暴力、政治敏感等内容，违规用户将被封号处理！添加菜品时请填写完整信息，带*号为必填项'
          });
        }
      } else {
        // 使用默认公告
        this.setData({
          notice: '请勿上传违规图片和内容，包括但不限于色情、暴力、政治敏感等内容，违规用户将被封号处理！添加菜品时请填写完整信息，带*号为必填项'
        });
      }

      // 检查公告是否需要滚动
      this.checkNoticeOverflow();
    } catch (err) {
      console.error('设置餐厅公告失败', err);
      // 使用默认公告
      this.setData({
        notice: '请勿上传违规图片和内容，包括但不限于色情、暴力、政治敏感等内容，违规用户将被封号处理！添加菜品时请填写完整信息，带*号为必填项'
      });
      this.checkNoticeOverflow();
    }
  },

  // 加载分类数据
  async loadCategories() {
    try {
      // 获取当前厨房ID
      const { currentKitchenId } = this.data;

      if (!currentKitchenId) {
        console.error('当前厨房ID不存在，清空分类数据');
        this.setData({
          categories: []
        });
        wx.showToast({
          title: '请先选择厨房',
          icon: 'none'
        });
        return;
      }

      console.log('加载分类数据，使用厨房ID:', currentKitchenId);

      // 传递当前厨房ID
      const result = await getDishCategories(currentKitchenId);

      console.log('获取分类列表响应:', result);

      if (result.error === 0) {
        // 检查返回数据结构
        let categories = result.body;

        // 兼容两种返回数据结构：直接数组或包含categories字段的对象
        if (result.body && result.body.categories) {
          categories = result.body.categories;
        }

        if (Array.isArray(categories) && categories.length > 0) {
          console.log('获取到分类数据:', categories);
          this.setData({
            categories: categories
          });
        } else {
          console.log('未获取到分类数据或数据为空，清空分类数据');
          this.setData({
            categories: []
          });
          wx.showToast({
            title: '当前厨房暂无分类，请先添加分类',
            icon: 'none'
          });
        }
      } else {
        console.error('获取分类列表失败', result.message);
        // API调用失败时清空分类数据
        this.setData({
          categories: []
        });
        wx.showToast({
          title: result.message || '获取分类失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('获取分类列表失败', err);
      // 发生错误时清空分类数据
      this.setData({
        categories: []
      });
      wx.showToast({
        title: '获取分类失败',
        icon: 'none'
      });
    }
  },

  // 检查公告文字是否溢出需要滚动
  checkNoticeOverflow() {
    const query = wx.createSelectorQuery();
    query.select('.notice-content').boundingClientRect();
    query.select('.scrolling-text').boundingClientRect();
    query.exec(rects => {
      if (rects.length === 2) {
        const containerWidth = rects[0].width;
        const textWidth = rects[1].width;
        this.setData({
          noticeOverflow: textWidth > containerWidth
        });
      }
    });
  },

  // 选择图片
  chooseImage() {
    // 限制最多上传5张图片
    const currentCount = this.data.imageList.length;
    const remainCount = 5 - currentCount;

    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传5张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1, // 每次只选一张
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
        const tempFilePath = res.tempFilePaths[0];
          // 先上传到服务器
          console.log('开始上传主图片:', tempFilePath);
          const uploadResult = await uploadDishImage(tempFilePath, this.data.currentKitchenId) as UploadResult;

          if (uploadResult.error === 0 && uploadResult.body && uploadResult.body.imageUrl) {
            const serverImageUrl = uploadResult.body.imageUrl;
            console.log('图片上传成功，服务器URL:', serverImageUrl);

            // 更新图片列表，使用服务器返回的URL而非临时路径
            const imageList = [...this.data.imageList, serverImageUrl];

        this.setData({
              tempFilePath: serverImageUrl, // 显示服务器图片
          imageList,
          'formData.image': imageList[0], // 主图片设为第一张
          'formData.imageList': imageList
        });
          } else {
            console.error('图片上传失败，响应:', JSON.stringify(uploadResult));
            throw new Error('上传失败: ' + (uploadResult.message || '未知错误'));
          }
        } catch (err) {
          console.error('图片上传失败:', err);
          wx.showToast({
            title: '图片上传失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },

  // 删除图片
  deleteImage(e: any) {
    const index = e.currentTarget.dataset.index;
    const imageList = [...this.data.imageList];
    imageList.splice(index, 1);

    this.setData({
      imageList,
      tempFilePath: imageList.length > 0 ? imageList[0] : '',
      'formData.image': imageList.length > 0 ? imageList[0] : '',
      'formData.imageList': imageList
    });
  },

  // 选择步骤图片
  chooseStepImage(e: any) {
    const index = e.currentTarget.dataset.index;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
        const tempFilePath = res.tempFilePaths[0];
          // 上传图片到服务器
          console.log('开始上传步骤图片:', tempFilePath);
          const uploadRes = await uploadDishImage(tempFilePath, this.data.currentKitchenId) as UploadResult;

          if (uploadRes.error === 0 && uploadRes.body && uploadRes.body.imageUrl) {
            // 更新步骤图片URL
            const serverImageUrl = uploadRes.body.imageUrl;
            console.log('步骤图片上传成功，服务器URL:', serverImageUrl);
        const key = `formData.cookingSteps[${index}].image`;
        this.setData({
              [key]: uploadRes.body.imageUrl
            });
          } else {
            console.error('步骤图片上传失败，响应:', JSON.stringify(uploadRes));
            throw new Error('上传失败: ' + (uploadRes.message || '未知错误'));
          }
        } catch (err) {
          console.error('步骤图片上传失败:', err);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
        });
        }
      }
    });
  },

  // 输入框获得焦点时的处理
  onInputFocus(e: any) {
    // 使用原生的adjust-position属性处理滚动，无需额外代码
    console.log('输入框获取焦点');
  },

  // 输入框失去焦点时的处理
  onInputBlur(e: any) {
    // 可以在这里处理输入框失去焦点的逻辑
    console.log('输入框失去焦点');
  },

  // 基本信息输入处理
  onInputChange(e: any) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    const key = `formData.${field}`;
    this.setData({
      [key]: value
    });

    // 实时敏感词检查（可选）
    if (value && value.length > 2) {
      const checkResult = checkSensitiveWord(value, field === 'name' ? 'dish' : 'content');
      if (checkResult.hasSensitiveWord) {
        // 可以在这里添加实时提示，比如输入框变红等
        console.warn('检测到敏感词:', checkResult.sensitiveWords);
      }
    }
  },

  // 处理星级评分变化
  onRatingChange(e: any) {
    const rating = Number(e.currentTarget.dataset.rating);
    console.log('星级评分变更为:', rating);
    this.setData({
      'formData.rating': rating
    });
  },

  // 配料输入处理
  onIngredientChange(e: any) {
    const { field, index } = e.currentTarget.dataset;
    const { value } = e.detail;
    const key = `formData.ingredients[${index}].${field}`;
    this.setData({
      [key]: value
    });
  },

  // 步骤输入处理
  onStepChange(e: any) {
    const { field, index } = e.currentTarget.dataset;
    const { value } = e.detail;
    const key = `formData.cookingSteps[${index}].${field}`;
    this.setData({
      [key]: value
    });
  },

  // 营养信息输入处理
  onNutritionChange(e: any) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const key = `formData.nutrition.${field}`;
    this.setData({
      [key]: value
    });
  },

  // 添加配料
  addIngredient() {
    const ingredients = this.data.formData.ingredients;
    ingredients.push({ name: '', amount: '' });
    this.setData({
      'formData.ingredients': ingredients
    });
  },

  // 删除配料
  deleteIngredient(e: any) {
    const index = e.currentTarget.dataset.index;
    const ingredients = this.data.formData.ingredients;

    // 如果只有一个配料，不允许删除，只清空
    if (ingredients.length === 1) {
      ingredients[0] = { name: '', amount: '' };
    } else {
      ingredients.splice(index, 1);
    }

    this.setData({
      'formData.ingredients': ingredients
    });
  },

  // 添加步骤
  addStep() {
    const steps = this.data.formData.cookingSteps;
    steps.push({ title: '', description: '', image: '' });
    this.setData({
      'formData.cookingSteps': steps
    });
  },

  // 删除步骤
  deleteStep(e: any) {
    const index = e.currentTarget.dataset.index;
    const steps = this.data.formData.cookingSteps;

    // 如果只有一个步骤，不允许删除，只清空
    if (steps.length === 1) {
      steps[0] = { title: '', description: '', image: '' };
    } else {
      steps.splice(index, 1);
    }

    this.setData({
      'formData.cookingSteps': steps
    });
  },

  // 移动步骤上移
  moveStepUp(e: any) {
    const index = e.currentTarget.dataset.index;
    if (index <= 0) return; // 第一个步骤不能上移

    const steps = this.data.formData.cookingSteps;
    // 交换当前步骤和上一步骤
    const temp = steps[index];
    steps[index] = steps[index - 1];
    steps[index - 1] = temp;

    this.setData({
      'formData.cookingSteps': steps
    });
  },

  // 移动步骤下移
  moveStepDown(e: any) {
    const index = e.currentTarget.dataset.index;
    const steps = this.data.formData.cookingSteps;

    if (index >= steps.length - 1) return; // 最后一个步骤不能下移

    // 交换当前步骤和下一步骤
    const temp = steps[index];
    steps[index] = steps[index + 1];
    steps[index + 1] = temp;

    this.setData({
      'formData.cookingSteps': steps
    });
  },

  // 打开分类选择弹窗
  openCategorySelect() {
    this.setData({
      showCategoryModal: true
    });
  },

  // 关闭分类选择弹窗
  closeCategoryModal() {
    this.setData({
      showCategoryModal: false
    });
  },

  // 选择分类
  selectCategory(e: any) {
    const { id, name } = e.currentTarget.dataset;
    this.setData({
      selectedCategoryId: id,
      selectedCategoryName: name
    });
  },

  // 确认分类选择
  confirmCategorySelect() {
    this.setData({
      'formData.categoryId': this.data.selectedCategoryId,
      'formData.categoryName': this.data.selectedCategoryName,
      showCategoryModal: false
    });
  },

  // 打开确认对话框
  openConfirmDialog(title: string, content: string, action: string) {
    this.setData({
      showConfirmDialog: true,
      confirmDialogTitle: title,
      confirmDialogContent: content,
      confirmDialogAction: action
    });
  },

  // 取消确认对话框
  onConfirmCancel() {
    this.setData({
      showConfirmDialog: false
    });
  },

  // 确认对话框确认操作
  onConfirmDialogConfirm() {
    const action = this.data.confirmDialogAction;

    if (action === 'cancel') {
      // 取消添加，返回上一页
      wx.navigateBack();
    } else if (action === 'save') {
      // 确认保存
      this.doSave();
    }

    this.setData({
      showConfirmDialog: false
    });
  },

  // 取消操作
  onCancel() {
    // 如果表单已填写内容，弹出确认对话框
    if (this.isFormFilled()) {
      this.openConfirmDialog(
        this.data.isEdit ? '取消编辑' : '取消添加',
        this.data.isEdit ? '您已修改的内容将不会保存，确定要取消吗？' : '您已填写的内容将不会保存，确定要取消吗？',
        'cancel'
      );
    } else {
      wx.navigateBack();
    }
  },

  // 保存操作
  onSave() {
    // 验证表单数据
    if (!this.validateForm()) {
      return;
    }

    // 敏感词检查
    const sensitiveCheckResult = this.checkSensitiveWords();
    if (sensitiveCheckResult.hasSensitiveWord) {
      wx.showModal({
        title: '内容审核',
        content: `检测到敏感内容：${sensitiveCheckResult.sensitiveWords.join('、')}，请修改后重新提交`,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF6B35'
      });
      return;
    }

    // 确认保存
    this.openConfirmDialog(
      this.data.isEdit ? '更新菜品' : '保存菜品',
      this.data.isEdit ? '确定要更新该菜品吗？' : '确定要保存该菜品吗？',
      'save'
    );
  },

  // 执行实际保存操作
  async doSave() {
    try {
      // 先处理图片上传
      let imageUrl = '';
      if (this.data.imageList.length > 0) {
        try {
          // 先上传图片
          const tempFilePath = this.data.imageList[0];

          // 检查如果图片路径已经是http开头，说明是已存在的远程图片，不需要再上传
          if (!tempFilePath.startsWith('http')) {
            console.log('上传菜品图片:', tempFilePath);
            const uploadResult: any = await uploadDishImage(tempFilePath, this.data.currentKitchenId);

            if (uploadResult && uploadResult.error === 0 && uploadResult.body && uploadResult.body.imageUrl) {
              imageUrl = uploadResult.body.imageUrl;
              console.log('图片上传成功:', imageUrl);
            } else {
              console.error('图片上传失败:', uploadResult);
              throw new Error('图片上传失败');
            }
          } else {
            // 已经是远程图片，直接使用
            imageUrl = tempFilePath;
            console.log('使用已有远程图片:', imageUrl);
          }
        } catch (uploadErr) {
          console.error('图片上传异常:', uploadErr);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 处理nutrition字段，将空字符串转为null
      const nutrition = { ...this.data.formData.nutrition };
      (Object.keys(nutrition) as Array<keyof typeof nutrition>).forEach(key => {
        if (nutrition[key] === '') nutrition[key] = null;
      });

      // 构造极简数据，只包含必要字段
      const formData: any = {
        name: this.data.formData.name.trim(), // 菜品名称
        categoryId: String(this.data.formData.categoryId), // 始终转为字符串
        kitchenId: this.data.currentKitchenId, // 厨房ID
        price: Number(this.data.formData.price) || 0, // 价格
        description: this.data.formData.description ? this.data.formData.description.trim() : '', // 描述
        image: imageUrl, // 已上传的图片URL
        images: this.data.imageList, // 多图列表，传递给后端的字段名为images
        rating: this.data.formData.rating || 3, // 星级评分
        ingredients: this.data.formData.ingredients.filter(item => item && item.name && item.name.trim() !== ''), // 过滤空的配料
        cookingSteps: this.data.formData.cookingSteps
          .filter(item => item && ((item.title && item.title.trim() !== '') || (item.description && item.description.trim() !== '')))
          .map(item => ({
            title: item.title || '', // 确保title字段始终有值
            description: item.description || '', // 确保description字段始终有值
            image: item.image || ''
          })), // 过滤空的步骤
        nutrition // 营养信息
      };

      // 如果是编辑模式，添加菜品ID
      if (this.data.isEdit && this.data.dishId) {
        formData.id = this.data.dishId;
      }

      // 输出提交的数据，方便调试
      console.log('准备提交菜品数据:', JSON.stringify(formData));

      let res;
      try {
        // 根据是否为编辑模式调用不同的API
        if (this.data.isEdit) {
          // 编辑模式调用updateDish
          res = await updateDish(formData);
        } else {
          // 添加模式调用addDish
        res = await addDish(formData);
        }
        console.log('API返回结果:', res);
      } catch (apiErr) {
        console.error('API调用出错:', apiErr);
        throw apiErr;
      }

      if (res && res.error === 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟返回上一页，让用户看到保存成功的提示
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      } else {
        console.log('保存返回错误:', res);
        wx.showToast({
          title: (res && res.message) || '保存失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (err) {
      console.error('保存失败', err);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 检查表单是否填写了内容
  isFormFilled() {
    const { formData } = this.data;
    return formData.name.trim() !== '' ||
           formData.description.trim() !== '' ||
           formData.price !== '' ||
           formData.categoryId !== '' ||
           formData.image !== '';
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    // 验证必填字段
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入菜品名称',
        icon: 'none'
      });
      // 滚动到菜品名称区域
      this.scrollToElement('name-section');
      return false;
    }

    if (!formData.categoryId) {
      wx.showToast({
        title: '请选择菜品分类',
        icon: 'none'
      });
      // 滚动到分类区域
      this.scrollToElement('category-section');
      return false;
    }

    // 价格可以为空，会默认为0
    if (formData.price) {
      const price = parseFloat(formData.price as string);
      if (isNaN(price) || price < 0) {
        wx.showToast({
          title: '价格必须为非负数',
          icon: 'none'
        });
        return false;
      }
    }

    // 验证图片
    if (!formData.image) {
      wx.showToast({
        title: '请上传菜品图片',
        icon: 'none'
      });
      // 滚动到图片上传区域
      this.scrollToElement('image-section');
      return false;
    }

    // 验证配料表
    const hasIngredients = formData.ingredients.some(item => item.name.trim() !== '');
    if (!hasIngredients) {
      wx.showToast({
        title: '请至少添加一种配料',
        icon: 'none'
      });
      // 滚动到配料表区域
      this.scrollToElement('ingredients-section');
      return false;
    }

    return true;
  },

  // 滚动到指定元素（用于表单验证错误提示）
  scrollToElement(elementId: string) {
    if (!elementId) return;
    
    const query = wx.createSelectorQuery();
    query.select('#' + elementId).boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        const rect = res[0];
        // 简单滚动到元素位置，留出一些上边距
        wx.pageScrollTo({
          scrollTop: Math.max(0, rect.top - 100),
          duration: 300
        });
      }
    });
  },

  // 加载菜品数据（编辑模式）
  async loadDishData(dishId: string) {
    try {
      // 调用获取菜品详情的API
      const res = await getDishDetail(dishId);
      if (res.error === 0 && res.body) {
        // 处理多图数据
        const imageUrls = res.body.images && res.body.images.length > 0
          ? res.body.images.map((img: any) => img.url)
          : (res.body.image ? [res.body.image] : []);

        // 设置表单数据
        this.setData({
          formData: {
            ...this.data.formData,
            name: res.body.name || '',
            categoryId: res.body.categoryId || '',
            categoryName: res.body.categoryName || '',
            price: res.body.price !== undefined ? String(res.body.price) : '',
            rating: res.body.rating || 3,
            description: res.body.description || '',
            image: res.body.image || '',
            imageList: imageUrls,
            ingredients: res.body.ingredients && res.body.ingredients.length > 0
              ? res.body.ingredients
              : [{ name: '', amount: '' }],
            cookingSteps: res.body.cookingSteps && res.body.cookingSteps.length > 0
              ? res.body.cookingSteps.map((step: any) => ({
                  title: step.title || '',
                  description: step.description || '',
                  image: step.image || ''
                }))
              : [{ title: '', description: '', image: '' }],
            nutrition: {
              calories: (res.body.nutrition && res.body.nutrition.calories) || '',
              protein: (res.body.nutrition && res.body.nutrition.protein) || '',
              carbs: (res.body.nutrition && res.body.nutrition.carbs) || '',
              fat: (res.body.nutrition && res.body.nutrition.fat) || ''
            }
          },
          // 更新图片列表
          imageList: imageUrls,
          tempFilePath: res.body.image || '',
          // 更新分类选择
          selectedCategoryId: res.body.categoryId || '',
          selectedCategoryName: res.body.categoryName || ''
        });
      } else {
        throw new Error('获取菜品数据失败');
      }
    } catch (err) {
      console.error('获取菜品详情失败', err);
      wx.showToast({
        title: '获取菜品信息失败',
        icon: 'none'
      });
    }
  },

  /**
   * 检查敏感词
   */
  checkSensitiveWords() {
    const { formData } = this.data;
    const foundWords: string[] = [];
    let hasSensitive = false;

    // 检查菜品名称
    const nameCheck = checkSensitiveWord(formData.name, 'dish');
    if (nameCheck.hasSensitiveWord) {
      hasSensitive = true;
      foundWords.push(...nameCheck.sensitiveWords);
    }

    // 检查菜品介绍
    if (formData.description) {
      const descCheck = checkSensitiveWord(formData.description, 'content');
      if (descCheck.hasSensitiveWord) {
        hasSensitive = true;
        foundWords.push(...descCheck.sensitiveWords);
      }
    }

    // 检查配料名称
    formData.ingredients.forEach((ingredient: any) => {
      if (ingredient.name) {
        const ingredientCheck = checkSensitiveWord(ingredient.name, 'dish');
        if (ingredientCheck.hasSensitiveWord) {
          hasSensitive = true;
          foundWords.push(...ingredientCheck.sensitiveWords);
        }
      }
    });

    // 检查烹饪步骤
    formData.cookingSteps.forEach((step: any) => {
      if (step.title) {
        const titleCheck = checkSensitiveWord(step.title, 'content');
        if (titleCheck.hasSensitiveWord) {
          hasSensitive = true;
          foundWords.push(...titleCheck.sensitiveWords);
        }
      }
      if (step.description) {
        const descCheck = checkSensitiveWord(step.description, 'content');
        if (descCheck.hasSensitiveWord) {
          hasSensitive = true;
          foundWords.push(...descCheck.sensitiveWords);
        }
      }
    });

    return {
      hasSensitiveWord: hasSensitive,
      sensitiveWords: Array.from(new Set(foundWords)) // 去重
    };
  },
});