/* 容器样式 - 确保能完全继承外部样式 */
.smart-image-container {
  position: relative;
  overflow: hidden;
  display: block;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 自适应高度容器 */
.smart-image-container.adaptive-height {
  height: auto;
  min-height: 200rpx;
}

.smart-image-container.adaptive-height .smart-image {
  position: relative;
  height: auto;
  width: 100%;
  display: block;
}

.smart-image-container.adaptive-height .smart-image-placeholder,
.smart-image-container.adaptive-height .smart-image-error {
  position: relative;
  height: 200rpx;
}

/* 占位层样式 */
.smart-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #F8F9FA 0%, #E2F3FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  box-sizing: border-box;
}

/* 呼吸动画 */
.breathing {
  animation: breathing 2s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 1.0;
    transform: scale(1.02);
  }
}

/* 占位内容 */
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #C8D6E5;
  width: 100%;
  height: 100%;
}

.placeholder-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  background: linear-gradient(45deg, #E2F3FF 0%, #F8F9FA 100%);
  opacity: 0.8;
  position: relative;
}

.placeholder-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid #C8D6E5;
  border-radius: 4rpx;
  box-sizing: border-box;
}

.placeholder-icon::after {
  content: '';
  position: absolute;
  top: 28rpx;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #C8D6E5;
}

/* 图片样式 - 确保完全填充容器 */
.smart-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease-in-out;
  z-index: 1;
  display: block;
  box-sizing: border-box;
}

.fade-in {
  opacity: 1;
}

.fade-out {
  opacity: 0;
}

/* 错误层样式 */
.smart-image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  animation: breathing 2s ease-in-out infinite;
  box-sizing: border-box;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999999;
  width: 100%;
  height: 100%;
}

.error-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
}

.error-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #CCCCCC;
  border-radius: 4rpx;
  box-sizing: border-box;
}

.error-icon::after {
  content: '×';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  color: #CCCCCC;
  font-weight: bold;
}

.error-text {
  font-size: 24rpx;
  color: #999999;
} 