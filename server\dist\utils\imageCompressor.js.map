{"version": 3, "file": "imageCompressor.js", "sourceRoot": "", "sources": ["../../src/utils/imageCompressor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;GAGG;AACH,kDAA0B;AAC1B,gDAAwB;AACxB,4CAAoB;AACpB,sDAA8B;AAC9B,gDAAqD;AAErD,SAAS;AACT,MAAM,kBAAkB,GAAG;IACzB,YAAY;IACZ,MAAM,EAAE;QACN,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,MAAe;KACxB;IACD,UAAU,EAAE;QACV,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,MAAe;KACxB;IACD,OAAO;IACP,OAAO,EAAE;QACP,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,MAAe;KACxB;IACD,OAAO;IACP,IAAI,EAAE;QACJ,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,MAAe;KACxB;IACD,OAAO;IACP,QAAQ,EAAE;QACR,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,MAAe;KACxB;IACD,MAAM;IACN,MAAM,EAAE;QACN,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,OAAO,EAAE,EAAE,EAAE,yBAAyB;QACtC,MAAM,EAAE,KAAc;KACvB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,eAAe;IACnB;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc;QAC3B,sCAAsC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;QACjE,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAEpD,eAAe;QACf,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,YAAY,CAAC,GAAW;QACrC,MAAM,KAAK,GAAG,sCAAsC,CAAC;QACrD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YAC9C,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;CACF;AAED;;;;;;;GAOG;AACI,MAAM,gBAAgB,GAAG,CAC9B,MAAc,EACd,IAA+D,EAC/D,SAAkB,EAClB,YAAoB,MAAM,EAClB,EAAE;IACV,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY;YACf,6BAA6B;YAC7B,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE,CAAC,CAAC;YACtE,OAAO,GAAG,MAAM,IAAI,WAAW,GAAG,SAAS,EAAE,CAAC;QAEhD,KAAK,SAAS;YACZ,6BAA6B;YAC7B,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAa,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,MAAM,YAAY,SAAS,EAAE,CAAC,CAAC;YACtF,OAAO,GAAG,MAAM,IAAI,SAAS,IAAI,cAAc,GAAG,SAAS,EAAE,CAAC;QAEhE,KAAK,MAAM;YACT,kCAAkC;YAClC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAa,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;YACtF,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,MAAM,YAAY,SAAS,OAAO,CAAC,CAAC;YACxF,OAAO,GAAG,MAAM,IAAI,SAAS,IAAI,UAAU,IAAI,WAAW,GAAG,SAAS,EAAE,CAAC;QAE3E,KAAK,UAAU;YACb,6BAA6B;YAC7B,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAa,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,MAAM,YAAY,SAAS,WAAW,CAAC,CAAC;YAChG,OAAO,GAAG,MAAM,IAAI,SAAS,IAAI,eAAe,GAAG,SAAS,EAAE,CAAC;QAEjE;YACE,MAAM,IAAI,qBAAa,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;AACH,CAAC,CAAC;AA3CW,QAAA,gBAAgB,oBA2C3B;AAEF;;;;;;GAMG;AACI,MAAM,aAAa,GAAG,CAC3B,SAAiB,EACjB,UAAkB,EAClB,IAAqC,EACtB,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,qBAAa,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,WAAW;QACX,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,YAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,gBAAgB;QAChB,MAAM,aAAa,GAAG,IAAA,eAAK,EAAC,SAAS,CAAC;aACnC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE;YACzC,GAAG,EAAE,QAAQ;YACb,kBAAkB,EAAE,IAAI;SACzB,CAAC,CAAC;QAEL,aAAa;QACb,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC5B,MAAM,aAAa;iBAChB,GAAG,CAAC;gBACH,gBAAgB,EAAE,CAAC;gBACnB,iBAAiB,EAAE,KAAK;gBACxB,KAAK,EAAE,IAAI;aACZ,CAAC;iBACD,MAAM,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,MAAM,aAAa;iBAChB,IAAI,CAAC;gBACJ,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,IAAI;aAClB,CAAC;iBACD,MAAM,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC;QAED,gBAAM,CAAC,IAAI,CAAC,WAAW,SAAS,OAAO,UAAU,EAAE,EAAE;YACnD,IAAI;YACJ,MAAM;SACP,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,WAAW,KAAK,EAAE,EAAE;YAC/B,SAAS;YACT,UAAU;YACV,IAAI;SACL,CAAC,CAAC;QACH,MAAM,IAAI,qBAAa,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAA,CAAC;AAxDW,QAAA,aAAa,iBAwDxB;AAEF;;;;GAIG;AACI,MAAM,YAAY,GAAG,CAAO,QAAgB,EAAE,EAAE;IACrD,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QAClD,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,IAAI,EAAE,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI;SACjC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;QACnC,MAAM,IAAI,qBAAa,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC;AACH,CAAC,CAAA,CAAC;AAbW,QAAA,YAAY,gBAavB;AAEF,kBAAe;IACb,gBAAgB,EAAhB,wBAAgB;IAChB,aAAa,EAAb,qBAAa;IACb,YAAY,EAAZ,oBAAY;IACZ,eAAe;CAChB,CAAC"}