/**
 * 系统设置表迁移文件
 */
import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.createTable('system_settings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '设置ID',
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '设置键名',
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '设置值',
    },
    type: {
      type: DataTypes.ENUM('boolean', 'string', 'number', 'json'),
      allowNull: false,
      defaultValue: 'string',
      comment: '值类型',
    },
    description: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '设置描述',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  });

  // 创建索引
  await queryInterface.addIndex('system_settings', ['key'], {
    name: 'idx_key',
    unique: true,
  });

  await queryInterface.addIndex('system_settings', ['type'], {
    name: 'idx_type',
  });

  // 插入默认设置
  await queryInterface.bulkInsert('system_settings', [
    {
      key: 'comment_enabled',
      value: 'true',
      type: 'boolean',
      description: '是否开启评论功能',
      created_at: new Date(),
      updated_at: new Date(),
    },
  ]);
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  await queryInterface.dropTable('system_settings');
}; 