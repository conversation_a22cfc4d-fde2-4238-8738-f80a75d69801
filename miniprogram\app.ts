// app.ts
import { checkLogin } from './utils/util'

// 定义背景设置类型
interface BackgroundSettings {
  shopBg: string;
  navBgStyle: string;
  navBgIndex: number;
}

// 定义未读消息计数类型
interface UnreadCounts {
  likes: number;
  system: number;
  comment: number;
}

// 定义监听器回调类型
type ThemeChangeListener = (settings: BackgroundSettings) => void;
type MessageCountChangeListener = (counts: UnreadCounts) => void;

// 扩展全局数据接口
interface IAppGlobalData {
  isLoggedIn: boolean;
  userInfo: WechatMiniprogram.UserInfo;
  systemInfo: WechatMiniprogram.SystemInfo;
  backgroundSettings: BackgroundSettings;
  themeChangeListeners: ThemeChangeListener[];
  unreadCounts: UnreadCounts;
  messageCountChangeListeners: MessageCountChangeListener[];
  currentKitchenId?: string; // 当前选中的厨房ID
}

// 扩展App选项接口
interface IAppOption {
  globalData: IAppGlobalData;
  getSystemInfo(): void;
  checkLoginStatus(): void;
  setLoginStatus(isLoggedIn: boolean, userInfo?: any): void;
  loadBackgroundSettings(): void;
  updateBackgroundSettings(settings: BackgroundSettings): void;
  addThemeChangeListener(listener: ThemeChangeListener): void;
  removeThemeChangeListener(listener: ThemeChangeListener): void;
  notifyThemeChanged(): void;
  loadUnreadCounts(): void;
  updateUnreadCounts(counts: UnreadCounts): void;
  updateSingleUnreadCount(type: 'likes' | 'system' | 'comment', count: number): void;
  addMessageCountChangeListener(listener: MessageCountChangeListener): void;
  removeMessageCountChangeListener(listener: MessageCountChangeListener): void;
  notifyMessageCountChanged(): void;
  setupRequestInterceptor(): void;
  initImagePreloader(): void;
}

App<IAppOption>({
  globalData: {
    isLoggedIn: false,
    userInfo: {} as WechatMiniprogram.UserInfo,
    systemInfo: {} as WechatMiniprogram.SystemInfo,
    // 背景设置
    backgroundSettings: {
      shopBg: '',
      navBgStyle: 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)', // 默认为第一个渐变色
      navBgIndex: 0
    },
    // 主题变更监听器集合
    themeChangeListeners: [],
    // 未读消息计数
    unreadCounts: {
      likes: 0,
      system: 0,
      comment: 0
    },
    // 消息计数变更监听器集合
    messageCountChangeListeners: [],
    currentKitchenId: undefined
  },

  onLaunch() {
    // 获取系统信息
    this.getSystemInfo()

    // 检查登录状态
    this.checkLoginStatus()

    // 加载背景设置
    this.loadBackgroundSettings()

    // 加载未读消息计数
    this.loadUnreadCounts()

    // 设置请求拦截器
    this.setupRequestInterceptor()

    // 智能预加载常用图标
    this.initImagePreloader()

    console.log('小程序启动完成')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onShow() {
    console.log('小程序显示')
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.globalData.systemInfo = systemInfo
    } catch (e) {
      console.error('获取系统信息失败:', e)
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = checkLogin()
    this.globalData.isLoggedIn = isLoggedIn

    if (isLoggedIn) {
      const userInfo = wx.getStorageSync('userInfo') || {}
      this.globalData.userInfo = userInfo
    }
  },

  // 设置用户登录状态
  setLoginStatus(isLoggedIn: boolean, userInfo?: any) {
    this.globalData.isLoggedIn = isLoggedIn

    if (userInfo) {
      this.globalData.userInfo = userInfo
      wx.setStorageSync('userInfo', userInfo)
    }
  },

  // 加载背景设置
  loadBackgroundSettings() {
    const shopBg = wx.getStorageSync('shopBg') || ''
    const navBgStyle = wx.getStorageSync('navBgStyle') || 'linear-gradient(to bottom, #F8F9FA, #E2F3FF)'
    const navBgIndex = wx.getStorageSync('navBgIndex') !== undefined ? wx.getStorageSync('navBgIndex') : 0

    this.globalData.backgroundSettings = {
      shopBg,
      navBgStyle,
      navBgIndex
    }
  },

  // 更新背景设置并通知所有页面
  updateBackgroundSettings(settings: BackgroundSettings) {
    // 更新全局数据
    this.globalData.backgroundSettings = settings

    // 保存到本地存储
    wx.setStorageSync('shopBg', settings.shopBg)
    wx.setStorageSync('navBgStyle', settings.navBgStyle)
    wx.setStorageSync('navBgIndex', settings.navBgIndex)

    // 通知所有监听器
    this.notifyThemeChanged()
  },

  // 添加主题变更监听器
  addThemeChangeListener(listener: ThemeChangeListener) {
    this.globalData.themeChangeListeners.push(listener)
  },

  // 移除主题变更监听器
  removeThemeChangeListener(listener: ThemeChangeListener) {
    const index = this.globalData.themeChangeListeners.indexOf(listener)
    if (index > -1) {
      this.globalData.themeChangeListeners.splice(index, 1)
    }
  },

  // 通知所有主题变更监听器
  notifyThemeChanged() {
    const settings = this.globalData.backgroundSettings
    this.globalData.themeChangeListeners.forEach(listener => {
      try {
        listener(settings)
      } catch (error) {
        console.error('主题变更监听器执行失败:', error)
      }
    })
  },

  // 加载未读消息计数
  loadUnreadCounts() {
    const counts = {
      likes: wx.getStorageSync('unreadLikes') || 0,
      system: wx.getStorageSync('unreadSystem') || 0,
      comment: wx.getStorageSync('unreadComment') || 0
    }
    this.globalData.unreadCounts = counts
  },

  // 更新未读消息计数
  updateUnreadCounts(counts: UnreadCounts) {
    this.globalData.unreadCounts = counts

    // 保存到本地存储
    wx.setStorageSync('unreadLikes', counts.likes)
    wx.setStorageSync('unreadSystem', counts.system)
    wx.setStorageSync('unreadComment', counts.comment)

    // 通知所有监听器
    this.notifyMessageCountChanged()
  },

  // 更新单个未读消息计数
  updateSingleUnreadCount(type: 'likes' | 'system' | 'comment', count: number) {
    this.globalData.unreadCounts[type] = count

    // 保存到本地存储
    const storageKey = `unread${type.charAt(0).toUpperCase() + type.slice(1)}`
    wx.setStorageSync(storageKey, count)

    // 通知所有监听器
    this.notifyMessageCountChanged()
  },

  // 添加消息计数变更监听器
  addMessageCountChangeListener(listener: MessageCountChangeListener) {
    this.globalData.messageCountChangeListeners.push(listener)
  },

  // 移除消息计数变更监听器
  removeMessageCountChangeListener(listener: MessageCountChangeListener) {
    const index = this.globalData.messageCountChangeListeners.indexOf(listener)
    if (index > -1) {
      this.globalData.messageCountChangeListeners.splice(index, 1)
    }
  },

  // 通知所有消息计数变更监听器
  notifyMessageCountChanged() {
    const counts = this.globalData.unreadCounts
    this.globalData.messageCountChangeListeners.forEach(listener => {
      try {
        listener(counts)
      } catch (error) {
        console.error('消息计数变更监听器执行失败:', error)
      }
    })
  },

  // 设置请求拦截器
  setupRequestInterceptor() {
    // 拦截 wx.request 请求
    const originalRequest = wx.request

    // @ts-ignore
    wx.request = (options: WechatMiniprogram.RequestOption) => {
      // 获取 token
      const token = wx.getStorageSync('token') || ''

      // 添加 token 到请求头
      const header = options.header || {}
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }

      // 合并请求选项
      const newOptions = {
        ...options,
        header,
        fail: (res: any) => {
          console.error('请求失败:', res)
          if (options.fail) {
            options.fail(res)
          }
        }
      }

      // 调用原始请求方法
      return originalRequest(newOptions)
    }
  },

  // 初始化图片预加载
  initImagePreloader() {
    // 延迟执行，避免影响启动速度
    setTimeout(() => {
      try {
        const { smartPreload } = require('./utils/imagePreloader');
        smartPreload();
      } catch (error) {
        console.error('图片预加载模块加载失败:', error);
        // 预加载失败不影响正常功能
      }
    }, 1000); // 延迟1秒执行
  },
})