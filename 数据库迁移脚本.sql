-- ================================
-- 厨房二维码URL格式转换
-- ================================

-- 更新厨房二维码URL：完整URL → 相对路径
UPDATE kitchens 
SET qr_code_url = CASE 
  WHEN qr_code_url LIKE 'https://dzcdd.zj1.natnps.cn/%' THEN SUBSTRING(qr_code_url, 28)  -- 移除 'https://dzcdd.zj1.natnps.cn'
  WHEN qr_code_url LIKE 'http://dzcdd.zj1.natnps.cn/%' THEN SUBSTRING(qr_code_url, 27)   -- 移除 'http://dzcdd.zj1.natnps.cn'
  WHEN qr_code_url LIKE 'https://%' AND qr_code_url LIKE '%/uploads/%' THEN 
    CONCAT('/', SUBSTRING_INDEX(qr_code_url, '/uploads/', -1))  -- 提取 /uploads/ 之后的部分
  WHEN qr_code_url LIKE 'http://%' AND qr_code_url LIKE '%/uploads/%' THEN 
    CONCAT('/', SUBSTRING_INDEX(qr_code_url, '/uploads/', -1))  -- 提取 /uploads/ 之后的部分
  ELSE qr_code_url  -- 如果已经是相对路径，保持不变
END
WHERE qr_code_url IS NOT NULL 
  AND qr_code_url != '' 
  AND (qr_code_url LIKE 'http%' OR qr_code_url LIKE 'https%');

-- 显示厨房二维码URL转换结果
SELECT 
  'kitchens qr_code_url转换完成' as table_name,
  COUNT(*) as total_count,
  SUM(CASE WHEN qr_code_url LIKE 'http%' THEN 1 ELSE 0 END) as remaining_full_urls,
  SUM(CASE WHEN qr_code_url LIKE '/uploads/%' THEN 1 ELSE 0 END) as converted_relative_paths
FROM kitchens 
WHERE qr_code_url IS NOT NULL AND qr_code_url != '';

-- 显示转换示例
SELECT 
  'kitchens qr_code_url转换示例' as description,
  id as kitchen_id,
  qr_code_url as current_url
FROM kitchens 
WHERE qr_code_url IS NOT NULL AND qr_code_url != ''
LIMIT 5;

-- ================================
-- 用户ID格式修复
-- ================================

-- 备份现有用户数据到临时表
CREATE TEMPORARY TABLE users_backup AS SELECT * FROM users;

-- 停用外键检查（避免关联表约束问题）
SET FOREIGN_KEY_CHECKS = 0;

-- 创建用户ID映射表（旧ID -> 新ID）
CREATE TEMPORARY TABLE user_id_mapping (
  old_id INT PRIMARY KEY,
  new_id INT NOT NULL
);

-- 为每个现有用户生成新的5位数ID
SET @new_id = 10001; -- 从配置的起始ID开始

INSERT INTO user_id_mapping (old_id, new_id)
SELECT 
  id as old_id,
  @new_id := @new_id + (@new_id := @new_id) * 0 + 1 as new_id
FROM users 
ORDER BY id;

-- 显示ID映射关系
SELECT 
  'users ID映射关系' as description,
  old_id,
  new_id,
  CONCAT('用户ID: ', old_id, ' -> ', new_id) as mapping
FROM user_id_mapping
ORDER BY old_id;

-- 更新关联表中的用户ID引用
-- 1. 更新厨房拥有者ID
UPDATE kitchens k 
JOIN user_id_mapping m ON k.owner_id = m.old_id 
SET k.owner_id = m.new_id;

-- 2. 更新厨房成员用户ID
UPDATE kitchen_members km 
JOIN user_id_mapping m ON km.user_id = m.old_id 
SET km.user_id = m.new_id;

-- 3. 更新会员记录用户ID
UPDATE memberships mem 
JOIN user_id_mapping m ON mem.user_id = m.old_id 
SET mem.user_id = m.new_id;

-- 4. 更新交易记录用户ID
UPDATE transactions t 
JOIN user_id_mapping m ON t.user_id = m.old_id 
SET t.user_id = m.new_id;

-- 5. 更新签到记录用户ID
UPDATE sign_ins si 
JOIN user_id_mapping m ON si.user_id = m.old_id 
SET si.user_id = m.new_id;

-- 6. 更新背景设置用户ID
UPDATE background_settings bs 
JOIN user_id_mapping m ON bs.user_id = m.old_id 
SET bs.user_id = m.new_id;

-- 7. 更新搜索历史用户ID
UPDATE search_histories sh 
JOIN user_id_mapping m ON sh.user_id = m.old_id 
SET sh.user_id = m.new_id;

-- 8. 更新广告观看记录用户ID
UPDATE ad_views av 
JOIN user_id_mapping m ON av.user_id = m.old_id 
SET av.user_id = m.new_id;

-- 9. 更新菜品创建者ID
UPDATE dishes d 
JOIN user_id_mapping m ON d.created_by = m.old_id 
SET d.created_by = m.new_id;

-- 10. 更新订单用户ID（如果有订单表）
-- UPDATE orders o 
-- JOIN user_id_mapping m ON o.user_id = m.old_id 
-- SET o.user_id = m.new_id;

-- 清空用户表并插入新的ID数据
DELETE FROM users;

INSERT INTO users (
  id, open_id, nick_name, avatar_url, gender, coins, 
  likes, dishes, kitchens, token, created_at, updated_at
)
SELECT 
  m.new_id, ub.open_id, ub.nick_name, ub.avatar_url, ub.gender, ub.coins,
  ub.likes, ub.dishes, ub.kitchens, ub.token, ub.created_at, ub.updated_at
FROM users_backup ub
JOIN user_id_mapping m ON ub.id = m.old_id
ORDER BY m.new_id;

-- 重置AUTO_INCREMENT为下一个可用的5位数ID
SET @max_id = (SELECT MAX(id) FROM users);
SET @next_id = @max_id + 1;
SET @sql = CONCAT('ALTER TABLE users AUTO_INCREMENT = ', @next_id);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证修复结果
SELECT 
  'users ID修复完成' as table_name,
  COUNT(*) as total_users,
  MIN(id) as min_id,
  MAX(id) as max_id,
  CASE 
    WHEN MIN(id) >= 10001 THEN '✅ ID格式正确(5位数起始)'
    ELSE '❌ ID格式异常'
  END as id_format_status
FROM users;

-- 显示修复后的用户ID示例
SELECT 
  'users ID修复示例' as description,
  id as user_id,
  nick_name,
  CASE 
    WHEN id >= 10001 THEN '✅ 5位数ID'
    ELSE '❌ 异常ID'
  END as id_status
FROM users 
ORDER BY id
LIMIT 10;

-- 清理临时表
DROP TEMPORARY TABLE users_backup;
DROP TEMPORARY TABLE user_id_mapping;

-- ================================ 