{"version": 3, "file": "createTables.js", "sourceRoot": "", "sources": ["../../../src/scripts/migrations/createTables.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;GAGG;AACH,qEAA8C;AAC9C,gEAAwC;AACxC,qDAAuC;AAEvC;;GAEG;AACH,SAAe,YAAY;;QACzB,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE3B,IAAI,CAAC;YACH,SAAS;YACT,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,kBAAS,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAEpD,QAAQ;YACR,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,MAAM,kBAAS,CAAC,iBAAiB,EAAE,CAAC,aAAa,EAAE,CAAC;YAEpD,aAAa;YACb,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE3B,kBAAkB;YAClB,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxB,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAExC,gBAAgB;YAChB,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxB,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3C,gBAAgB;YAChB,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxB,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5C,oBAAoB;YACpB,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxB,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAExC,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1C,MAAM,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACrD,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE1C,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAEzC,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACxC,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1C,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3C,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE9C,WAAW;YACX,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,IAAI,CAAC;gBACH,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1B,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gBAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC3B,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvC,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC;gBACH,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxB,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gBAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBACzB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvC,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC;gBACH,gBAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzB,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gBAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC1B,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvC,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,WAAW;YACX,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3C,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhD,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhD,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5C,YAAY;YACZ,gBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5B,MAAM,CAAC,sBAAsB,EAAE,CAAC;YAEhC,SAAS;YACT,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,kBAAS,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAEpD,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC1B,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvC,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,EAAE,CAAC,CAAC;YACjC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAAA;AAED,mBAAmB;AACnB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,YAAY,EAAE;SACX,IAAI,CAAC,GAAG,EAAE;QACT,gBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,gBAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kBAAe,YAAY,CAAC"}