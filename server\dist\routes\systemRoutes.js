"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 系统设置路由
 */
const express_1 = require("express");
const systemSettingController_1 = __importDefault(require("../controllers/systemSettingController"));
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// 获取评论功能开关状态（公开接口）
router.get('/comment-enabled', systemSettingController_1.default.isCommentEnabled);
// 以下接口需要管理员权限
router.use(auth_1.verifyAdminToken);
// 获取所有系统设置
router.get('/settings', systemSettingController_1.default.getAllSettings);
// 获取指定设置
router.get('/setting/:key', systemSettingController_1.default.getSetting);
// 更新系统设置
router.post('/setting', systemSettingController_1.default.updateSetting);
exports.default = router;
//# sourceMappingURL=systemRoutes.js.map