"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 发现服务
 * 处理发现页相关的业务逻辑
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
const helpers_1 = require("../utils/helpers");
const dishService_1 = __importDefault(require("./dishService"));
/**
 * 获取发现列表
 * @param userId 用户ID（可选）
 * @param page 页码
 * @param pageSize 每页大小
 * @param sortType 排序类型 'hot' | 'time' | 'random'
 * @param searchValue 搜索关键词
 * @returns 发现列表
 */
const getDiscoverList = (userId_1, ...args_1) => __awaiter(void 0, [userId_1, ...args_1], void 0, function* (userId, page = 1, pageSize = 10, sortType = 'hot', searchValue = '') {
    try {
        // 分页参数
        const { limit, offset } = (0, helpers_1.getPagination)(page, pageSize);
        // 构建查询条件
        const whereConditions = {};
        const includeConditions = [];
        // 搜索条件
        if (searchValue && searchValue.trim()) {
            includeConditions.push({
                model: models_1.Dish,
                as: 'dish',
                where: {
                    name: {
                        [sequelize_1.Op.like]: `%${searchValue.trim()}%`
                    }
                },
                include: [
                    {
                        model: models_1.Kitchen,
                        as: 'kitchen',
                        attributes: ['id', 'name', 'avatar_url'],
                    },
                    {
                        model: models_1.User,
                        as: 'creator',
                        attributes: ['id', 'nick_name', 'avatar_url'],
                    },
                    {
                        model: models_1.Ingredient,
                        as: 'ingredients',
                        attributes: ['id', 'name', 'amount'],
                        order: [['sort', 'ASC']],
                    },
                ],
            });
        }
        else {
            includeConditions.push({
                model: models_1.Dish,
                as: 'dish',
                include: [
                    {
                        model: models_1.Kitchen,
                        as: 'kitchen',
                        attributes: ['id', 'name', 'avatar_url'],
                    },
                    {
                        model: models_1.User,
                        as: 'creator',
                        attributes: ['id', 'nick_name', 'avatar_url'],
                    },
                    {
                        model: models_1.Ingredient,
                        as: 'ingredients',
                        attributes: ['id', 'name', 'amount'],
                        order: [['sort', 'ASC']],
                    },
                ],
            });
        }
        // 排序条件
        let orderConditions = [];
        if (sortType === 'time') {
            // 按菜品更新时间排序
            orderConditions = [[{ model: models_1.Dish, as: 'dish' }, 'updated_at', 'DESC']];
        }
        else if (sortType === 'random') {
            // 随机排序
            orderConditions = [database_1.default.fn('RAND')];
        }
        else {
            // 热门排序：按菜品销量排序
            orderConditions = [[{ model: models_1.Dish, as: 'dish' }, 'sales', 'DESC']];
        }
        // 查询发现项
        const { count, rows } = yield models_1.DiscoverItem.findAndCountAll({
            where: whereConditions,
            include: includeConditions,
            order: orderConditions,
            limit,
            offset,
            distinct: true,
        });
        logger_1.default.info(`查询到发现项数量: ${rows.length}`);
        // 如果有用户ID，查询用户的添加记录
        let userAddedDishNames = new Set();
        if (userId) {
            try {
                // 查询用户在自己厨房中添加过的菜品名称
                const userDishes = yield models_1.Dish.findAll({
                    where: {
                        created_by: userId,
                    },
                    attributes: ['name'],
                });
                // 创建用户已添加菜品名称的集合
                userAddedDishNames = new Set(userDishes.map(dish => dish.name));
            }
            catch (error) {
                logger_1.default.error('查询用户添加记录失败:', error);
            }
        }
        // 构建返回结果
        const items = rows.map(item => {
            const dish = item.dish;
            if (!dish) {
                logger_1.default.warn(`发现项 ${item.id} 关联的菜品不存在`);
                return null;
            }
            // 判断用户是否已添加该菜品（通过菜品名称判断，因为用户可能添加了同名菜品）
            const isAdded = userId ? userAddedDishNames.has(dish.name) : false;
            // 从配料表生成标签，取前5个配料的名称
            const tags = (dish === null || dish === void 0 ? void 0 : dish.ingredients) ? dish.ingredients.slice(0, 5).map((ingredient) => ingredient.name) : [];
            return {
                id: dish === null || dish === void 0 ? void 0 : dish.id,
                dishId: dish === null || dish === void 0 ? void 0 : dish.id,
                name: dish === null || dish === void 0 ? void 0 : dish.name,
                image: dish === null || dish === void 0 ? void 0 : dish.image,
                price: dish === null || dish === void 0 ? void 0 : dish.price,
                originalPrice: dish === null || dish === void 0 ? void 0 : dish.original_price,
                description: dish === null || dish === void 0 ? void 0 : dish.description,
                tags: tags, // 使用配料表生成的标签而不是原有的tags字段
                sales: dish === null || dish === void 0 ? void 0 : dish.sales,
                rating: dish === null || dish === void 0 ? void 0 : dish.rating,
                kitchen: (dish === null || dish === void 0 ? void 0 : dish.kitchen) ? {
                    id: dish.kitchen.id,
                    name: dish.kitchen.name,
                    avatarUrl: dish.kitchen.avatar_url,
                } : null,
                user: (dish === null || dish === void 0 ? void 0 : dish.creator) ? {
                    userId: dish.creator.id,
                    nickName: dish.creator.nick_name,
                    avatarUrl: dish.creator.avatar_url,
                } : null,
                creator: (dish === null || dish === void 0 ? void 0 : dish.creator) ? {
                    id: dish.creator.id,
                    nickName: dish.creator.nick_name,
                    avatarUrl: dish.creator.avatar_url,
                } : null,
                tabType: item.tab_type,
                isAdded: isAdded,
                addedCount: (dish === null || dish === void 0 ? void 0 : dish.sales) || 0, // 使用菜品销量作为"*人添加"数据
                createTime: item.created_at,
            };
        }).filter(item => item !== null);
        logger_1.default.info(`处理后的发现项数量: ${items.length}`);
        return (0, helpers_1.buildPaginationResult)(items, count, page, pageSize);
    }
    catch (error) {
        logger_1.default.error(`发现服务处理失败: ${error.message}`);
        throw error;
    }
});
/**
 * 添加到分类
 * @param userId 用户ID
 * @param dishId 菜品ID
 * @param targetKitchenId 目标厨房ID
 * @param targetCategoryId 目标分类ID
 * @returns 添加结果
 */
const addToDish = (userId, dishId, targetKitchenId, targetCategoryId) => __awaiter(void 0, void 0, void 0, function* () {
    // 调用菜品服务的添加到厨房方法
    return yield dishService_1.default.addToKitchen(userId, dishId, targetKitchenId, targetCategoryId);
});
/**
 * 获取发现页消息数量
 * @param userId 用户ID
 * @returns 消息数量
 */
const getMessageCount = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    // 获取各标签页未读数量
    const recommendCount = yield getUnreadCountByType(userId, 'recommend');
    const newestCount = yield getUnreadCountByType(userId, 'newest');
    const popularCount = yield getUnreadCountByType(userId, 'popular');
    // 获取总未读数量
    const totalCount = recommendCount + newestCount + popularCount;
    return {
        recommend: recommendCount,
        newest: newestCount,
        popular: popularCount,
        total: totalCount,
    };
});
/**
 * 获取指定类型的未读数量
 * @param userId 用户ID
 * @param type 标签类型
 * @returns 未读数量
 */
const getUnreadCountByType = (userId, type) => __awaiter(void 0, void 0, void 0, function* () {
    // 获取所有该类型的发现项
    const allItems = yield models_1.DiscoverItem.findAll({
        where: { tab_type: type },
        attributes: ['id'],
    });
    // 获取用户已读的该类型发现项
    const readItems = yield models_1.DiscoverItem.findAll({
        where: {
            user_id: userId,
            tab_type: type,
            is_read: true,
        },
        attributes: ['id'],
    });
    // 计算未读数量
    return allItems.length - readItems.length;
});
/**
 * 获取未读数量
 * @param userId 用户ID
 * @returns 未读数量
 */
const getUnreadCounts = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    // 导入消息服务
    const messageService = require('./messageService').default;
    // 获取真实的消息数量
    const messageCounts = yield messageService.getMessageCount(userId);
    return {
        likes: messageCounts.like + messageCounts.add, // 赞和添加合并
        system: messageCounts.system, // 系统消息
        comment: messageCounts.comment, // 评论消息
        total: messageCounts.total
    };
});
/**
 * 获取标签内容
 * @param userId 用户ID（可选）
 * @param type 标签类型
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 标签内容
 */
const getTabContent = (userId_1, ...args_1) => __awaiter(void 0, [userId_1, ...args_1], void 0, function* (userId, type = 'recommend', page = 1, pageSize = 10) {
    // 检查类型是否有效
    if (!['recommend', 'newest', 'popular'].includes(type)) {
        throw new error_1.BusinessError('无效的标签类型');
    }
    // 分页参数
    const { limit, offset } = (0, helpers_1.getPagination)(page, pageSize);
    // 查询发现项
    const { count, rows } = yield models_1.DiscoverItem.findAndCountAll({
        where: { tab_type: type },
        include: [
            {
                model: models_1.Dish,
                as: 'dish',
                include: [
                    {
                        model: models_1.Kitchen,
                        as: 'kitchen',
                        attributes: ['id', 'name', 'avatar_url'],
                    },
                    {
                        model: models_1.User,
                        as: 'creator',
                        attributes: ['id', 'nick_name', 'avatar_url'],
                    },
                    {
                        model: models_1.Ingredient,
                        as: 'ingredients',
                        attributes: ['id', 'name', 'amount'],
                        order: [['sort', 'ASC']],
                    },
                ],
            },
        ],
        order: [['sort_value', 'DESC']],
        limit,
        offset,
        distinct: true,
    });
    // 注释：移除了会导致数据重复的已读状态更新逻辑
    // 如果需要已读状态功能，应该在用户明确操作时单独处理
    // 构建返回结果
    const items = rows.map(item => {
        const dish = item.dish;
        // 从配料表生成标签，取前5个配料的名称
        const tags = (dish === null || dish === void 0 ? void 0 : dish.ingredients) ? dish.ingredients.slice(0, 5).map((ingredient) => ingredient.name) : [];
        return {
            id: item.id,
            dishId: dish === null || dish === void 0 ? void 0 : dish.id,
            name: dish === null || dish === void 0 ? void 0 : dish.name,
            image: dish === null || dish === void 0 ? void 0 : dish.image,
            price: dish === null || dish === void 0 ? void 0 : dish.price,
            originalPrice: dish === null || dish === void 0 ? void 0 : dish.original_price,
            description: dish === null || dish === void 0 ? void 0 : dish.description,
            tags: tags, // 使用配料表生成的标签而不是原有的tags字段
            sales: dish === null || dish === void 0 ? void 0 : dish.sales,
            rating: dish === null || dish === void 0 ? void 0 : dish.rating,
            kitchen: (dish === null || dish === void 0 ? void 0 : dish.kitchen) ? {
                id: dish.kitchen.id,
                name: dish.kitchen.name,
                avatarUrl: dish.kitchen.avatar_url,
            } : null,
            user: (dish === null || dish === void 0 ? void 0 : dish.creator) ? {
                userId: dish.creator.id,
                nickName: dish.creator.nick_name,
                avatarUrl: dish.creator.avatar_url,
            } : null,
            creator: (dish === null || dish === void 0 ? void 0 : dish.creator) ? {
                id: dish.creator.id,
                nickName: dish.creator.nick_name,
                avatarUrl: dish.creator.avatar_url,
            } : null,
            tabType: item.tab_type,
            isAdded: false,
            addedCount: (dish === null || dish === void 0 ? void 0 : dish.sales) || 0,
            createTime: item.created_at,
        };
    });
    return (0, helpers_1.buildPaginationResult)(items, count, page, pageSize);
});
/**
 * 添加发现项
 * @param dishId 菜品ID
 * @param kitchenId 厨房ID
 * @param tabType 标签类型
 * @param sortValue 排序值
 * @returns 添加结果
 */
const addDiscoverItem = (dishId, kitchenId, tabType, sortValue) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查类型是否有效
    if (!['recommend', 'newest', 'popular'].includes(tabType)) {
        throw new error_1.BusinessError('无效的标签类型');
    }
    // 检查菜品是否存在
    const dish = yield models_1.Dish.findOne({
        where: {
            id: dishId,
            kitchen_id: kitchenId,
        },
    });
    if (!dish) {
        throw new error_1.BusinessError('菜品不存在');
    }
    // 创建发现项
    const item = yield models_1.DiscoverItem.create({
        dish_id: dishId,
        kitchen_id: kitchenId,
        tab_type: tabType,
        sort_value: sortValue,
    });
    return {
        id: item.id,
        dishId: item.dish_id,
        kitchenId: item.kitchen_id,
        tabType: item.tab_type,
        sortValue: item.sort_value,
    };
});
exports.default = {
    getDiscoverList,
    addToDish,
    getMessageCount,
    getUnreadCounts,
    getTabContent,
    addDiscoverItem,
};
//# sourceMappingURL=discoverService.js.map