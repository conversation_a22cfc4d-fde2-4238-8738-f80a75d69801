"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 厨房成员模型
 * 存储厨房成员信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
// 厨房成员模型类
class KitchenMember extends sequelize_1.Model {
    // 检查是否是拥有者
    isOwner() {
        return this.role === 'owner';
    }
    // 检查是否是管理员
    isAdmin() {
        return this.role === 'admin' || this.role === 'owner';
    }
    // 检查是否有编辑权限
    canEdit() {
        return this.role === 'owner' || this.role === 'admin' || this.role === 'editor';
    }
}
// 初始化厨房成员模型
KitchenMember.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '成员ID',
    },
    kitchen_id: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '厨房ID',
        references: {
            model: 'kitchens',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    role: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'member',
        comment: '角色(owner:拥有者,admin:管理员,editor:编辑者,member:成员)',
        validate: {
            isIn: [['owner', 'admin', 'editor', 'member']],
        },
    },
    join_time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '加入时间',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'KitchenMember',
    tableName: 'kitchen_members',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_kitchen_user',
            unique: true,
            fields: ['kitchen_id', 'user_id'],
        },
        {
            name: 'idx_user_role',
            fields: ['user_id', 'role'],
        },
    ],
});
exports.default = KitchenMember;
//# sourceMappingURL=KitchenMember.js.map