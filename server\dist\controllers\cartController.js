"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cartService_1 = __importDefault(require("../services/cartService"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取购物车列表
 * @route GET /api/cart/list
 */
const getCartList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId } = req.query;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        const cartItems = yield cartService_1.default.getCartList(userId, kitchenId);
        (0, response_1.success)(res, { cartItems });
    }
    catch (err) {
        next(err);
    }
});
/**
 * 添加到购物车
 * @route POST /api/cart/add
 */
const addToCart = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { dishId, kitchenId, count } = req.body;
        if (!dishId || !kitchenId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield cartService_1.default.addToCart(userId, parseInt(dishId), kitchenId, count || 1);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 更新购物车项数量
 * @route POST /api/cart/updateCount
 */
const updateCartItemCount = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { dishId, count, kitchenId } = req.body;
        if (!dishId || count === undefined || !kitchenId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield cartService_1.default.updateCartItemCount(userId, parseInt(dishId), count, kitchenId);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 清空购物车
 * @route POST /api/cart/clear
 */
const clearCart = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { kitchenId } = req.body;
        if (!kitchenId) {
            throw new error_1.BusinessError('缺少参数: kitchenId', response_1.ResponseCode.VALIDATION);
        }
        yield cartService_1.default.clearCart(userId, kitchenId);
        (0, response_1.success)(res, { success: true });
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getCartList,
    addToCart,
    updateCartItemCount,
    clearCart
};
//# sourceMappingURL=cartController.js.map