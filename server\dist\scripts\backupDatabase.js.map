{"version": 3, "file": "backupDatabase.js", "sourceRoot": "", "sources": ["../../src/scripts/backupDatabase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;GAGG;AACH,iDAAqC;AACrC,4CAAoB;AACpB,gDAAwB;AACxB,8DAAsC;AACtC,6DAAqC;AAErC,OAAO;AACP,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AAEzD;;GAEG;AACH,SAAS,eAAe;IACtB,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/B,YAAE,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9C,gBAAM,CAAC,IAAI,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;IACvC,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,sBAAsB;IAC7B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAC1D,OAAO,UAAU,gBAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,MAAM,CAAC;AAC3D,CAAC;AAED;;;GAGG;AACH,SAAe,cAAc;;QAC3B,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1B,SAAS;QACT,eAAe,EAAE,CAAC;QAElB,UAAU;QACV,MAAM,cAAc,GAAG,sBAAsB,EAAE,CAAC;QAChD,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAE7D,kBAAkB;QAClB,MAAM,OAAO,GAAG,gBAAgB,gBAAM,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAM,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAM,CAAC,QAAQ,CAAC,IAAI,MAAM,gBAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,gBAAM,CAAC,QAAQ,CAAC,IAAI,OAAO,cAAc,GAAG,CAAC;QAEzL,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAA,oBAAI,EAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;gBACtC,IAAI,KAAK,EAAE,CAAC;oBACV,gBAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC1C,MAAM,CAAC,KAAK,CAAC,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,IAAI,MAAM,EAAE,CAAC;oBACX,gBAAM,CAAC,IAAI,CAAC,aAAa,MAAM,EAAE,CAAC,CAAC;gBACrC,CAAC;gBAED,gBAAM,CAAC,IAAI,CAAC,YAAY,cAAc,EAAE,CAAC,CAAC;gBAC1C,OAAO,CAAC,cAAc,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CAAA;AAED;;;GAGG;AACH,SAAS,iBAAiB,CAAC,aAAqB,CAAC;IAC/C,gBAAM,CAAC,IAAI,CAAC,iBAAiB,UAAU,SAAS,CAAC,CAAC;IAElD,WAAW;IACX,MAAM,WAAW,GAAG,YAAE,CAAC,WAAW,CAAC,UAAU,CAAC;SAC3C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACnE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACZ,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QACjC,IAAI,EAAE,YAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE;KAC/D,CAAC,CAAC;SACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;IAE9C,YAAY;IACZ,IAAI,WAAW,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;QACpC,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEpD,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,gBAAM,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,gBAAM,CAAC,IAAI,CAAC,OAAO,aAAa,CAAC,MAAM,SAAS,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACN,gBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAe,IAAI;;QACjB,IAAI,CAAC;YACH,QAAQ;YACR,MAAM,cAAc,EAAE,CAAC;YAEvB,UAAU;YACV,iBAAiB,EAAE,CAAC;YAEpB,gBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CAAA;AAED,kBAAkB;AAClB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC;AACT,CAAC;AAED,kBAAe,cAAc,CAAC"}