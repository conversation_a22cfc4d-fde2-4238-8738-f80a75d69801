import { request } from '@/utils/request'

// 管理员相关类型
export interface Admin {
  id: string
  username: string
  email: string
  name: string
  avatar?: string
  role: 'super_admin' | 'admin' | 'operator'
  permissions: string[]
  status: 'active' | 'inactive' | 'locked'
  lastLoginTime?: string
  lastLoginIp?: string
  createTime: string
  updateTime: string
  creatorId?: string
  creatorName?: string
}

export interface AdminRole {
  id: string
  name: string
  code: string
  description: string
  permissions: string[]
  isDefault: boolean
  createTime: string
  updateTime: string
}

export interface Permission {
  id: string
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  parentId?: string
  path?: string
  method?: string
  description: string
  children?: Permission[]
}

// 系统设置相关类型
export interface SystemSetting {
  key: string
  value: any
  type: 'boolean' | 'string' | 'number' | 'json'
  description: string
  label?: string
  rawValue?: string
}

// 系统配置相关类型
export interface SystemConfig {
  id: string
  category: string
  categoryName: string
  configKey: string
  configName: string
  configValue: string
  valueType: 'string' | 'number' | 'boolean' | 'json' | 'file'
  description: string
  isPublic: boolean
  isEditable: boolean
  sort: number
  createTime: string
  updateTime: string
}

export interface SystemInfo {
  version: string
  environment: 'development' | 'production' | 'test'
  database: {
    type: string
    version: string
    size: string
    status: 'connected' | 'disconnected'
  }
  server: {
    os: string
    nodeVersion: string
    memory: {
      total: string
      used: string
      free: string
    }
    cpu: {
      model: string
      cores: number
      usage: number
    }
    uptime: string
  }
  storage: {
    total: string
    used: string
    free: string
    usage: number
  }
}

// 操作日志相关类型
export interface OperationLog {
  id: string
  adminId: string
  adminName: string
  module: string
  operation: string
  description: string
  ip: string
  userAgent: string
  requestData?: any
  responseData?: any
  duration: number
  status: 'success' | 'error'
  errorMessage?: string
  createTime: string
}

// API参数类型
export interface AdminListParams {
  page?: number
  pageSize?: number
  keyword?: string
  role?: string
  status?: string
}

export interface AdminCreateData {
  username: string
  email: string
  name: string
  password: string
  role: string
  permissions?: string[]
}

export interface AdminUpdateData {
  email?: string
  name?: string
  avatar?: string
  role?: string
  permissions?: string[]
  status?: string
}

export interface SystemConfigListParams {
  page?: number
  pageSize?: number
  category?: string
  keyword?: string
}

export interface OperationLogListParams {
  page?: number
  pageSize?: number
  adminId?: string
  module?: string
  operation?: string
  status?: string
  startDate?: string
  endDate?: string
}

// 管理员管理API
export const getAdminList = async (params: AdminListParams): Promise<{
  admins: Admin[]
  total: number
  page: number
  pageSize: number
}> => {
  return request.get('/admin/admins', { params })
}

export const getAdmin = async (adminId: string): Promise<Admin> => {
  return request.get(`/admin/admins/${adminId}`)
}

export const createAdmin = async (data: AdminCreateData): Promise<Admin> => {
  return request.post('/admin/admins', data)
}

export const updateAdmin = async (adminId: string, data: AdminUpdateData): Promise<Admin> => {
  return request.put(`/admin/admins/${adminId}`, data)
}

export const deleteAdmin = async (adminId: string): Promise<void> => {
  return request.delete(`/admin/admins/${adminId}`)
}

export const updateAdminStatus = async (adminId: string, status: 'active' | 'inactive' | 'locked'): Promise<void> => {
  return request.post(`/admin/admins/${adminId}/status`, { status })
}

export const resetAdminPassword = async (adminId: string, newPassword: string): Promise<void> => {
  return request.post(`/admin/admins/${adminId}/reset-password`, { newPassword })
}

// 角色权限管理API
export const getAdminRoles = async (): Promise<AdminRole[]> => {
  return request.get('/admin/roles')
}

export const getPermissions = async (): Promise<Permission[]> => {
  return request.get('/admin/permissions')
}

export const createAdminRole = async (data: Partial<AdminRole>): Promise<AdminRole> => {
  return request.post('/admin/roles', data)
}

export const updateAdminRole = async (roleId: string, data: Partial<AdminRole>): Promise<AdminRole> => {
  return request.put(`/admin/roles/${roleId}`, data)
}

export const deleteAdminRole = async (roleId: string): Promise<void> => {
  return request.delete(`/admin/roles/${roleId}`)
}

// 系统配置API
export const getSystemConfigList = async (params: SystemConfigListParams): Promise<{
  configs: SystemConfig[]
  total: number
  page: number
  pageSize: number
}> => {
  return request.get('/admin/system/config', { params })
}

export const getSystemConfig = async (configId: string): Promise<SystemConfig> => {
  return request.get(`/admin/system/config/${configId}`)
}

export const updateSystemConfig = async (configId: string, data: { configValue: string }): Promise<SystemConfig> => {
  return request.put(`/admin/system/config/${configId}`, data)
}

export const getSystemInfo = async (): Promise<SystemInfo> => {
  return request.get('/admin/system/info')
}

export const clearSystemCache = async (cacheType?: string): Promise<void> => {
  return request.post('/admin/system/clear-cache', { cacheType })
}

export const backupDatabase = async (): Promise<{ downloadUrl: string }> => {
  return request.post('/admin/system/backup')
}

export const restoreDatabase = async (backupFile: string): Promise<void> => {
  return request.post('/admin/system/restore', { backupFile })
}

// 操作日志API
export const getOperationLogs = async (params: OperationLogListParams): Promise<{
  logs: OperationLog[]
  total: number
  page: number
  pageSize: number
}> => {
  return request.get('/admin/system/operation-logs', { params })
}

export const getOperationLog = async (logId: string): Promise<OperationLog> => {
  return request.get(`/admin/system/operation-logs/${logId}`)
}

export const clearOperationLogs = async (beforeDate: string): Promise<void> => {
  return request.post('/admin/system/operation-logs/clear', { beforeDate })
}

// 系统设置管理API
export const getSystemSettings = async (): Promise<{ settings: SystemSetting[] }> => {
  return request.get('/system/settings')
}

export const getSystemSetting = async (key: string): Promise<SystemSetting> => {
  return request.get(`/system/setting/${key}`)
}

export const updateSystemSetting = async (key: string, value: any, type: string = 'boolean', description?: string): Promise<void> => {
  return request.post('/system/setting', { key, value, type, description })
}

export const getCommentEnabled = async (): Promise<{ enabled: boolean }> => {
  return request.get('/system/comment-enabled')
}