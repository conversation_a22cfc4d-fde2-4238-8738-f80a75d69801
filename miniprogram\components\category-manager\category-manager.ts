// 分类管理组件
import { checkSensitiveWord, hasSensitiveWord } from '../../utils/sensitiveWordChecker';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    categories: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    },
    ready() {
      // 组件完全准备好
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isAddingCategory: false,
    editCategory: {
      id: '',
      name: '',
      icon: '',
      sort: 0
    },
    icons: [
      '/static/images/icons/class/bento.png',
      '/static/images/icons/class/chicken.png', 
      '/static/images/icons/class/choy.png',
      '/static/images/icons/class/soup.png',
      '/static/images/icons/class/bowl.png',
      '/static/images/icons/class/cola.png',
      '/static/images/icons/class/salad.png',
      '/static/images/icons/class/seafood.png',
      '/static/images/icons/class/cream.png',
      '/static/images/icons/class/hamburger.png',
      '/static/images/icons/class/food.png',
      '/static/images/icons/class/prawn.png',
      '/static/images/icons/class/paella.png',
      '/static/images/icons/class/noodles.png',
      '/static/images/icons/class/toast.png',
      '/static/images/icons/class/dumplings.png',
      '/static/images/icons/class/crab.png',
      '/static/images/icons/class/cheesecake.png',
      '/static/images/icons/class/french.png',
      '/static/images/icons/class/bread.png',
      '/static/images/icons/class/flour.png',
      '/static/images/icons/class/cheese.png',
      '/static/images/icons/class/mulled.png',
      '/static/images/icons/class/tapas.png',
      '/static/images/icons/class/grapes.png',
      '/static/images/icons/class/lamb.png',
      '/static/images/icons/class/pancake.png',
      '/static/images/icons/class/strawberry.png',
      '/static/images/icons/class/watermelon.png',
      '/static/images/icons/class/orange.png',
      '/static/images/icons/class/banana.png',
      '/static/images/icons/class/cherry.png',
      '/static/images/icons/class/kiwi.png',
      '/static/images/icons/class/tomato.png',
      '/static/images/icons/class/carrot.png',
      '/static/images/icons/class/eggplant.png',
      '/static/images/icons/class/potato.png',
      '/static/images/icons/class/pumpkin.png',
      '/static/images/icons/class/paprika.png',
      '/static/images/icons/class/sabzeh.png'
    ],
    editingIndex: -1, // 当前编辑的分类索引，-1表示新增
    currentDragId: '', // 当前拖动的分类ID
    startY: 0, // 拖动开始的Y坐标
    currentIndex: -1, // 当前拖动的分类索引
    moveToIndex: -1, // 移动到的目标索引
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 阻止滑动穿透
    preventTouchMove(): boolean {
      return false;
    },
    // 关闭弹窗
    closeModal(): void {
      // 使用setTimeout确保在下一个事件循环中关闭弹窗，避免滚动条引起的布局问题
      setTimeout(() => {
        this.triggerEvent('close');
      }, 50);
    },

    // 显示新增分类表单
    showAddCategoryForm(): void {
      this.setData({
        isAddingCategory: true,
        editingIndex: -1,
        editCategory: {
          id: '',
          name: '',
          icon: this.data.icons[0],
          sort: this.data.categories.length
        }
      });
    },

    // 取消新增分类
    cancelAddCategory(): void {
      this.setData({
        isAddingCategory: false
      });
    },

    // 编辑分类
    editCategory(e: WechatMiniprogram.TouchEvent): void {
      const { index } = e.currentTarget.dataset;
      const category = this.data.categories[index];

      this.setData({
        isAddingCategory: true,
        editingIndex: index,
        editCategory: {
          id: category.id,
          name: category.name,
          icon: category.icon,
          sort: category.sort || index
        }
      });
    },

    // 删除分类
    deleteCategory(e: WechatMiniprogram.TouchEvent): void {
      const { index } = e.currentTarget.dataset;
      const category = this.data.categories[index];

      wx.showModal({
        title: '确认删除',
        content: `确定要删除"${category.name}"分类吗？该分类下的菜品将被隐藏。`,
        confirmColor: '#FF6B35',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', { index, category });
          }
        }
      });
    },

    // 分类名称输入
    onNameInput(e: WechatMiniprogram.Input): void {
      const value = e.detail.value;
      
      this.setData({
        'editCategory.name': value
      });

      // 实时敏感词检查
      if (value && value.length > 1) {
        const checkResult = checkSensitiveWord(value, 'content');
        if (checkResult.hasSensitiveWord) {
          console.warn('分类名称包含敏感词:', checkResult.sensitiveWords);
        }
      }
    },

    // 选择图标
    selectIcon(e: WechatMiniprogram.TouchEvent): void {
      const { icon } = e.currentTarget.dataset;
      this.setData({
        'editCategory.icon': icon
      });
    },

    // 保存分类
    saveCategory(): void {
      const { editCategory, editingIndex } = this.data;

      if (!editCategory.name || !editCategory.name.trim()) {
        wx.showToast({
          title: '请输入分类名称',
          icon: 'none'
        });
        return;
      }

      // 字数限制检查
      if (editCategory.name.trim().length > 20) {
        wx.showToast({
          title: '分类名称不能超过20个字符',
          icon: 'none'
        });
        return;
      }

      // 敏感词检查
      const nameCheck = checkSensitiveWord(editCategory.name.trim(), 'content');
      if (nameCheck.hasSensitiveWord) {
        wx.showModal({
          title: '内容审核',
          content: '分类名称包含不当内容，请重新输入',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF6B35'
        });
        return;
      }

      // 编辑模式
      if (editingIndex !== -1) {
        this.triggerEvent('update', { index: editingIndex, category: editCategory });
      } else {
        // 新增模式
        this.triggerEvent('add', { category: editCategory });
      }

      // 重置并返回分类列表
      this.setData({
        isAddingCategory: false
      });
    },

    // 触摸开始，记录开始位置
    onTouchStart(e: WechatMiniprogram.TouchEvent): void {
      // 确保事件来自拖动按钮
      const { index, id } = e.currentTarget.dataset;
      const startY = e.touches[0].clientY;

      this.setData({
        currentDragId: id,
        startY,
        currentIndex: index
      });
    },

    // 触摸移动，判断移动位置
    onTouchMove(e: WechatMiniprogram.TouchEvent): void {
      const { currentIndex, currentDragId } = this.data;
      if (!currentDragId) return;

      // 获取当前Y坐标
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.data.startY;

      // 获取所有分类项的高度
      const itemHeight = 90; // 约90rpx

      // 计算应该移动到哪个索引
      const moveOffset = Math.round(deltaY / itemHeight);
      let moveToIndex = currentIndex + moveOffset;

      // 限制索引范围
      moveToIndex = Math.max(0, Math.min(this.data.categories.length - 1, moveToIndex));

      // 如果位置有变化，更新UI
      if (moveToIndex !== this.data.moveToIndex) {
        this.setData({
          moveToIndex
        });
      }
    },

    // 触摸结束，执行排序
    onTouchEnd(e: WechatMiniprogram.TouchEvent): void {
      const { currentIndex, moveToIndex, currentDragId } = this.data;

      // 如果没有拖动或目标位置相同，不做处理
      if (!currentDragId || currentIndex === moveToIndex || moveToIndex === -1) {
        this.setData({
          currentDragId: '',
          startY: 0,
          currentIndex: -1,
          moveToIndex: -1
        });
        return;
      }

      // 获取当前分类列表的副本
      const categories = [...this.properties.categories];

      // 执行排序，将当前分类移动到目标位置
      const [movedItem] = categories.splice(currentIndex, 1);
      categories.splice(moveToIndex, 0, movedItem);

      // 更新排序值
      const sortedCategories = categories.map((item, index) => ({
        ...item,
        sort: index
      }));

      // 通知父组件更新排序
      this.triggerEvent('sort', { categories: sortedCategories });

      // 重置拖动状态
      this.setData({
        currentDragId: '',
        startY: 0,
        currentIndex: -1,
        moveToIndex: -1
      });
    }
  }
})