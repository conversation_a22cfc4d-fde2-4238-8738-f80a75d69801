'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('payment_orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '支付订单ID',
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      out_trade_no: {
        type: Sequelize.STRING(32),
        allowNull: false,
        unique: true,
        comment: '商户订单号',
      },
      transaction_id: {
        type: Sequelize.STRING(32),
        allowNull: true,
        comment: '微信支付订单号',
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: '支付金额（元）',
      },
      coins: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '对应大米数量',
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '订单描述',
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'pending',
        comment: '订单状态(pending:待支付,paid:已支付,failed:支付失败,cancelled:已取消)',
      },
      payment_method: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'wechat',
        comment: '支付方式',
      },
      prepay_id: {
        type: Sequelize.STRING(64),
        allowNull: true,
        comment: '微信预支付ID',
      },
      paid_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '支付时间',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '创建时间',
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '更新时间',
      },
    });

    // 创建索引
    await queryInterface.addIndex('payment_orders', ['user_id'], {
      name: 'idx_payment_orders_user_id'
    });
    
    await queryInterface.addIndex('payment_orders', ['out_trade_no'], {
      name: 'idx_payment_orders_out_trade_no',
      unique: true
    });
    
    await queryInterface.addIndex('payment_orders', ['transaction_id'], {
      name: 'idx_payment_orders_transaction_id'
    });
    
    await queryInterface.addIndex('payment_orders', ['status'], {
      name: 'idx_payment_orders_status'
    });
    
    await queryInterface.addIndex('payment_orders', ['created_at'], {
      name: 'idx_payment_orders_created_at'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('payment_orders');
  }
};
