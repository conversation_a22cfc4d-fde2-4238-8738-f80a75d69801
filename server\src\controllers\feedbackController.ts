/**
 * 反馈控制器
 * 处理用户反馈相关的API请求
 */
import { Request, Response } from 'express';
import { success, validationError, error, notFound, ResponseCode } from '../utils/response';
import logger from '../utils/logger';
import Feedback from '../models/Feedback';
import User from '../models/User';
import { Op } from 'sequelize';
import path from 'path';
import config from '../config/config';

// 辅助函数
const badRequest = (res: Response, message: string = '请求参数错误') => {
  return validationError(res, message);
};

const serverError = (res: Response, message: string = '服务器内部错误') => {
  return error(res, ResponseCode.SERVER_ERROR, message);
};

/**
 * 提交反馈
 */
export const submitFeedback = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, description, images, deviceInfo } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!type || !description) {
      logger.warn('提交反馈缺少必填字段', { userId, type, description: description?.length });
      return badRequest(res, '反馈类型和问题描述为必填项');
    }

    // 验证反馈类型
    if (!['bug', 'feature', 'other'].includes(type)) {
      logger.warn('无效的反馈类型', { userId, type });
      return badRequest(res, '无效的反馈类型');
    }

    // 验证描述长度
    if (description.length > 200) {
      logger.warn('反馈描述过长', { userId, length: description.length });
      return badRequest(res, '问题描述不能超过200字符');
    }

    // 处理图片数组
    let imagesJson: string | undefined = undefined;
    if (images && Array.isArray(images) && images.length > 0) {
      if (images.length > 3) {
        logger.warn('上传图片过多', { userId, count: images.length });
        return badRequest(res, '最多只能上传3张图片');
      }
      imagesJson = JSON.stringify(images);
    }

    // 处理设备信息
    let deviceInfoJson: string | undefined = undefined;
    if (deviceInfo && typeof deviceInfo === 'object') {
      deviceInfoJson = JSON.stringify(deviceInfo);
    }

    // 创建反馈记录
    const feedback = await Feedback.create({
      user_id: userId,
      type,
      description: description.trim(),
      images: imagesJson,
      device_info: deviceInfoJson,
      status: 'pending'
    });

    logger.info('用户提交反馈成功', { 
      feedbackId: feedback.id, 
      userId, 
      type 
    });

    success(res, {
      feedbackId: feedback.id,
      status: feedback.status,
      submitTime: feedback.created_at
    }, '反馈提交成功，我们会尽快处理');

  } catch (error) {
    logger.error('提交反馈失败', error);
    serverError(res, '提交反馈失败，请稍后再试');
  }
};

/**
 * 上传反馈图片
 */
export const uploadImage = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.file) {
      logger.warn('上传反馈图片时未提供文件');
      return badRequest(res, '请选择要上传的图片');
    }

    // 构建图片URL
    const imageUrl = `${config.server.baseUrl}/uploads/feedback/${req.file.filename}`;

    logger.info('反馈图片上传成功', { 
      userId: req.user.id,
      filename: req.file.filename,
      size: req.file.size 
    });

    success(res, {
      imageUrl
    }, '图片上传成功');

  } catch (error) {
    logger.error('上传反馈图片失败', error);
    serverError(res, '图片上传失败，请稍后再试');
  }
};

/**
 * 获取反馈列表（管理员用）
 */
export const getFeedbackList = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, pageSize = 10, type, status } = req.body;
    
    // 构建查询条件
    const where: any = {};
    if (type && ['bug', 'feature', 'other'].includes(type)) {
      where.type = type;
    }
    if (status && ['pending', 'processing', 'completed', 'rejected'].includes(status)) {
      where.status = status;
    }

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 查询反馈列表
    const { rows: feedbacks, count: total } = await Feedback.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nick_name', 'avatar_url']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(pageSize),
      offset: offset
    });

    // 格式化返回数据
    const list = feedbacks.map(feedback => ({
      id: feedback.id,
      type: feedback.type,
      description: feedback.description,
      images: feedback.images ? JSON.parse(feedback.images) : [],
      status: feedback.status,
      submitTime: feedback.created_at,
      userId: feedback.user_id,
      userNickname: feedback.user?.nick_name || '未知用户',
      reply: feedback.reply,
      replyTime: feedback.reply_at,
      deviceInfo: feedback.device_info ? JSON.parse(feedback.device_info) : null
    }));

    logger.info('管理员获取反馈列表', { 
      adminId: req.user.id,
      total,
      page,
      pageSize,
      type,
      status 
    });

    success(res, {
      list,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    logger.error('获取反馈列表失败', error);
    serverError(res, '获取反馈列表失败，请稍后再试');
  }
};

/**
 * 更新反馈状态（管理员用）
 */
export const updateFeedbackStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { feedbackId, status, reply } = req.body;

    // 验证必填字段
    if (!feedbackId || !status) {
      logger.warn('更新反馈状态缺少必填字段', { feedbackId, status });
      return badRequest(res, '反馈ID和状态为必填项');
    }

    // 验证状态值
    if (!['pending', 'processing', 'completed', 'rejected'].includes(status)) {
      logger.warn('无效的反馈状态', { feedbackId, status });
      return badRequest(res, '无效的反馈状态');
    }

    // 查找反馈记录
    const feedback = await Feedback.findByPk(feedbackId);
    if (!feedback) {
      logger.warn('反馈记录不存在', { feedbackId });
      return notFound(res, '反馈记录不存在');
    }

    // 更新数据
    const updateData: any = {
      status,
      updated_at: new Date()
    };

    if (reply && reply.trim()) {
      updateData.reply = reply.trim();
      updateData.reply_at = new Date();
    }

    // 执行更新
    await feedback.update(updateData);

    logger.info('管理员更新反馈状态', { 
      adminId: req.user.id,
      feedbackId,
      oldStatus: feedback.status,
      newStatus: status,
      hasReply: !!reply 
    });

    success(res, {
      feedbackId,
      status,
      reply: updateData.reply,
      updateTime: updateData.updated_at
    }, '状态更新成功');

  } catch (error) {
    logger.error('更新反馈状态失败', error);
    serverError(res, '更新反馈状态失败，请稍后再试');
  }
}; 