"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateFeedbackStatus = exports.getFeedbackList = exports.uploadImage = exports.submitFeedback = void 0;
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../utils/logger"));
const Feedback_1 = __importDefault(require("../models/Feedback"));
const User_1 = __importDefault(require("../models/User"));
const config_1 = __importDefault(require("../config/config"));
// 辅助函数
const badRequest = (res, message = '请求参数错误') => {
    return (0, response_1.validationError)(res, message);
};
const serverError = (res, message = '服务器内部错误') => {
    return (0, response_1.error)(res, response_1.ResponseCode.SERVER_ERROR, message);
};
/**
 * 提交反馈
 */
const submitFeedback = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { type, description, images, deviceInfo } = req.body;
        const userId = req.user.id;
        // 验证必填字段
        if (!type || !description) {
            logger_1.default.warn('提交反馈缺少必填字段', { userId, type, description: description === null || description === void 0 ? void 0 : description.length });
            return badRequest(res, '反馈类型和问题描述为必填项');
        }
        // 验证反馈类型
        if (!['bug', 'feature', 'other'].includes(type)) {
            logger_1.default.warn('无效的反馈类型', { userId, type });
            return badRequest(res, '无效的反馈类型');
        }
        // 验证描述长度
        if (description.length > 200) {
            logger_1.default.warn('反馈描述过长', { userId, length: description.length });
            return badRequest(res, '问题描述不能超过200字符');
        }
        // 处理图片数组
        let imagesJson = undefined;
        if (images && Array.isArray(images) && images.length > 0) {
            if (images.length > 3) {
                logger_1.default.warn('上传图片过多', { userId, count: images.length });
                return badRequest(res, '最多只能上传3张图片');
            }
            imagesJson = JSON.stringify(images);
        }
        // 处理设备信息
        let deviceInfoJson = undefined;
        if (deviceInfo && typeof deviceInfo === 'object') {
            deviceInfoJson = JSON.stringify(deviceInfo);
        }
        // 创建反馈记录
        const feedback = yield Feedback_1.default.create({
            user_id: userId,
            type,
            description: description.trim(),
            images: imagesJson,
            device_info: deviceInfoJson,
            status: 'pending'
        });
        logger_1.default.info('用户提交反馈成功', {
            feedbackId: feedback.id,
            userId,
            type
        });
        (0, response_1.success)(res, {
            feedbackId: feedback.id,
            status: feedback.status,
            submitTime: feedback.created_at
        }, '反馈提交成功，我们会尽快处理');
    }
    catch (error) {
        logger_1.default.error('提交反馈失败', error);
        serverError(res, '提交反馈失败，请稍后再试');
    }
});
exports.submitFeedback = submitFeedback;
/**
 * 上传反馈图片
 */
const uploadImage = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.file) {
            logger_1.default.warn('上传反馈图片时未提供文件');
            return badRequest(res, '请选择要上传的图片');
        }
        // 构建图片URL
        const imageUrl = `${config_1.default.server.baseUrl}/uploads/feedback/${req.file.filename}`;
        logger_1.default.info('反馈图片上传成功', {
            userId: req.user.id,
            filename: req.file.filename,
            size: req.file.size
        });
        (0, response_1.success)(res, {
            imageUrl
        }, '图片上传成功');
    }
    catch (error) {
        logger_1.default.error('上传反馈图片失败', error);
        serverError(res, '图片上传失败，请稍后再试');
    }
});
exports.uploadImage = uploadImage;
/**
 * 获取反馈列表（管理员用）
 */
const getFeedbackList = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, pageSize = 10, type, status } = req.body;
        // 构建查询条件
        const where = {};
        if (type && ['bug', 'feature', 'other'].includes(type)) {
            where.type = type;
        }
        if (status && ['pending', 'processing', 'completed', 'rejected'].includes(status)) {
            where.status = status;
        }
        // 计算偏移量
        const offset = (page - 1) * pageSize;
        // 查询反馈列表
        const { rows: feedbacks, count: total } = yield Feedback_1.default.findAndCountAll({
            where,
            include: [
                {
                    model: User_1.default,
                    as: 'user',
                    attributes: ['id', 'nick_name', 'avatar_url']
                }
            ],
            order: [['created_at', 'DESC']],
            limit: parseInt(pageSize),
            offset: offset
        });
        // 格式化返回数据
        const list = feedbacks.map(feedback => {
            var _a;
            return ({
                id: feedback.id,
                type: feedback.type,
                description: feedback.description,
                images: feedback.images ? JSON.parse(feedback.images) : [],
                status: feedback.status,
                submitTime: feedback.created_at,
                userId: feedback.user_id,
                userNickname: ((_a = feedback.user) === null || _a === void 0 ? void 0 : _a.nick_name) || '未知用户',
                reply: feedback.reply,
                replyTime: feedback.reply_at,
                deviceInfo: feedback.device_info ? JSON.parse(feedback.device_info) : null
            });
        });
        logger_1.default.info('管理员获取反馈列表', {
            adminId: req.user.id,
            total,
            page,
            pageSize,
            type,
            status
        });
        (0, response_1.success)(res, {
            list,
            total,
            page: parseInt(page),
            pageSize: parseInt(pageSize)
        });
    }
    catch (error) {
        logger_1.default.error('获取反馈列表失败', error);
        serverError(res, '获取反馈列表失败，请稍后再试');
    }
});
exports.getFeedbackList = getFeedbackList;
/**
 * 更新反馈状态（管理员用）
 */
const updateFeedbackStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { feedbackId, status, reply } = req.body;
        // 验证必填字段
        if (!feedbackId || !status) {
            logger_1.default.warn('更新反馈状态缺少必填字段', { feedbackId, status });
            return badRequest(res, '反馈ID和状态为必填项');
        }
        // 验证状态值
        if (!['pending', 'processing', 'completed', 'rejected'].includes(status)) {
            logger_1.default.warn('无效的反馈状态', { feedbackId, status });
            return badRequest(res, '无效的反馈状态');
        }
        // 查找反馈记录
        const feedback = yield Feedback_1.default.findByPk(feedbackId);
        if (!feedback) {
            logger_1.default.warn('反馈记录不存在', { feedbackId });
            return (0, response_1.notFound)(res, '反馈记录不存在');
        }
        // 更新数据
        const updateData = {
            status,
            updated_at: new Date()
        };
        if (reply && reply.trim()) {
            updateData.reply = reply.trim();
            updateData.reply_at = new Date();
        }
        // 执行更新
        yield feedback.update(updateData);
        logger_1.default.info('管理员更新反馈状态', {
            adminId: req.user.id,
            feedbackId,
            oldStatus: feedback.status,
            newStatus: status,
            hasReply: !!reply
        });
        (0, response_1.success)(res, {
            feedbackId,
            status,
            reply: updateData.reply,
            updateTime: updateData.updated_at
        }, '状态更新成功');
    }
    catch (error) {
        logger_1.default.error('更新反馈状态失败', error);
        serverError(res, '更新反馈状态失败，请稍后再试');
    }
});
exports.updateFeedbackStatus = updateFeedbackStatus;
//# sourceMappingURL=feedbackController.js.map