/**
 * 订单控制器
 * 处理订单相关的请求
 */
import { Request, Response, NextFunction } from 'express';
import orderService from '../services/orderService';
import logger from '../utils/logger';
import { success, error, ResponseCode } from '../utils/response';
import { BusinessError } from '../middlewares/error';
import validator from '../utils/validator';

/**
 * 提交订单
 * @route POST /api/order/submit
 */
const submitOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { kitchenId, tableNo, remark } = req.body;

    // 验证厨房ID
    if (!kitchenId) {
      throw new BusinessError('缺少参数: kitchenId', ResponseCode.VALIDATION);
    }
    validator.validateId(kitchenId, '厨房ID');

    // 验证桌号（非必选，允许为空或"未选择"）
    let validatedTableNo = '未选择';
    if (tableNo && tableNo !== '未选择' && tableNo.trim() !== '') {
      validator.validateStringLength(tableNo, 1, 20, '桌号');
      validatedTableNo = tableNo;
    }

    // 验证备注（非必选，允许为空）
    let validatedRemark = '';
    if (remark && remark.trim() !== '') {
      validator.validateStringLength(remark, 1, 200, '备注');
      // 过滤XSS
      validatedRemark = validator.sanitizeInput(remark);
    }

    const result = await orderService.submitOrder(userId, kitchenId, validatedTableNo, validatedRemark);
    success(res, result);
  } catch (err) {
    next(err);
  }
};

/**
 * 获取订单列表
 * @route GET /api/order/list
 */
const getOrderList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { kitchenId, status, page = 1, pageSize = 10 } = req.query;

    // 验证厨房ID（如果提供）
    if (kitchenId) {
      validator.validateId(kitchenId as string, '厨房ID');
    }

    // 验证订单状态（如果提供）
    if (status && status !== 'all') {
      validator.validateEnum(
        status as string,
        ['pending', 'accepted', 'cooking', 'completed', 'cancelled'],
        '订单状态'
      );
    }

    // 验证分页参数
    const pageNum = parseInt(page as string);
    const pageSizeNum = parseInt(pageSize as string);

    validator.validateNumberRange(pageNum, 1, 1000, '页码');
    validator.validateNumberRange(pageSizeNum, 1, 100, '每页数量');

    const orders = await orderService.getOrderList(
      userId,
      kitchenId as string,
      status as string,
      pageNum,
      pageSizeNum
    );

    success(res, orders);
  } catch (err) {
    next(err);
  }
};

/**
 * 获取订单详情
 * @route GET /api/order/detail
 */
const getOrderDetail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.query;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    const order = await orderService.getOrderDetail(userId, id as string);
    success(res, order);
  } catch (err) {
    next(err);
  }
};

/**
 * 获取订单详情（公开访问，用于分享）
 * @route GET /api/order/public/:id
 */
const getPublicOrderDetail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    const order = await orderService.getPublicOrderDetail(id);
    success(res, order);
  } catch (err) {
    next(err);
  }
};

/**
 * 取消订单
 * @route POST /api/order/cancel
 */
const cancelOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.cancelOrder(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 拒绝订单
 * @route POST /api/order/reject
 */
const rejectOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.rejectOrder(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 接单
 * @route POST /api/order/accept
 */
const acceptOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.acceptOrder(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 开始烹饪
 * @route POST /api/order/startCooking
 */
const startCooking = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.startCooking(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 完成订单
 * @route POST /api/order/complete
 */
const completeOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.completeOrder(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 取消烹饪
 * @route POST /api/order/cancelCooking
 */
const cancelCooking = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.cancelCooking(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * 取消已接单订单
 * @route POST /api/order/cancelAcceptedOrder
 */
const cancelAcceptedOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user.id;
    const { id } = req.body;

    if (!id) {
      throw new BusinessError('缺少参数: id', ResponseCode.VALIDATION);
    }

    await orderService.cancelAcceptedOrder(userId, id);
    success(res, { success: true });
  } catch (err) {
    next(err);
  }
};

export {
  submitOrder,
  getOrderList,
  getOrderDetail,
  getPublicOrderDetail,
  cancelOrder,
  rejectOrder,
  acceptOrder,
  startCooking,
  completeOrder,
  cancelCooking,
  cancelAcceptedOrder,
};
