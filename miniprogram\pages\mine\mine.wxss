/* 我的页面样式 */
.mine-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

/* 顶部背景区域 */
.user-info-area {
  position: relative;
  height: 400rpx;
  width: 100%;
  background-color: transparent;
  color: var(--text-color, #333333); /* 使用CSS变量控制文字颜色 */
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
  z-index: 0;
  background: linear-gradient(135deg, #F8F9FA, #E2F3FF);
}

/* 顶部标题区域 */
.top-title {
  position: absolute;
  z-index: 1;
  left: 30rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 60rpx;
  box-sizing: border-box;
  color: inherit; /* 继承父元素颜色 */
  /* 由内联样式控制top */
}

.page-title {
  font-size: 34rpx;
  font-weight: 600;
  line-height: 1;
}

.user-info {
  position: absolute;
  z-index: 1;
  display: flex;
  padding: 0 30rpx;
  align-items: center;
  top: 225rpx;
  left: 0;
  right: 0;
}

.restaurant-avatar-area {
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.avatar:active {
  transform: scale(0.95);
}

.edit-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.user-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: block;
  text-shadow: none;
}

.user-id-row {
  display: flex;
  align-items: center;
}

.user-id {
  font-size: 24rpx;
  opacity: 0.8;
  margin-right: 10rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  padding: 4rpx;
  transition: all 0.2s ease;
}

.copy-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.copy-icon {
  font-size: 24rpx;
  width: 24rpx;
  height: 24rpx;
  object-fit: contain;
}

/* 登录按钮 */
.login-btn-container {
  display: flex;
  align-items: center;
  margin-left: -300rpx;
}

.login-btn {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  color: inherit; /* 继承父元素颜色 */
  font-size: 28rpx;
  padding: 8rpx 0;
  border-radius: 40rpx;
  width: 160rpx !important;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  height: 50rpx;
  min-height: auto;
  transition: all 0.2s ease-out;
}

.login-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 功能区域 */
.function-area {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -30rpx;
  position: relative;
  z-index: 2;
  padding: 20rpx 30rpx 30rpx;
}

/* 功能卡片 */
.function-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
}

.card-more {
  display: flex;
  align-items: center;
  color: #999999;
}

.more-text {
  font-size: 26rpx;
}

.more-icon {
  font-size: 26rpx;
  margin-left: 6rpx;
}

/* 订单类型 */
.order-types {
  display: flex;
  justify-content: space-between;
}

.order-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.type-icon {
  font-size: 50rpx;
  margin-bottom: 10rpx;
}

.type-name {
  font-size: 24rpx;
  color: #666666;
}

/* 功能列表 */
.feature-list {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-top: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
  transition: background-color 0.3s ease;
}

.feature-item:active {
  background-color: #F5F5F5;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 42rpx;
  margin-right: 20rpx;
  width: 45rpx;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-name {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  font-weight: 400;
}

.feature-arrow {
  font-size: 28rpx;
  color: #999999;
}

/* 通用阴影效果 */
.card-shadow {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 用户数据统计 */
.user-stats {
  background-color: #FFFFFF;
  padding: 0;
  margin-bottom: 15rpx;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
}

.stats-left {
  display: flex;
  flex: 1;
  justify-content: flex-start;
  padding-left: 10rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  margin-right: 35rpx;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.coins-value {
  color: #FF9966;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

.stats-right {
  display: flex;
  align-items: center;
  margin-right: 10rpx;
}

.edit-profile-btn {
  background-color: #F5F5F5;
  color: #666666;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  border: none;
  line-height: 1.4;
  transition: all 0.2s ease;
  width: auto !important;
  min-width: 120rpx;
  margin: 0;
  min-height: auto;
}

.edit-profile-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 会员按钮样式 */
.member-button-container {
  margin: 0 10rpx 15rpx;
  position: relative;
}

.member-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #FFF9F5, #FFF0E5);
  padding: 24rpx 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.1);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.member-button::before {
  content: "";
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(255, 230, 210, 0.7), rgba(255, 150, 102, 0.2));
  border-radius: 50%;
  z-index: -1;
}

.member-button::after {
  content: "";
  position: absolute;
  bottom: -30rpx;
  left: 30%;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(255, 215, 185, 0.5), rgba(255, 180, 140, 0.1));
  border-radius: 50%;
  z-index: -1;
}

.member-button.is-member {
  background: linear-gradient(135deg, #FFF5E0, #FFEAD0);
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.1);
}

.member-button.is-member::before {
  background: linear-gradient(135deg, rgba(255, 236, 179, 0.7), rgba(255, 193, 7, 0.2));
}

.member-button.is-member::after {
  background: linear-gradient(135deg, rgba(255, 226, 162, 0.5), rgba(255, 213, 79, 0.1));
}

.member-button:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.member-icon {
  font-size: 44rpx;
  margin-right: 16rpx;
  position: relative;
  z-index: 2;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-icon image {
  width: 44rpx;
  height: 44rpx;
  object-fit: contain;
}

.member-arrow {
  font-size: 28rpx;
  color: #FF6B35;
  margin-left: 16rpx;
  background-color: rgba(255, 255, 255, 0.8);
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

/* 快捷功能按钮 */
.quick-functions {
  display: flex;
  justify-content: space-between;
  background-color: #FFFFFF;
  padding: 15rpx 0 25rpx;
  margin-bottom: 25rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  transition: all 0.2s ease;
}

.function-item:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.function-icon {
  font-size: 50rpx;
  margin-bottom: 12rpx;
  width: 65rpx;
  height: 65rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-label {
  font-size: 24rpx;
  color: #666666;
}

/* 功能列表标题 */
.section-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  padding-left: 10rpx;
}

/* 功能列表项目 */
.feature-name {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  font-weight: 400;
}

/* 切换主题按钮 */
.theme-btn {
  position: absolute;
  top: 250rpx;
  right: 30rpx;
  z-index: 2;
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-btn:active {
  transform: scale(0.9);
}

.theme-icon {
  font-size: 40rpx;
  width: 40rpx;
  height: 40rpx;
  object-fit: contain;
}

/* 编辑资料表单样式 */
.profile-form {
  padding: 0;
}

/* 头像编辑 */
.avatar-editor {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto 40rpx;
}

.avatar-upload-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 50rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 2;
  border: none;
}

.avatar-upload-btn:active {
  transform: scale(0.9);
}

.upload-icon {
  font-size: 40rpx;
  color: #333333;
  width: 40rpx;
  height: 40rpx;
  object-fit: contain;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 性别选择器 */
.gender-selector {
  display: flex;
  justify-content: flex-start;
}

.gender-option {
  display: flex;
  align-items: center;
  padding: 12rpx 30rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  margin-right: 20rpx;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  position: relative;
  overflow: hidden;
}

.gender-option.selected {
  background-color: rgba(255, 107, 53, 0.1);
  border: 1rpx solid #FF6B35;
}

.gender-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
  width: 32rpx;
  height: 32rpx;
  object-fit: contain;
}

.gender-text {
  font-size: 28rpx;
  color: #333333;
}

/* 背景设置按钮 */
.bg-settings-btn {
  position: absolute;
  top: 40rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.3s ease;
}

.bg-settings-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-settings-icon {
  font-size: 32rpx;
}

.member-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.member-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.member-subtitle {
  font-size: 22rpx;
  color: #999999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-badge {
  background: linear-gradient(135deg, #FF6B35, #FF9966);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.2);
}

.is-member .member-badge {
  background: linear-gradient(135deg, #FFB74D, #FFA726);
}

/* ==================== 问题反馈模态框样式 ==================== */

/* 反馈表单 */
.feedback-form {
  padding: 0;
}

/* 反馈类型选择器 */
.feedback-type-selector {
  display: flex;
  justify-content: flex-start;
  gap: 20rpx;
}

.type-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  border: 1rpx solid transparent;
  min-width: 120rpx;
}

.type-option.selected {
  background-color: rgba(255, 107, 53, 0.1);
  border-color: #FF6B35;
}

.type-text {
  font-size: 26rpx;
  color: #333333;
}

.type-option.selected .type-text {
  color: #FF6B35;
  font-weight: 500;
}

/* 表单文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  resize: none;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #FF6B35;
  outline: none;
}

/* 字符计数 */
.char-count {
  font-size: 22rpx;
  color: #999999;
  text-align: right;
  margin-top: 8rpx;
  display: block;
}

/* 必填标识 */
.required {
  color: #E53935;
  font-size: 24rpx;
}

/* 图片上传区域 */
.image-upload-area {
  margin-top: 16rpx;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: flex-start;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}

/* 优化smart-image组件的样式 */
.uploaded-image {
  width: 160rpx !important;
  height: 160rpx !important;
  border-radius: 8rpx;
  overflow: hidden;
  display: block;
}

.delete-image-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #E53935;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  line-height: 1;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #CCCCCC;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.upload-btn:active {
  background-color: #F0F0F0;
  border-color: #FF6B35;
  transform: scale(0.98);
}

.upload-btn .upload-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 22rpx;
  color: #999999;
  text-align: center;
}

.upload-tip {
  font-size: 22rpx;
  color: #999999;
  margin-top: 16rpx;
  display: block;
  text-align: left;
}

/* 动画效果 */
.fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 联系客服模态框样式 */
.service-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.service-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 10rpx;
  text-align: center;
}

.tip-subtitle {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #E3F2FD 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.service-qrcode {
  width: 400rpx;
  height: 400rpx;
  border-radius: 12rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.service-qrcode:active {
  transform: scale(0.98);
}

.qrcode-tip {
  font-size: 22rpx;
  color: #666666;
  text-align: center;
  margin-top: 16rpx;
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.service-desc {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.desc-text {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
} 