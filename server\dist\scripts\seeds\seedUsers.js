"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 用户种子数据脚本
 * 创建测试用户数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
// 测试用户数据
const testUsers = [
    {
        id: 10001,
        open_id: 'test_openid_1',
        nick_name: '测试用户1',
        avatar_url: 'https://example.com/avatar1.jpg',
        gender: 1,
        coins: 5000,
        likes: 0,
        dishes: 0,
        kitchens: 1,
    },
    {
        id: 10002,
        open_id: 'test_openid_2',
        nick_name: '测试用户2',
        avatar_url: 'https://example.com/avatar2.jpg',
        gender: 2,
        coins: 3000,
        likes: 0,
        dishes: 0,
        kitchens: 1,
    },
    {
        id: 10003,
        open_id: 'test_openid_3',
        nick_name: '会员用户',
        avatar_url: 'https://example.com/avatar3.jpg',
        gender: 1,
        coins: 10000,
        likes: 0,
        dishes: 0,
        kitchens: 2,
    },
];
/**
 * 创建测试用户数据
 */
function seedUsers() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试用户数据...');
        try {
            // 清空现有数据
            yield models_1.User.destroy({ where: {} });
            yield models_1.Membership.destroy({ where: {} });
            yield models_1.BackgroundSetting.destroy({ where: {} });
            yield models_1.Transaction.destroy({ where: {} });
            logger_1.default.info('已清空现有用户相关数据');
            // 创建测试用户
            for (const userData of testUsers) {
                const user = yield models_1.User.create(userData);
                logger_1.default.info(`创建用户: ${user.nick_name} (ID: ${user.id})`);
                // 创建会员记录
                const isMember = user.id === 10003; // 只有ID为10003的用户是会员
                const expireDate = isMember ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null; // 30天后过期
                yield models_1.Membership.create({
                    user_id: user.id,
                    is_member: isMember,
                    member_type: isMember ? 'monthly' : null,
                    expire_date: expireDate,
                });
                // 创建背景设置
                yield models_1.BackgroundSetting.create({
                    user_id: user.id,
                    shop_bg: '',
                    nav_bg_style: 'default',
                    nav_bg_index: 0,
                });
                // 创建初始交易记录
                yield models_1.Transaction.create({
                    user_id: user.id,
                    amount: user.coins,
                    title: '初始大米',
                    type: 'in',
                    time: new Date(),
                });
            }
            logger_1.default.info(`共创建 ${testUsers.length} 个测试用户`);
        }
        catch (error) {
            logger_1.default.error('创建测试用户数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试用户
if (require.main === module) {
    seedUsers()
        .then(() => {
        logger_1.default.info('创建测试用户数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试用户数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedUsers;
//# sourceMappingURL=seedUsers.js.map