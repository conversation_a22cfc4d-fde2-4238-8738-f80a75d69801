"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 分类种子数据脚本
 * 创建测试分类数据
 */
const models_1 = require("../../models");
const logger_1 = __importDefault(require("../../utils/logger"));
// 测试分类数据
const testCategories = [
    // 厨房1的分类
    {
        kitchen_id: 'KIT001',
        name: '热菜',
        icon: 'https://example.com/icons/hot.png',
        sort: 1,
    },
    {
        kitchen_id: 'KIT001',
        name: '凉菜',
        icon: 'https://example.com/icons/cold.png',
        sort: 2,
    },
    {
        kitchen_id: 'KIT001',
        name: '主食',
        icon: 'https://example.com/icons/staple.png',
        sort: 3,
    },
    {
        kitchen_id: 'KIT001',
        name: '饮品',
        icon: 'https://example.com/icons/drink.png',
        sort: 4,
    },
    // 厨房2的分类
    {
        kitchen_id: 'KIT002',
        name: '招牌菜',
        icon: 'https://example.com/icons/signature.png',
        sort: 1,
    },
    {
        kitchen_id: 'KIT002',
        name: '家常菜',
        icon: 'https://example.com/icons/homemade.png',
        sort: 2,
    },
    {
        kitchen_id: 'KIT002',
        name: '小吃',
        icon: 'https://example.com/icons/snack.png',
        sort: 3,
    },
    // 厨房3的分类
    {
        kitchen_id: 'KIT003',
        name: '川菜',
        icon: 'https://example.com/icons/sichuan.png',
        sort: 1,
    },
    {
        kitchen_id: 'KIT003',
        name: '粤菜',
        icon: 'https://example.com/icons/cantonese.png',
        sort: 2,
    },
    {
        kitchen_id: 'KIT003',
        name: '湘菜',
        icon: 'https://example.com/icons/hunan.png',
        sort: 3,
    },
    {
        kitchen_id: 'KIT003',
        name: '鲁菜',
        icon: 'https://example.com/icons/shandong.png',
        sort: 4,
    },
    {
        kitchen_id: 'KIT003',
        name: '甜点',
        icon: 'https://example.com/icons/dessert.png',
        sort: 5,
    },
];
/**
 * 创建测试分类数据
 */
function seedCategories() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建测试分类数据...');
        try {
            // 清空现有数据
            yield models_1.Category.destroy({ where: {} });
            logger_1.default.info('已清空现有分类数据');
            // 创建测试分类
            for (const categoryData of testCategories) {
                const category = yield models_1.Category.create(categoryData);
                logger_1.default.info(`创建分类: ${category.name} (ID: ${category.id}, 厨房: ${category.kitchen_id})`);
            }
            logger_1.default.info(`共创建 ${testCategories.length} 个测试分类`);
        }
        catch (error) {
            logger_1.default.error('创建测试分类数据失败:', error);
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建测试分类
if (require.main === module) {
    seedCategories()
        .then(() => {
        logger_1.default.info('创建测试分类数据脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建测试分类数据脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = seedCategories;
//# sourceMappingURL=seedCategories.js.map