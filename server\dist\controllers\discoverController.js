"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discoverService_1 = __importDefault(require("../services/discoverService"));
const logger_1 = __importDefault(require("../utils/logger"));
const response_1 = require("../utils/response");
const error_1 = require("../middlewares/error");
/**
 * 获取发现列表
 * @route GET /api/discover/list
 */
const getDiscoverList = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { page = 1, pageSize = 10, sortType = 'hot', searchValue = '' } = req.query;
        logger_1.default.info(`发现页面请求: userId=${userId}, page=${page}, pageSize=${pageSize}, sortType=${sortType}, searchValue=${searchValue}`);
        const result = yield discoverService_1.default.getDiscoverList(userId, parseInt(page), parseInt(pageSize), sortType, searchValue);
        (0, response_1.success)(res, result);
    }
    catch (err) {
        logger_1.default.error(`发现页面控制器错误: ${err.message}`);
        next(err);
    }
});
/**
 * 添加到分类
 * @route POST /api/discover/addToDish
 */
const addToDish = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const { dishId, targetKitchenId, targetCategoryId } = req.body;
        if (!dishId || !targetKitchenId || !targetCategoryId) {
            throw new error_1.BusinessError('缺少必要参数', response_1.ResponseCode.VALIDATION);
        }
        const result = yield discoverService_1.default.addToDish(userId, parseInt(dishId), targetKitchenId, parseInt(targetCategoryId));
        (0, response_1.success)(res, result);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取发现页消息数量
 * @route GET /api/discover/messageCount
 */
const getMessageCount = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const counts = yield discoverService_1.default.getMessageCount(userId);
        (0, response_1.success)(res, counts);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取未读数量
 * @route GET /api/discover/unreadCounts
 */
const getUnreadCounts = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user.id;
        const counts = yield discoverService_1.default.getUnreadCounts(userId);
        (0, response_1.success)(res, counts);
    }
    catch (err) {
        next(err);
    }
});
/**
 * 获取标签内容
 * @route GET /api/discover/tabContent
 */
const getTabContent = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const { type, page = 1, pageSize = 10 } = req.query;
        if (!type) {
            throw new error_1.BusinessError('缺少参数: type', response_1.ResponseCode.VALIDATION);
        }
        const content = yield discoverService_1.default.getTabContent(userId, type, parseInt(page), parseInt(pageSize));
        (0, response_1.success)(res, content);
    }
    catch (err) {
        next(err);
    }
});
exports.default = {
    getDiscoverList,
    addToDish,
    getMessageCount,
    getUnreadCounts,
    getTabContent
};
//# sourceMappingURL=discoverController.js.map