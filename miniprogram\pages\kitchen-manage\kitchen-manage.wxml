<!-- 厨房管理页面 -->
<view class="kitchen-manage-container">
  <!-- 使用自定义导航栏 -->
  <custom-navbar 
    title="厨房管理" 
    showBack="{{true}}"
    navBgStyle="{{navBgStyle}}"
    useShadow="{{true}}"
  />
  
  <!-- 顶部厨房信息 -->
  <view class="kitchen-info-area">
    <view class="kitchen-main-info">
      <view class="kitchen-avatar-name">
        <view class="avatar-area">
          <smart-image 
            class="kitchen-avatar" 
            src="{{isLoggedIn ? kitchenInfo.avatarUrl : defaultImages.KITCHEN_AVATAR}}" 
            mode="aspectFill"
            width="100rpx"
            height="100rpx"
            border-radius="50"
          />
        </view>
        <view class="kitchen-name-area">
          <text class="kitchen-name">{{kitchenInfo.name || '暂无厨房'}}</text>
          <view class="kitchen-level">Lv.{{kitchenInfo.level || '1'}}</view>
        </view>
      </view>
      <view class="switch-kitchen" bindtap="switchKitchen">
        <text>切换厨房</text>
        <text class="arrow-icon">▼</text>
      </view>
    </view>
    
    <view class="kitchen-id-row">
      <text class="kitchen-id">厨房ID：{{kitchenInfo.id || '暂无ID'}}</text>
      <view class="copy-id" bindtap="copyKitchenId">
        <image class="copy-icon" src="/static/images/icons/copy.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  
  <!-- 厨房统计信息 -->
  <view class="kitchen-stats-card">
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-info">
          <text class="stats-value">{{kitchenInfo.kitchenCount || '0'}}</text>
        </view>
        <text class="stats-label">厨房数量</text>
      </view>
      <view class="stats-item">
        <view class="stats-info">
          <text class="stats-value">{{kitchenInfo.categoryCount || '0'}}/{{kitchenInfo.categoryLimit || '5'}}</text>
        </view>
        <text class="stats-label">分类额度</text>
      </view>
      <view class="stats-item">
        <view class="stats-info">
          <text class="stats-value">{{kitchenInfo.dishCount || '0'}}/{{kitchenInfo.dishLimit || '50'}}</text>
        </view>
        <text class="stats-label">菜品额度</text>
      </view>
      <view class="stats-item">
        <view class="stats-info">
          <text class="stats-value">{{kitchenInfo.memberCount || '0'}}人</text>
        </view>
        <text class="stats-label">厨房成员</text>
      </view>
    </view>
  </view>
  
  <!-- 主要操作按钮 -->
  <view class="main-action-buttons">
    <view class="action-button upgrade" bindtap="showUpgradeConfirm">
      <text>升级厨房</text>
    </view>
    <view class="action-button dismiss" bindtap="showDismissConfirm">
      <text wx:if="{{kitchenInfo.isOwner}}">解散厨房</text>
      <text wx:else>退出厨房</text>
    </view>
    <view class="action-button manage" bindtap="manageKitchen">
      <text>管理厨房</text>
    </view>
  </view>
  
  <!-- 功能入口区域 -->
  <view class="function-entry-area">
    <view class="function-grid">
      <view class="function-item" bindtap="showAddKitchenModal" data-function="addKitchen">
        <view class="function-icon">
          <image src="/static/images/icons/plus.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">新增厨房</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="myKitchen">
        <view class="function-icon">
          <image src="/static/images/icons/kitchen.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">我的厨房</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="kitchenMember">
        <view class="function-icon">
          <image src="/static/images/icons/crown.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">厨房会员</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="kitchenTheme">
        <view class="function-icon">
          <image src="/static/images/icons/theme-switch.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">厨房主题</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="cloneMenu">
        <view class="function-icon">
          <image src="/static/images/icons/copy.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">克隆菜谱</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="memberManage">
        <view class="function-icon">
          <image src="/static/images/icons/male.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">成员管理</text>
      </view>
      <view class="function-item" bindtap="navigateToFunction" data-function="kitchenTable">
        <view class="function-icon">
          <image src="/static/images/icons/table.png" mode="aspectFit"></image>
        </view>
        <text class="function-label">厨房桌号</text>
      </view>
    </view>
  </view>
  
  <!-- 使用modal-dialog组件作为切换厨房弹窗 -->
  <modal-dialog
    visible="{{switchKitchenVisible}}"
    title="切换厨房"
    showCancel="{{false}}"
    confirmText="关闭"
    bind:close="closeSwitchKitchen"
    bind:confirm="closeSwitchKitchen"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <view class="kitchen-list">
      <view 
        wx:for="{{kitchenList}}" 
        wx:key="id" 
        class="kitchen-item {{currentKitchenIndex === index ? 'active' : ''}}"
        bindtap="selectKitchen"
        data-index="{{index}}"
      >
        <view class="kitchen-item-info">
          <view class="avatar-area">
            <smart-image 
              class="kitchen-item-avatar" 
              src="{{isLoggedIn ? item.avatarUrl : defaultImages.KITCHEN_AVATAR}}" 
              mode="aspectFill"
              width="80rpx"
              height="80rpx"
              border-radius="40"
            />
          </view>
          <view class="kitchen-item-details">
            <text class="kitchen-item-name">{{item.name}}</text>
            <text class="kitchen-item-level">Lv.{{item.level}}</text>
          </view>
        </view>
        <view class="kitchen-item-owner" wx:if="{{item.isOwner}}">我创建的</view>
        <view class="kitchen-item-owner" wx:else>我加入的</view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 升级厨房弹窗 -->
  <modal-dialog
    visible="{{showUpgradeConfirm}}"
    title="升级厨房"
    showCancel="{{true}}"
    cancelText="取消"
    confirmText="确认升级"
    bind:close="cancelUpgrade"
    bind:cancel="cancelUpgrade"
    bind:confirm="confirmUpgrade"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <view class="upgrade-content">
      <view class="upgrade-item">
        <text class="upgrade-label">当前等级:</text>
        <text class="upgrade-value">Lv.{{kitchenInfo.level || 1}}</text>
      </view>
      <view class="upgrade-item">
        <text class="upgrade-label">升级后:</text>
        <text class="upgrade-value">Lv.{{kitchenInfo.level + 1 || 2}}</text>
      </view>
      <view class="upgrade-divider"></view>
      <view class="upgrade-item">
        <text class="upgrade-label">升级费用:</text>
        <text class="upgrade-value">38大米</text>
      </view>
      <view class="upgrade-item">
        <text class="upgrade-label">当前余额:</text>
        <text class="upgrade-value">{{userCoins || 520}}大米</text>
      </view>
      <view class="upgrade-divider"></view>
      <view class="upgrade-benefit-title">升级后获得</view>
      <view class="upgrade-benefits">
        <view class="upgrade-benefit-item">
          <text class="benefit-label">分类额度</text>
          <text class="benefit-value">+2</text>
        </view>
        <view class="upgrade-benefit-item">
          <text class="benefit-label">菜品额度</text>
          <text class="benefit-value">+5</text>
        </view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 新增/加入厨房弹窗 -->
  <modal-dialog
    visible="{{showAddKitchenModal}}"
    title="厨房管理"
    showCancel="{{true}}"
    cancelText="取消"
    confirmText="{{activeTab === 'create' ? '创建厨房' : '加入厨房'}}"
    bind:close="closeAddKitchenModal"
    bind:cancel="closeAddKitchenModal"
    bind:confirm="confirmAddKitchen"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <view class="kitchen-tabs">
      <view 
        class="kitchen-tab {{activeTab === 'create' ? 'active' : ''}}" 
        bindtap="switchTab" 
        data-tab="create"
      >
        <text>新增厨房</text>
      </view>
      <view 
        class="kitchen-tab {{activeTab === 'join' ? 'active' : ''}}" 
        bindtap="switchTab" 
        data-tab="join"
      >
        <text>加入厨房</text>
      </view>
    </view>
    <view class="kitchen-panel" wx:if="{{activeTab === 'create'}}">
      <!-- 大米消耗提示 -->
      <view class="rice-cost-tip">
        <view class="cost-icon">
          <image class="cost-icon-img" src="/static/images/icons/coins.png" mode="aspectFit"></image>
        </view>
        <view class="cost-text">
          <text class="cost-title">创建厨房需要</text>
          <text class="cost-amount">288大米</text>
        </view>
        <view class="balance-info">
          <text class="balance-label">当前余额:</text>
          <text class="balance-amount">{{userCoins || 0}}大米</text>
        </view>
      </view>
      
      <!-- 上传厨房头像 -->
      <view class="avatar-upload" bindtap="chooseKitchenAvatar">
        <smart-image
          class="kitchen-avatar-preview" 
          src="{{newKitchenAvatarUrl || defaultImages.KITCHEN_AVATAR}}"
          mode="aspectFill"
          width="120rpx"
          height="120rpx"
          border-radius="60"
        />
        <view class="upload-text">点击上传厨房头像</view>
      </view>
      
      <view class="form-item">
        <text class="form-label">厨房名称</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入厨房名称" 
          value="{{newKitchenName}}"
          bindinput="inputNewKitchenName"
          maxlength="10"
        />
      </view>
      <view class="form-item">
        <text class="form-label">厨房公告</text>
        <textarea 
          class="form-textarea" 
          placeholder="添加厨房公告（可选）" 
          value="{{newKitchenNotice}}"
          bindinput="inputNewKitchenNotice"
          maxlength="30"
        />
      </view>
    </view>
    <view class="kitchen-panel" wx:else>
      <view class="form-item">
        <text class="form-label">厨房ID</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入厨房ID" 
          value="{{joinKitchenId}}"
          bindinput="inputJoinKitchenId"
        />
      </view>
      <view class="form-tip">请输入由创建者提供的厨房ID</view>
    </view>
  </modal-dialog>
  
  <!-- 我的厨房弹窗 -->
  <modal-dialog
    visible="{{showMyKitchenModal}}"
    title="我的厨房"
    showCancel="{{false}}"
    confirmText="关闭"
    bind:close="closeMyKitchenModal"
    bind:confirm="closeMyKitchenModal"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <view class="my-kitchen-tabs">
      <view 
        class="my-kitchen-tab {{myKitchenActiveTab === 'owned' ? 'active' : ''}}" 
        bindtap="switchMyKitchenTab" 
        data-tab="owned"
      >
        <text>我创建的</text>
      </view>
      <view 
        class="my-kitchen-tab {{myKitchenActiveTab === 'joined' ? 'active' : ''}}" 
        bindtap="switchMyKitchenTab" 
        data-tab="joined"
      >
        <text>我加入的</text>
      </view>
    </view>
    
    <!-- 我创建的厨房列表 -->
    <view class="my-kitchen-panel" wx:if="{{myKitchenActiveTab === 'owned'}}">
      <view class="my-kitchen-list">
        <view wx:if="{{ownedKitchens.length === 0}}" class="empty-kitchen-tip">
          <text>暂无创建的厨房</text>
        </view>
        <view 
          wx:for="{{ownedKitchens}}" 
          wx:key="id" 
          class="my-kitchen-item"
        >
          <view class="my-kitchen-item-info">
            <smart-image 
              class="my-kitchen-item-avatar" 
              src="{{item.avatarUrl || defaultImages.KITCHEN_AVATAR}}" 
              mode="aspectFill"
              width="80rpx"
              height="80rpx"
              border-radius="40"
            />
            <view class="my-kitchen-item-details">
              <text class="my-kitchen-item-name">{{item.name}}</text>
              <view class="my-kitchen-item-meta">
                <text class="my-kitchen-item-id">ID: {{item.id}}</text>
                <text class="my-kitchen-item-level">Lv.{{item.level}}</text>
              </view>
            </view>
          </view>
          <view class="my-kitchen-item-actions">
            <view class="my-kitchen-action-btn dismiss-btn" bindtap="dismissKitchenFromModal" data-id="{{item.id}}">解散</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 我加入的厨房列表 -->
    <view class="my-kitchen-panel" wx:else>
      <view class="my-kitchen-list">
        <view wx:if="{{joinedKitchens.length === 0}}" class="empty-kitchen-tip">
          <text>暂无加入的厨房</text>
        </view>
        <view 
          wx:for="{{joinedKitchens}}" 
          wx:key="id" 
          class="my-kitchen-item"
        >
          <view class="my-kitchen-item-info">
            <smart-image 
              class="my-kitchen-item-avatar" 
              src="{{item.avatarUrl || defaultImages.KITCHEN_AVATAR}}" 
              mode="aspectFill"
              width="80rpx"
              height="80rpx"
              border-radius="40"
            />
            <view class="my-kitchen-item-details">
              <text class="my-kitchen-item-name">{{item.name}}</text>
              <view class="my-kitchen-item-meta">
                <text class="my-kitchen-item-id">ID: {{item.id}}</text>
                <text class="my-kitchen-item-level">Lv.{{item.level}}</text>
              </view>
            </view>
          </view>
          <view class="my-kitchen-item-actions">
            <view class="my-kitchen-action-btn leave-btn" bindtap="leaveKitchenFromModal" data-id="{{item.id}}">退出</view>
          </view>
        </view>
      </view>
    </view>
  </modal-dialog>
  
  <!-- 添加厨房会员组件 -->
  <kitchen-member
    visible="{{showKitchenMember}}"
    userCoins="{{userCoins}}"
    bind:close="closeKitchenMember"
    bind:success="onMembershipSuccess"
  />
  
  <!-- 确认对话框 -->
  <confirm-dialog
    visible="{{showConfirmDialog}}"
    title="{{confirmDialogTitle}}"
    content="{{confirmDialogContent}}"
    cancelText="取消"
    confirmText="{{confirmDialogConfirmText}}"
    confirmColor="#FF6B35"
    bind:cancel="onCancelConfirm"
    bind:confirm="onConfirmAction"
  />
  
  <!-- 解散厨房确认弹窗 -->
  <confirm-dialog
    visible="{{showDismissConfirm}}"
    title="{{kitchenInfo.isOwner ? '解散厨房' : '退出厨房'}}"
    content="{{kitchenInfo.isOwner ? '确定要解散当前厨房吗？此操作不可恢复！' : '确定要退出当前厨房吗？'}}"
    cancelText="取消"
    confirmText="{{kitchenInfo.isOwner ? '确定解散' : '确定退出'}}"
    confirmColor="#E53935"
    bind:cancel="cancelDismiss"
    bind:confirm="confirmDismiss"
  />

  <!-- 背景设置组件 -->
  <background-settings
    visible="{{showBackgroundSettings}}"
    currentShopBg="{{shopBg}}"
    currentNavBg="{{navBgStyle}}"
    currentNavBgIndex="{{navBgIndex}}"
    isVip="{{isVip}}"
    kitchenId="{{kitchenInfo.id}}"
    bind:close="closeBackgroundSettings"
    bind:save="saveBackgroundSettings"
  />

  <!-- 克隆菜谱弹窗 -->
  <modal-dialog
    visible="{{showCloneMenuModal}}"
    title="克隆菜谱"
    showCancel="{{true}}"
    cancelText="取消"
    confirmText="克隆菜品"
    bind:close="closeCloneMenuModal"
    bind:cancel="closeCloneMenuModal"
    bind:confirm="confirmCloneMenu"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <!-- 克隆菜谱步骤 -->
    <view class="clone-steps">
      <view class="step {{cloneStep >= 1 ? 'active' : ''}}">
        <view class="step-num">1</view>
        <view class="step-text">厨房ID</view>
      </view>
      <view class="step-line"></view>
      <view class="step {{cloneStep >= 2 ? 'active' : ''}}">
        <view class="step-num">2</view>
        <view class="step-text">选择分类</view>
      </view>
      <view class="step-line"></view>
      <view class="step {{cloneStep >= 3 ? 'active' : ''}}">
        <view class="step-num">3</view>
        <view class="step-text">选择菜品</view>
      </view>
    </view>

    <!-- 第一步：输入厨房ID -->
    <view class="clone-panel" wx:if="{{cloneStep === 1}}">
      <view class="form-item">
        <text class="form-label">厨房ID</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入要克隆菜谱的厨房ID" 
          value="{{sourceKitchenId}}"
          bindinput="inputSourceKitchenId"
        />
      </view>
      <view class="form-tip">请输入要克隆菜谱的厨房ID</view>
      <button class="search-btn" bindtap="searchSourceKitchen">搜索厨房</button>
    </view>

    <!-- 第二步：显示厨房信息和选择分类 -->
    <view class="clone-panel" wx:elif="{{cloneStep === 2}}">
      <!-- 厨房信息 -->
      <view class="source-kitchen-info">
        <smart-image 
          class="kitchen-avatar-sm" 
          src="{{sourceKitchen.avatarUrl || defaultImages.KITCHEN_AVATAR}}" 
          mode="aspectFill"
          width="80rpx"
          height="80rpx"
          border-radius="40"
        ></smart-image>
        <view class="kitchen-info-text">
          <text class="kitchen-name-text">{{sourceKitchen.name}}</text>
          <text class="kitchen-level-text">Lv.{{sourceKitchen.level}}</text>
        </view>
      </view>

      <!-- 分类列表（可滚动） -->
      <scroll-view class="category-list-scroll" scroll-y="true">
        <view class="category-list">
          <block wx:if="{{sourceCategories && sourceCategories.length > 0}}">
            <view 
              wx:for="{{sourceCategories}}" 
              wx:key="id" 
              class="category-item {{selectedCategoryId === item.id ? 'selected' : ''}}"
              bindtap="selectCategory"
              data-id="{{item.id}}"
            >
              <image class="category-icon" src="{{item.icon || '/static/images/icons/class/food.png'}}" mode="aspectFit"></image>
              <view class="category-name">{{item.name}}</view>
              <view class="category-count">{{(item.dishCount !== undefined && item.dishCount !== null) ? item.dishCount : 0}}个菜品</view>
            </view>
          </block>
          <block wx:else>
            <view class="empty-category">
              <text>暂无分类数据</text>
            </view>
          </block>
        </view>
      </scroll-view>
    </view>

    <!-- 第三步：选择菜品 -->
    <view class="clone-panel" wx:elif="{{cloneStep === 3}}">
      <!-- 选择目标分类 -->
      <view class="form-item">
        <text class="form-label">目标分类</text>
        <view class="picker-view" bindtap="showTargetCategoryModal">
          {{targetCategories && targetCategories.length > 0 && targetCategories[targetCategoryIndex] ? targetCategories[targetCategoryIndex].name : '请选择分类'}}
          <text class="picker-arrow">▼</text>
        </view>
      </view>

      <!-- 菜品列表（可滚动） -->
      <scroll-view class="dish-list-scroll" scroll-y="true">
        <view class="dish-list">
          <block wx:if="{{sourceDishes && sourceDishes.length > 0}}">
            <view 
              wx:for="{{sourceDishes}}" 
              wx:key="id" 
              class="dish-item"
              bindtap="toggleDishSelection"
              data-id="{{item.id}}"
            >
              <!-- 圆形气泡勾选框 -->
              <view class="custom-checkbox-alt {{selectedDishes.includes(String(item.id)) ? 'checked' : ''}}">
                <view class="checkbox-inner"></view>
              </view>
              
              <image class="dish-image" src="{{item.image || defaultImages.DISH}}" mode="aspectFill"></image>
              <view class="dish-info">
                <view class="dish-name">{{item.name || '未命名菜品'}}</view>
                <view class="dish-desc">{{item.description || '暂无描述'}}</view>
                <view class="dish-price">{{item.price || 0}}大米</view>
              </view>
            </view>
          </block>
          <block wx:else>
            <view class="empty-dish-list">
              <icon type="info" size="64"></icon>
              <text>暂无菜品数据</text>
            </view>
          </block>
        </view>
      </scroll-view>
      <view class="clone-summary" wx:if="{{selectedDishes.length > 0}}">
        已选择 {{selectedDishes.length}} 个菜品
      </view>
    </view>

    <!-- 克隆结果 -->
    <view class="clone-panel" wx:elif="{{cloneStep === 4}}">
      <view class="clone-result">
        <icon type="success" size="64"></icon>
        <view class="result-text">克隆成功！</view>
        <view class="result-desc">成功克隆 {{cloneResult.clonedCount}} 个菜品到 {{targetCategories && targetCategories.length > targetCategoryIndex && targetCategories[targetCategoryIndex] ? targetCategories[targetCategoryIndex].name : ''}} 分类</view>
      </view>
    </view>
  </modal-dialog>

  <!-- 目标分类选择模态框 -->
  <modal-dialog
    visible="{{showTargetCategoryModal}}"
    title="选择目标分类"
    bind:close="closeTargetCategoryModal"
    bind:cancel="closeTargetCategoryModal"
    bind:confirm="confirmTargetCategory"
    width="90%"
    maxWidth="650rpx"
    themeColor="#FF6B35"
  >
    <scroll-view class="target-category-scroll" scroll-y="true">
      <view class="target-category-list">
        <view 
          wx:for="{{targetCategories}}"
          wx:key="id"
          class="target-category-item {{targetCategorySelectIndex === index ? 'selected' : ''}}"
          bindtap="selectTargetCategory"
          data-index="{{index}}"
        >
          <image class="category-icon" src="{{item.icon || '/static/images/icons/class/food.png'}}" mode="aspectFit"></image>
          <view class="category-name">{{item.name}}</view>
          <view class="category-count">{{(item.dishCount !== undefined && item.dishCount !== null) ? item.dishCount : 0}}个菜品</view>
        </view>
      </view>
    </scroll-view>
  </modal-dialog>

  <!-- 管理厨房弹窗 -->
  <modal-dialog
    visible="{{showManageKitchenModal}}"
    title="管理厨房"
    showCancel="{{true}}"
    cancelText="取消"
    confirmText="保存修改"
    bind:close="closeManageKitchenModal"
    bind:cancel="closeManageKitchenModal"
    bind:confirm="confirmUpdateKitchen"
    width="90%"
    maxWidth="680rpx"
    themeColor="#FF6B35"
  >
    <view class="kitchen-panel">
      <!-- 上传厨房头像 -->
      <view class="avatar-upload" bindtap="chooseEditKitchenAvatar">
        <smart-image
          class="kitchen-avatar-preview" 
          src="{{editKitchenAvatarUrl || defaultImages.KITCHEN_AVATAR}}"
          mode="aspectFill"
          width="120rpx"
          height="120rpx"
          border-radius="60"
        />
        <view class="upload-text">点击上传厨房头像</view>
      </view>
      
      <view class="form-item">
        <text class="form-label">厨房名称</text>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入厨房名称" 
          value="{{editKitchenName}}"
          bindinput="inputEditKitchenName"
          maxlength="10"
        />
      </view>
      <view class="form-item">
        <text class="form-label">厨房公告</text>
        <textarea 
          class="form-textarea" 
          placeholder="添加厨房公告（可选）" 
          value="{{editKitchenNotice}}"
          bindinput="inputEditKitchenNotice"
          maxlength="30"
        />
      </view>
    </view>
  </modal-dialog>
  
  <!-- 成员管理组件 -->
  <kitchen-member-manage
    visible="{{showMemberManageModal}}"
    kitchenId="{{kitchenInfo.id}}"
    isOwner="{{kitchenInfo.isOwner}}"
    bind:close="closeMemberManageModal"
    bind:confirm="closeMemberManageModal"
  />

  <!-- 桌号管理组件 -->
  <table-manager
    visible="{{showTableManager}}"
    tables="{{tableList}}"
    isVip="{{isVip}}"
    bind:close="closeTableManager"
    bind:add="addTable"
    bind:update="updateTable"
    bind:delete="deleteTable"
    bind:sort="sortTables"
  />
</view> 