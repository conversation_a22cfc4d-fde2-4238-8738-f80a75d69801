/**
 * 通用请求函数
 * 用于发送HTTP请求到后端服务
 */

// 导入文件
import { RequestOptions, ResponseData } from '../types/api'
import { SERVER_BASE_URL } from './constants'

// loading计数器，用于追踪showLoading调用
let loadingCounter = 0;

// 安全显示加载状态
export const safeShowLoading = (options: Partial<WechatMiniprogram.ShowLoadingOption> = {}) => {
  loadingCounter++;
  console.log('显示loading，当前计数:', loadingCounter);
  wx.showLoading({
    title: options.title || '加载中...',
    mask: options.mask !== undefined ? options.mask : true
  });
};

// 安全隐藏加载状态
export const safeHideLoading = () => {
  loadingCounter--;
  console.log('隐藏loading，当前计数:', loadingCounter);

  if (loadingCounter <= 0) {
    loadingCounter = 0; // 防止负数
    wx.hideLoading();
  }
};

// 请求函数
export const request = (options: RequestOptions): Promise<ResponseData> => {
  // 获取用户token
  const token = wx.getStorageSync('token') || ''

  // 发送实际请求
  return new Promise((resolve, reject) => {
    console.log('请求接口:', options.url)
    console.log('请求参数:', options.data)

    // 计算请求开始时间
    const startTime = Date.now()
    // 设置超时时间
    const TIMEOUT = 15000 // 15秒超时
    let isTimeout = false
    let timeoutTimer: any = null

    // 使用安全的loading方法
    if (options.showLoading !== false) {
      safeShowLoading({
        title: options.loadingText || '加载中...',
      })
    }

    // 设置超时定时器
    timeoutTimer = setTimeout(() => {
      isTimeout = true
      console.error(`请求超时: ${options.url}，耗时 ${Date.now() - startTime}ms`)
      wx.showToast({
        title: '请求超时，请稍后再试',
        icon: 'none'
      })
      reject({
        error: 500,
        body: null,
        message: '请求超时，请稍后再试'
      })
      safeHideLoading()
    }, TIMEOUT)

    // 网络请求
    wx.request({
      url: `${SERVER_BASE_URL}${options.url}`,
      data: options.data,
      method: options.method || 'POST',
      header: {
        'content-type': 'application/json',
        ...(options.noAuth ? {} : { 'auth': token })
      },
      success: (res: any) => {
        // 清除超时定时器
        if (timeoutTimer) {
          clearTimeout(timeoutTimer)
          timeoutTimer = null
        }

        // 已超时就不处理了
        if (isTimeout) return

        // 记录请求耗时
        const endTime = Date.now()
        console.log(`请求耗时: ${endTime - startTime}ms - ${options.url}`)

        // 打印返回数据
        console.log('返回数据:', res.data)

        // 处理请求结果
        const { data } = res

        // 处理登录失效
        if (data.error === 401) {
          console.error('登录失效，请重新登录')
          // 清除用户信息
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          // 只显示Toast提示，不强制跳转登录页面
          wx.showToast({
            title: '登录已失效',
            icon: 'none',
            duration: 2000
          })
          reject(data)
          return
        }

          // 系统错误
        if (data.error === 500) {
          wx.showToast({
            title: '系统错误，请稍后再试',
            icon: 'none'
          })
          reject(data)
          return
        }

        // 业务错误
        if (data.error !== 0 && data.error !== 500 && data.error !== 401) {
          wx.showToast({
            title: data.message || '操作失败',
            icon: 'none'
          })
        }

        // 返回数据
        resolve(data)
      },
      fail: (err) => {
        // 清除超时定时器
        if (timeoutTimer) {
          clearTimeout(timeoutTimer)
          timeoutTimer = null
        }

        // 已超时就不处理了
        if (isTimeout) return

        console.error('请求失败:', err)
        
        // 更详细的错误处理
        let errorMessage = '网络错误，请检查网络连接'
        
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请稍后再试'
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请检查网络'
          } else if (err.errMsg.includes('abort')) {
            errorMessage = '请求被取消'
          }
        }
        
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
        
        reject({
          error: 500,
          body: null,
          message: errorMessage
        })
      },
      complete: () => {
        // 已超时就不处理了
        if (!isTimeout && options.showLoading !== false) {
          safeHideLoading()
        }
      }
    })
  })
}