{"version": 3, "file": "setupDatabase.js", "sourceRoot": "", "sources": ["../../src/scripts/setupDatabase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;GAGG;AACH,6DAAqC;AACrC,kEAA0C;AAC1C,6EAAqD;AACrD,kEAA0C;AAC1C,wEAAgD;AAChD,4EAAoD;AACpD,oEAA4C;AAC5C,oEAA4C;AAC5C,oEAA4C;AAE5C;;;GAGG;AACH,SAAS,SAAS;IAChB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,OAAO,GAAQ;QACnB,IAAI,EAAE,KAAK;QACX,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,KAAK;QACX,GAAG,EAAE,KAAK;KACX,CAAC;IAEF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;IACrB,CAAC;SAAM,CAAC;QACN,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,QAAQ;oBACX,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;oBACpB,MAAM;gBACR,KAAK,WAAW;oBACd,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvB,MAAM;gBACR,KAAK,QAAQ;oBACX,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;oBACpB,MAAM;gBACR,KAAK,OAAO;oBACV,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;oBACnB,MAAM;gBACR;oBACE,gBAAM,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAe,aAAa;;QAC1B,MAAM,OAAO,GAAG,SAAS,EAAE,CAAC;QAE5B,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,gBAAM,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE9C,IAAI,CAAC;YACH,SAAS;YACT,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChC,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,sBAAY,GAAE,CAAC;YACvB,CAAC;YAED,MAAM;YACN,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBACnC,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,sBAAY,GAAE,CAAC;YACvB,CAAC;YAED,SAAS;YACT,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChC,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAE9B,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,mBAAS,GAAE,CAAC;gBAElB,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,sBAAY,GAAE,CAAC;gBAErB,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,wBAAc,GAAE,CAAC;gBAEvB,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,oBAAU,GAAE,CAAC;gBAEnB,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,oBAAU,GAAE,CAAC;gBAEnB,gBAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,MAAM,IAAA,oBAAU,GAAE,CAAC;YACrB,CAAC;YAED,gBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACzB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvC,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,gBAAM,CAAC,KAAK,CAAC,SAAS,KAAK,EAAE,CAAC,CAAC;YACjC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAAA;AAED,qBAAqB;AACrB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,aAAa,EAAE;SACZ,IAAI,CAAC,GAAG,EAAE;QACT,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,gBAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kBAAe,aAAa,CAAC"}