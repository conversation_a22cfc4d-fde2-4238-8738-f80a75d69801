# 餐厅点菜小程序接口文档

## 公共约定

### 基础URL
- 开发环境: `https://dev-api.example.com`
- 生产环境: `https://api.example.com`

### 请求格式
- 请求方式默认为POST
- 请求参数使用JSON格式，即使参数为空也需要使用 `{}` 表示
- 参数命名使用小驼峰，与后端字段名保持一致
- HTTP头部需要包含auth字段，值为当前登录后保存的token

### 响应格式
所有接口返回格式统一为：
```json
{
  "error": 0,        // 0表示成功，非0表示错误
  "body": {},        // 返回的数据主体
  "message": ""      // 错误信息，成功时为空
}
```

错误码说明：
- `0`: 成功
- `401`: 未登录或登录过期，需要重新登录
- `500`: 系统错误
- 其他值: 业务错误，直接显示message内容

## 接口实现状态总览

### 已完成接口
- 用户模块：登录、获取用户信息、更新用户信息
- 菜品模块：获取分类、获取列表、获取详情、搜索菜品、热门关键词
- 购物车模块：添加、更新、获取列表、清空
- 订单模块：提交订单、获取订单列表、获取订单详情、取消订单、接单、拒绝订单、完成订单、取消烹饪
- 搜索模块：搜索菜品、热门关键词、搜索历史、清除搜索历史
- 菜品管理：修改菜品信息、删除菜品、修改菜品状态、更新菜品排序、添加菜品、上传菜品图片
- 分类管理：获取列表、添加、更新、删除、更新排序
- 餐厅信息：获取餐厅基本信息（店铺名称、logo、公告）
- 背景设置：获取用户背景设置、更新用户背景设置、上传背景图片
- 厨房管理：获取厨房列表、厨房详情、创建厨房、加入厨房、退出厨房、解散厨房、升级厨房、获取厨房成员
- 问题反馈：提交反馈、上传反馈图片、获取反馈列表、更新反馈状态

### 待实现接口
- 分类管理：批量更新分类

## 接口列表

### 1. 用户模块

#### 1.1 登录接口
- **接口地址**: `/api/user/login`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "code": "string"  // 微信登录获取的临时凭证
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "token": "string",   // 用户令牌
      "userId": "string"   // 用户ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 1.2 获取用户信息
- **接口地址**: `/api/user/info`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "userId": "10001",     // 用户ID
      "nickName": "string",   // 昵称
      "avatarUrl": "string",  // 头像URL
      "gender": 1,            // 性别：0未知，1男，2女
      "coins": 520,           // 大米数量
      "likes": 68,            // 获赞数量
      "dishes": 12,           // 添加的菜品数量
      "kitchens": 3           // 厨房数量
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 1.3 更新用户信息
- **接口地址**: `/api/user/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "nickName": "string",  // 昵称
    "avatarUrl": "string", // 头像URL
    "gender": 1,           // 性别
    "coins": 520           // 大米数量（可选）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 1.4 获取会员状态
- **接口地址**: `/api/user/membershipStatus`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "isMember": true,        // 是否是会员
      "memberType": "string",  // 会员类型：monthly-月度会员，yearly-年度会员
      "expireDate": "string",  // 到期时间
      "privileges": {         // 会员特权
        "customTheme": true,  // 自定义主题
        "tableManagement": true // 桌号管理
      }
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 1.5 订阅会员
- **接口地址**: `/api/user/subscribeMembership`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "memberType": "string"   // 会员类型：monthly-月度会员，yearly-年度会员
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "isMember": true,        // 会员状态
      "memberType": "string",  // 会员类型
      "expireDate": "string",  // 到期时间
      "updatedAt": "string",   // 更新时间
      "privileges": {          // 会员特权
        "customTheme": true,   // 自定义主题
        "tableManagement": true  // 桌号管理
      }
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调
- **说明**: 会员功能是用户级别的，对所有厨房都有效

### 2. 菜品模块

#### 2.1 获取菜品分类
- **接口地址**: `/api/dish/categories`
- **请求方式**: GET
- **请求参数**: 
  ```json
  {
    "kitchenId": "string"  // 厨房ID（可选，不传时使用用户当前选择的厨房）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",    // 分类ID
        "name": "string",  // 分类名称
        "icon": "string",   // 分类图标
        "sort": 1          // 排序值（数值越小越靠前）
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 2.2 获取菜品列表
- **接口地址**: `/api/dish/list`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "categoryId": "string",  // 分类ID，不传则获取全部菜品
    "kitchenId": "string"    // 厨房ID（可选，不传时使用用户当前选择的厨房）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",           // 菜品ID
        "categoryId": "string",   // 分类ID
        "name": "string",         // 菜品名称
        "image": "string",        // 菜品图片
        "price": 36,              // 价格（大米）
        "description": "string",  // 描述
        "sales": 238,             // 销量
        "rating": 4.8,            // 评分
        "categoryName": "string"  // 分类名称
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 2.3 获取菜品详情
- **接口地址**: `/api/dish/detail`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "dishId": "string"  // 菜品ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 菜品ID
      "categoryId": "string",   // 分类ID
      "name": "string",         // 菜品名称
      "image": "string",        // 菜品图片
      "price": 36,              // 价格（大米）
      "description": "string",  // 描述
      "sales": 238,             // 销量
      "rating": 4.8,            // 评分
      "ingredients": [          // 配料
        {
          "name": "string",     // 配料名称
          "amount": "string"    // 配料用量
        }
      ],
      "categoryName": "string", // 分类名称
      "nutrition": {            // 营养信息（可选）
        "calories": 650,        // 热量（千卡）
        "protein": "28克",      // 蛋白质
        "carbs": "75克",        // 碳水化合物
        "fat": "25克"           // 脂肪
      },
      "cookingSteps": [         // 烹饪步骤（可选）
        {
          "title": "string",    // 步骤标题
          "description": "string", // 步骤描述
          "image": "string"     // 步骤图片
        }
      ],
      "recommendation": [       // 推荐菜品（可选）
        {
          "id": "string",       // 菜品ID
          "name": "string",     // 菜品名称
          "image": "string",    // 菜品图片
          "price": 58           // 价格（大米）
        }
      ],
      "commentCount": 156,      // 评论总数（可选）
      "commentPreview": [       // 评论预览（可选）
        {
          "avatar": "string",   // 用户头像
          "name": "string",     // 用户名称
          "content": "string",  // 评论内容
          "time": "string"      // 评论时间
        }
      ]
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 2.4 点赞菜品
- **接口地址**: `/api/dish/like`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string"  // 菜品ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "dishId": "string"  // 菜品ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 2.5 举报菜品
- **接口地址**: `/api/dish/report`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string",    // 菜品ID
    "reason": "string",    // 举报原因：fake-虚假信息, porn-色情低俗, illegal-违法违规, rights-侵犯权益, other-其他问题
    "description": "string" // 详细描述
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 2.6 添加菜品到厨房
- **接口地址**: `/api/dish/addToKitchen`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string",     // 菜品ID
    "kitchenId": "string",  // 厨房ID
    "categoryId": "string"  // 分类ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "dishId": "string"    // 新生成的菜品ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 3. 购物车模块

#### 3.1 添加菜品到购物车
- **接口地址**: `/api/cart/add`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string",  // 菜品ID
    "count": 1           // 数量
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 3.2 修改购物车菜品数量
- **接口地址**: `/api/cart/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string",  // 菜品ID
    "count": 2           // 新数量
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 3.3 获取购物车列表
- **接口地址**: `/api/cart/list`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "cartList": [
        {
          "id": "string",           // 菜品ID
          "name": "string",         // 菜品名称
          "image": "string",        // 菜品图片
          "price": 36,              // 价格（大米）
          "count": 2,               // 数量
        }
      ],
      "totalPrice": 72,            // 总价（大米）
      "totalCount": 2              // 总数量
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 3.4 清空购物车
- **接口地址**: `/api/cart/clear`
- **请求方式**: POST
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 4. 订单模块

#### 4.1 提交订单
- **接口地址**: `/api/order/submit`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",   // 厨房ID（可选，不传时使用用户当前选择的厨房）
    "tableNo": "string",     // 桌号
    "remark": "string",      // 备注
    "items": [              // 订单菜品（可选，当不提供时使用当前购物车内容）
      {
        "dishId": "string",  // 菜品ID
        "count": 2           // 数量
      }
    ]
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "orderId": "string",   // 订单ID
      "totalPrice": 72       // 订单总价
    },
    "message": ""
  }
  ```
- **特性说明**:
  - 当不提供items参数时，自动使用当前购物车内容作为订单菜品
  - 订单ID基于当前时间戳自动生成，格式为：年月日+4位随机数
  - 订单创建后会立即显示在订单列表中
  - 自动记录订单创建时间、用户信息等基本信息
  
- **状态**: 已实现-可联调
- **更新记录**: 2023-09-10 添加从购物车动态添加商品到订单的功能

#### 4.2 获取订单列表
- **接口地址**: `/api/order/list`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "status": "string",      // 订单状态：all-全部，pending-待接单，cooking-烹饪中，completed-已完成，canceled-已取消
    "page": 1,               // 页码
    "pageSize": 10           // 每页数量
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "list": [
        {
          "orderId": "string",      // 订单ID
          "createTime": "string",   // 创建时间
          "status": "string",       // 订单状态
          "totalPrice": 72,         // 订单总价
          "tableNo": "string",      // 桌号
          "itemCount": 3            // 菜品数量
        }
      ],
      "total": 20,                 // 总订单数
      "pages": 2                   // 总页数
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.3 获取订单详情
- **接口地址**: `/api/order/detail`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "orderId": "string",        // 订单ID
      "createTime": "string",     // 创建时间
      "status": "string",         // 订单状态
      "totalPrice": 72,           // 订单总价
      "tableNo": "string",        // 桌号
      "remark": "string",         // 备注
      "cookingTime": "string",    // 接单时间（仅cooking和completed状态有值）
      "completedTime": "string",  // 完成时间（仅completed状态有值）
      "items": [                  // 订单菜品
        {
          "dishId": "string",      // 菜品ID
          "name": "string",        // 菜品名称
          "image": "string",       // 菜品图片
          "price": 36,             // 价格
          "count": 2,              // 数量
          "tags": ["string"]       // 标签
        }
      ]
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.4 取消订单
- **接口地址**: `/api/order/cancel`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.5 接单
- **接口地址**: `/api/order/accept`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.6 拒绝订单（已被取消订单接口替代）
- **接口地址**: `/api/order/cancel`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调
- **说明**: 原拒绝订单功能已被取消订单接口（`/api/order/cancel`）替代，两者功能相同

#### 4.7 完成订单
- **接口地址**: `/api/order/complete`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.8 取消烹饪（从烹饪中状态回到待接单状态）
- **接口地址**: `/api/order/cancelCooking`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 4.9 开始烹饪
- **接口地址**: `/api/order/startCooking`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderId": "string"  // 订单ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 5. 其他接口

> **注意**：已废弃的接口将从接口文档和代码中移除，请使用推荐的替代接口。

### 6. 搜索模块

#### 6.1 搜索菜品
- **接口地址**: `/api/dish/search`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "keyword": "string",    // 搜索关键词
    "page": 1,              // 页码(可选)
    "pageSize": 10          // 每页数量(可选)
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "list": [
        {
          "id": "string",           // 菜品ID
          "categoryId": "string",   // 分类ID
          "name": "string",         // 菜品名称
          "image": "string",        // 菜品图片
          "price": 36,              // 价格
          "originalPrice": 45,      // 原价
          "description": "string",  // 描述
          "tags": ["string"],       // 标签
          "sales": 238,             // 销量
          "rating": 4.8,            // 评分
          "categoryName": "string"  // 分类名称
        }
      ],
      "total": 20,                  // 总记录数
      "pages": 2                    // 总页数
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 6.2 获取热门搜索关键词
- **接口地址**: `/api/dish/hotKeywords`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "keywords": ["麻辣", "凉菜", "特价", "招牌", "素食"]
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 6.3 获取搜索历史
- **接口地址**: `/api/user/searchHistory`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "history": ["红烧肉", "水煮鱼", "回锅肉"]
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 6.4 清空搜索历史
- **接口地址**: `/api/user/clearSearchHistory`
- **请求方式**: POST
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 7. 用户背景设置模块

#### 7.1 获取用户背景设置
- **接口地址**: `/api/user/backgroundSettings`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "shopBg": "string",              // 店铺背景图片URL
      "navBgStyle": "string",          // 导航栏背景样式（渐变值）
      "navBgIndex": 0                  // 预设背景索引，-1表示自定义
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 7.2 更新用户背景设置
- **接口地址**: `/api/user/updateBackgroundSettings`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "shopBg": "string",               // 店铺背景图片URL（可选）
    "navBgStyle": "string",           // 导航栏背景样式（可选）
    "navBgIndex": 0                   // 预设背景索引（可选）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 7.3 上传背景图片
- **接口地址**: `/api/upload/backgroundImage`
- **请求方式**: POST
- **请求参数**: `FormData包含图片文件`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "imageUrl": "string"        // 上传成功后的图片URL
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 7.4 背景设置功能实现说明
背景设置功能允许用户自定义小程序的视觉风格，包括店铺背景图和导航栏背景色。该功能通过以下步骤实现：

1. **初始化和数据获取**:
   - 打开设置面板时，组件首先从父组件获取当前背景设置
   - 随后调用`getUserBackgroundSettings`API获取用户的最新背景设置
   - 如果API返回有效数据，则使用服务器数据更新本地状态

2. **背景设置处理逻辑**:
   - 店铺背景图：支持从相册或拍照选择新图片，上传到服务器后获取URL
   - 导航栏背景：提供预设渐变背景选择，或允许用户输入自定义颜色
   - 自定义渐变色：当用户选择自定义颜色时，navBgIndex设置为-1

3. **保存流程**:
   - 用户点击保存按钮时，组件将构建完整的背景设置对象
   - 调用`updateUserBackgroundSettings`API将数据保存到服务器
   - 保存成功后，触发save事件并将数据传递给父组件
   - 父组件负责更新全局状态，确保背景设置在整个应用中生效

4. **背景设置数据结构**:
   ```typescript
   interface BackgroundSettings {
     shopBg: string;           // 店铺背景图URL
     navBgStyle: string;       // 导航栏背景样式(渐变值)
     navBgIndex: number;       // 预设背景索引，-1表示自定义
   }
   ```

5. **错误处理**:
   - 图片上传失败：显示错误提示，同时允许用户继续预览本地图片
   - API调用失败：显示错误提示，不影响用户继续进行设置
   - 保存失败：显示错误提示，用户可以重试保存操作

6. **注意事项**:
   - 背景图片上传尺寸建议为750x422像素(16:9)，以获得最佳显示效果
   - 自定义渐变色必须使用十六进制颜色值(如#FFFFFF)
   - 导航栏背景渐变方向固定为从上到下(to bottom)
   - 建议店铺背景图使用较浅色调，以确保文字内容清晰可见

### 8. 菜品管理模块

#### 8.1 添加菜品
- **接口地址**: `/api/dish/add`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",      // 厨房ID（必需）
    "name": "string",           // 菜品名称
    "categoryId": "string",     // 分类ID
    "price": 36,                // 价格（大米）
    "description": "string",    // 描述
    "image": "string",          // 菜品图片
    "imageList": ["string"],    // 菜品图片列表（最多5张）
    "rating": 3,                // 星级评分（1-5）
    "ingredients": [            // 配料清单（可选）
      {
        "name": "string",       // 配料名称
        "amount": "string"      // 配料用量
      }
    ],
    "cookingSteps": [           // 烹饪步骤（可选）
      {
        "title": "string",      // 步骤标题
        "description": "string", // 步骤描述
        "image": "string"       // 步骤图片
      }
    ],
    "nutrition": {              // 营养信息（可选）
      "calories": 650,          // 热量（千卡）
      "protein": "string",      // 蛋白质
      "carbs": "string",        // 碳水化合物
      "fat": "string"           // 脂肪
    }
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 新添加的菜品ID
      "name": "string",         // 菜品名称
      "categoryId": "string",   // 分类ID
      "price": 36,              // 价格
      "description": "string",  // 描述
      "image": "string"         // 菜品图片
    },
    "message": ""
  }
  ```
- **状态**: 已实现-待联调

#### 8.2 上传菜品图片
- **接口地址**: `/api/upload/dishImage`
- **请求方式**: POST
- **请求参数**: `FormData包含图片文件`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "imageUrl": "string"      // 上传成功后的图片URL
    },
    "message": ""
  }
  ```
- **状态**: 已实现-待联调

#### 8.3 添加菜品页面表单验证说明
添加菜品页面包含以下必填字段的验证：
- **菜品名称**: 不能为空
- **菜品分类**: 必须选择一个分类
- **菜品图片**: 至少需要上传一张图片，最多支持5张
- **配料表**: 至少需要添加一种配料

表单验证失败时的处理流程：
1. 显示对应的错误提示
2. 自动滚动到需要修改的表单区域
3. 聚焦到需要修改的表单元素

图片上传限制：
- 每次只能选择一张图片上传
- 总共最多上传5张图片
- 图片大小和格式有相应限制，详见微信小程序接口规范

价格处理：
- 如果用户未填写价格，默认设置为0
- 价格必须为非负数

星级评分默认值为3星，用户可以调整为1-5星

#### 8.4 修改菜品状态
- **接口地址**: `/api/dish/updateStatus`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string",    // 菜品ID
    "disabled": boolean    // 是否禁用：true-下架，false-上架
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "dishId": "string",  // 菜品ID
      "disabled": boolean  // 更新后的禁用状态
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.5 更新菜品排序
- **接口地址**: `/api/dish/updateSort`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "categoryId": "string", // 分类ID，指定更新哪个分类下的菜品排序
    "dishes": [           // 排序后的菜品列表
      {
        "id": "string",   // 菜品ID
        "sort": 1         // 排序值（数值越小越靠前）
      }
    ]
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.6 修改菜品信息
- **接口地址**: `/api/dish/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",      // 厨房ID（必需）
    "id": "string",             // 菜品ID
    "name": "string",           // 菜品名称
    "categoryId": "string",     // 分类ID
    "price": 36,                // 价格（大米）
    "description": "string",    // 描述
    "image": "string",          // 菜品图片
    "imageList": ["string"],    // 菜品图片列表（最多5张）
    "ingredients": [          // 配料清单（可选）
      {
        "name": "string",     // 配料名称
        "amount": "string"    // 配料用量
      }
    ],
    "cookingSteps": [         // 烹饪步骤（可选）
      {
        "title": "string",    // 步骤标题
        "description": "string", // 步骤描述
        "image": "string"     // 步骤图片
      }
    ],
    "nutrition": {            // 营养信息（可选）
      "calories": 650,        // 热量（千卡）
      "protein": "string",    // 蛋白质
      "carbs": "string",      // 碳水化合物
      "fat": "string"         // 脂肪
    }
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "id": "string"          // 修改的菜品ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.7 删除菜品
- **接口地址**: `/api/dish/delete`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "dishId": "string"        // 菜品ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "dishId": "string"      // 删除的菜品ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 8.8 分类管理接口

#### 8.8.1 添加分类
- **接口地址**: `/api/category/add`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",     // 厨房ID（必需）
    "name": "string",          // 分类名称
    "icon": "string",          // 分类图标（可选）
    "sort": 1                  // 排序值（数值越小越靠前）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",         // 新添加的分类ID
      "name": "string",       // 分类名称
      "icon": "string",       // 分类图标
      "sort": 1               // 排序值
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.8.2 更新分类
- **接口地址**: `/api/category/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",     // 厨房ID（必需）
    "id": "string",            // 分类ID
    "name": "string",          // 分类名称
    "icon": "string",          // 分类图标（可选）
    "sort": 1                  // 排序值（可选）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "id": "string"          // 更新的分类ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.8.3 删除分类
- **接口地址**: `/api/category/delete`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "categoryId": "string"    // 分类ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "categoryId": "string"  // 删除的分类ID
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.8.4 更新分类排序
- **接口地址**: `/api/category/updateSort`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "sortList": [           // 排序后的分类列表
      {
        "id": "string",       // 分类ID
        "sort": 1             // 排序值（数值越小越靠前）
      }
    ]
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 8.8.5 批量更新分类
- **接口地址**: `/api/category/batchUpdate`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "categories": [           // 要更新的分类列表
      {
        "id": "string",       // 分类ID
        "name": "string",     // 分类名称
        "icon": "string",     // 分类图标（可选）
        "sort": 1             // 排序值（可选）
      }
    ]
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "updatedCount": 3       // 成功更新的分类数量
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

## 更新记录
- 2025-07-20: 更新接口文档，添加更新厨房信息接口，完善管理厨房功能
- 2025-07-15: 更新接口文档，添加"克隆菜谱"相关接口，更新API状态
- 2025-07-07: 更新接口文档，添加"我的厨房"弹窗功能说明，优化厨房管理模块文档
- 2025-07-05: 更新接口文档，扩展厨房管理接口，添加厨房头像和公告字段，完善厨房创建功能
- 2025-06-30: 更新接口文档，添加厨房管理相关接口
- 2025-06-11: 更新接口文档，添加搜索相关接口
- 2025-06-10: 更新接口文档，添加用户背景设置相关接口
- 2025-06-01: 更新接口文档，标记餐厅信息接口为已完成状态
- 2025-05-30: 更新接口文档，完成全部API实现及mock数据更新，添加接口实现状态总览
- 2025-05-25: 更新接口文档，添加分类管理拖拽排序相关接口，更新接口状态
- 2025-05-20: 更新接口文档，添加菜品管理相关接口
- 2025-05-15: 更新接口文档，更新接口文档，添加搜索相关接口
- 2025-04-11: 首次创建接口文档 

### 9. 厨房管理模块

#### 9.1 获取厨房列表
- **接口地址**: `/api/kitchen/list`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",           // 厨房ID（6位字母数字组合）
        "name": "string",         // 厨房名称
        "level": 1,               // 厨房等级
        "owner": "string",        // 厨房拥有者ID
        "isOwner": true,          // 是否是拥有者
        "kitchenCount": 3,        // 厨房数量
        "kitchenLimit": 5,        // 厨房数量上限
        "categoryCount": 5,       // 分类数量
        "categoryLimit": 5,       // 分类数量上限
        "dishCount": 25,          // 菜品数量
        "dishLimit": 50,          // 菜品数量上限
        "memberCount": 2,         // 成员数量
        "createdAt": "2023-05-20 10:30:00", // 创建时间
        "notice": "string",       // 厨房公告
        "avatarUrl": "string"     // 厨房头像URL
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.2 获取厨房详细信息
- **接口地址**: `/api/kitchen/info`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 厨房ID，不传则返回默认厨房
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 厨房ID
      "name": "string",         // 厨房名称
      "level": 1,               // 厨房等级
      "owner": "string",        // 厨房拥有者ID
      "isOwner": true,          // 是否是拥有者
      "kitchenCount": 3,        // 厨房数量
      "kitchenLimit": 5,        // 厨房数量上限
      "categoryCount": 5,       // 分类数量
      "categoryLimit": 5,       // 分类数量上限
      "dishCount": 25,          // 菜品数量
      "dishLimit": 50,          // 菜品数量上限
      "memberCount": 2,         // 成员数量
      "createdAt": "2023-05-20 10:30:00", // 创建时间
      "notice": "string",       // 厨房公告
      "avatarUrl": "string"     // 厨房头像URL
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.3 创建新厨房
- **接口地址**: `/api/kitchen/create`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "string",           // 厨房名称（必填）
    "notice": "string",         // 厨房公告（可选）
    "avatarUrl": "string"       // 厨房头像URL（可选）
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 新创建的厨房ID
      "name": "string",         // 厨房名称
      "level": 1,               // 厨房等级
      "owner": "string",        // 厨房拥有者ID
      "isOwner": true,          // 是否是拥有者
      "kitchenCount": 1,        // 厨房数量
      "kitchenLimit": 5,        // 厨房数量上限
      "categoryCount": 0,       // 分类数量
      "categoryLimit": 5,       // 分类数量上限
      "dishCount": 0,           // 菜品数量
      "dishLimit": 50,          // 菜品数量上限
      "memberCount": 1,         // 成员数量
      "createdAt": "2023-05-20 10:30:00", // 创建时间
      "notice": "string",       // 厨房公告
      "avatarUrl": "string"     // 厨房头像URL
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.4 加入厨房
- **接口地址**: `/api/kitchen/join`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 要加入的厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.5 退出厨房
- **接口地址**: `/api/kitchen/leave`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 要退出的厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.6 解散厨房
- **接口地址**: `/api/kitchen/dismiss`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 要解散的厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.7 升级厨房
- **接口地址**: `/api/kitchen/upgrade`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 要升级的厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "level": 2,           // 升级后的等级
      "kitchenLimit": 8,    // 升级后的厨房上限
      "categoryLimit": 8,   // 升级后的分类上限
      "dishLimit": 80       // 升级后的菜品上限
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.8 获取厨房成员
- **接口地址**: `/api/kitchen/members`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "userId": "string",      // 用户ID
        "nickName": "string",    // 用户昵称
        "avatarUrl": "string",   // 用户头像
        "role": "owner",         // 角色：owner-拥有者, member-成员
        "joinTime": "string"     // 加入时间
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.9 获取当前厨房基本信息
- **接口地址**: `/api/kitchen/baseInfo`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "name": "string",         // 厨房名称
      "logo": "string",         // 厨房logo URL
      "notice": "string"        // 厨房公告
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.10 获取用户创建的厨房列表
- **接口地址**: `/api/kitchen/owned`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",           // 厨房ID（6位字母数字组合）
        "name": "string",         // 厨房名称
        "level": 1,               // 厨房等级
        "owner": "string",        // 厨房拥有者ID
        "isOwner": true,          // 是否是拥有者（这里始终为true）
        "kitchenCount": 3,        // 厨房数量
        "kitchenLimit": 5,        // 厨房数量上限
        "categoryCount": 5,       // 分类数量
        "categoryLimit": 5,       // 分类数量上限
        "dishCount": 25,          // 菜品数量
        "dishLimit": 50,          // 菜品数量上限
        "memberCount": 2,         // 成员数量
        "createdAt": "2023-05-20 10:30:00", // 创建时间
        "notice": "string",       // 厨房公告
        "avatarUrl": "string"     // 厨房头像URL
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.11 获取用户加入的厨房列表
- **接口地址**: `/api/kitchen/joined`
- **请求方式**: GET
- **请求参数**: `{}`
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",           // 厨房ID（6位字母数字组合）
        "name": "string",         // 厨房名称
        "level": 1,               // 厨房等级
        "owner": "string",        // 厨房拥有者ID
        "isOwner": false,         // 是否是拥有者（这里始终为false）
        "kitchenCount": 3,        // 厨房数量
        "kitchenLimit": 5,        // 厨房数量上限
        "categoryCount": 5,       // 分类数量
        "categoryLimit": 5,       // 分类数量上限
        "dishCount": 25,          // 菜品数量
        "dishLimit": 50,          // 菜品数量上限
        "memberCount": 2,         // 成员数量
        "createdAt": "2023-05-20 10:30:00", // 创建时间
        "notice": "string",       // 厨房公告
        "avatarUrl": "string"     // 厨房头像URL
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.12 更新厨房信息
- **接口地址**: `/api/kitchen/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "kitchenId": "string",     // 厨房ID
    "name": "string",          // 厨房名称
    "notice": "string",        // 厨房公告
    "avatarUrl": "string"      // 厨房头像URL
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,         // 更新成功
      "name": "string",        // 更新后的厨房名称
      "notice": "string",      // 更新后的厨房公告
      "avatarUrl": "string"    // 更新后的厨房头像URL
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调
- **说明**: 只有厨房拥有者可以修改厨房信息，厨房名称每30天只能修改一次

#### 9.13 获取厨房二维码
- **接口地址**: `/api/kitchen/qrcode`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "qrcodeUrl": "string",    // 二维码图片URL
      "expireTime": "string"    // 过期时间
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 9.14 根据ID查找厨房
- **接口地址**: `/api/kitchen/findById`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 厨房ID
      "name": "string",         // 厨房名称
      "level": 1,               // 厨房等级
      "owner": "string",        // 厨房拥有者ID
      "isOwner": false,         // 当前用户是否是拥有者
      "avatarUrl": "string",    // 厨房头像URL
      "notice": "string",       // 厨房公告
      "createdAt": "string"     // 创建时间
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

### 厨房管理功能说明

1. **厨房角色权限**:
   - 厨房拥有者：可以对厨房进行全部操作，包括升级、解散、管理成员等
   - 厨房成员：可以查看厨房信息，管理菜品，但不能修改厨房基本设置

2. **厨房等级体系**:
   - Lv.1 (基础厨房)：最大可拥有5个厨房，5个分类，50个菜品
   - Lv.2：最大可拥有8个厨房，8个分类，80个菜品
   - Lv.3：最大可拥有12个厨房，12个分类，120个菜品
   - 以此类推，每升一级都会增加相应资源上限

3. **升级费用**:
   - 升级到Lv.2：500大米
   - 升级到Lv.3：1000大米
   - 升级到Lv.4：2000大米
   - 升级到Lv.5：5000大米
   - 以此类推，每升一级所需费用翻倍

4. **厨房ID规则**:
   - 厨房ID为6位字母数字组合
   - 创建厨房时系统自动生成，不可修改
   - 用于邀请他人加入厨房

5. **厨房名称规则**:
   - 长度限制：2-15个字符
   - 不得包含敏感词
   - 创建后支持修改，但每30天只能修改一次

6. **厨房切换逻辑**:
   - 用户可以拥有或加入多个厨房
   - 可以随时在不同厨房间切换
   - 当前选中的厨房将作为用户操作的上下文

7. **厨房退出/解散规则**:
   - 退出：成员可以随时退出厨房，退出后无法访问该厨房资源
   - 解散：只有拥有者可以解散厨房，解散后所有数据将被删除
   - 若用户没有任何厨房，系统会自动创建一个默认厨房

8. **我的厨房弹窗**:
   - 功能：集中展示用户创建和加入的所有厨房，提供快速管理功能
   - 分类显示：通过标签页分别展示"我创建的"和"我加入的"厨房
   - 信息展示：包含厨房头像、名称、ID和等级
   - 操作功能：
     - 创建的厨房：提供"解散"操作
     - 加入的厨房：提供"退出"操作
   - 使用标准modal-dialog组件实现，风格统一，交互友好

### 10. 菜谱克隆功能

#### 10.1 搜索厨房信息
- **接口地址**: `/api/kitchen/search`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 要搜索的厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "id": "string",           // 厨房ID
      "name": "string",         // 厨房名称
      "level": 1,               // 厨房等级
      "owner": "string",        // 厨房拥有者ID
      "avatarUrl": "string",    // 厨房头像URL
      "categoryCount": 5,       // 分类数量
      "dishCount": 25           // 菜品数量
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调
- **说明**: 用于查找要克隆菜谱的源厨房，支持使用测试厨房ID：A2B3C4

#### 10.2 获取源厨房分类列表
- **接口地址**: `/api/kitchen/categories`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string"  // 源厨房ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",    // 分类ID
        "name": "string",  // 分类名称
        "icon": "string",  // 分类图标
        "sort": 1,         // 排序值
        "dishCount": 10    // 该分类下的菜品数量
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 10.3 获取源厨房分类下的菜品
- **接口地址**: `/api/kitchen/categoryDishes`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "kitchenId": "string",    // 源厨房ID
    "categoryId": "string"    // 分类ID
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": [
      {
        "id": "string",           // 菜品ID
        "name": "string",         // 菜品名称
        "image": "string",        // 菜品图片
        "price": 36,              // 价格
        "description": "string",  // 描述
        "ingredients": [          // 配料
          {
            "name": "string",     // 配料名称
            "amount": "string"    // 配料用量
          }
        ],
        "nutrition": {            // 营养信息
          "calories": 650,        // 热量
          "protein": "28克",      // 蛋白质
          "carbs": "75克",        // 碳水化合物
          "fat": "25克"           // 脂肪
        }
      }
    ],
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 10.4 克隆菜品到目标厨房
- **接口地址**: `/api/kitchen/cloneDishes`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "sourceKitchenId": "string",   // 源厨房ID
    "targetCategoryId": "string",  // 目标分类ID
    "dishIds": ["string"]          // 要克隆的菜品ID数组
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "success": true,
      "clonedCount": 5,            // 成功克隆的菜品数量
      "failedCount": 0,            // 克隆失败的菜品数量
      "clonedDishes": [            // 成功克隆的菜品列表
        {
          "id": "string",          // 新菜品ID
          "name": "string",        // 菜品名称
          "image": "string"        // 菜品图片
        }
      ]
    },
    "message": ""
  }
  ```
- **状态**: 已实现-可联调

#### 10.5 菜谱克隆功能实现说明
菜谱克隆功能允许用户从其他厨房复制菜谱到自己的厨房，该功能通过以下步骤实现：

1. **克隆流程**:
   - 步骤一：输入源厨房ID，搜索并获取厨房基本信息
   - 步骤二：浏览源厨房的分类，选择需要的分类
   - 步骤三：选择该分类下的菜品，可多选
   - 步骤四：选择目标分类，确认克隆，显示克隆结果

2. **权限控制**:
   - 任何用户均可搜索公开厨房并克隆其菜谱
   - 用户必须拥有足够的菜品配额（受厨房等级限制）
   - 用户只能将菜谱克隆到自己创建或加入的厨房中

3. **数据限制**:
   - 每次最多可选择20个菜品进行克隆
   - 菜品附带的图片、配料、营养信息等数据一并克隆
   - 克隆记录在目标厨房的操作日志中保留

4. **测试说明**:
   - 系统提供测试厨房ID：A2B3C4，包含多种分类和菜品样本
   - 测试厨房不受普通限制，适合功能演示和测试

5. **错误处理**:
   - 厨房ID不存在：显示未找到相关厨房提示
   - 菜品配额不足：显示无法克隆全部菜品，提示升级厨房
   - 克隆失败：返回具体失败原因和成功克隆的菜品数量

## 更新记录
- 2025-07-20: 更新接口文档，添加菜谱克隆功能相关接口，新增测试厨房ID(A2B3C4)说明

## 用户大米相关API

### 获取用户大米信息

**接口地址**：`/api/user/coins`

**请求方式**：GET

**入参**：
```json
{}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "balance": 52000,  // 大米余额
    "total": 58112,    // 累计获得
    "used": 6112       // 已经使用
  },
  "message": ""
}
```

### 充值大米

**接口地址**：`/api/user/recharge`

**请求方式**：POST

**入参**：
```json
{
  "amount": 3000  // 充值数量
}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "success": true,
    "orderId": "ORDER123456789",
    "amount": 3000
  },
  "message": ""
}
```

### 获取大米交易记录

**接口地址**：`/api/user/coinRecords`

**请求方式**：GET

**入参**：
```json
{
  "type": "all"  // 可选值：all-全部, in-收入, out-支出
}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "records": [
      {
        "id": "1001",
        "title": "完成每日签到",
        "time": "2023-08-01 08:30",
        "amount": 100,
        "type": "in"  // in-收入, out-支出
      },
      // 更多记录...
    ]
  },
  "message": ""
}
```

### 获取用户签到数据

**接口地址**：`/api/user/signInData`

**请求方式**：GET

**入参**：
```json
{}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "signInData": [
      {
        "date": "2023-08-01",
        "day": 1,
        "weekday": "周二",
        "signed": true,
        "canSignIn": false,
        "isToday": false
      },
      // 更多日期数据...
    ],
    "signInCount": 15,          // 本月已签到天数
    "continuousSignIn": 5,      // 连续签到天数
    "signInRewards": 360,       // 累计奖励
    "todaySigned": false        // 今日是否已签到
  },
  "message": ""
}
```

### 签到接口

**接口地址**：`/api/user/signIn`

**请求方式**：POST

**入参**：
```json
{}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "success": true,
    "reward": 20,                // 获得的奖励
    "signInData": [              // 更新后的签到数据
      // 同上，包含更新后的签到状态
    ],
    "signInCount": 16,           // 更新后的本月已签到天数
    "continuousSignIn": 6,       // 更新后的连续签到天数
    "signInRewards": 380         // 更新后的累计奖励
  },
  "message": ""
}
```

### 观看广告接口

**接口地址**：`/api/user/watchAd`

**请求方式**：POST

**入参**：
```json
{}
```

**出参**：
```json
{
  "error": 0,
  "body": {
    "success": true,
    "reward": 10            // 获得的奖励
  },
  "message": ""
}
```

<!-- 任务大厅相关接口部分已删除，该功能已通过 GET /api/user/signInData 实现 -->

## 厨房成员管理接口

### 获取厨房成员列表
- 接口名称：getKitchenMembers
- 请求方式：GET
- 接口地址：/api/kitchen/members
- 请求参数：
```json
{
  "kitchenId": "string" // 厨房ID
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "members": [{
      "userId": "string", // 用户ID
      "nickName": "string", // 用户昵称
      "avatarUrl": "string", // 用户头像
      "joinTime": "string", // 加入时间
      "isOwner": boolean, // 是否是创建者
      "permissions": { // 权限配置
        "canEditKitchen": boolean, // 是否可以编辑厨房信息
        "canManageDish": boolean, // 是否可以管理菜品
        "canUpgradeKitchen": boolean // 是否可以升级厨房
      }
    }],
    "total": number // 成员总数
  }
}
```

### 更新成员权限
- 接口名称：updateMemberPermission
- 请求方式：POST
- 接口地址：/api/kitchen/updateMemberPermission
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "memberId": "string", // 用户ID
  "hasPermission": boolean // 是否有权限
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "success": boolean
  }
}
```

### 移除厨房成员
- 接口名称：removeMember
- 请求方式：POST
- 接口地址：/api/kitchen/removeMember
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "memberId": "string" // 要移除的用户ID
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "success": boolean
  }
}
```

**状态说明**：
- error = 0: 请求成功
- error = 401: 需要登录
- error = 403: 无权限操作
- error = 404: 厨房或用户不存在
- error = 500: 服务器错误

**权限说明**：
- 只有厨房创建者可以管理成员权限和移除成员
- 创建者不能被移除
- 成员权限修改立即生效

## 厨房桌号管理接口

### 获取厨房桌号列表
- 接口名称：getTableList
- 请求方式：GET
- 接口地址：/api/kitchen/table/list
- 请求参数：
```json
{
  "kitchenId": "string" // 厨房ID
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": [
    {
      "id": "string", // 桌号ID
      "name": "string", // 桌号名称
      "sort": number // 排序顺序
    }
  ]
}
```

### 添加桌号
- 接口名称：addTable
- 请求方式：POST
- 接口地址：/api/kitchen/table/add
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "name": "string", // 桌号名称
  "sort": number // 排序顺序
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "id": "string", // 新创建的桌号ID
    "name": "string", // 桌号名称
    "sort": number // 排序顺序
  }
}
```

### 更新桌号
- 接口名称：updateTable
- 请求方式：POST
- 接口地址：/api/kitchen/table/update
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "tableId": "string", // 桌号ID
  "name": "string", // 桌号名称
  "sort": number // 排序顺序
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "id": "string", // 桌号ID
    "name": "string", // 桌号名称
    "sort": number // 排序顺序
  }
}
```

### 删除桌号
- 接口名称：deleteTable
- 请求方式：POST
- 接口地址：/api/kitchen/table/delete
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "tableId": "string" // 桌号ID
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "success": true
  }
}
```

### 排序桌号
- 接口名称：sortTables
- 请求方式：POST
- 接口地址：/api/kitchen/table/sort
- 请求参数：
```json
{
  "kitchenId": "string", // 厨房ID
  "tables": [ // 排序后的桌号列表
    {
      "id": "string", // 桌号ID
      "name": "string", // 桌号名称
      "sort": number // 排序顺序
    }
  ]
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "success": true
  }
}
```

**桌号管理说明**：
- 桌号用于点单时指定订单的目标桌位
- 桌号支持自定义名称，如"1号桌"、"大厅2号"、"包间1"等
- 桌号排序影响在点单页面的显示顺序
- 删除桌号不会影响已使用该桌号的历史订单
- 最多支持添加30个桌号

## 用户会员相关接口

_注意：以下接口已移至用户模块，请使用路径为`/api/user/membershipStatus`和`/api/user/subscribeMembership`的接口_

### 获取会员状态
- 接口名称：getMembershipStatus
- 请求方式：GET
- 接口地址：/api/user/membershipStatus
- 请求参数：
```json
{}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "isMember": boolean, // 是否是会员
    "memberType": "string", // 会员类型：monthly-月度会员，yearly-年度会员
    "expireDate": "string", // 到期时间
    "privileges": { // 会员特权
      "customTheme": true, // 自定义主题
      "tableManagement": true // 桌号管理
    }
  }
}
```

### 订阅会员
- 接口名称：subscribeMembership
- 请求方式：POST
- 接口地址：/api/user/subscribeMembership
- 请求参数：
```json
{
  "memberType": "string" // 会员类型：monthly-月度会员，yearly-年度会员
}
```
- 响应参数：
```json
{
  "error": 0,
  "message": "",
  "body": {
    "isMember": true, // 会员状态
    "memberType": "string", // 会员类型
    "expireDate": "string", // 到期时间
    "updatedAt": "string", // 更新时间
    "privileges": { // 会员特权
      "customTheme": true, // 自定义主题
      "tableManagement": true // 桌号管理
    }
  }
}
```

**会员特权说明**：
- 会员特权是餐厅点菜小程序中的高级功能，需要通过消费大米开通会员后才能使用
- 会员是用户级别的，对所有厨房都有效
- 会员分为月度会员和年度会员两种类型：
  - 月度会员：666大米/月，有效期1个月
  - 年度会员：5888大米/年，有效期12个月（相当于月度会员的9折优惠）
- 会员特权包括但不限于：
  - 自定义主题：解锁所有高级主题，并可自定义颜色
  - 桌号管理：支持设置和管理餐厅桌号
- 会员到期后，特权功能将自动失效，可以选择续费继续使用
- 更换会员类型时，新的会员类型将立即生效，原会员有效期不会叠加计算


# 微信小程序API接口文档

## 消息相关API (messageApi.ts)

### 获取消息数量
- 接口名称: `getMessageCount`
- 接口地址: `/api/message/messageCount`
- 描述: 获取未读消息数量
- 请求参数: 无
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      count: number;     // 未读消息数量
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 获取标签页内容
- 接口名称: `getTabContent`
- 接口地址: `/api/message/tabContent`
- 描述: 根据标签类型获取对应内容列表，支持分页
- 请求参数:
  ```typescript
  {
    tab: string;         // 标签类型: 'all'(全部), 'likes'(赞和添加), 'system'(系统消息), 'comment'(用餐评价)
    page: number;        // 页码，从1开始
    pageSize: number;    // 每页条数
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      list: Message[];   // 消息列表
      total: number;     // 总数量
      page: number;      // 当前页码
      pageSize: number;  // 每页条数
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 消息对象结构:
  ```typescript
  interface Message {
    id: string;          // 消息ID
    type: string;        // 消息类型: 'like', 'add', 'system', 'comment'
    title: string;       // 消息标题
    content: string;     // 消息内容
    image: string;       // 图片URL，系统消息可能为空
    time: string;        // 消息时间，格式: 'YYYY-MM-DD HH:MM'
    isRead: boolean;     // 是否已读
  }
  ```
- 状态: 已实现-可联调

### 标记消息为已读
- 接口名称: `markMessageRead`
- 接口地址: `/api/message/markRead`
- 描述: 将单条消息标记为已读状态
- 请求参数:
  ```typescript
  {
    messageId: string;   // 消息ID
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      success: boolean;  // 操作是否成功
      messageId: string; // 消息ID
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 批量标记消息为已读
- 接口名称: `markAllRead`
- 接口地址: `/api/message/markAllRead`
- 描述: 将指定类型或全部消息标记为已读
- 请求参数:
  ```typescript
  {
    type?: string;       // 可选，消息类型: 'like', 'add', 'system', 'comment'，不传则标记所有类型
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      success: boolean;  // 操作是否成功
      type: string;      // 消息类型
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

## 发现页API (discoverApi.ts)

### 获取发现页菜品列表
- 接口名称: `getDiscoverList`
- 接口地址: `/api/discover/list`
- 描述: 获取发现页的菜品列表，支持分页、排序和搜索
- 请求参数:
  ```typescript
  {
    page: number;        // 页码，从1开始
    pageSize: number;    // 每页条数
    sortType: string;    // 排序类型: 'hot'(热门) 或 'time'(时间)
    searchValue?: string; // 可选，搜索关键词
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      list: Dish[];      // 菜品列表
      total: number;     // 总数量
      page: number;      // 当前页码
      pageSize: number;  // 每页条数
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 添加菜品到分类
- 接口名称: `addToDish`
- 接口地址: `/api/discover/addToDish`
- 描述: 将发现页的菜品添加到指定分类
- 请求参数:
  ```typescript
  {
    dishId: string;      // 菜品ID
    categoryId: string;  // 分类ID
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      success: boolean;  // 是否添加成功
      dishId: string;    // 菜品ID
      categoryId: string; // 分类ID
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 获取发现页消息数量
- 接口名称: `getMessageCount`
- 接口地址: `/api/discover/messageCount`
- 描述: 获取发现页的未读消息数量
- 请求参数: 无
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      count: number;     // 未读消息数量
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 获取发现页未读数量
- 接口名称: `getUnreadCounts`
- 接口地址: `/api/discover/unreadCounts`
- 描述: 获取发现页各分类的未读数量
- 请求参数: 无
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      likes: number;     // 赞未读数量
      comments: number;  // 评论未读数量
      system: number;    // 系统消息未读数量
      total: number;     // 总未读数量
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

### 获取发现页标签内容
- 接口名称: `getTabContent`
- 接口地址: `/api/discover/tabContent`
- 描述: 获取发现页指定标签的内容
- 请求参数:
  ```typescript
  {
    tab: string;         // 标签类型: 'recommend'(推荐), 'newest'(最新), 'popular'(流行)
    page: number;        // 页码
    pageSize: number;    // 每页条数
  }
  ```
- 返回数据:
  ```typescript
  {
    error: 0,            // 0表示成功
    body: {
      list: Dish[];      // 菜品列表
      total: number;     // 总数量
      page: number;      // 当前页码
      pageSize: number;  // 每页条数
    },
    message: string      // 错误信息，成功时为空
  }
  ```
- 状态: 已实现-可联调

## 更新记录
- 2025-08-20: 更新接口文档，将会员相关API从厨房级别改为用户级别
- 2025-08-15: 更新接口文档，添加厨房会员相关接口文档
- 2025-08-10: 更新接口文档，添加厨房桌号管理接口文档
- 2025-07-20: 更新接口文档，添加菜谱克隆功能相关接口，新增测试厨房ID(A2B3C4)说明
- 2023-10-01: 更新接口文档，补充缺失的点赞、举报、添加菜品到厨房接口说明
- 2023-10-01: 修正克隆菜品接口路径不一致问题，从/api/dish/cloneDishes修改为/api/kitchen/cloneDishes
- 2023-10-01: 修正成员权限管理接口路径，从/api/kitchen/member/*修改为/api/kitchen/*
- 2023-10-01: 添加获取厨房二维码接口说明
- 2023-10-01: 移动会员相关接口从厨房模块到用户模块，统一API位置
- 2023-10-03: 全面完善接口文档，补充12个缺失的接口文档，包括:
  - 开始烹饪接口: `/api/order/startCooking`
  - 修改菜品状态接口: `/api/dish/updateStatus`
  - 分类管理相关接口: 添加、更新、删除和排序 
  - 菜品管理接口: 更新、删除和排序
  - 根据ID查找厨房接口: `/api/kitchen/findById`
  - 清空搜索历史接口: `/api/user/clearSearchHistory`
  - 发现页相关接口: 消息数量、未读数量、标签内容
- 2023-10-03: 明确标注消息相关API和发现页API的完整URL路径
- 2023-10-03: 统一接口文档的格式和标准，确保所有接口使用相同的描述方式

## 问题反馈API (feedbackApi.ts)

### 提交问题反馈
- **接口地址**: `/api/feedback/submit`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "type": "bug",           // 反馈类型: "bug"(问题反馈), "feature"(功能建议), "other"(其他)
    "description": "string", // 问题描述，必填，最大500字符
    "contact": "string",     // 联系方式，选填
    "images": ["string"],    // 图片URL数组，最多3张
    "deviceInfo": {          // 设备信息
      "platform": "string", // 平台
      "system": "string",    // 系统版本
      "version": "string",   // 微信版本
      "model": "string",     // 设备型号
      "brand": "string",     // 设备品牌
      "screenWidth": 375,    // 屏幕宽度
      "screenHeight": 812    // 屏幕高度
    }
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "feedbackId": "string",    // 反馈ID
      "status": "pending",       // 状态: pending(待处理), processing(处理中), completed(已完成), rejected(已拒绝)
      "submitTime": "string"     // 提交时间，ISO格式
    },
    "message": "反馈提交成功，我们会尽快处理"
  }
  ```
- **状态**: 已实现-待联调

### 上传反馈图片
- **接口地址**: `/api/feedback/upload-image`
- **请求方式**: POST (文件上传)
- **请求参数**: 
  - 文件字段名: `image`
  - 支持格式: jpg, jpeg, png, gif
  - 文件大小限制: 5MB
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "imageUrl": "string"     // 上传后的图片URL
    },
    "message": "图片上传成功"
  }
  ```
- **状态**: 已实现-待联调

### 获取反馈列表（管理员用）
- **接口地址**: `/api/feedback/list`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "page": 1,               // 页码，从1开始
    "pageSize": 10,          // 每页条数
    "type": "bug",           // 可选，反馈类型筛选
    "status": "pending"      // 可选，状态筛选
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "list": [
        {
          "id": "string",          // 反馈ID
          "type": "bug",           // 反馈类型
          "description": "string", // 问题描述
          "contact": "string",     // 联系方式
          "images": ["string"],    // 图片URL数组
          "status": "pending",     // 处理状态
          "submitTime": "string",  // 提交时间
          "userId": "string",      // 用户ID
          "userNickname": "string",// 用户昵称
          "reply": "string",       // 回复内容
          "replyTime": "string"    // 回复时间
        }
      ],
      "total": 100,              // 总数量
      "page": 1,                 // 当前页码
      "pageSize": 10             // 每页条数
    },
    "message": ""
  }
  ```
- **状态**: 已实现-待联调

### 更新反馈状态（管理员用）
- **接口地址**: `/api/feedback/update-status`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "feedbackId": "string",    // 反馈ID
    "status": "completed",     // 新状态: pending, processing, completed, rejected
    "reply": "string"          // 可选，回复内容
  }
  ```
- **响应参数**:
  ```json
  {
    "error": 0,
    "body": {
      "feedbackId": "string",  // 反馈ID
      "status": "completed",   // 更新后的状态
      "reply": "string",       // 回复内容
      "updateTime": "string"   // 更新时间
    },
    "message": "状态更新成功"
  }
  ```
- **状态**: 已实现-待联调

## 更新记录
- 2025-01-15: 新增问题反馈模块接口，包括提交反馈、上传图片、获取反馈列表和更新状态功能
- 2025-08-20: 更新接口文档，将会员相关API从厨房级别改为用户级别
- 2025-08-15: 更新接口文档，添加厨房会员相关接口文档
- 2025-08-10: 更新接口文档，添加厨房桌号管理接口文档
- 2025-07-20: 更新接口文档，添加菜谱克隆功能相关接口，新增测试厨房ID(A2B3C4)说明
- 2023-10-01: 更新接口文档，补充缺失的点赞、举报、添加菜品到厨房接口说明
- 2023-10-01: 修正克隆菜品接口路径不一致问题，从/api/dish/cloneDishes修改为/api/kitchen/cloneDishes
- 2023-10-01: 修正成员权限管理接口路径，从/api/kitchen/member/*修改为/api/kitchen/*
- 2023-10-01: 添加获取厨房二维码接口说明
- 2023-10-01: 移动会员相关接口从厨房模块到用户模块，统一API位置
- 2023-10-03: 全面完善接口文档，补充12个缺失的接口文档，包括:
  - 开始烹饪接口: `/api/order/startCooking`
  - 修改菜品状态接口: `/api/dish/updateStatus`
  - 分类管理相关接口: 添加、更新、删除和排序 
  - 菜品管理接口: 更新、删除和排序
  - 根据ID查找厨房接口: `/api/kitchen/findById`
  - 清空搜索历史接口: `/api/user/clearSearchHistory`
  - 发现页相关接口: 消息数量、未读数量、标签内容
- 2023-10-03: 明确标注消息相关API和发现页API的完整URL路径
- 2023-10-03: 统一接口文档的格式和标准，确保所有接口使用相同的描述方式

## 反馈相关接口

### 1. 提交反馈
- **接口地址**: `/api/feedback/submit`
- **请求方式**: `POST`
- **需要权限**: 需要登录
- **接口描述**: 用户提交问题反馈或功能建议

**请求参数**:
```json
{
  "type": "bug",
  "description": "问题详细描述（最多200字符）",
  "images": ["图片URL数组", "最多3张"],
  "deviceInfo": {
    "platform": "设备平台",
    "system": "系统版本",
    "version": "微信版本",
    "model": "设备型号"
  }
}
```

**返回格式**:
```json
{
  "error": 0,
  "body": {
    "feedbackId": "反馈ID", 
    "status": "pending",
    "submitTime": "提交时间"
  },
  "message": "反馈提交成功，我们会尽快处理"
}
```

### 2. 上传反馈图片
- **接口地址**: `/api/feedback/upload-image`
- **请求方式**: `POST`
- **需要权限**: 需要登录
- **接口描述**: 上传反馈相关图片

**请求参数**: FormData
- `image`: 图片文件（最大5MB，支持jpg/png/gif格式）

**返回格式**:
```json
{
  "error": 0,
  "body": {
    "imageUrl": "图片访问URL"
  },
  "message": "图片上传成功"
}
```

### 3. 获取反馈列表（管理员）
- **接口地址**: `/api/feedback/list`
- **请求方式**: `POST`
- **需要权限**: 管理员权限
- **接口描述**: 获取用户反馈列表

**请求参数**:
```json
{
  "page": 1,
  "pageSize": 10,
  "type": "bug",
  "status": "pending"
}
```

**返回格式**:
```json
{
  "error": 0,
  "body": {
    "list": [
      {
        "id": "反馈ID",
        "type": "反馈类型",
        "description": "问题描述",
        "images": ["图片URL数组"],
        "status": "处理状态",
        "submitTime": "提交时间",
        "userId": "用户ID",
        "userNickname": "用户昵称",
        "reply": "管理员回复",
        "replyTime": "回复时间",
        "deviceInfo": {}
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 4. 更新反馈状态（管理员）
- **接口地址**: `/api/feedback/update-status`
- **请求方式**: `POST`
- **需要权限**: 管理员权限
- **接口描述**: 更新反馈处理状态和回复

**请求参数**:
```json
{
  "feedbackId": "反馈ID",
  "status": "completed",
  "reply": "管理员回复内容（可选）"
}
```

**返回格式**:
```json
{
  "error": 0,
  "body": {
    "feedbackId": "反馈ID",
    "status": "更新后状态",
    "reply": "回复内容",
    "updateTime": "更新时间"
  },
  "message": "状态更新成功"
}
```