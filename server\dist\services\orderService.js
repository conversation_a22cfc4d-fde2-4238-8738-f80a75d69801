"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = __importDefault(require("../config/database"));
const error_1 = require("../middlewares/error");
const models_1 = require("../models");
const helpers_1 = require("../utils/helpers");
const userService_1 = __importDefault(require("./userService"));
/**
 * 提交订单
 * @param userId 用户ID
 * @param kitchenId 厨房ID
 * @param tableNo 桌号
 * @param remark 备注
 * @returns 订单信息
 */
const submitOrder = (userId, kitchenId, tableNo, remark) => __awaiter(void 0, void 0, void 0, function* () {
    // 检查厨房是否存在
    const kitchen = yield models_1.Kitchen.findByPk(kitchenId);
    if (!kitchen) {
        throw new error_1.BusinessError('厨房不存在');
    }
    // 注意：在分享厨房模式下，用户可能不是厨房成员，但仍然可以下单
    // 这是正常的业务逻辑，不需要检查厨房成员身份
    // 检查桌号是否存在（仅当桌号不是"未选择"时验证）
    if (tableNo && tableNo !== '未选择') {
        const table = yield models_1.Table.findOne({
            where: {
                kitchen_id: kitchenId,
                name: tableNo,
            },
        });
        if (!table) {
            throw new error_1.BusinessError('桌号不存在');
        }
    }
    // 获取购物车项
    const cartItems = yield models_1.CartItem.findAll({
        where: {
            user_id: userId,
            kitchen_id: kitchenId,
        },
        include: [
            {
                model: models_1.Dish,
                as: 'dish',
            },
        ],
    });
    if (cartItems.length === 0) {
        throw new error_1.BusinessError('购物车为空');
    }
    // 计算订单总价
    let totalPrice = 0;
    for (const item of cartItems) {
        if (!item.dish || item.dish.status !== 'on') {
            throw new error_1.BusinessError(`菜品 ${item.dish_id} 不存在或已下架`);
        }
        totalPrice += item.dish.price * item.count;
    }
    // 检查用户大米是否足够
    const user = yield models_1.User.findByPk(userId);
    if (!user) {
        throw new error_1.BusinessError('用户不存在');
    }
    if (user.coins < totalPrice) {
        throw new error_1.BusinessError('大米不足，无法下单');
    }
    // 创建订单
    const transaction = yield database_1.default.transaction();
    try {
        // 生成订单ID
        const orderId = (0, helpers_1.generateOrderId)();
        // 创建订单
        const order = yield models_1.Order.create({
            id: orderId,
            user_id: userId,
            kitchen_id: kitchenId,
            total_price: totalPrice,
            status: 'pending',
            table_no: tableNo,
            remark: remark || '',
        }, { transaction });
        // 创建订单项
        for (const item of cartItems) {
            yield models_1.OrderItem.create({
                order_id: orderId,
                dish_id: item.dish_id,
                name: item.dish.name,
                image: item.dish.image,
                price: item.dish.price,
                count: item.count,
                tags: item.dish.tags,
            }, { transaction });
        }
        // 清空购物车
        yield models_1.CartItem.destroy({
            where: {
                user_id: userId,
                kitchen_id: kitchenId,
            },
            transaction,
        });
        // 扣除用户大米
        yield user.update({
            coins: user.coins - totalPrice,
        }, { transaction });
        // 记录交易（创建交易记录）
        yield userService_1.default.createTransaction(userId, 'out', totalPrice, `订单支付 #${orderId}`, transaction);
        yield transaction.commit();
        return {
            orderId,
            totalPrice,
        };
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
/**
 * 获取订单列表
 * @param userId 用户ID
 * @param kitchenId 厨房ID（可选）
 * @param status 订单状态（可选）
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 订单列表
 */
const getOrderList = (userId_1, kitchenId_1, status_1, ...args_1) => __awaiter(void 0, [userId_1, kitchenId_1, status_1, ...args_1], void 0, function* (userId, kitchenId, status, page = 1, pageSize = 10) {
    // 构建查询条件
    const where = {};
    // 根据用户角色确定查询条件
    if (kitchenId) {
        // 查询厨房订单
        where.kitchen_id = kitchenId;
        // 检查用户是否是厨房成员
        const member = yield models_1.KitchenMember.findOne({
            where: {
                user_id: userId,
                kitchen_id: kitchenId,
            },
        });
        if (!member) {
            throw new error_1.BusinessError('没有权限查看该厨房的订单');
        }
    }
    else {
        // 查询用户自己的订单
        where.user_id = userId;
    }
    // 根据状态筛选
    if (status && status !== 'all') {
        where.status = status;
    }
    // 分页参数
    const { limit, offset } = (0, helpers_1.getPagination)(page, pageSize);
    // 查询订单
    const { count, rows } = yield models_1.Order.findAndCountAll({
        where,
        include: [
            {
                model: models_1.OrderItem,
                as: 'items',
            },
            {
                model: models_1.User,
                as: 'user',
                attributes: ['id', 'nick_name', 'avatar_url'],
            },
            {
                model: models_1.Kitchen,
                as: 'kitchen',
                attributes: ['id', 'name', 'avatar_url'],
            },
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset,
        distinct: true,
    });
    // 构建返回结果
    const orders = rows.map(order => {
        var _a, _b, _c, _d, _e;
        return ({
            id: order.id,
            userId: order.user_id,
            userName: (_a = order.user) === null || _a === void 0 ? void 0 : _a.nick_name,
            userAvatar: (_b = order.user) === null || _b === void 0 ? void 0 : _b.avatar_url,
            kitchenId: order.kitchen_id,
            kitchenName: (_c = order.kitchen) === null || _c === void 0 ? void 0 : _c.name,
            kitchenAvatar: (_d = order.kitchen) === null || _d === void 0 ? void 0 : _d.avatar_url,
            totalPrice: order.total_price,
            status: order.status,
            tableNo: order.table_no,
            remark: order.remark,
            createdAt: order.created_at,
            items: ((_e = order.items) === null || _e === void 0 ? void 0 : _e.map(item => ({
                id: item.id,
                dishId: item.dish_id,
                name: item.name,
                image: item.image,
                price: item.price,
                count: item.count,
            }))) || [],
        });
    });
    return (0, helpers_1.buildPaginationResult)(orders, count, page, pageSize);
});
/**
 * 获取订单详情
 * @param userId 用户ID
 * @param orderId 订单ID
 * @returns 订单详情
 */
const getOrderDetail = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId, {
        include: [
            {
                model: models_1.OrderItem,
                as: 'items',
            },
            {
                model: models_1.User,
                as: 'user',
                attributes: ['id', 'nick_name', 'avatar_url'],
            },
            {
                model: models_1.Kitchen,
                as: 'kitchen',
                attributes: ['id', 'name', 'avatar_url'],
            },
        ],
    });
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    if (order.user_id !== userId) {
        // 如果不是订单所有者，检查是否是厨房成员
        const member = yield models_1.KitchenMember.findOne({
            where: {
                user_id: userId,
                kitchen_id: order.kitchen_id,
            },
        });
        if (!member) {
            throw new error_1.BusinessError('没有权限查看该订单');
        }
    }
    // 构建返回结果
    return {
        id: order.id,
        userId: order.user_id,
        userName: (_a = order.user) === null || _a === void 0 ? void 0 : _a.nick_name,
        userAvatar: (_b = order.user) === null || _b === void 0 ? void 0 : _b.avatar_url,
        kitchenId: order.kitchen_id,
        kitchenName: (_c = order.kitchen) === null || _c === void 0 ? void 0 : _c.name,
        kitchenAvatar: (_d = order.kitchen) === null || _d === void 0 ? void 0 : _d.avatar_url,
        totalPrice: order.total_price,
        status: order.status,
        tableNo: order.table_no,
        remark: order.remark,
        cookingTime: order.cooking_time,
        completedTime: order.completed_time,
        createdAt: order.created_at,
        items: ((_e = order.items) === null || _e === void 0 ? void 0 : _e.map(item => ({
            id: item.id,
            dishId: item.dish_id,
            name: item.name,
            image: item.image,
            price: item.price,
            count: item.count,
            tags: item.tags ? item.tags.split(',') : [],
        }))) || [],
    };
});
/**
 * 获取订单详情（公开访问，用于分享）
 * @param orderId 订单ID
 * @returns 订单详情
 */
const getPublicOrderDetail = (orderId) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId, {
        include: [
            {
                model: models_1.OrderItem,
                as: 'items',
            },
            {
                model: models_1.User,
                as: 'user',
                attributes: ['id', 'nick_name', 'avatar_url'],
            },
            {
                model: models_1.Kitchen,
                as: 'kitchen',
                attributes: ['id', 'name', 'avatar_url'],
            },
        ],
    });
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 公开访问不进行权限检查，任何人都可以查看分享的订单
    // 构建返回结果
    return {
        id: order.id,
        userId: order.user_id,
        userName: (_a = order.user) === null || _a === void 0 ? void 0 : _a.nick_name,
        userAvatar: (_b = order.user) === null || _b === void 0 ? void 0 : _b.avatar_url,
        kitchenId: order.kitchen_id,
        kitchenName: (_c = order.kitchen) === null || _c === void 0 ? void 0 : _c.name,
        kitchenAvatar: (_d = order.kitchen) === null || _d === void 0 ? void 0 : _d.avatar_url,
        totalPrice: order.total_price,
        status: order.status,
        tableNo: order.table_no,
        remark: order.remark,
        cookingTime: order.cooking_time,
        completedTime: order.completed_time,
        createdAt: order.created_at,
        items: ((_e = order.items) === null || _e === void 0 ? void 0 : _e.map(item => ({
            id: item.id,
            dishId: item.dish_id,
            name: item.name,
            image: item.image,
            price: item.price,
            count: item.count,
            tags: item.tags ? item.tags.split(',') : [],
        }))) || [],
    };
});
/**
 * 取消订单
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const cancelOrder = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    if (order.user_id !== userId) {
        throw new error_1.BusinessError('没有权限取消该订单');
    }
    // 检查订单状态
    if (order.status !== 'pending') {
        throw new error_1.BusinessError('只能取消待接单状态的订单');
    }
    // 开始事务
    const transaction = yield database_1.default.transaction();
    try {
        // 取消订单
        yield order.update({
            status: 'cancelled',
        }, { transaction });
        // 退还大米
        const user = yield models_1.User.findByPk(userId);
        if (user) {
            yield user.update({
                coins: user.coins + order.total_price,
            }, { transaction });
            // 记录交易
            yield userService_1.default.createTransaction(userId, 'in', order.total_price, `订单退款 #${orderId}`, transaction);
        }
        yield transaction.commit();
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
/**
 * 接单
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const acceptOrder = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限接单');
    }
    // 检查订单状态
    if (order.status !== 'pending') {
        throw new error_1.BusinessError('只能接待接单状态的订单');
    }
    // 接单
    yield order.update({
        status: 'accepted',
    });
});
/**
 * 开始烹饪
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const startCooking = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限开始烹饪');
    }
    // 检查订单状态
    if (order.status !== 'accepted') {
        throw new error_1.BusinessError('只能开始已接单状态的订单');
    }
    // 开始烹饪
    yield order.update({
        status: 'cooking',
        cooking_time: new Date(),
    });
});
/**
 * 完成订单
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const completeOrder = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限完成订单');
    }
    // 检查订单状态
    if (order.status !== 'cooking') {
        throw new error_1.BusinessError('只能完成烹饪中状态的订单');
    }
    // 开始事务
    const transaction = yield database_1.default.transaction();
    try {
        // 完成订单
        yield order.update({
            status: 'completed',
            completed_time: new Date(),
        }, { transaction });
        // 获取订单项
        const orderItems = yield models_1.OrderItem.findAll({
            where: { order_id: orderId },
            transaction,
        });
        // 更新菜品销量
        for (const item of orderItems) {
            // 检查dish_id是否存在
            if (item.dish_id !== null) {
                const dish = yield models_1.Dish.findByPk(item.dish_id, { transaction });
                if (dish) {
                    yield dish.update({
                        sales: dish.sales + item.count,
                    }, { transaction });
                }
            }
        }
        // 更新厨房订单统计
        const kitchen = yield models_1.Kitchen.findByPk(order.kitchen_id, { transaction });
        if (kitchen) {
            yield kitchen.update({
                order_count: kitchen.order_count + 1,
                total_sales: kitchen.total_sales + order.total_price,
            }, { transaction });
            // 给厨房主分配大米收入
            const kitchenOwner = yield models_1.User.findByPk(kitchen.owner_id, { transaction });
            if (kitchenOwner) {
                // 厨房主获得订单金额的大米收入
                yield kitchenOwner.update({
                    coins: kitchenOwner.coins + order.total_price,
                }, { transaction });
                // 记录厨房主的收入交易
                yield userService_1.default.createTransaction(kitchen.owner_id, 'in', order.total_price, `订单收入 #${orderId}`, transaction);
                console.log(`厨房主 ${kitchen.owner_id} 获得订单收入 ${order.total_price} 大米`);
            }
        }
        yield transaction.commit();
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
/**
 * 取消烹饪
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const cancelCooking = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限取消烹饪');
    }
    // 检查订单状态
    if (order.status !== 'cooking') {
        throw new error_1.BusinessError('只能取消烹饪中状态的订单');
    }
    // 取消烹饪，回到已接单状态
    yield order.update({
        status: 'accepted',
        cooking_time: null,
    });
});
/**
 * 取消已接单订单
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const cancelAcceptedOrder = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限取消接单');
    }
    // 检查订单状态
    if (order.status !== 'accepted') {
        throw new error_1.BusinessError('只能取消已接单状态的订单');
    }
    // 取消接单，回到待接单状态
    yield order.update({
        status: 'pending',
    });
});
/**
 * 拒绝订单（厨房主拒绝接单）
 * @param userId 用户ID
 * @param orderId 订单ID
 */
const rejectOrder = (userId, orderId) => __awaiter(void 0, void 0, void 0, function* () {
    // 查询订单
    const order = yield models_1.Order.findByPk(orderId);
    if (!order) {
        throw new error_1.BusinessError('订单不存在');
    }
    // 检查权限 - 只有厨房成员可以拒绝订单
    const member = yield models_1.KitchenMember.findOne({
        where: {
            user_id: userId,
            kitchen_id: order.kitchen_id,
        },
    });
    if (!member || !member.canEdit()) {
        throw new error_1.BusinessError('没有权限拒绝该订单');
    }
    // 检查订单状态
    if (order.status !== 'pending') {
        throw new error_1.BusinessError('只能拒绝待接单状态的订单');
    }
    // 开始事务
    const transaction = yield database_1.default.transaction();
    try {
        // 拒绝订单，设置状态为已取消
        yield order.update({
            status: 'cancelled',
        }, { transaction });
        // 退还大米给下单用户
        const user = yield models_1.User.findByPk(order.user_id);
        if (user) {
            yield user.update({
                coins: user.coins + order.total_price,
            }, { transaction });
            // 记录交易
            yield userService_1.default.createTransaction(order.user_id, 'in', order.total_price, `订单被拒绝退款 #${orderId}`, transaction);
        }
        yield transaction.commit();
    }
    catch (error) {
        yield transaction.rollback();
        throw error;
    }
});
exports.default = {
    submitOrder,
    getOrderList,
    getOrderDetail,
    getPublicOrderDetail,
    cancelOrder,
    acceptOrder,
    startCooking,
    completeOrder,
    cancelCooking,
    cancelAcceptedOrder,
    rejectOrder,
};
//# sourceMappingURL=orderService.js.map