"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 创建数据库表脚本
 * 按照正确的顺序逐个创建表，避免外键约束问题
 */
const database_1 = __importDefault(require("../../config/database"));
const logger_1 = __importDefault(require("../../utils/logger"));
const models = __importStar(require("../../models"));
/**
 * 创建所有数据库表
 */
function createTables() {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.default.info('开始创建数据库表...');
        try {
            // 禁用外键检查
            logger_1.default.info('正在禁用外键检查...');
            yield database_1.default.query('SET FOREIGN_KEY_CHECKS = 0');
            // 删除所有表
            logger_1.default.info('正在删除所有表...');
            yield database_1.default.getQueryInterface().dropAllTables();
            // 按照正确的顺序创建表
            logger_1.default.info('正在按顺序创建表...');
            // 1. 用户相关表（无外键依赖）
            logger_1.default.info('创建用户表...');
            yield models.User.sync({ force: true });
            // 2. 厨房表（依赖用户表）
            logger_1.default.info('创建厨房表...');
            yield models.Kitchen.sync({ force: true });
            // 3. 分类表（依赖厨房表）
            logger_1.default.info('创建分类表...');
            yield models.Category.sync({ force: true });
            // 4. 菜品表（依赖厨房表和分类表）
            logger_1.default.info('创建菜品表...');
            yield models.Dish.sync({ force: true });
            // 5. 用户相关从表
            logger_1.default.info('创建用户相关从表...');
            yield models.Membership.sync({ force: true });
            yield models.Transaction.sync({ force: true });
            yield models.SignIn.sync({ force: true });
            yield models.BackgroundSetting.sync({ force: true });
            yield models.SearchHistory.sync({ force: true });
            yield models.AdView.sync({ force: true });
            // 6. 厨房相关从表
            logger_1.default.info('创建厨房相关从表...');
            yield models.KitchenMember.sync({ force: true });
            yield models.Table.sync({ force: true });
            // 7. 菜品相关从表
            logger_1.default.info('创建菜品相关从表...');
            yield models.DishImage.sync({ force: true });
            yield models.Ingredient.sync({ force: true });
            yield models.Nutrition.sync({ force: true });
            yield models.CookingStep.sync({ force: true });
            yield models.Like.sync({ force: true });
            yield models.Report.sync({ force: true });
            yield models.Comment.sync({ force: true });
            yield models.HotKeyword.sync({ force: true });
            // 8. 订单相关表
            logger_1.default.info('创建订单相关表...');
            try {
                logger_1.default.info('创建购物车项表...');
                yield models.CartItem.sync({ force: true });
            }
            catch (error) {
                logger_1.default.error('创建购物车项表失败:');
                if (error instanceof Error) {
                    logger_1.default.error(`错误消息: ${error.message}`);
                    logger_1.default.error(`错误堆栈: ${error.stack}`);
                }
                throw error;
            }
            try {
                logger_1.default.info('创建订单表...');
                yield models.Order.sync({ force: true });
            }
            catch (error) {
                logger_1.default.error('创建订单表失败:');
                if (error instanceof Error) {
                    logger_1.default.error(`错误消息: ${error.message}`);
                    logger_1.default.error(`错误堆栈: ${error.stack}`);
                }
                throw error;
            }
            try {
                logger_1.default.info('创建订单项表...');
                yield models.OrderItem.sync({ force: true });
            }
            catch (error) {
                logger_1.default.error('创建订单项表失败:');
                if (error instanceof Error) {
                    logger_1.default.error(`错误消息: ${error.message}`);
                    logger_1.default.error(`错误堆栈: ${error.stack}`);
                }
                throw error;
            }
            // 9. 消息相关表
            logger_1.default.info('创建消息相关表...');
            yield models.Message.sync({ force: true });
            yield models.DiscoverItem.sync({ force: true });
            // 10. 系统相关表
            logger_1.default.info('创建系统相关表...');
            yield models.SystemSetting.sync({ force: true });
            yield models.PaymentOrder.sync({ force: true });
            // 11. 反馈相关表
            logger_1.default.info('创建反馈相关表...');
            yield models.Feedback.sync({ force: true });
            // 初始化模型关联关系
            logger_1.default.info('初始化模型关联关系...');
            models.initializeAssociations();
            // 启用外键检查
            logger_1.default.info('正在启用外键检查...');
            yield database_1.default.query('SET FOREIGN_KEY_CHECKS = 1');
            logger_1.default.info('所有数据库表创建成功');
        }
        catch (error) {
            logger_1.default.error('创建数据库表失败:');
            if (error instanceof Error) {
                logger_1.default.error(`错误消息: ${error.message}`);
                logger_1.default.error(`错误堆栈: ${error.stack}`);
            }
            else {
                logger_1.default.error(`未知错误: ${error}`);
            }
            throw error;
        }
    });
}
// 如果直接运行此脚本，则执行创建表
if (require.main === module) {
    createTables()
        .then(() => {
        logger_1.default.info('创建数据库表脚本执行完成');
        process.exit(0);
    })
        .catch((error) => {
        logger_1.default.error('创建数据库表脚本执行失败:', error);
        process.exit(1);
    });
}
exports.default = createTables;
//# sourceMappingURL=createTables.js.map