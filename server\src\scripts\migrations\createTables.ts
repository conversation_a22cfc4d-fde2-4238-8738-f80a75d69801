/**
 * 创建数据库表脚本
 * 按照正确的顺序逐个创建表，避免外键约束问题
 */
import sequelize from '../../config/database';
import logger from '../../utils/logger';
import * as models from '../../models';

/**
 * 创建所有数据库表
 */
async function createTables() {
  logger.info('开始创建数据库表...');

  try {
    // 禁用外键检查
    logger.info('正在禁用外键检查...');
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 0');

    // 删除所有表
    logger.info('正在删除所有表...');
    await sequelize.getQueryInterface().dropAllTables();

    // 按照正确的顺序创建表
    logger.info('正在按顺序创建表...');

    // 1. 用户相关表（无外键依赖）
    logger.info('创建用户表...');
    await models.User.sync({ force: true });

    // 2. 厨房表（依赖用户表）
    logger.info('创建厨房表...');
    await models.Kitchen.sync({ force: true });

    // 3. 分类表（依赖厨房表）
    logger.info('创建分类表...');
    await models.Category.sync({ force: true });

    // 4. 菜品表（依赖厨房表和分类表）
    logger.info('创建菜品表...');
    await models.Dish.sync({ force: true });

    // 5. 用户相关从表
    logger.info('创建用户相关从表...');
    await models.Membership.sync({ force: true });
    await models.Transaction.sync({ force: true });
    await models.SignIn.sync({ force: true });
    await models.BackgroundSetting.sync({ force: true });
    await models.SearchHistory.sync({ force: true });
    await models.AdView.sync({ force: true });

    // 6. 厨房相关从表
    logger.info('创建厨房相关从表...');
    await models.KitchenMember.sync({ force: true });
    await models.Table.sync({ force: true });

    // 7. 菜品相关从表
    logger.info('创建菜品相关从表...');
    await models.DishImage.sync({ force: true });
    await models.Ingredient.sync({ force: true });
    await models.Nutrition.sync({ force: true });
    await models.CookingStep.sync({ force: true });
    await models.Like.sync({ force: true });
    await models.Report.sync({ force: true });
    await models.Comment.sync({ force: true });
    await models.HotKeyword.sync({ force: true });

    // 8. 订单相关表
    logger.info('创建订单相关表...');
    try {
      logger.info('创建购物车项表...');
      await models.CartItem.sync({ force: true });
    } catch (error) {
      logger.error('创建购物车项表失败:');
      if (error instanceof Error) {
        logger.error(`错误消息: ${error.message}`);
        logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }

    try {
      logger.info('创建订单表...');
      await models.Order.sync({ force: true });
    } catch (error) {
      logger.error('创建订单表失败:');
      if (error instanceof Error) {
        logger.error(`错误消息: ${error.message}`);
        logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }

    try {
      logger.info('创建订单项表...');
      await models.OrderItem.sync({ force: true });
    } catch (error) {
      logger.error('创建订单项表失败:');
      if (error instanceof Error) {
        logger.error(`错误消息: ${error.message}`);
        logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }

    // 9. 消息相关表
    logger.info('创建消息相关表...');
    await models.Message.sync({ force: true });
    await models.DiscoverItem.sync({ force: true });

    // 10. 系统相关表
    logger.info('创建系统相关表...');
    await models.SystemSetting.sync({ force: true });
    await models.PaymentOrder.sync({ force: true });

    // 11. 反馈相关表
    logger.info('创建反馈相关表...');
    await models.Feedback.sync({ force: true });

    // 初始化模型关联关系
    logger.info('初始化模型关联关系...');
    models.initializeAssociations();

    // 启用外键检查
    logger.info('正在启用外键检查...');
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 1');

    logger.info('所有数据库表创建成功');
  } catch (error) {
    logger.error('创建数据库表失败:');
    if (error instanceof Error) {
      logger.error(`错误消息: ${error.message}`);
      logger.error(`错误堆栈: ${error.stack}`);
    } else {
      logger.error(`未知错误: ${error}`);
    }
    throw error;
  }
}

// 如果直接运行此脚本，则执行创建表
if (require.main === module) {
  createTables()
    .then(() => {
      logger.info('创建数据库表脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('创建数据库表脚本执行失败:', error);
      process.exit(1);
    });
}

export default createTables;
