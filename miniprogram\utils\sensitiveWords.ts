/**
 * 敏感词库
 * 根据不同内容类型分类管理敏感词
 */

// 政治敏感词
const politicalWords: string[] = [
  '政治', '政府', '官员', '贪污', '腐败', '示威', '游行', '抗议',
  '革命', '推翻', '暴动', '独立', '分裂', '反政府', '颠覆'
];

// 色情相关词汇
const pornographicWords: string[] = [
  '色情', '黄色', '裸体', '性爱', '做爱', '性交', '淫荡', '骚',
  '妓女', '嫖娼', '性服务', '援交', '包养', '一夜情', '约炮'
];

// 暴力相关词汇
const violenceWords: string[] = [
  '杀死', '杀害', '杀人', '谋杀', '暴力', '打死', '砍死', '捅死',
  '自杀', '死亡', '血腥', '残忍', '酷刑', '虐待', '伤害', '攻击'
];

// 违法犯罪词汇
const illegalWords: string[] = [
  '毒品', '吸毒', '贩毒', '制毒', '海洛因', '冰毒', '摇头丸', '大麻',
  '赌博', '赌场', '老虎机', '博彩', '洗钱', '诈骗', '传销', '非法集资',
  '走私', '偷渡', '黑社会', '黑帮', '枪支', '炸弹', '爆炸物'
];

// 歧视性词汇
const discriminatoryWords: string[] = [
  '种族歧视', '性别歧视', '地域歧视', '残疾歧视', '智障', '傻逼', '白痴',
  '弱智', '神经病', '精神病', '疯子', '废物', '垃圾', '贱人'
];

// 宗教极端词汇
const religiousWords: string[] = [
  '宗教极端', '恐怖主义', '圣战', '异教徒', '邪教', '法轮功',
  '宗教冲突', '宗教仇恨', '亵渎神明'
];

// 网络欺凌词汇
const cyberbullyingWords: string[] = [
  '滚', '死去', '去死', '找死', '该死', '活该', '蠢货', '废柴',
  'loser', '屌丝', '土鳖', '装逼', '傻叉', '煞笔', '脑残'
];

// 广告营销词汇
const advertisingWords: string[] = [
  '加微信', '微信号', 'QQ号', '电话', '联系方式', '代刷', '代练',
  '兼职', '招聘', '赚钱', '投资理财', '股票', '彩票', '贷款',
  '信用卡', 'pos机', '办证', '发票', '外挂', '辅助软件'
];

// 敏感词分类常量
export const SensitiveWordCategories = {
  POLITICAL: 'political',        // 政治敏感
  PORNOGRAPHIC: 'pornographic', // 色情内容
  VIOLENCE: 'violence',         // 暴力内容
  ILLEGAL: 'illegal',           // 违法犯罪
  DISCRIMINATORY: 'discriminatory', // 歧视性语言
  RELIGIOUS: 'religious',       // 宗教极端
  CYBERBULLYING: 'cyberbullying', // 网络欺凌
  ADVERTISING: 'advertising',   // 广告营销
};

// 获取所有敏感词（合并所有分类）
export function getAllSensitiveWords(): string[] {
  const allWords: string[] = [];
  
  // 合并所有分类的敏感词
  const allCategories = [
    politicalWords,
    pornographicWords,
    violenceWords,
    illegalWords,
    discriminatoryWords,
    religiousWords,
    cyberbullyingWords,
    advertisingWords
  ];
  
  for (let i = 0; i < allCategories.length; i++) {
    const categoryWords = allCategories[i];
    for (let j = 0; j < categoryWords.length; j++) {
      allWords.push(categoryWords[j]);
    }
  }
  
  // 去重
  const uniqueWords: string[] = [];
  for (let i = 0; i < allWords.length; i++) {
    const word = allWords[i];
    let exists = false;
    for (let j = 0; j < uniqueWords.length; j++) {
      if (uniqueWords[j] === word) {
        exists = true;
        break;
      }
    }
    if (!exists) {
      uniqueWords.push(word);
    }
  }
  
  return uniqueWords;
}

// 根据内容类型获取对应的敏感词
export function getSensitiveWordsByType(type: 'username' | 'content' | 'dish' | 'comment'): string[] {
  let targetWords: string[] = [];
  
  if (type === 'username') {
    // 用户名检查：政治、色情、歧视、网络欺凌
    targetWords = targetWords.concat(politicalWords);
    targetWords = targetWords.concat(pornographicWords);
    targetWords = targetWords.concat(discriminatoryWords);
    targetWords = targetWords.concat(cyberbullyingWords);
  } else if (type === 'content') {
    // 内容检查：除广告外的所有类型
    targetWords = targetWords.concat(politicalWords);
    targetWords = targetWords.concat(pornographicWords);
    targetWords = targetWords.concat(violenceWords);
    targetWords = targetWords.concat(illegalWords);
    targetWords = targetWords.concat(discriminatoryWords);
    targetWords = targetWords.concat(religiousWords);
    targetWords = targetWords.concat(cyberbullyingWords);
  } else if (type === 'dish') {
    // 菜品名称检查：相对宽松，主要检查明显不当内容
    targetWords = targetWords.concat(pornographicWords);
    targetWords = targetWords.concat(violenceWords);
    targetWords = targetWords.concat(illegalWords);
    targetWords = targetWords.concat(discriminatoryWords);
  } else if (type === 'comment') {
    // 评论检查：包含所有类型
    return getAllSensitiveWords();
  } else {
    return getAllSensitiveWords();
  }
  
  return targetWords;
} 