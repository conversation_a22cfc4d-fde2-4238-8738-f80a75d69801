// 导入API
import { login } from '../../api/userApi'
import { createKitchen } from '../../api/kitchenApi'
import { addCategory } from '../../api/dishApi'
import { DEFAULT_IMAGES } from '../../utils/constants'
import { getKitchenList } from '../../api/kitchenApi'

const app = getApp<IAppOption>()

Page({
  data: {
    isLogining: false,
    showPrivacyModal: false,
    errorMessage: '', // 添加错误消息状态
    retryCount: 0,    // 添加重试计数器
    defaultImages: DEFAULT_IMAGES, // 添加默认图片配置
  },
  
  // 登录按钮点击
  async onLogin() {
    // 避免重复请求
    if (this.data.isLogining) {
      console.log('登录请求正在进行中，避免重复请求');
      return
    }
    
    this.setData({
      isLogining: true,
      errorMessage: '' // 清除之前的错误消息
    })
    
    try {
      console.log('开始登录流程');
      // 微信登录获取code
      const loginRes = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
        wx.login({
          timeout: 10000, // 设置10秒超时
          success: resolve,
          fail: (err) => {
            console.error('wx.login获取code失败:', err);
            reject(new Error('获取登录凭证失败，请检查网络后重试'));
          }
        })
      })
      
      if (!loginRes.code) {
        const error = new Error('登录失败，未获取到code');
        console.error(error.message);
        throw error;
      }
      
      console.log('成功获取微信登录code');
      
      // 调用后端登录接口
      let result;
      try {
          console.log('调用后端登录接口');
          result = await login(loginRes.code);
      } catch (apiError) {
        console.error('登录接口调用失败:', apiError);
        // 如果是网络错误，尝试重试
        if (this.data.retryCount < 2) {
          console.log(`登录重试 (${this.data.retryCount + 1}/2)`);
          this.setData({ retryCount: this.data.retryCount + 1 });
          setTimeout(() => {
            this.setData({ isLogining: false });
            this.onLogin();
          }, 1000);
          return;
      } else {
          throw new Error('登录服务暂时不可用，请稍后再试');
        }
      }
      
      if (result.error === 0) {
        console.log('登录成功，保存token和用户信息');
        // 保存token
        wx.setStorageSync('token', result.body.token)
        
        // 使用后端返回的用户信息
        const userData = {
          userId: String(result.body.userId), // 确保userId是字符串类型
          nickName: result.body.userInfo.nickName || '新用户', // 使用后端返回的昵称
          avatarUrl: result.body.userInfo.avatarUrl || '', // 使用后端返回的头像
          gender: result.body.userInfo.gender || 0 // 使用后端返回的性别
        }
        wx.setStorageSync('userInfo', userData)
        
        // 更新全局状态
        app.setLoginStatus(true, userData)
        
        // 检查是否需要创建默认厨房
        try {
          console.log('检查是否需要创建默认厨房');
          await this.initDefaultKitchenIfNeeded()
        } catch (kitchenError) {
          console.warn('创建默认厨房失败，但不阻止登录流程:', kitchenError);
          // 初始化失败不阻止登录流程
        }
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        console.log('登录流程完成，准备返回');
        // 延迟返回，显示登录成功提示
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        // 处理业务错误
        const errorMsg = result.message || `登录失败 (错误码:${result.error})`;
        console.error('登录失败，业务错误:', errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败，请重试';
      console.error('登录过程发生错误:', errorMessage);
      
      // 显示错误消息
      this.setData({
        errorMessage: errorMessage
      })
      
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    } finally {
      this.setData({
        isLogining: false
      })
    }
  },
  
  // 为新用户创建默认厨房和分类
  async initDefaultKitchenIfNeeded() {
    try {
      console.log('检查用户是否已有厨房')
      
      // 调用API获取用户厨房列表，而不是仅检查本地存储
      const kitchenListResult = await getKitchenList()
      
      // 如果获取厨房列表成功并且用户没有厨房，则创建默认厨房
      if (kitchenListResult.error === 0 && 
          (!kitchenListResult.body || kitchenListResult.body.length === 0)) {
        console.log('用户没有厨房，创建默认厨房')
        
        // 获取用户信息
        const userInfo = wx.getStorageSync('userInfo') || {}
        const nickName = userInfo.nickName || '新用户'
        
        // 创建默认厨房
        const kitchenResult = await createKitchen({
          name: nickName + '的厨房', // 使用用户昵称作为厨房名称
          notice: '欢迎来到我的厨房',
          avatarUrl: '' // 默认不设置厨房头像
        })
        
        if (kitchenResult.error === 0) {
          // 厨房创建成功，保存厨房信息
          wx.setStorageSync('currentKitchen', kitchenResult.body)
          
          // 创建默认分类"热销套餐"
          try {
            const categoryResult = await addCategory('热销套餐', '/static/images/icons/class/bento.png', kitchenResult.body.id)
            
            if (categoryResult.error === 0) {
              console.log('默认分类创建成功')
            } else {
              console.error('创建默认分类失败', categoryResult.message)
            }
          } catch (categoryError) {
            console.error('创建默认分类时发生错误:', categoryError)
          }
        } else {
          console.error('创建默认厨房失败', kitchenResult.message)
        }
      } else {
        console.log('用户已有厨房，无需创建默认厨房')
        
        // 如果用户已有厨房但本地没有存储，则保存第一个厨房到本地
        if (kitchenListResult.body && kitchenListResult.body.length > 0 && 
            !wx.getStorageSync('currentKitchen')) {
          console.log('保存用户现有厨房到本地存储')
          wx.setStorageSync('currentKitchen', kitchenListResult.body[0])
        }
      }
    } catch (error) {
      console.error('初始化默认厨房失败', error)
      // 初始化失败不阻止登录流程，但重新抛出以便于上层函数记录
      throw error;
    }
  },
  
  // 显示用户协议和隐私政策弹窗
  showPrivacyDialog() {
    this.setData({
      showPrivacyModal: true
    })
  },
  
  // 关闭用户协议和隐私政策弹窗
  closePrivacyDialog() {
    this.setData({
      showPrivacyModal: false
    })
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  },

  // 重置错误状态
  resetError() {
    this.setData({
      errorMessage: '',
      retryCount: 0
    });
  }
}) 