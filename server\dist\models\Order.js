"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 订单模型
 * 存储用户订单信息
 */
const sequelize_1 = require("sequelize");
const database_1 = __importDefault(require("../config/database"));
const helpers_1 = require("../utils/helpers");
// 订单模型类
class Order extends sequelize_1.Model {
    // 检查订单是否可以取消
    canCancel() {
        return this.status === 'pending';
    }
    // 检查订单是否可以接单
    canAccept() {
        return this.status === 'pending';
    }
    // 检查订单是否可以开始烹饪
    canStartCooking() {
        return this.status === 'accepted';
    }
    // 检查订单是否可以完成
    canComplete() {
        return this.status === 'cooking';
    }
    // 检查订单是否可以取消烹饪
    canCancelCooking() {
        return this.status === 'cooking';
    }
}
// 初始化订单模型
Order.init({
    id: {
        type: sequelize_1.DataTypes.STRING(20),
        primaryKey: true,
        defaultValue: () => (0, helpers_1.generateOrderId)(),
        comment: '订单ID',
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    kitchen_id: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: false,
        comment: '厨房ID',
        references: {
            model: 'kitchens',
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    total_price: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        comment: '订单总价',
    },
    status: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'pending',
        comment: '订单状态(pending:待接单,accepted:已接单,cooking:烹饪中,completed:已完成,cancelled:已取消)',
        validate: {
            isIn: [['pending', 'accepted', 'cooking', 'completed', 'cancelled']],
        },
    },
    table_no: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        comment: '桌号',
    },
    remark: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        comment: '备注',
    },
    cooking_time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '接单时间',
    },
    completed_time: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '完成时间',
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        defaultValue: sequelize_1.DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    sequelize: database_1.default,
    modelName: 'Order',
    tableName: 'orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['user_id'],
        },
        {
            name: 'idx_kitchen_id',
            fields: ['kitchen_id'],
        },
        {
            name: 'idx_status',
            fields: ['status'],
        },
        {
            name: 'idx_created_at',
            fields: ['created_at'],
        },
    ],
});
exports.default = Order;
//# sourceMappingURL=Order.js.map