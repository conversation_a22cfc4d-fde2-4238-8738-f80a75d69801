"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 微信支付服务
 * 基于微信支付 API v3
 */
const fs_1 = __importDefault(require("fs"));
const config_1 = __importDefault(require("../config/config"));
const logger_1 = __importDefault(require("../utils/logger"));
const error_1 = require("../middlewares/error");
// 使用require导入微信支付模块
const WxPay = require('wechatpay-node-v3');
class PaymentService {
    constructor() {
        this.appId = config_1.default.wechatPay.appId;
        this.mchId = config_1.default.wechatPay.mchIdV3 || config_1.default.wechatPay.mchId;
        this.apiV3Key = config_1.default.wechatPay.apiV3Key;
        this.serialNo = config_1.default.wechatPay.serialNo;
        this.notifyUrl = config_1.default.wechatPay.notifyUrl;
        // 读取商户私钥和证书
        try {
            this.privateKey = fs_1.default.readFileSync(config_1.default.wechatPay.privateKeyPath, 'utf8');
            this.publicKey = fs_1.default.readFileSync(config_1.default.wechatPay.certificatePath, 'utf8');
        }
        catch (error) {
            logger_1.default.error('读取微信支付证书失败:', error);
            throw new error_1.BusinessError('微信支付配置错误，证书文件不存在');
        }
        // 初始化微信支付实例
        this.payment = new WxPay({
            appid: this.appId,
            mchid: this.mchId,
            publicKey: this.publicKey,
            privateKey: this.privateKey,
            key: this.apiV3Key,
        });
        logger_1.default.info('微信支付服务初始化成功');
    }
    /**
     * 小程序统一下单
     * @param params 下单参数
     * @returns 支付参数
     */
    jsapiOrder(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orderParams = {
                    appid: this.appId,
                    mchid: this.mchId,
                    description: params.description,
                    out_trade_no: params.outTradeNo,
                    time_expire: params.timeExpire || this.getExpireTime(),
                    attach: params.attach || '',
                    notify_url: this.notifyUrl,
                    amount: {
                        total: params.totalFee,
                        currency: 'CNY'
                    },
                    payer: {
                        openid: params.openid
                    }
                };
                logger_1.default.info('微信支付统一下单请求参数:');
                logger_1.default.info(JSON.stringify(orderParams, null, 2));
                // 调用统一下单接口
                const result = yield this.payment.transactions_jsapi(orderParams);
                logger_1.default.info('微信支付统一下单响应:');
                logger_1.default.info(JSON.stringify(result, null, 2));
                // 检查响应是否包含错误
                if (result.status && result.status !== 200) {
                    logger_1.default.error('微信支付统一下单返回错误状态:');
                    logger_1.default.error(JSON.stringify(result, null, 2));
                    throw new error_1.BusinessError(`微信支付下单失败: ${result.error || '未知错误'}`);
                }
                // 检查是否成功获取到支付参数
                if (!result.data || !result.data.package) {
                    logger_1.default.error('微信支付响应数据格式错误:', result);
                    throw new error_1.BusinessError('获取预支付ID失败');
                }
                // 从package中提取prepay_id
                const packageStr = result.data.package;
                const prepayIdMatch = packageStr.match(/prepay_id=(.+)/);
                if (!prepayIdMatch) {
                    logger_1.default.error('无法从package中提取prepay_id:', packageStr);
                    throw new error_1.BusinessError('获取预支付ID失败');
                }
                const prepayId = prepayIdMatch[1];
                logger_1.default.info('成功获取prepay_id:', prepayId);
                return Object.assign(Object.assign({}, result.data), { prepayId: prepayId, outTradeNo: params.outTradeNo });
            }
            catch (error) {
                logger_1.default.error('微信支付统一下单失败:', error);
                if (error instanceof error_1.BusinessError) {
                    throw error;
                }
                throw new error_1.BusinessError(`微信支付下单失败: ${error.message}`);
            }
        });
    }
    /**
     * 生成小程序支付参数
     * @param prepayId 预支付ID
     * @returns 支付参数
     */
    generatePayParams(prepayId) {
        const timeStamp = Math.floor(Date.now() / 1000).toString();
        const nonceStr = this.generateNonceStr();
        const packageStr = `prepay_id=${prepayId}`;
        const signType = 'RSA';
        // 生成签名
        const signMessage = `${this.appId}\n${timeStamp}\n${nonceStr}\n${packageStr}\n`;
        const paySign = this.payment.getSignature('POST', nonceStr, timeStamp, '/v3/pay/transactions/jsapi', signMessage);
        return {
            timeStamp,
            nonceStr,
            package: packageStr,
            signType,
            paySign
        };
    }
    /**
     * 查询订单
     * @param outTradeNo 商户订单号
     * @returns 订单信息
     */
    queryOrder(outTradeNo) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d;
            try {
                logger_1.default.info('查询微信支付订单:', outTradeNo);
                const result = yield this.payment.query({
                    out_trade_no: outTradeNo,
                    mchid: this.mchId
                });
                logger_1.default.info('微信支付订单查询结果:', JSON.stringify(result, null, 2));
                return {
                    transaction_id: result.transaction_id,
                    out_trade_no: result.out_trade_no,
                    trade_state: result.trade_state,
                    trade_state_desc: result.trade_state_desc,
                    bank_type: result.bank_type || '',
                    total_fee: ((_a = result.amount) === null || _a === void 0 ? void 0 : _a.total) || 0,
                    cash_fee: ((_b = result.amount) === null || _b === void 0 ? void 0 : _b.payer_total) || ((_c = result.amount) === null || _c === void 0 ? void 0 : _c.total) || 0,
                    success_time: result.success_time,
                    payer: {
                        openid: ((_d = result.payer) === null || _d === void 0 ? void 0 : _d.openid) || ''
                    }
                };
            }
            catch (error) {
                logger_1.default.error('查询微信支付订单失败:', error);
                throw new error_1.BusinessError(`查询订单失败: ${error.message}`);
            }
        });
    }
    /**
     * 关闭订单
     * @param outTradeNo 商户订单号
     */
    closeOrder(outTradeNo) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                logger_1.default.info('关闭微信支付订单:', outTradeNo);
                yield this.payment.close({
                    out_trade_no: outTradeNo,
                    mchid: this.mchId
                });
                logger_1.default.info('微信支付订单关闭成功:', outTradeNo);
            }
            catch (error) {
                logger_1.default.error('关闭微信支付订单失败:', error);
                throw new error_1.BusinessError(`关闭订单失败: ${error.message}`);
            }
        });
    }
    /**
     * 验证支付回调通知
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体
     * @param serial 证书序列号
     * @returns 验证结果
     */
    verifyNotification(signature, timestamp, nonce, body, serial) {
        try {
            return this.payment.verifySign({
                timestamp,
                nonce,
                serial,
                signature,
                body
            });
        }
        catch (error) {
            logger_1.default.error('验证支付通知签名失败:', error);
            return false;
        }
    }
    /**
     * 解密支付通知数据
     * @param encryptedData 加密数据
     * @param nonce 随机字符串
     * @param associatedData 关联数据
     * @returns 解密后的数据
     */
    decryptNotification(encryptedData, nonce, associatedData) {
        try {
            const decryptedData = this.payment.decipher_gcm(encryptedData, nonce, associatedData);
            return JSON.parse(decryptedData);
        }
        catch (error) {
            logger_1.default.error('解密支付通知数据失败:', error);
            throw new error_1.BusinessError('解密通知数据失败');
        }
    }
    /**
     * 生成随机字符串
     * @param length 长度
     * @returns 随机字符串
     */
    generateNonceStr(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    /**
     * 获取订单过期时间
     * @returns ISO 8601格式的时间字符串
     */
    getExpireTime() {
        const expireTime = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期
        return expireTime.toISOString();
    }
    /**
     * 申请退款
     * @param outTradeNo 商户订单号
     * @param outRefundNo 商户退款单号
     * @param totalFee 订单总金额
     * @param refundFee 退款金额
     * @param reason 退款原因
     * @returns 退款结果
     */
    refund(outTradeNo, outRefundNo, totalFee, refundFee, reason) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const refundParams = {
                    out_trade_no: outTradeNo,
                    out_refund_no: outRefundNo,
                    reason: reason || '用户申请退款',
                    notify_url: this.notifyUrl.replace('/notify', '/refund-notify'),
                    amount: {
                        refund: refundFee,
                        total: totalFee,
                        currency: 'CNY'
                    }
                };
                logger_1.default.info('微信支付申请退款请求参数:', JSON.stringify(refundParams, null, 2));
                const result = yield this.payment.refunds(refundParams);
                logger_1.default.info('微信支付申请退款响应:', JSON.stringify(result, null, 2));
                return result;
            }
            catch (error) {
                logger_1.default.error('微信支付申请退款失败:', error);
                throw new error_1.BusinessError(`申请退款失败: ${error.message}`);
            }
        });
    }
    /**
     * 查询退款
     * @param outRefundNo 商户退款单号
     * @returns 退款信息
     */
    queryRefund(outRefundNo) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                logger_1.default.info('查询微信支付退款:', outRefundNo);
                const result = yield this.payment.find_refunds({
                    out_refund_no: outRefundNo
                });
                logger_1.default.info('微信支付退款查询结果:', JSON.stringify(result, null, 2));
                return result;
            }
            catch (error) {
                logger_1.default.error('查询微信支付退款失败:', error);
                throw new error_1.BusinessError(`查询退款失败: ${error.message}`);
            }
        });
    }
}
// 创建单例
const paymentService = new PaymentService();
exports.default = paymentService;
//# sourceMappingURL=paymentService.js.map