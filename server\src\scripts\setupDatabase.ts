/**
 * 数据库设置主脚本
 * 按顺序执行所有初始化、迁移和种子数据脚本
 */
import logger from '../utils/logger';
import initDatabase from './initDatabase';
import createTables from './migrations/createTables';
import seedSystemSettings from './seeds/seedSystemSettings';

/**
 * 解析命令行参数
 * @returns 命令行参数对象
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options: any = {
    init: false,
    migrate: false,
    seed: false,
    all: false,
  };

  if (args.length === 0) {
    options.all = true;
  } else {
    for (const arg of args) {
      switch (arg) {
        case '--init':
          options.init = true;
          break;
        case '--migrate':
          options.migrate = true;
          break;
        case '--seed':
          options.seed = true;
          break;
        case '--all':
          options.all = true;
          break;
        default:
          logger.warn(`未知参数: ${arg}`);
      }
    }
  }

  return options;
}

/**
 * 设置数据库
 */
async function setupDatabase() {
  const options = parseArgs();

  logger.info('开始设置数据库...');
  logger.info(`选项: ${JSON.stringify(options)}`);

  try {
    // 初始化数据库
    if (options.init || options.all) {
      logger.info('=== 初始化数据库 ===');
      await initDatabase();
    }

    // 创建表
    if (options.migrate || options.all) {
      logger.info('=== 创建数据库表 ===');
      await createTables();
    }

    // 添加种子数据
    if (options.seed || options.all) {
      logger.info('=== 添加种子数据 ===');

      logger.info('--- 创建系统设置数据 ---');
      await seedSystemSettings();
    }

    logger.info('数据库设置完成');
  } catch (error) {
    logger.error('数据库设置失败:');
    if (error instanceof Error) {
      logger.error(`错误消息: ${error.message}`);
      logger.error(`错误堆栈: ${error.stack}`);
    } else {
      logger.error(`未知错误: ${error}`);
    }
    throw error;
  }
}

// 如果直接运行此脚本，则执行设置数据库
if (require.main === module) {
  setupDatabase()
    .then(() => {
      logger.info('数据库设置脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('数据库设置脚本执行失败:', error);
      process.exit(1);
    });
}

export default setupDatabase;
