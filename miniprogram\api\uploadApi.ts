/**
 * 上传API
 * 处理文件上传相关的API调用
 */

import { SERVER_BASE_URL } from '../utils/constants'

// 服务器地址
const BASE_URL = SERVER_BASE_URL;

/**
 * 上传图片
 * @param filePath 本地文件路径
 * @param type 图片类型（dish-菜品图片, category-分类图标, kitchen-厨房头像, avatar-用户头像, background-背景图片）
 * @param kitchenId 厨房ID（厨房头像、菜品图片、分类图标需要）
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadImage = (
  filePath: string, 
  type: 'dish' | 'category' | 'kitchen' | 'avatar' | 'background' = 'dish',
  kitchenId?: string
) => {
  return new Promise((resolve, reject) => {
    // 获取用户token
    const token = wx.getStorageSync('token') || '';
    
    console.log('上传图片:', filePath, '类型:', type, '厨房ID:', kitchenId);
    
    wx.showLoading({
      title: '压缩上传中...',
    });
    
    // 根据类型选择上传接口
    let uploadUrl = '';
    switch (type) {
      case 'dish':
        uploadUrl = '/api/upload/dishImage';
        break;
      case 'category':
        uploadUrl = '/api/upload/categoryIcon';
        break;
      case 'kitchen':
        uploadUrl = '/api/upload/kitchenAvatar';
        break;
      case 'avatar':
        uploadUrl = '/api/upload/userAvatar';
        break;
      case 'background':
        uploadUrl = '/api/upload/backgroundImage';
        break;
      default:
        uploadUrl = '/api/upload/dishImage';
    }
    
    const fullUrl = BASE_URL + uploadUrl;
    console.log('上传URL:', fullUrl);
    console.log('认证Token:', token ? '已设置' : '未设置');
    
    // 准备表单数据
    const formData: any = {};
    if (kitchenId && (type === 'kitchen' || type === 'dish' || type === 'category')) {
      formData.kitchenId = kitchenId;
    }
    
    wx.uploadFile({
      url: fullUrl,
      filePath: filePath,
      name: 'file', // 修改为服务器期望的字段名称
      formData: formData, // 传递厨房ID等参数
      header: {
        'Authorization': `Bearer ${token}` // 使用Bearer Token认证
      },
      timeout: 20000, // 20秒超时时间
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          console.log('上传结果:', data);
          
          // 转换响应格式以匹配请求函数的格式
          if (data.error === 0 && data.body) {
            // 兼容处理：后端可能返回imageUrl或url字段
            const imageUrl = data.body.imageUrl || data.body.url;
            console.log('解析到的图片URL:', imageUrl);
            
            if (imageUrl) {
            resolve({
              error: 0,
              body: {
                  imageUrl: imageUrl
              },
              message: ''
            });
            } else {
              console.error('上传成功但未返回图片URL，完整响应:', data);
              reject({
                error: 500,
                body: null,
                message: '上传成功但未返回图片URL'
              });
            }
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
            console.error('上传API返回错误:', data);
            reject({
              error: data.error || 500,
              body: null,
              message: data.message || '上传失败'
            });
          }
        } catch (e) {
          console.error('解析响应失败:', e);
          wx.showToast({
            title: '解析响应失败',
            icon: 'none'
          });
          reject({
            error: 500,
            body: null,
            message: '解析响应失败'
          });
        }
      },
      fail: (err) => {
        console.error('上传请求失败:', err);
        wx.showToast({
          title: '上传失败，请检查网络',
          icon: 'none'
        });
        reject({
          error: 500,
          body: null,
          message: '上传失败，请检查网络'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  });
};

/**
 * 上传菜品图片
 * @param filePath 本地文件路径
 * @param kitchenId 厨房ID
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadDishImage = (filePath: string, kitchenId: string) => {
  return uploadImage(filePath, 'dish', kitchenId);
};

/**
 * 上传分类图标
 * @param filePath 本地文件路径
 * @param kitchenId 厨房ID
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadCategoryIcon = (filePath: string, kitchenId: string) => {
  return uploadImage(filePath, 'category', kitchenId);
};

/**
 * 上传厨房头像
 * @param filePath 本地文件路径
 * @param kitchenId 厨房ID
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadKitchenAvatar = (filePath: string, kitchenId?: string) => {
  return uploadImage(filePath, 'kitchen', kitchenId);
};

/**
 * 上传用户头像
 * @param filePath 本地文件路径
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadUserAvatar = (filePath: string) => {
  return uploadImage(filePath, 'avatar');
};

/**
 * 上传背景图片
 * @param filePath 本地文件路径
 * @returns Promise<{error: number, body: {imageUrl: string}, message: string}>
 */
export const uploadBackgroundImage = (filePath: string) => {
  return uploadImage(filePath, 'background');
};

export default {
  uploadImage,
  uploadDishImage,
  uploadCategoryIcon,
  uploadKitchenAvatar,
  uploadUserAvatar,
  uploadBackgroundImage
};
