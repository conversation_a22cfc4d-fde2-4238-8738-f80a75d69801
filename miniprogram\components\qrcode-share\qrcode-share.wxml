<!-- 厨房二维码分享组件 -->
<modal-dialog
  visible="{{visible}}"
  title="分享厨房"
  showCancel="{{!isSharedKitchen}}"
  cancelText="刷新二维码"
  confirmText="保存图片"
  bind:close="onClose"
  bind:cancel="onRefresh"
  bind:confirm="saveQrCode"
>
  <view class="qrcode-container">
    <view class="kitchen-info">
      <smart-image 
        class="kitchen-avatar" 
        src="{{kitchenInfo.avatarUrl || defaultImages.KITCHEN_AVATAR}}" 
        mode="aspectFill"
        width="80rpx"
        height="80rpx"
        borderRadius="40"
        lazy="{{false}}"
        showAnimation="{{true}}"
      ></smart-image>
      <view class="kitchen-detail">
        <text class="kitchen-name">{{kitchenInfo.name}}</text>
        <text class="kitchen-id">ID: {{kitchenInfo.id || kitchenId}}</text>
      </view>
    </view>

    <view class="qrcode-wrapper">
      <smart-image 
        wx:if="{{qrCodeUrl}}" 
        class="qrcode-image" 
        src="{{qrCodeUrl}}" 
        mode="aspectFit"
        width="100%"
        height="100%"
        borderRadius="0"
        lazy="{{false}}"
        showAnimation="{{true}}"
      ></smart-image>
      <view wx:else class="qrcode-loading">
        <text class="loading-text">二维码准备中</text>
      </view>
    </view>

    <view class="share-tips">
      <text class="tips-text">长按二维码可保存或分享给好友</text>
      <text class="tips-text">扫描二维码可直接进入此厨房</text>
      <text wx:if="{{!isSharedKitchen}}" class="tips-text">点击"刷新二维码"可重新生成</text>
    </view>
  </view>
</modal-dialog>